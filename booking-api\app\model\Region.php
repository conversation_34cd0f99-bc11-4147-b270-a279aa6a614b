<?php

namespace app\model;

/**
 * 地区模型
 * 对应数据库表：regions
 */
class Region extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'regions';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name',
        'parent_id',
        'level',
        'sort_order',
        'status'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'parent_id' => 'integer',
        'level' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 地区层级常量
     */
    const LEVEL_PROVINCE = 1;
    const LEVEL_CITY = 2;
    const LEVEL_DISTRICT = 3;

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取父级地区
     */
    public function parent()
    {
        return $this->belongsTo(Region::class, 'parent_id');
    }

    /**
     * 获取子级地区
     */
    public function children()
    {
        return $this->hasMany(Region::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * 获取所有酒店
     */
    public function hotels()
    {
        return $this->hasMany(Hotel::class, 'city', 'name');
    }

    /**
     * 作用域：省份
     */
    public function scopeProvinces($query)
    {
        return $query->where('level', self::LEVEL_PROVINCE);
    }

    /**
     * 作用域：城市
     */
    public function scopeCities($query)
    {
        return $query->where('level', self::LEVEL_CITY);
    }

    /**
     * 作用域：区县
     */
    public function scopeDistricts($query)
    {
        return $query->where('level', self::LEVEL_DISTRICT);
    }

    /**
     * 作用域：按父级ID筛选
     */
    public function scopeByParent($query, $parentId)
    {
        return $query->where('parent_id', $parentId);
    }

    /**
     * 获取层级名称
     */
    public function getLevelNameAttribute()
    {
        $levels = [
            self::LEVEL_PROVINCE => '省份',
            self::LEVEL_CITY => '城市',
            self::LEVEL_DISTRICT => '区县',
        ];

        return $levels[$this->level] ?? '未知';
    }

    /**
     * 获取完整路径
     */
    public function getFullPathAttribute()
    {
        $path = [$this->name];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($path, $parent->name);
            $parent = $parent->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * 作用域：启用状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 获取层级树
     */
    public static function getTree($parentId = null)
    {
        return static::where('parent_id', $parentId)
            ->active()
            ->orderBy('sort_order')
            ->with(['children' => function ($query) {
                $query->active()->orderBy('sort_order');
            }])
            ->get();
    }
}
