<?php

namespace app\model;

use support\Model;

/**
 * 酒店停车政策模型
 */
class HotelPoliciesParkingPolicy extends Model
{
    /**
     * 表名
     */
    protected $table = 'hotel_policies_parking_policies';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'hotel_id',
        'parking_available',
        'parking_type',
        'parking_fee',
        'parking_fee_unit',
        'parking_spaces',
        'parking_location',
        'valet_service',
        'valet_fee',
        'electric_charging',
        'charging_fee',
        'reservation_required',
        'height_restriction',
        'parking_notes',
        'status',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'parking_available' => 'boolean',
        'parking_fee' => 'decimal:2',
        'parking_spaces' => 'integer',
        'valet_service' => 'boolean',
        'valet_fee' => 'decimal:2',
        'electric_charging' => 'boolean',
        'charging_fee' => 'decimal:2',
        'reservation_required' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(): array
    {
        return [
            'hotel_id' => 'required|integer|min:1',
            'parking_available' => 'boolean',
            'parking_type' => 'nullable|string|in:free,paid,valet',
            'parking_fee' => 'nullable|numeric|min:0',
            'parking_fee_unit' => 'nullable|string|in:per_hour,per_night,per_day',
            'parking_spaces' => 'nullable|integer|min:0',
            'parking_location' => 'nullable|string|max:200',
            'valet_service' => 'boolean',
            'valet_fee' => 'nullable|numeric|min:0',
            'electric_charging' => 'boolean',
            'charging_fee' => 'nullable|numeric|min:0',
            'reservation_required' => 'boolean',
            'height_restriction' => 'nullable|string|max:50',
            'parking_notes' => 'nullable|string',
            'status' => 'in:active,inactive',
        ];
    }

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id', 'id');
    }

    /**
     * 作用域：启用的政策
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 获取政策类型
     */
    public function getPolicyType(): string
    {
        return 'parking';
    }

    /**
     * 获取政策显示名称
     */
    public function getPolicyDisplayName(): string
    {
        return '停车场政策';
    }

    /**
     * 获取政策英文名称
     */
    public function getPolicyEnglishName(): string
    {
        return 'Parking Policy';
    }

    /**
     * 获取停车类型显示文本
     */
    public function getParkingTypeText(): string
    {
        $types = [
            'free' => '免费停车',
            'paid' => '收费停车',
            'valet' => '代客泊车',
        ];
        return $types[$this->parking_type] ?? $this->parking_type ?? '-';
    }

    /**
     * 获取费用单位显示文本
     */
    public function getFeeUnitText(): string
    {
        $units = [
            'per_hour' => '每小时',
            'per_night' => '每晚',
            'per_day' => '每天',
        ];
        return $units[$this->parking_fee_unit] ?? $this->parking_fee_unit ?? '每晚';
    }

    /**
     * 格式化停车费用显示
     */
    public function getFormattedParkingFee(): string
    {
        if (!$this->parking_fee) {
            return '免费';
        }
        return $this->parking_fee . '元/' . $this->getFeeUnitText();
    }

    /**
     * 创建默认政策
     */
    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId,
            'parking_available' => false,
            'parking_type' => 'paid',
            'parking_fee_unit' => 'per_night',
            'valet_service' => false,
            'electric_charging' => false,
            'reservation_required' => false,
            'status' => 'active',
        ]);
    }
}
