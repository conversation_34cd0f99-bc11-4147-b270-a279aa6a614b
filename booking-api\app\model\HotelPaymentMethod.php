<?php

namespace app\model;

use support\Model;

/**
 * 酒店支付方式模型
 */
class HotelPaymentMethod extends Model
{
    /**
     * 表名
     */
    protected $table = 'hotel_payment_methods';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'hotel_id',
        'payment_id',
        'payment_name',
        'payment_code',
        'payment_type',
        'bookable',
        'cvc_required',
        'payable',
        'sort_order',
        'status',
        'description',
        'is_default',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'payment_id' => 'integer',
        'bookable' => 'boolean',
        'cvc_required' => 'boolean',
        'payable' => 'boolean',
        'sort_order' => 'integer',
        'is_default' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(): array
    {
        return [
            'hotel_id' => 'required|integer|min:1',
            'payment_name' => 'required|string|max:100',
            'payment_code' => 'nullable|string|max:50',
            'payment_type' => 'nullable|string|max:50',
            'bookable' => 'boolean',
            'cvc_required' => 'boolean',
            'payable' => 'boolean',
            'sort_order' => 'integer|min:0|max:9999',
            'status' => 'in:active,inactive',
            'description' => 'nullable|string|max:500',
            'is_default' => 'boolean',
        ];
    }

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id', 'id');
    }

    /**
     * 关联支付方式
     */
    public function payment()
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_id', 'id');
    }

    /**
     * 作用域：启用的支付方式
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：可预订的支付方式
     */
    public function scopeBookable($query)
    {
        return $query->where('bookable', true);
    }

    /**
     * 作用域：可支付的支付方式
     */
    public function scopePayable($query)
    {
        return $query->where('payable', true);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取支付方式图标
     */
    public function getIcon(): string
    {
        $iconMap = [
            'credit_card' => 'credit-card',
            'debit_card' => 'credit-card',
            'alipay' => 'smartphone',
            'wechat_pay' => 'smartphone',
            'bank_transfer' => 'building-2',
            'cash' => 'banknote',
            'paypal' => 'credit-card',
            'apple_pay' => 'smartphone',
            'google_pay' => 'smartphone',
        ];
        
        return $iconMap[$this->payment_type] ?? 'credit-card';
    }

    /**
     * 获取支付方式标签
     */
    public function getLabel(): string
    {
        $labelMap = [
            'credit_card' => '信用卡',
            'debit_card' => '借记卡',
            'alipay' => '支付宝',
            'wechat_pay' => '微信支付',
            'bank_transfer' => '银行转账',
            'cash' => '现金',
            'paypal' => 'PayPal',
            'apple_pay' => 'Apple Pay',
            'google_pay' => 'Google Pay',
        ];
        
        return $labelMap[$this->payment_type] ?? $this->payment_name;
    }

    /**
     * 设置为默认支付方式
     */
    public function setAsDefault(): bool
    {
        // 先取消同一酒店的其他默认支付方式
        self::where('hotel_id', $this->hotel_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);
        
        // 设置当前支付方式为默认
        return $this->update(['is_default' => true]);
    }

    /**
     * 获取酒店的默认支付方式
     */
    public static function getDefaultByHotel(int $hotelId): ?self
    {
        return self::where('hotel_id', $hotelId)
            ->where('is_default', true)
            ->where('status', 'active')
            ->first();
    }

    /**
     * 获取酒店的可预订支付方式
     */
    public static function getBookableByHotel(int $hotelId): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('hotel_id', $hotelId)
            ->where('status', 'active')
            ->where('bookable', true)
            ->ordered()
            ->get();
    }

    /**
     * 获取酒店的可支付支付方式
     */
    public static function getPayableByHotel(int $hotelId): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('hotel_id', $hotelId)
            ->where('status', 'active')
            ->where('payable', true)
            ->ordered()
            ->get();
    }

    /**
     * 批量更新排序
     */
    public static function updateSortOrders(array $orders): bool
    {
        try {
            foreach ($orders as $order) {
                if (isset($order['id']) && isset($order['sort_order'])) {
                    self::where('id', $order['id'])
                        ->update(['sort_order' => $order['sort_order']]);
                }
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 创建默认支付方式
     */
    public static function createDefaults(int $hotelId): array
    {
        $defaults = [
            [
                'hotel_id' => $hotelId,
                'payment_name' => '现金',
                'payment_code' => 'CASH',
                'payment_type' => 'cash',
                'bookable' => true,
                'cvc_required' => false,
                'payable' => true,
                'sort_order' => 1,
                'status' => 'active',
                'description' => '现金支付',
                'is_default' => true,
            ],
            [
                'hotel_id' => $hotelId,
                'payment_name' => '信用卡',
                'payment_code' => 'CREDIT_CARD',
                'payment_type' => 'credit_card',
                'bookable' => true,
                'cvc_required' => true,
                'payable' => true,
                'sort_order' => 2,
                'status' => 'active',
                'description' => '信用卡支付',
                'is_default' => false,
            ],
        ];

        $created = [];
        foreach ($defaults as $default) {
            $created[] = self::create($default);
        }

        return $created;
    }

    /**
     * 转换为数组格式
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'hotel_id' => $this->hotel_id,
            'payment_id' => $this->payment_id,
            'payment_name' => $this->payment_name,
            'payment_code' => $this->payment_code,
            'payment_type' => $this->payment_type,
            'bookable' => $this->bookable,
            'cvc_required' => $this->cvc_required,
            'payable' => $this->payable,
            'sort_order' => $this->sort_order,
            'status' => $this->status,
            'description' => $this->description,
            'is_default' => $this->is_default,
            'icon' => $this->getIcon(),
            'label' => $this->getLabel(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
