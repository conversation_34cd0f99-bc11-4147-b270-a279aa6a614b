<?php

namespace base\AuditLog\traits;

use base\AuditLog\AirLog;

trait Message
{
    /**
     * 描述
     *
     * @var array|string
     */
    protected string|array $message;

    protected array $desc = [];
    /**
     * 当前日志描述模板
     *
     * @var string
     */
    private string $currentMessage = '';

    /**
     * 设置当前描述模板
     *
     * @param $message
     * @return AirLog|Message
     */
    public function message($message): self
    {
        $this->currentMessage = $message;

        return $this;
    }

    private function getMessage(): string
    {
        if ( ! $this->currentMessage) {
            if ($this->currentScene && is_array($this->message)) {
                if (isset($this->message[$this->currentScene])) {
                    $this->currentMessage = $this->message[$this->currentScene];
                } else {
                    $this->currentMessage = $this->currentScene;
                }
            } elseif (is_string($this->message)) {
                $this->currentMessage = $this->message;
            }
        }

        return $this->currentMessage;
    }
}
