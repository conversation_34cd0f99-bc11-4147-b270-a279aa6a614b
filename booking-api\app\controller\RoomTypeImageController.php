<?php

namespace app\controller;

use app\model\RoomTypeImage;
use support\Request;
use support\Response;

/**
 * 房型图片控制器
 */
class RoomTypeImageController extends BaseController
{
    /**
     * REST: 列表 获取指定房型的图片
     */
    public function index(Request $request, $id): Response
    {
        try {
            $roomType = \app\model\RoomType::find($id);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $images = RoomTypeImage::where('room_type_id', (int)$id)
                ->where('status', true)
                ->orderBy('display_order', 'asc')
                ->orderBy('id', 'asc')
                ->get();

            return $this->success($images);
        } catch (\Exception $e) {
            return $this->error('获取房型图片失败: ' . $e->getMessage());
        }
    }

    /**
     * REST: 获取单个图片
     */
    public function show(Request $request, $id, $image_id): Response
    {
        try {
            $image = RoomTypeImage::where('room_type_id', (int)$id)
                ->where('id', (int)$image_id)
                ->first();

            if (!$image) {
                return $this->error('图片不存在', 404);
            }

            return $this->success($image);
        } catch (\Exception $e) {
            return $this->error('获取图片失败: ' . $e->getMessage());
        }
    }

    /**
     * REST: 创建图片
     */
    public function store(Request $request, $id): Response
    {
        try {
            $roomType = \app\model\RoomType::find($id);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $data = $request->post();
            $data['room_type_id'] = (int)$id;

            if (!isset($data['image_url']) || empty($data['image_url'])) {
                return $this->error('image_url 不能为空', 400);
            }

            if (!isset($data['display_order'])) {
                $data['display_order'] = RoomTypeImage::getNextDisplayOrder((int)$id);
            }
            if (!isset($data['status'])) {
                $data['status'] = true;
            }

            $image = RoomTypeImage::create($data);

            if (!empty($data['is_primary'])) {
                $image->setPrimary();
            }

            return $this->success($image->fresh(), '创建成功');
        } catch (\Exception $e) {
            return $this->error('创建图片失败: ' . $e->getMessage());
        }
    }

    /**
     * REST: 更新图片
     */
    public function update(Request $request, $id, $image_id): Response
    {
        try {
            $image = RoomTypeImage::where('room_type_id', (int)$id)
                ->where('id', (int)$image_id)
                ->first();

            if (!$image) {
                return $this->error('图片不存在', 404);
            }

            $data = $request->post();
            if (isset($data['room_type_id'])) {
                unset($data['room_type_id']);
            }

            $image->update($data);
            if (!empty($data['is_primary'])) {
                $image->setPrimary();
            }

            return $this->success($image->fresh(), '更新成功');
        } catch (\Exception $e) {
            return $this->error('更新图片失败: ' . $e->getMessage());
        }
    }

    /**
     * REST: 删除图片（软删除）
     */
    public function destroy(Request $request, $id, $image_id): Response
    {
        try {
            $image = RoomTypeImage::where('room_type_id', (int)$id)
                ->where('id', (int)$image_id)
                ->first();

            if (!$image) {
                return $this->error('图片不存在', 404);
            }

            // 支持通过查询参数 ?hard=1 进行硬删除
            $hard = (bool)$request->get('hard', false);
            if ($hard) {
                $image->hardDelete();
            } else {
                $image->softDelete();
            }

            return $this->success(null, '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取房型图片
     */
    public function getRoomTypeImages(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->get('room_type_id');
            $imageType = $request->get('image_type');
            
            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }
            
            if ($imageType) {
                $data = RoomTypeImage::getImagesByType($roomTypeId, $imageType);
            } else {
                $data = RoomTypeImage::getRoomTypeImages($roomTypeId);
            }
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 上传房型图片
     */
    public function uploadImage(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->post('room_type_id');
            $imageType = $request->post('image_type', RoomTypeImage::TYPE_ROOM);
            $imageTitle = $request->post('image_title');
            $imageDescription = $request->post('image_description');
            $isPrimary = (bool)$request->post('is_primary', false);
            
            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }
            
            // 验证房型是否存在
            if (!\app\model\RoomType::find($roomTypeId)) {
                return $this->error('房型不存在', 404);
            }
            
            $file = $request->file('image');
            if (!$file || !$file->isValid()) {
                return $this->error('请选择有效的图片文件', 400);
            }
            
            // 验证文件类型
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file->getUploadMimeType(), $allowedTypes)) {
                return $this->error('只支持 JPEG、PNG、GIF、WebP 格式的图片', 400);
            }
            
            // 验证文件大小 (5MB)
            if ($file->getSize() > 5 * 1024 * 1024) {
                return $this->error('图片文件不能超过5MB', 400);
            }
            
            // 生成文件名
            $extension = $file->getUploadExtension();
            $fileName = 'room_' . $roomTypeId . '_' . time() . '_' . uniqid() . '.' . $extension;
            $uploadPath = 'uploads/room_images/' . date('Y/m/');
            
            // 确保目录存在
            $publicPath = config('app.public_path', base_path() . '/public');
            $fullUploadPath = $publicPath . '/' . $uploadPath;
            if (!is_dir($fullUploadPath)) {
                mkdir($fullUploadPath, 0755, true);
            }
            
            // 移动文件
            $filePath = $uploadPath . $fileName;
            $fullFilePath = $publicPath . '/' . $filePath;
            
            if (!$file->move($fullFilePath)) {
                return $this->error('文件上传失败', 500);
            }
            
            // 获取图片尺寸
            $imageInfo = getimagesize($fullFilePath);
            $width = $imageInfo[0] ?? null;
            $height = $imageInfo[1] ?? null;
            
            // 保存到数据库
            $imageData = [
                'room_type_id' => $roomTypeId,
                'image_url' => '/' . $filePath,
                'image_title' => $imageTitle,
                'image_description' => $imageDescription,
                'image_type' => $imageType,
                'file_name' => $fileName,
                'file_size' => $file->getSize(),
                'file_type' => $file->getUploadMimeType(),
                'width' => $width,
                'height' => $height,
                'display_order' => RoomTypeImage::getNextDisplayOrder($roomTypeId, $imageType),
                'is_primary' => $isPrimary,
                'status' => true,
            ];
            
            $image = RoomTypeImage::create($imageData);
            
            // 如果设置为主图，更新其他图片
            if ($isPrimary) {
                $image->setPrimary();
            }
            
            return $this->success($image, '图片上传成功');
        } catch (\Exception $e) {
            return $this->error('上传图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量上传图片
     */
    public function batchUpload(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->post('room_type_id');
            $images = $request->post('images', []);
            
            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }
            
            if (empty($images)) {
                return $this->error('图片数据不能为空', 400);
            }
            
            // 验证房型是否存在
            if (!\app\model\RoomType::find($roomTypeId)) {
                return $this->error('房型不存在', 404);
            }
            
            RoomTypeImage::batchUpload($roomTypeId, $images);
            
            return $this->success(null, '批量上传成功');
        } catch (\Exception $e) {
            return $this->error('批量上传失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新图片信息
     */
    public function updateImage(Request $request): Response
    {
        try {
            $id = (int)$request->post('id');
            $data = $request->post();
            
            $image = RoomTypeImage::find($id);
            if (!$image) {
                return $this->error('图片不存在', 404);
            }
            
            // 更新图片信息
            $updateData = [];
            $allowedFields = ['image_title', 'image_description', 'image_type', 'is_primary'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            if (!empty($updateData)) {
                $image->update($updateData);
            }
            
            // 如果设置为主图，更新其他图片
            if (isset($data['is_primary']) && $data['is_primary']) {
                $image->setPrimary();
            }
            
            return $this->success($image, '图片信息更新成功');
        } catch (\Exception $e) {
            return $this->error('更新图片信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置主图
     */
    public function setPrimary(Request $request): Response
    {
        try {
            $id = (int)$request->post('id');
            
            $image = RoomTypeImage::find($id);
            if (!$image) {
                return $this->error('图片不存在', 404);
            }
            
            $image->setPrimary();
            
            return $this->success(null, '主图设置成功');
        } catch (\Exception $e) {
            return $this->error('设置主图失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新图片排序
     */
    public function updateOrder(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->post('room_type_id');
            $imageOrders = $request->post('image_orders', []);

            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }

            RoomTypeImage::updateDisplayOrder($roomTypeId, $imageOrders);

            return $this->success(null, '排序更新成功');
        } catch (\Exception $e) {
            return $this->error('更新排序失败: ' . $e->getMessage());
        }
    }

    /**
     * REST: 更新图片排序 (PUT /room-types/{id}/images/order)
     */
    public function reorder(Request $request, $id): Response
    {
        try {
            $roomTypeId = (int)$id;
            $imageOrders = $request->post('image_orders', []);
            if (empty($imageOrders)) {
                // 兼容 JSON body
                $body = $request->all();
                $imageOrders = $body['image_orders'] ?? [];
            }

            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }

            RoomTypeImage::updateDisplayOrder($roomTypeId, $imageOrders);
            return $this->success(null, '排序更新成功');
        } catch (\Exception $e) {
            return $this->error('更新排序失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除图片
     */
    public function deleteImage(Request $request): Response
    {
        try {
            $id = (int)$request->post('id');
            $hard = (bool)$request->post('hard', false);
            
            $image = RoomTypeImage::find($id);
            if (!$image) {
                return $this->error('图片不存在', 404);
            }
            
            if ($hard) {
                $image->hardDelete();
            } else {
                $image->softDelete();
            }
            
            return $this->success(null, '图片删除成功');
        } catch (\Exception $e) {
            return $this->error('删除图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除图片
     */
    public function batchDelete(Request $request): Response
    {
        try {
            $imageIds = $request->post('image_ids', []);
            $hard = (bool)$request->post('hard', false);
            
            if (empty($imageIds)) {
                return $this->error('请选择要删除的图片', 400);
            }
            
            RoomTypeImage::batchDelete($imageIds, $hard);
            
            return $this->success(null, '批量删除成功');
        } catch (\Exception $e) {
            return $this->error('批量删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取图片类型列表
     */
    public function getImageTypes(Request $request): Response
    {
        try {
            $data = RoomTypeImage::getImageTypes();
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取图片类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取图片统计信息
     */
    public function getImageStats(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->get('room_type_id');
            
            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }
            
            $data = RoomTypeImage::getImageStats($roomTypeId);
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计信息失败: ' . $e->getMessage());
        }
    }
}
