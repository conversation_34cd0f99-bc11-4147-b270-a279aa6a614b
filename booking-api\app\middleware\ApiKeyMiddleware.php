<?php

namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use app\model\ApiKey;

/**
 * API密钥认证中间件
 */
class ApiKeyMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param callable $handler
     * @return Response
     */
    public function process(Request $request, callable $handler): Response
    {
        // 获取API密钥，支持多种方式
        $apiKey = $this->getApiKey($request);
        
        if (!$apiKey) {
            return $this->unauthorizedResponse('缺少API密钥');
        }

        // 验证API密钥
        $apiKeyModel = ApiKey::where('key', $apiKey)->first();
        
        if (!$apiKeyModel) {
            return $this->unauthorizedResponse('无效的API密钥');
        }

        if (!$apiKeyModel->is_active) {
            return $this->unauthorizedResponse('API密钥已被禁用');
        }

        if ($apiKeyModel->isExpired()) {
            return $this->unauthorizedResponse('API密钥已过期');
        }

        // 检查IP白名单
        if (!$apiKeyModel->isIpAllowed($request->getRealIp())) {
            return $this->unauthorizedResponse('IP地址不在白名单中');
        }

        // 检查请求频率限制
        if (!$apiKeyModel->checkRateLimit()) {
            return $this->unauthorizedResponse('请求频率超限');
        }

        // 记录API使用情况
        $apiKeyModel->recordUsage($request);

        // 将API密钥信息添加到请求中
        $request->api_key = $apiKeyModel;

        return $handler($request);
    }

    /**
     * 获取API密钥
     *
     * @param Request $request
     * @return string|null
     */
    private function getApiKey(Request $request): ?string
    {
        // 1. 从Authorization头获取 (Bearer token格式)
        $authorization = $request->header('Authorization');
        if ($authorization && preg_match('/^Bearer\s+(.+)$/', $authorization, $matches)) {
            return $matches[1];
        }

        // 2. 从X-API-Key头获取
        $apiKeyHeader = $request->header('X-API-Key');
        if ($apiKeyHeader) {
            return $apiKeyHeader;
        }

        // 3. 从查询参数获取
        $apiKeyParam = $request->get('api_key');
        if ($apiKeyParam) {
            return $apiKeyParam;
        }

        return null;
    }

    /**
     * 返回未授权响应
     *
     * @param string $message
     * @return Response
     */
    private function unauthorizedResponse(string $message): Response
    {
        return json([
            'code' => 401,
            'message' => $message,
            'timestamp' => time()
        ], 401);
    }
}
