<?php

namespace base\exception;

use support\exception\BusinessException;
use Throwable;
use <PERSON>man\Http\Request;
use Webman\Http\Response;

class BaseException extends BusinessException
{
    private int $statusCode;

    /**
     *
     * @param string $message The Exception message to throw.
     * @param int $code The Exception code.
     * @param int $statusCode Business Status Code
     *
     * @param Throwable|null $previous
     */
    public function __construct(string $message = "", int $statusCode = 0, $code = 200, Throwable $previous = null)
    {
        $this->statusCode = $statusCode;

        parent::__construct($message, $code, $previous);
    }

    public function render(Request $request): ?Response
    {
        if ($request->expectsJson()) {
            $code = $this->getCode();
            $statusCode = $this->statusCode ?: 500;
            $json = ['code' => $statusCode, 'message' => $this->getMessage()];
            return new Response(
                $code,
                ['Content-Type' => 'application/json'],
                json_encode($json, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES)
            );
        }
        return new Response(200, [], $this->getMessage());
    }
}