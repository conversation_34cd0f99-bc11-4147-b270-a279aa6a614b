<?php

namespace app\controller;

use app\model\RoomType;
use app\model\RatePlan;
use app\model\Hotel;
use support\Request;
use support\Response;

/**
 * 房价计划管理控制器
 */
class PricePlanController extends BaseController
{
    /**
     * 获取房价计划列表（按房型分组）
     * 对应 CheckRoomType.vue 需要的数据结构
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $hotelId = $params['hotel_id'] ?? null;
            $rangeOptions = $params['range_options'] ?? 'All';

            // 构建查询
            $query = RoomType::with(['ratePlans' => function ($query) use ($rangeOptions) {
                // 根据 rangeOptions 过滤房价计划
                if ($rangeOptions === 'AllExceptPriceType') {
                    $query->where('price_type', 0); // 只包含非价格类型
                }
                $query->where('is_active', 1); // 只包含活跃的房价计划
                $query->orderBy('priority', 'asc');
            }]);

            // 按酒店筛选
            if ($hotelId) {
                $query->where('hotel_id', $hotelId);
            }

            // 只获取有房价计划的房型
            $query->whereHas('ratePlans', function ($q) use ($rangeOptions) {
                if ($rangeOptions === 'AllExceptPriceType') {
                    $q->where('price_type', 0);
                }
                $q->where('is_active', 1);
            });

            // 获取房型列表
            $roomTypes = $query->get();

            $result = [];
            foreach ($roomTypes as $roomType) {
                $ratePlans = $roomType->ratePlans;
                if ($ratePlans->isEmpty()) {
                    continue;
                }

                // 构建语言信息
                $languages = [
                    'zh-CN' => $roomType->name,
                    'en-US' => $roomType->name_en ?: $roomType->name,
                ];

                // 构建房价计划数据，使用数据库真实字段
                $pricePlanData = [];
                foreach ($ratePlans as $ratePlan) {
                    $pricePlanData[] = [
                        'id' => $ratePlan->id,
                        'room_type_id' => $roomType->id,
                        'name' => $ratePlan->name,
                        'priceType' => $ratePlan->price_type ?? 0,
                        'saleType' => $this->getSaleTypeFromRatePlan($ratePlan),
                        'salePayType' => $this->getSalePayTypeFromRatePlan($ratePlan),
                        'sort' => $ratePlan->priority ?? 0,
                        'mealType' => $this->getMealTypeFromRatePlan($ratePlan),
                        'mealCount' => $ratePlan->breakfast_count ?? 0,
                        'hourlyRoom' => [
                            'stayHours' => 0, // 房价计划通常不是小时房
                            'latestArrivalTime' => $ratePlan->check_in_time_end ?? '',
                            'earliestArrivalTime' => $ratePlan->check_in_time_start ?? '',
                        ],
                        'channels' => $this->getChannelsFromRatePlan($ratePlan),
                    ];
                }

                $result[] = [
                    'id' => $roomType->id,
                    'name' => $roomType->name,
                    'languages' => $languages,
                    'ratePlans' => $pricePlanData, // 使用真实的字段名
                ];
            }

            return $this->success([
                'list' => $result,
                'total' => count($result),
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房价计划列表');
        }
    }

    /**
     * 根据房型ID获取房价计划列表
     *
     * @param Request $request
     * @param int $roomTypeId
     * @return Response
     */
    public function getByRoomType(Request $request, $roomTypeId)
    {
        try {
            $roomType = RoomType::with(['ratePlans' => function ($query) {
                $query->where('is_active', 1)->orderBy('priority', 'asc');
            }])->find($roomTypeId);

            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $ratePlans = $roomType->ratePlans;
            $pricePlanData = [];

            foreach ($ratePlans as $ratePlan) {
                $pricePlanData[] = [
                    'pmsSaleRoomTypeId' => $ratePlan->id,
                    'pmsRoomTypeId' => $roomType->id,
                    'name' => $ratePlan->name,
                    'priceType' => $ratePlan->price_type ?? 0,
                    'saleType' => $this->getSaleTypeFromRatePlan($ratePlan),
                    'salePayType' => $this->getSalePayTypeFromRatePlan($ratePlan),
                    'sort' => $ratePlan->priority ?? 0,
                    'mealType' => $this->getMealTypeFromRatePlan($ratePlan),
                    'mealCount' => $ratePlan->breakfast_count ?? 0,
                    'hourlyRoom' => [
                        'stayHours' => 0,
                        'latestArrivalTime' => $ratePlan->check_in_time_end ?? '',
                        'earliestArrivalTime' => $ratePlan->check_in_time_start ?? '',
                    ],
                    'channels' => $this->getChannelsFromRatePlan($ratePlan),
                ];
            }

            return $this->success([
                'list' => [
                    [
                        'pmsRoomTypeId' => $roomType->id,
                        'name' => $roomType->name,
                        'languages' => [
                            'zh-CN' => $roomType->name,
                            'en-US' => $roomType->name_en ?: $roomType->name,
                        ],
                        'saleRoomType' => $pricePlanData,
                    ]
                ],
                'total' => 1,
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型房价计划');
        }
    }

    /**
     * 获取房价计划详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $ratePlanId = (int)$id;

            if ($ratePlanId <= 0) {
                return $this->error('房价计划ID无效');
            }

            $ratePlan = RatePlan::with(['roomType', 'hotel'])->find($ratePlanId);

            if (!$ratePlan) {
                return $this->error('房价计划不存在', 404);
            }

            return $this->success($ratePlan);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房价计划详情');
        }
    }

    /**
     * 根据房价计划ID和日期获取房价和早餐数量信息
     *
     * @param Request $request
     * @return Response
     */
    public function getPriceInfo(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $ratePlanIds = $params['ratePlanIds'] ?? [];
            $dates = $params['dates'] ?? [];

            if (empty($ratePlanIds) || empty($dates)) {
                return $this->error('房价计划ID和日期不能为空');
            }

            $result = [];
            foreach ($ratePlanIds as $ratePlanId) {
                $ratePlan = RatePlan::with(['roomType', 'hotel'])->find($ratePlanId);

                if (!$ratePlan) {
                    continue;
                }

                // 构建房价信息数组
                $priceInfo = [];
                foreach ($dates as $date) {
                    // 获取该日期的房价信息
                    $priceData = $this->getPriceDataForDate($ratePlan, $date);
                    $priceInfo[] = $priceData;
                }

                $result[] = [
                    'id' => $ratePlan->id,
                    'room_type_id' => $ratePlan->room_type_id,
                    'name' => $ratePlan->name,
                    'priceType' => $ratePlan->price_type ?? 0,
                    'saleType' => $this->getSaleTypeFromRatePlan($ratePlan),
                    'salePayType' => $this->getSalePayTypeFromRatePlan($ratePlan),
                    'sort' => $ratePlan->priority ?? 0,
                    'mealType' => $this->getMealTypeFromRatePlan($ratePlan),
                    'mealCount' => $ratePlan->breakfast_count ?? 0,
                    'hourlyRoom' => [
                        'stayHours' => 0,
                        'latestArrivalTime' => $ratePlan->check_in_time_end ?? '',
                        'earliestArrivalTime' => $ratePlan->check_in_time_start ?? '',
                    ],
                    'channels' => $this->getChannelsFromRatePlan($ratePlan),
                    'priceInfo' => $priceInfo,
                ];
            }

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房价信息');
        }
    }

    /**
     * 获取指定日期的房价数据
     *
     * @param RatePlan $ratePlan
     * @param string $date
     * @return array
     */
    private function getPriceDataForDate($ratePlan, $date)
    {
        // 从RoomRate表中获取该日期和价格计划的房价数据
        $roomRate = \app\model\RoomRate::where([
            'rate_plan_id' => $ratePlan->id,
            'date' => $date,
        ])->first();

        $dateObj = new \DateTime($date);
        $isWeekend = $dateObj->format('N') >= 6; // 周六和周日

        if ($roomRate) {
            // 如果有具体的房价数据，使用真实数据
            $basePrice = $roomRate->sell_price ?? $roomRate->base_price ?? 0;
            $weekendPrice = null;

            // 如果是周末，可以设置周末价格（这里可以根据业务逻辑调整）
            if ($isWeekend) {
                $weekendPrice = $basePrice * 1.1; // 周末价格比平日高10%
            }

            return [
                'date' => $date,
                'price' => $basePrice,
                'weekendPrice' => $weekendPrice,
                'mealCount' => $ratePlan->breakfast_count ?? 0,
                'mealType' => $this->getMealTypeFromRatePlan($ratePlan),
            ];
        } else {
            // 如果没有具体的房价数据，使用价格计划的基础价格
            $basePrice = $ratePlan->base_price ?? 0;
            $weekendPrice = null;

            // 周末价格计算：基础价格 + 20% 作为周末价格
            if ($isWeekend) {
                $weekendPrice = $basePrice * 1.2;
            }

            return [
                'date' => $date,
                'price' => $basePrice,
                'weekendPrice' => $weekendPrice,
                'mealCount' => $ratePlan->breakfast_count ?? 0,
                'mealType' => $this->getMealTypeFromRatePlan($ratePlan),
            ];
        }
    }

    /**
     * 从房价计划获取销售类型
     */
    private function getSaleTypeFromRatePlan($ratePlan)
    {
        // 根据房价计划的特性判断销售类型
        if ($ratePlan->promotion_type) {
            return 1; // 促销销售
        } elseif ($ratePlan->package_inclusions) {
            return 2; // 包价销售
        } else {
            return 0; // 标准销售
        }
    }

    /**
     * 从房价计划获取销售支付类型
     */
    private function getSalePayTypeFromRatePlan($ratePlan)
    {
        if ($ratePlan->prepayment_required) {
            return 0; // 在线支付
        } else {
            return 1; // 到店支付
        }
    }

    /**
     * 从房价计划获取餐食类型
     */
    private function getMealTypeFromRatePlan($ratePlan)
    {
        if ($ratePlan->breakfast_included) {
            return 1; // 早餐
        } elseif ($ratePlan->meal_plan_options) {
            return 2; // 半餐
        } else {
            return 0; // 无餐食
        }
    }

    /**
     * 从房价计划获取渠道信息
     */
    private function getChannelsFromRatePlan($ratePlan)
    {
        $channels = [];
        
        // 从 OTA 渠道关联获取
        if ($ratePlan->otaChannels) {
            foreach ($ratePlan->otaChannels as $otaChannel) {
                if ($otaChannel->pivot->is_active) {
                    $channels[] = $otaChannel->id;
                }
            }
        }

        // 从 distribution_channels 字段获取
        if ($ratePlan->distribution_channels) {
            $channels = array_merge($channels, (array)$ratePlan->distribution_channels);
        }

        return array_unique($channels);
    }
}
