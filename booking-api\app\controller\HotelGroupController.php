<?php

namespace app\controller;

use app\model\HotelGroup;
use app\model\HotelBrand;
use app\model\Hotel;
use support\Request;
use support\Response;
use support\Db;

/**
 * 酒店集团管理控制器
 */
class HotelGroupController extends BaseController
{
    /**
     * 获取酒店集团列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $page = (int)$request->get('page', 1);
            $perPage = (int)$request->get('per_page', 10);
            $name = $request->get('name');
            $code = $request->get('code');
            $status = $request->get('status');

            $query = HotelGroup::query();

            // 搜索条件
            if ($name) {
                $query->where('name', 'like', "%{$name}%");
            }
            if ($code) {
                $query->where('code', 'like', "%{$code}%");
            }
            if ($status) {
                $query->where('status', $status);
            }

            // 分页查询
            $total = $query->count();
            $groups = $query->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            $items = $groups->map(function ($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'code' => $group->code,
                    'name_en' => $group->name_en,
                    'description' => $group->description,
                    'website' => $group->website,
                    'address' => $group->address,
                    'contact_person' => $group->contact_person,
                    'contact_phone' => $group->contact_phone,
                    'contact_email' => $group->contact_email,
                    'established_year' => $group->established_year,
                    'logo_url' => $group->logo_url,
                    'status' => $group->status,
                    'created_at' => $group->created_at,
                    'updated_at' => $group->updated_at,
                    'hotel_count' => Hotel::where('group_id', $group->id)->count(),
                ];
            });

            return $this->success([
                'items' => $items,
                'pagination' => [
                    'total' => $total,
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'last_page' => ceil($total / $perPage),
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店集团列表');
        }
    }

    /**
     * 获取酒店集团详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $groupId = (int)$id;

            if ($groupId <= 0) {
                return $this->error('酒店集团ID无效');
            }

            $group = HotelGroup::find($groupId);

            if (!$group) {
                return $this->error('酒店集团不存在', 404);
            }

            return $this->success([
                'id' => $group->id,
                'name' => $group->name,
                'code' => $group->code,
                'name_en' => $group->name_en,
                'description' => $group->description,
                'website' => $group->website,
                'address' => $group->address,
                'contact_person' => $group->contact_person,
                'contact_phone' => $group->contact_phone,
                'contact_email' => $group->contact_email,
                'established_year' => $group->established_year,
                'logo_url' => $group->logo_url,
                'status' => $group->status,
                'created_at' => $group->created_at,
                'updated_at' => $group->updated_at,
                'hotel_count' => Hotel::where('group_id', $group->id)->count(),
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店集团详情');
        }
    }

    /**
     * 创建酒店集团
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['name', 'code']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'contact_email' => 'email',
                'website' => 'url'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查集团名称是否已存在
            if (HotelGroup::where('name', $data['name'])->exists()) {
                return $this->error('集团名称已存在');
            }

            // 检查集团代码是否已存在
            if (HotelGroup::where('code', $data['code'])->exists()) {
                return $this->error('集团代码已存在');
            }

            // 创建酒店集团
            $groupData = [
                'name' => $data['name'],
                'code' => $data['code'],
                'name_en' => $data['name_en'] ?? $data['name_en'] ?? null, // 兼容旧字段名
                'description' => $data['description'] ?? null,
                'website' => $data['website'] ?? null,
                'address' => $data['address'] ?? $data['headquarters_address'] ?? null, // 兼容旧字段名
                'contact_person' => $data['contact_person'] ?? null,
                'contact_phone' => $data['contact_phone'] ?? null,
                'contact_email' => $data['contact_email'] ?? null,
                'established_year' => $this->parseEstablishedYear($data),
                'logo_url' => $data['logo_url'] ?? null,
                'status' => $data['status'] ?? HotelGroup::STATUS_ACTIVE,
            ];

            $group = HotelGroup::create($groupData);

            return $this->success([
                'id' => $group->id,
                'name' => $group->name,
                'code' => $group->code,
                'name_en' => $group->name_en,
                'name_en' => $group->name_en, // 兼容旧字段名
                'status' => $group->status,
            ], '酒店集团创建成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '创建酒店集团');
        }
    }

    /**
     * 更新酒店集团
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $groupId = (int)$id;
            
            if ($groupId <= 0) {
                return $this->error('酒店集团ID无效');
            }

            $group = HotelGroup::find($groupId);
            
            if (!$group) {
                return $this->error('酒店集团不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'contact_email' => 'email',
                'website' => 'url'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查集团名称是否已存在（排除当前集团）
            if (!empty($data['name']) && 
                HotelGroup::where('name', $data['name'])->where('id', '!=', $groupId)->exists()) {
                return $this->error('集团名称已存在');
            }

            // 检查集团代码是否已存在（排除当前集团）
            if (!empty($data['code']) && 
                HotelGroup::where('code', $data['code'])->where('id', '!=', $groupId)->exists()) {
                return $this->error('集团代码已存在');
            }

            // 更新酒店集团信息
            $updateData = [];
            $allowedFields = [
                'name', 'code', 'name_en', 'description', 'website',
                'address', 'contact_person', 'contact_phone',
                'contact_email', 'established_year', 'logo_url', 'status'
            ];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    if ($field === 'established_year') {
                        $updateData[$field] = $this->parseEstablishedYear($data);
                    } else {
                        $updateData[$field] = $data[$field];
                    }
                }
            }
            if ($updateData) {
                $group->update($updateData);
            }

            return $this->success([
                'id' => $group->id,
                'name' => $group->name,
                'code' => $group->code,
                'name_en' => $group->name_en,
                'name_en' => $group->name_en, // 兼容旧字段名
                'status' => $group->status,
            ], '酒店集团更新成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店集团');
        }
    }

    /**
     * 删除酒店集团
     *
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $groupId = (int)$id;
            
            if ($groupId <= 0) {
                return $this->error('酒店集团ID无效');
            }

            $group = HotelGroup::find($groupId);
            
            if (!$group) {
                return $this->error('酒店集团不存在', 404);
            }

            // 检查是否有酒店属于此集团
            $hotelCount = Hotel::where('group_id', $groupId)->count();
            if ($hotelCount > 0) {
                return $this->error('该集团下还有酒店，无法删除');
            }

            $group->delete();

            return $this->success(null, '酒店集团删除成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '删除酒店集团');
        }
    }

    /**
     * 获取所有酒店集团（用于下拉选择）
     *
     * @param Request $request
     * @return Response
     */
    public function all(Request $request)
    {
        try {
            $groups = HotelGroup::where('status', HotelGroup::STATUS_ACTIVE)
                ->orderBy('name')
                ->get()
                ->map(function ($group) {
                    return [
                        'id' => $group->id,
                        'name' => $group->name,
                        'code' => $group->code,
                        'name_en' => $group->name_en,
                        'name_en' => $group->name_en, // 兼容旧字段名
                    ];
                });

            return $this->success($groups);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取所有酒店集团');
        }
    }

    /**
     * 获取集团下的品牌列表
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function getBrands(Request $request, $id)
    {
        try {
            $groupId = (int)$id;

            if ($groupId <= 0) {
                return $this->error('酒店集团ID无效');
            }

            $group = HotelGroup::find($groupId);

            if (!$group) {
                return $this->error('酒店集团不存在', 404);
            }

            $brands = HotelBrand::where('group_id', $groupId)
                ->where('status', HotelBrand::STATUS_ACTIVE)
                ->orderBy('name')
                ->get()
                ->map(function ($brand) {
                    return [
                        'id' => $brand->id,
                        'name' => $brand->name,
                        'code' => $brand->code,
                        'name_en' => $brand->name_en,
                        'brand_type' => $brand->brand_type,
                    ];
                });

            return $this->success($brands);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取集团品牌列表');
        }
    }

    /**
     * 解析成立年份字段
     * 支持多种格式：数字年份、年月格式(YYYY-MM)、founded_year字段
     */
    private function parseEstablishedYear(array $data): ?int
    {
        // 优先使用founded_year字段
        if (isset($data['founded_year']) && is_numeric($data['founded_year'])) {
            return (int) $data['founded_year'];
        }

        // 处理established_year字段
        if (isset($data['established_year'])) {
            $value = $data['established_year'];

            // 如果是数字，直接返回
            if (is_numeric($value)) {
                return (int) $value;
            }

            // 如果是年月格式 (YYYY-MM)，提取年份
            if (is_string($value) && preg_match('/^(\d{4})-\d{2}$/', $value, $matches)) {
                return (int) $matches[1];
            }
        }

        return null;
    }
}
