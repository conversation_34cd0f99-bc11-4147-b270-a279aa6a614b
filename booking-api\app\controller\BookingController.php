<?php

namespace app\controller;

use app\service\BookingService;
use support\Request;
use support\Response;

/**
 * 订单管理控制器
 */
class BookingController extends BaseController
{
    /**
     * 订单服务
     *
     * @var BookingService
     */
    private $bookingService;

    public function __construct()
    {
        $this->bookingService = new BookingService();
    }

    /**
     * 获取订单列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'check_in_date' => 'date',
                'check_in_start' => 'date',
                'check_in_end' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if (!empty($params['check_in_start']) && !empty($params['check_in_end'])) {
                if ($params['check_in_start'] > $params['check_in_end']) {
                    return $this->error('开始日期不能晚于结束日期');
                }
            }

            $result = $this->bookingService->getBookingList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取订单列表');
        }
    }

    /**
     * 获取订单详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request)
    {
        try {
            $bookingId = (int)$request->get('id');
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $result = $this->bookingService->getBookingDetail($bookingId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取订单详情');
        }
    }

    /**
     * 创建订单
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'room_type_id', 'rate_plan_id',
                'check_in_date', 'check_out_date',
                'rooms', 'adults', 'guest_name', 'guest_phone'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'rate_plan_id' => 'integer',
                'user_id' => 'integer',
                'check_in_date' => 'date',
                'check_out_date' => 'date',
                'rooms' => 'positive',
                'adults' => 'positive',
                'children' => 'integer',
                'guest_phone' => 'phone',
                'guest_email' => 'email'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['check_in_date'] >= $data['check_out_date']) {
                return $this->error('入住日期必须早于离店日期');
            }

            // 验证入住日期不能是过去
            if ($data['check_in_date'] < date('Y-m-d')) {
                return $this->error('入住日期不能是过去的日期');
            }

            // 验证房间和人数
            if ($data['rooms'] <= 0) {
                return $this->error('房间数量必须大于0');
            }

            if ($data['adults'] <= 0) {
                return $this->error('成人数量必须大于0');
            }

            if (isset($data['children']) && $data['children'] < 0) {
                return $this->error('儿童数量不能为负数');
            }

            $result = $this->bookingService->createBooking($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建订单');
        }
    }

    /**
     * 确认订单
     *
     * @param Request $request
     * @return Response
     */
    public function confirm(Request $request)
    {
        try {
            $bookingId = (int)$request->get('id');

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);
            $result = $this->bookingService->confirmBooking($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '确认订单');
        }
    }

    /**
     * 取消订单
     *
     * @param Request $request
     * @return Response
     */
    public function cancel(Request $request)
    {
        try {
            $bookingId = (int)$request->get('id');
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);
            $result = $this->bookingService->cancelBooking($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '取消订单');
        }
    }

    /**
     * 办理入住
     *
     * @param Request $request
     * @return Response
     */
    public function checkIn(Request $request)
    {
        try {
            $bookingId = (int)$request->get('id');
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证房间号格式
            if (!empty($data['room_numbers'])) {
                if (!is_array($data['room_numbers'])) {
                    return $this->error('房间号必须是数组格式');
                }

                foreach ($data['room_numbers'] as $roomNumber) {
                    if (empty($roomNumber) || !is_string($roomNumber)) {
                        return $this->error('房间号格式无效');
                    }
                }
            }

            $result = $this->bookingService->checkInBooking($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '办理入住');
        }
    }

    /**
     * 办理退房
     *
     * @param Request $request
     * @return Response
     */
    public function checkOut(Request $request)
    {
        try {
            $bookingId = (int)$request->get('id');
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);
            $result = $this->bookingService->checkOutBooking($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '办理退房');
        }
    }

    /**
     * 更新订单信息
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        try {
            $bookingId = (int)$request->get('id');
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'guest_phone' => 'phone',
                'guest_email' => 'email'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 获取订单
            $booking = \app\model\Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在', 404);
            }

            // 只允许更新特定字段
            $allowedFields = [
                'guest_name', 'guest_phone', 'guest_email', 'guest_id_card',
                'guest_nationality', 'special_requests', 'arrival_time'
            ];

            $updateData = array_intersect_key($data, array_flip($allowedFields));
            $updateData = array_filter($updateData, function($value) {
                return $value !== null && $value !== '';
            });

            if (empty($updateData)) {
                return $this->error('没有可更新的数据');
            }

            $booking->update($updateData);

            return $this->success($booking, '更新订单成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新订单信息');
        }
    }

    /**
     * 搜索订单
     *
     * @param Request $request
     * @return Response
     */
    public function search(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            // 验证搜索参数
            if (empty($params['booking_no']) && empty($params['guest_name']) && empty($params['guest_phone'])) {
                return $this->error('请提供订单号、客人姓名或手机号进行搜索');
            }

            $result = $this->bookingService->getBookingList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '搜索订单');
        }
    }

    /**
     * 获取订单统计
     *
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 设置默认日期范围（当月）
            $startDate = $params['start_date'] ?? date('Y-m-01');
            $endDate = $params['end_date'] ?? date('Y-m-t');

            // 验证日期格式
            $dateErrors = $this->validateFormat([
                'start_date' => $startDate,
                'end_date' => $endDate
            ], [
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($dateErrors) {
                return $this->error('日期格式错误', 400, $dateErrors);
            }

            $queryParams = array_merge($params, [
                'check_in_start' => $startDate,
                'check_in_end' => $endDate
            ]);

            // 获取订单列表
            $bookingResult = $this->bookingService->getBookingList($queryParams);
            if ($bookingResult['code'] !== 0) {
                return $this->handleServiceResult($bookingResult);
            }

            $bookings = $bookingResult['data']['items'] ?? [];

            // 计算统计数据
            $statistics = [
                'total_bookings' => count($bookings),
                'total_revenue' => 0,
                'avg_revenue_per_booking' => 0,
                'status_breakdown' => [],
                'payment_status_breakdown' => [],
                'channel_breakdown' => [],
                'total_rooms' => 0,
                'total_nights' => 0
            ];

            $statusCounts = [];
            $paymentStatusCounts = [];
            $channelCounts = [];

            foreach ($bookings as $booking) {
                $statistics['total_revenue'] += $booking['total_amount'] ?? 0;
                $statistics['total_rooms'] += $booking['rooms'] ?? 0;
                $statistics['total_nights'] += $booking['nights'] ?? 0;

                // 状态统计
                $status = $booking['status'] ?? 'unknown';
                $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;

                // 支付状态统计
                $paymentStatus = $booking['payment_status'] ?? 'unknown';
                $paymentStatusCounts[$paymentStatus] = ($paymentStatusCounts[$paymentStatus] ?? 0) + 1;

                // 渠道统计
                $channel = $booking['source_channel'] ?? 'unknown';
                $channelCounts[$channel] = ($channelCounts[$channel] ?? 0) + 1;
            }

            $statistics['status_breakdown'] = $statusCounts;
            $statistics['payment_status_breakdown'] = $paymentStatusCounts;
            $statistics['channel_breakdown'] = $channelCounts;

            if ($statistics['total_bookings'] > 0) {
                $statistics['avg_revenue_per_booking'] = round($statistics['total_revenue'] / $statistics['total_bookings'], 2);
            }

            return $this->success([
                'statistics' => $statistics,
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取订单统计');
        }
    }

    /**
     * 更新预订偏好信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updatePreferences(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证偏好数据格式
            $preferenceFields = [
                'accessibility_requirements', 'dietary_requirements', 'membership_benefits'
            ];

            foreach ($preferenceFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            // 验证枚举值
            $enumValidations = [
                'bed_type_preference' => ['single', 'double', 'twin', 'king', 'queen', 'sofa'],
                'room_location_preference' => ['low_floor', 'high_floor', 'middle_floor', 'corner', 'center', 'quiet', 'near_elevator'],
                'floor_preference' => ['ground', 'low', 'middle', 'high', 'top'],
                'view_preference' => ['city', 'sea', 'mountain', 'garden', 'pool', 'courtyard', 'street'],
                'smoking_preference' => ['smoking', 'non_smoking', 'no_preference'],
                'language_preference' => ['zh', 'en', 'ja', 'ko', 'fr', 'de', 'es', 'ru'],
                'communication_preference' => ['email', 'phone', 'sms', 'app', 'wechat']
            ];

            foreach ($enumValidations as $field => $validValues) {
                if (!empty($data[$field]) && !in_array($data[$field], $validValues)) {
                    return $this->error("字段 {$field} 的值无效");
                }
            }

            $result = $this->bookingService->updateBookingPreferences($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新预订偏好信息');
        }
    }

    /**
     * 获取预订同意信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function consents(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $result = $this->bookingService->getBookingConsents($bookingId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取预订同意信息');
        }
    }

    /**
     * 更新预订同意信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateConsents(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证布尔值字段
            $booleanFields = [
                'marketing_consent', 'data_processing_consent', 'third_party_sharing_consent',
                'promotional_consent', 'newsletter_consent', 'sms_consent', 'call_consent',
                'email_consent', 'push_notification_consent', 'privacy_policy_acceptance',
                'terms_conditions_acceptance', 'booking_conditions_acceptance',
                'rate_conditions_acceptance', 'cancellation_conditions_acceptance',
                'modification_conditions_acceptance', 'liability_waiver_acceptance',
                'damage_waiver_acceptance'
            ];

            foreach ($booleanFields as $field) {
                if (isset($data[$field]) && !is_bool($data[$field]) && !in_array($data[$field], [0, 1, '0', '1', 'true', 'false'])) {
                    return $this->error("字段 {$field} 必须是布尔值");
                }
            }

            $result = $this->bookingService->updateBookingConsents($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新预订同意信息');
        }
    }

    /**
     * 获取预订财务信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function financial(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $result = $this->bookingService->getBookingFinancialInfo($bookingId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取预订财务信息');
        }
    }

    /**
     * 更新预订财务信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateFinancial(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证数值字段
            $numericFields = [
                'exchange_rate', 'original_amount', 'commission_amount', 'commission_rate',
                'markup_amount', 'markup_rate', 'net_amount', 'gross_amount', 'insurance_amount'
            ];

            foreach ($numericFields as $field) {
                if (!empty($data[$field]) && !is_numeric($data[$field])) {
                    return $this->error("字段 {$field} 必须是数值");
                }
            }

            // 验证JSON字段格式
            $jsonFields = [
                'tax_breakdown', 'fee_breakdown', 'discount_breakdown',
                'payment_breakdown', 'refund_breakdown', 'penalty_breakdown',
                'insurance_details'
            ];

            foreach ($jsonFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->bookingService->updateBookingFinancialInfo($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新预订财务信息');
        }
    }

    /**
     * 获取预订验证状态
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function verificationStatus(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $result = $this->bookingService->getBookingVerificationStatus($bookingId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取预订验证状态');
        }
    }

    /**
     * 更新预订验证状态
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateVerificationStatus(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证布尔值字段
            $booleanFields = [
                'age_verification', 'identity_verification', 'credit_card_verification',
                'address_verification', 'phone_verification', 'email_verification',
                'document_verification', 'background_check', 'credit_check',
                'fraud_check', 'risk_assessment', 'compliance_check',
                'sanctions_check', 'pep_check', 'aml_check', 'kyc_check'
            ];

            foreach ($booleanFields as $field) {
                if (isset($data[$field]) && !is_bool($data[$field]) && !in_array($data[$field], [0, 1, '0', '1', 'true', 'false'])) {
                    return $this->error("字段 {$field} 必须是布尔值");
                }
            }

            $result = $this->bookingService->updateBookingVerificationStatus($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新预订验证状态');
        }
    }

    /**
     * 获取预订押金信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function deposits(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $result = $this->bookingService->getBookingDeposits($bookingId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取预订押金信息');
        }
    }

    /**
     * 更新预订押金信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateDeposits(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;

            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证数值字段
            $numericFields = [
                'security_deposit_amount', 'damage_deposit_amount', 'incidental_deposit_amount',
                'authorization_amount', 'pre_authorization_amount', 'hold_amount',
                'guarantee_amount', 'collateral_amount'
            ];

            foreach ($numericFields as $field) {
                if (!empty($data[$field]) && !is_numeric($data[$field])) {
                    return $this->error("字段 {$field} 必须是数值");
                }
            }

            // 验证状态字段
            $statusFields = [
                'security_deposit_status', 'damage_deposit_status', 'incidental_deposit_status',
                'authorization_status', 'pre_authorization_status', 'hold_status',
                'guarantee_status', 'collateral_status'
            ];

            $validStatuses = ['pending', 'authorized', 'captured', 'released', 'refunded', 'expired', 'failed'];

            foreach ($statusFields as $field) {
                if (!empty($data[$field]) && !in_array($data[$field], $validStatuses)) {
                    return $this->error("字段 {$field} 的状态值无效");
                }
            }

            $result = $this->bookingService->updateBookingDeposits($bookingId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新预订押金信息');
        }
    }
}
