<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use Webman\Route;

// API v1 路由组
Route::group('/api/v1', function () {

    // 酒店管理路由
    Route::group('/hotels', function () {
        Route::get('', [app\controller\HotelController::class, 'index']); // 获取酒店列表
        Route::post('', [app\controller\HotelController::class, 'store']); // 创建酒店
        Route::get('/{id}', [app\controller\HotelController::class, 'show']); // 获取酒店基本信息
        Route::get('/{id}/detail', [app\controller\HotelController::class, 'show']); // 获取酒店完整详情
        Route::put('/{id}', [app\controller\HotelController::class, 'update']); // 更新酒店
        Route::put('/{id}/basic-info', [app\controller\HotelDetailController::class, 'updateBasicInfo']); // 更新酒店基础信息
        Route::delete('/{id}', [app\controller\HotelController::class, 'destroy']); // 删除酒店
        Route::get('/{id}/room-types', [app\controller\HotelController::class, 'roomTypes']); // 获取酒店房型
        Route::post('/{id}/room-types', [app\controller\HotelController::class, 'createRoomType']); // 创建房型

//        // 酒店政策管理
//        Route::get('/{id}/policies', [app\controller\HotelController::class, 'policies']); // 获取酒店政策
//        Route::put('/{id}/policies', [app\controller\HotelController::class, 'updatePolicies']); // 更新酒店政策

        // 酒店营业时间管理
        Route::get('/{id}/business-hours', [app\controller\HotelController::class, 'businessHours']); // 获取营业时间
        Route::put('/{id}/business-hours', [app\controller\HotelController::class, 'updateBusinessHours']); // 更新营业时间

        // 酒店图片管理
        Route::get('/{id}/images', [app\controller\HotelImageController::class, 'index']); // 获取酒店图片列表
        Route::post('/{id}/images/{image_id}/set-primary', [app\controller\HotelImageController::class, 'setPrimary']); // 设置主图
        Route::put('/{id}/images/order', [app\controller\HotelImageController::class, 'updateOrder']); // 更新图片排序

        // 酒店政策管理
        Route::get('/{id}/policies', [app\controller\HotelPolicyController::class, 'index']); // 获取酒店所有政策列表
        Route::get('/{id}/policies/{type}', [app\controller\HotelPolicyController::class, 'show']); // 获取指定类型政策详情
        Route::put('/{id}/policies/{type}', [app\controller\HotelPolicyController::class, 'update']); // 更新指定类型政策
        Route::post('/{id}/policies/initialize', [app\controller\HotelPolicyController::class, 'initializeDefaults']); // 初始化默认政策

        // 酒店支付方式管理
        Route::get('/{id}/payment-methods', [app\controller\HotelPaymentController::class, 'index']); // 获取酒店支付方式列表
        Route::post('/{id}/payment-methods', [app\controller\HotelPaymentController::class, 'store']); // 创建支付方式
        Route::get('/{id}/payment-methods/{payment_id}', [app\controller\HotelPaymentController::class, 'show']); // 获取支付方式详情
        Route::put('/{id}/payment-methods/{payment_id}', [app\controller\HotelPaymentController::class, 'update']); // 更新支付方式
        Route::delete('/{id}/payment-methods/{payment_id}', [app\controller\HotelPaymentController::class, 'destroy']); // 删除支付方式
        Route::put('/{id}/payment-methods/batch', [app\controller\HotelPaymentController::class, 'batchUpdate']); // 批量更新支付方式
        Route::put('/{id}/payment-methods/order', [app\controller\HotelPaymentController::class, 'updateOrder']); // 更新支付方式排序
        Route::put('/{id}/payment-methods/{payment_id}/default', [app\controller\HotelPaymentController::class, 'setDefault']); // 设置默认支付方式
        Route::post('/{id}/payment-methods/common', [app\controller\HotelPaymentController::class, 'addCommonMethods']); // 批量添加常用支付方式

        // 酒店资质管理
        Route::get('/{id}/licenses', [app\controller\HotelLicenseController::class, 'index']); // 获取酒店资质列表
        Route::post('/{id}/licenses', [app\controller\HotelLicenseController::class, 'store']); // 创建酒店资质
        Route::get('/{id}/licenses/{license_id}', [app\controller\HotelLicenseController::class, 'show']); // 获取资质详情
        Route::put('/{id}/licenses/{license_id}', [app\controller\HotelLicenseController::class, 'update']); // 更新资质
        Route::delete('/{id}/licenses/{license_id}', [app\controller\HotelLicenseController::class, 'destroy']); // 删除资质
    });

    // 酒店设施管理路由
    Route::group('/hotels/{hotel_id}/facilities', function () {
        // 基础CRUD操作
        Route::get('', [app\controller\HotelFacilityController::class, 'index']); // 获取酒店设施列表
        Route::post('', [app\controller\HotelFacilityController::class, 'store']); // 创建酒店设施
        Route::get('/{id}', [app\controller\HotelFacilityController::class, 'show']); // 获取酒店设施详情
        Route::put('/{id}', [app\controller\HotelFacilityController::class, 'update']); // 更新酒店设施
        Route::delete('/{id}', [app\controller\HotelFacilityController::class, 'destroy']); // 删除酒店设施

        // 批量操作
        Route::post('/batch', [app\controller\HotelFacilityController::class, 'batchStore']); // 批量创建设施
        Route::put('/batch', [app\controller\HotelFacilityController::class, 'batchUpdate']); // 批量更新设施
        Route::delete('/batch', [app\controller\HotelFacilityController::class, 'batchDestroy']); // 批量删除设施

        // 排序管理
        Route::put('/order', [app\controller\HotelFacilityController::class, 'updateOrder']); // 更新设施显示顺序

        // 统计和分组
        Route::get('/stats/category', [app\controller\HotelFacilityController::class, 'categoryStats']); // 获取设施分类统计
        Route::get('/grouped', [app\controller\HotelFacilityController::class, 'grouped']); // 按分类分组获取设施
    });

    // 酒店详情管理路由
    Route::group('/hotel-details/{id}', function () {
        Route::put('/basic-info', [app\controller\HotelDetailController::class, 'updateBasicInfo']); // 更新基础信息

        // 设施管理
        Route::get('/facilities', [app\controller\HotelDetailController::class, 'getFacilities']); // 获取设施列表
        Route::put('/facilities', [app\controller\HotelDetailController::class, 'updateFacilities']); // 更新设施

        // 图片管理
        Route::get('/images', [app\controller\HotelDetailController::class, 'getImages']); // 获取图片列表
        Route::put('/images', [app\controller\HotelDetailController::class, 'updateImages']); // 更新图片

        // 政策管理
        Route::get('/policies', [app\controller\HotelDetailController::class, 'getPolicies']); // 获取政策列表
        Route::put('/policies', [app\controller\HotelDetailController::class, 'updatePolicy']); // 更新政策

        // 支付方式管理
        Route::get('/payments', [app\controller\HotelDetailController::class, 'getPayments']); // 获取支付方式
        Route::put('/payments', [app\controller\HotelDetailController::class, 'updatePayments']); // 更新支付方式

        // 资质管理
        Route::get('/licenses', [app\controller\HotelDetailController::class, 'getLicenses']); // 获取资质信息
        Route::put('/licenses', [app\controller\HotelDetailController::class, 'updateLicenses']); // 更新资质信息

        // 联系信息管理
        Route::put('/contact', [app\controller\HotelDetailController::class, 'updateContact']); // 更新联系信息

        // 房东信息管理
        Route::get('/host-info', [app\controller\HotelDetailController::class, 'getHostInfo']); // 获取房东信息
        Route::put('/host-info', [app\controller\HotelDetailController::class, 'updateHostInfo']); // 更新房东信息
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 政策类型管理路由
    Route::group('/policy-types', function () {
        Route::get('', [app\controller\HotelPolicyController::class, 'types']); // 获取政策类型列表
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 常用支付方式管理路由
    Route::group('/payment-methods', function () {
        Route::get('/common', [app\controller\HotelPaymentController::class, 'commonMethods']); // 获取常用支付方式列表
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 酒店集团管理路由
    Route::group('/hotel-groups', function () {
        Route::get('', [app\controller\HotelGroupController::class, 'index']); // 获取酒店集团列表
        Route::post('', [app\controller\HotelGroupController::class, 'store']); // 创建酒店集团
        Route::get('/all', [app\controller\HotelGroupController::class, 'all']); // 获取所有酒店集团（用于下拉选择）
        Route::get('/{id}', [app\controller\HotelGroupController::class, 'show']); // 获取酒店集团详情
        Route::put('/{id}', [app\controller\HotelGroupController::class, 'update']); // 更新酒店集团
        Route::delete('/{id}', [app\controller\HotelGroupController::class, 'destroy']); // 删除酒店集团
        Route::get('/{id}/brands', [app\controller\HotelGroupController::class, 'getBrands']); // 获取集团下的品牌列表
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 酒店品牌管理路由
    Route::group('/hotel-brands', function () {
        Route::get('', [app\controller\HotelBrandController::class, 'index']); // 获取酒店品牌列表
        Route::post('', [app\controller\HotelBrandController::class, 'store']); // 创建酒店品牌
        Route::get('/all', [app\controller\HotelBrandController::class, 'all']); // 获取所有酒店品牌（用于下拉选择）
        Route::get('/{id}', [app\controller\HotelBrandController::class, 'show']); // 获取酒店品牌详情
        Route::put('/{id}', [app\controller\HotelBrandController::class, 'update']); // 更新酒店品牌
        Route::delete('/{id}', [app\controller\HotelBrandController::class, 'destroy']); // 删除酒店品牌
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 设施模板管理路由
    Route::group('/facility-templates', function () {
        Route::get('', [app\controller\FacilityTemplateController::class, 'index']); // 获取设施模板列表
        Route::post('', [app\controller\FacilityTemplateController::class, 'store']); // 创建设施模板
        Route::get('/categories', [app\controller\FacilityTemplateController::class, 'categories']); // 获取分类列表
        Route::get('/recommended', [app\controller\FacilityTemplateController::class, 'recommended']); // 获取推荐设施
        Route::post('/batch-import', [app\controller\FacilityTemplateController::class, 'batchImport']); // 批量导入
        Route::post('/batch-destroy', [app\controller\FacilityTemplateController::class, 'batchDestroy']); // 批量删除
        Route::post('/batch-update-status', [app\controller\FacilityTemplateController::class, 'batchUpdateStatus']); // 批量更新状态
        Route::post('/sync-to-hotel', [app\controller\FacilityTemplateController::class, 'syncToHotel']); // 同步到酒店设施
        Route::get('/{id}', [app\controller\FacilityTemplateController::class, 'show']); // 获取设施模板详情
        Route::put('/{id}', [app\controller\FacilityTemplateController::class, 'update']); // 更新设施模板
        Route::delete('/{id}', [app\controller\FacilityTemplateController::class, 'destroy']); // 删除设施模板
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 图片分类管理路由
    Route::group('/image-categories', function () {
        Route::get('', [app\controller\ImageCategoryController::class, 'index']); // 获取图片分类列表
        Route::post('', [app\controller\ImageCategoryController::class, 'store']); // 创建图片分类
        Route::get('/tree', [app\controller\ImageCategoryController::class, 'tree']); // 获取树形分类
        Route::get('/flat', [app\controller\ImageCategoryController::class, 'flat']); // 获取扁平分类
        Route::get('/groups', [app\controller\ImageCategoryController::class, 'groups']); // 获取分组统计
        Route::post('/batch-import', [app\controller\ImageCategoryController::class, 'batchImport']); // 批量导入
        Route::post('/batch-update-status', [app\controller\ImageCategoryController::class, 'batchUpdateStatus']); // 批量更新状态
        Route::get('/{id}', [app\controller\ImageCategoryController::class, 'show']); // 获取图片分类详情
        Route::put('/{id}', [app\controller\ImageCategoryController::class, 'update']); // 更新图片分类
        Route::delete('/{id}', [app\controller\ImageCategoryController::class, 'destroy']); // 删除图片分类
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 图片上传路由
    Route::group('/images', function () {
        Route::post('/pre-upload', [app\controller\ImageUploadController::class, 'preUpload']); // 预上传图片
        Route::post('/confirm-upload', [app\controller\ImageUploadController::class, 'confirmUpload']); // 确认上传
        Route::post('/upload', [app\controller\ImageUploadController::class, 'upload']); // 单张图片上传
        Route::post('/batch-upload', [app\controller\ImageUploadController::class, 'batchUpload']); // 批量图片上传

        // 单个图片管理
        Route::get('/{id}', [app\controller\HotelImageController::class, 'show']); // 获取图片详情
        Route::put('/{id}', [app\controller\HotelImageController::class, 'update']); // 更新图片信息
        Route::delete('/{id}', [app\controller\HotelImageController::class, 'destroy']); // 删除图片

        // 兼容旧的删除接口
        Route::post('/batch-delete', [app\controller\ImageUploadController::class, 'batchDelete']); // 批量删除图片
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 房态房价日历路由
    Route::group('/room-calendar', function () {
        Route::get('/data', [app\controller\RoomCalendarController::class, 'getCalendarData']); // 获取房态房价日历数据
        Route::post('/inventory/batch-update', [app\controller\RoomCalendarController::class, 'batchUpdateInventory']); // 批量更新房态数据
        Route::post('/rates/batch-update', [app\controller\RoomCalendarController::class, 'batchUpdateRates']); // 批量更新房价数据
    })->middleware([app\middleware\AuthMiddleware::class]);


    // 房型管理路由
    Route::group('/room-types', function () {
        Route::get('', [app\controller\RoomTypeController::class, 'index']); // 获取房型列表
        Route::post('', [app\controller\RoomTypeController::class, 'store']); // 创建房型
        Route::get('/{id}', [app\controller\RoomTypeController::class, 'show']); // 获取房型详情
        Route::put('/{id}', [app\controller\RoomTypeController::class, 'update']); // 更新房型
        Route::delete('/{id}', [app\controller\RoomTypeController::class, 'destroy']); // 删除房型
        Route::get('/{id}/inventory', [app\controller\RoomTypeController::class, 'inventory']); // 获取房型库存
        Route::get('/{id}/rates', [app\controller\RoomTypeController::class, 'rates']); // 获取房型价格
        Route::get('/{id}/availability', [app\controller\RoomTypeController::class, 'availability']); // 检查可用性

        // 房型图片 REST 管理
        Route::get('/{id}/images', [app\controller\RoomTypeImageController::class, 'index']); // 列表
        Route::post('/{id}/images', [app\controller\RoomTypeImageController::class, 'store']); // 创建
        Route::get('/{id}/images/{image_id}', [app\controller\RoomTypeImageController::class, 'show']); // 详情
        Route::put('/{id}/images/{image_id}', [app\controller\RoomTypeImageController::class, 'update']); // 更新
        Route::delete('/{id}/images/{image_id}', [app\controller\RoomTypeImageController::class, 'destroy']); // 删除
        // 房型图片排序（REST）
        Route::put('/{id}/images/order', [app\controller\RoomTypeImageController::class, 'reorder']); // 更新排序


        // 房型设施管理
        Route::get('/{id}/amenities', [app\controller\RoomTypeController::class, 'amenities']); // 获取房型设施
        Route::put('/{id}/amenities', [app\controller\RoomTypeController::class, 'updateAmenities']); // 更新房型设施

        // 房型详细信息管理
        Route::get('/{id}/details', [app\controller\RoomTypeController::class, 'details']); // 获取房型详细信息
        Route::put('/{id}/details', [app\controller\RoomTypeController::class, 'updateDetails']); // 更新房型详细信息

        // 房型政策 REST 管理
        Route::get('/{id}/policies', [app\controller\RoomTypePolicyController::class, 'index']); // 获取（单记录）
        Route::post('/{id}/policies', [app\controller\RoomTypePolicyController::class, 'store']); // 创建
        Route::get('/{id}/policies/{policy_id}', [app\controller\RoomTypePolicyController::class, 'show']); // 详情
        Route::put('/{id}/policies/{policy_id}', [app\controller\RoomTypePolicyController::class, 'update']); // 更新
        Route::delete('/{id}/policies/{policy_id}', [app\controller\RoomTypePolicyController::class, 'destroy']); // 删除

        // 房型床位配置管理
        Route::get('/{id}/bed-config', [app\controller\RoomTypeBedConfigController::class, 'show']); // 获取床位配置
        Route::post('/{id}/bed-config', [app\controller\RoomTypeBedConfigController::class, 'store']); // 创建床位配置
        Route::put('/{id}/bed-config', [app\controller\RoomTypeBedConfigController::class, 'update']); // 更新床位配置
        Route::delete('/{id}/bed-config', [app\controller\RoomTypeBedConfigController::class, 'destroy']); // 删除床位配置
        Route::post('/{id}/bed-config/validate', [app\controller\RoomTypeBedConfigController::class, 'validateA']); // 验证床位配置
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 库存管理路由
    Route::group('/inventory', function () {
        Route::get('', [app\controller\InventoryController::class, 'index']); // 查询库存
        Route::post('/batch-query', [app\controller\InventoryController::class, 'batchQuery']); // 批量查询库存
        Route::put('', [app\controller\InventoryController::class, 'update']); // 更新库存
        Route::post('/batch-update', [app\controller\InventoryController::class, 'batchUpdate']); // 批量更新库存
        Route::post('/lock', [app\controller\InventoryController::class, 'lock']); // 锁定库存
        Route::post('/release', [app\controller\InventoryController::class, 'release']); // 释放库存
        Route::get('/statistics', [app\controller\InventoryController::class, 'statistics']); // 库存统计

        // 库存分配管理
        Route::get('/allocation', [app\controller\InventoryController::class, 'allocation']); // 获取库存分配信息
        Route::put('/allocation', [app\controller\InventoryController::class, 'updateAllocation']); // 更新库存分配信息

        // 收益管理
        Route::get('/revenue-management', [app\controller\InventoryController::class, 'revenueManagement']); // 获取收益管理信息
        Route::put('/revenue-management', [app\controller\InventoryController::class, 'updateRevenueManagement']); // 更新收益管理信息

        // 限制条件管理
        Route::get('/restrictions', [app\controller\InventoryController::class, 'restrictions']); // 获取库存限制条件
        Route::put('/restrictions', [app\controller\InventoryController::class, 'updateRestrictions']); // 更新库存限制条件

        // 库存优化
        Route::get('/optimization', [app\controller\InventoryController::class, 'optimization']); // 获取库存优化建议
    });

    // 价格管理路由
    Route::group('/rates', function () {
        Route::get('', [app\controller\RateController::class, 'index']); // 查询价格
        Route::put('', [app\controller\RateController::class, 'update']); // 更新价格
        Route::post('/batch-update', [app\controller\RateController::class, 'batchUpdate']); // 批量更新价格
        Route::get('/statistics', [app\controller\RateController::class, 'statistics']); // 价格统计
        Route::get('/compare', [app\controller\RateController::class, 'compare']); // 价格比较
    });

    // 价格计划管理路由
    Route::group('/hotels/{hotel_id:\d+}/rate-plans', function () {
        Route::get('', [app\controller\RatePlanController::class, 'index']); // 获取价格计划列表
        Route::get('/by-room-type', [app\controller\RatePlanController::class, 'indexByRoomType']); // 按房型分组获取价格计划列表
        Route::post('', [app\controller\RatePlanController::class, 'store']); // 创建价格计划
        Route::get('/{id}', [app\controller\RatePlanController::class, 'show']); // 获取价格计划详情
        Route::put('/{id}', [app\controller\RatePlanController::class, 'update']); // 更新价格计划
        Route::delete('/{id}', [app\controller\RatePlanController::class, 'destroy']); // 删除价格计划
        Route::post('/{id}/copy', [app\controller\RatePlanController::class, 'copy']); // 复制价格计划
        Route::get('/{id}/rates', [app\controller\RatePlanController::class, 'rates']); // 获取价格计划价格数据

        // 状态管理
        Route::post('/{id}/publish', [app\controller\RatePlanController::class, 'publish']); // 发布价格计划
        Route::post('/{id}/unpublish', [app\controller\RatePlanController::class, 'unpublish']); // 取消发布价格计划
        Route::post('/{id}/activate', [app\controller\RatePlanController::class, 'activate']); // 激活价格计划
        Route::post('/{id}/deactivate', [app\controller\RatePlanController::class, 'deactivate']); // 停用价格计划

        // OTA渠道关联管理
        Route::get('/{id}/ota-channels', [app\controller\RatePlanController::class, 'getOtaChannels']); // 获取房价计划关联的OTA渠道
        Route::post('/{id}/ota-channels', [app\controller\RatePlanController::class, 'updateOtaChannels']); // 更新房价计划的OTA渠道关联

        // 统计和日志
        Route::get('/{id}/statistics', [app\controller\RatePlanController::class, 'statistics']); // 获取价格计划统计数据
        Route::get('/{id}/logs', [app\controller\RatePlanController::class, 'logs']); // 获取价格计划操作日志

        // 套餐管理
        Route::get('/{id}/packages', [app\controller\RatePlanController::class, 'packages']); // 获取价格计划套餐信息
        Route::put('/{id}/packages', [app\controller\RatePlanController::class, 'updatePackages']); // 更新价格计划套餐信息

        // 折扣管理
        Route::get('/{id}/discounts', [app\controller\RatePlanController::class, 'discounts']); // 获取价格计划折扣信息
        Route::put('/{id}/discounts', [app\controller\RatePlanController::class, 'updateDiscounts']); // 更新价格计划折扣信息

        // 限制条件管理
        Route::get('/{id}/restrictions', [app\controller\RatePlanController::class, 'restrictions']); // 获取价格计划限制条件
        Route::put('/{id}/restrictions', [app\controller\RatePlanController::class, 'updateRestrictions']); // 更新价格计划限制条件
    })->middleware([app\middleware\AuthMiddleware::class]);


    // 价格计划批量操作和基础数据路由
    Route::group('/rate-plans', function () {
        // 批量操作
        Route::post('/batch', [app\controller\RatePlanController::class, 'batchAction']); // 批量操作价格计划

        // 导入导出
        Route::post('/import', [app\controller\RatePlanController::class, 'import']); // 导入价格计划数据
        Route::post('/export', [app\controller\RatePlanController::class, 'export']); // 导出价格计划数据

        // 基础数据接口
        Route::get('/templates', [app\controller\RatePlanController::class, 'templates']); // 获取价格计划模板
        Route::get('/breakfast-types', [app\controller\RatePlanController::class, 'breakfastTypes']); // 获取早餐类型列表
        Route::get('/cancellation-policies', [app\controller\RatePlanController::class, 'cancellationPolicies']); // 获取取消政策列表
    })->middleware([app\middleware\AuthMiddleware::class]);

    // OTA渠道管理路由
    Route::group('/ota-channels', function () {
        Route::get('', [app\controller\OtaChannelController::class, 'index']); // 获取OTA渠道列表
        Route::post('', [app\controller\OtaChannelController::class, 'store']); // 创建OTA渠道
        Route::get('/options', [app\controller\OtaChannelController::class, 'options']); // 获取OTA渠道选项
        Route::get('/{id}', [app\controller\OtaChannelController::class, 'show']); // 获取OTA渠道详情
        Route::put('/{id}', [app\controller\OtaChannelController::class, 'update']); // 更新OTA渠道
        Route::delete('/{id}', [app\controller\OtaChannelController::class, 'destroy']); // 删除OTA渠道

        // 状态管理
        Route::post('/{id}/toggle-status', [app\controller\OtaChannelController::class, 'toggleStatus']); // 切换状态

        // 批量操作
        Route::post('/batch', [app\controller\OtaChannelController::class, 'batchAction']); // 批量操作

        // 连接测试和数据同步
        Route::post('/{id}/test-connection', [app\controller\OtaChannelController::class, 'testConnection']); // 测试连接
        Route::post('/{id}/sync-data', [app\controller\OtaChannelController::class, 'syncData']); // 同步数据

        // 统计信息
        Route::get('/{id}/statistics', [app\controller\OtaChannelController::class, 'statistics']); // 获取统计信息

        // 导入导出
        Route::post('/import', [app\controller\OtaChannelController::class, 'import']); // 导入数据
        Route::post('/export', [app\controller\OtaChannelController::class, 'export']); // 导出数据
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 订单管理路由
    Route::group('/bookings', function () {
        Route::get('', [app\controller\BookingController::class, 'index']); // 获取订单列表
        Route::post('', [app\controller\BookingController::class, 'store']); // 创建订单
        Route::get('/search', [app\controller\BookingController::class, 'search']); // 搜索订单
        Route::get('/statistics', [app\controller\BookingController::class, 'statistics']); // 订单统计
        Route::get('/{id}', [app\controller\BookingController::class, 'show']); // 获取订单详情
        Route::put('/{id}', [app\controller\BookingController::class, 'update']); // 更新订单信息
        Route::post('/{id}/confirm', [app\controller\BookingController::class, 'confirm']); // 确认订单
        Route::post('/{id}/cancel', [app\controller\BookingController::class, 'cancel']); // 取消订单
        Route::post('/{id}/check-in', [app\controller\BookingController::class, 'checkIn']); // 办理入住
        Route::post('/{id}/check-out', [app\controller\BookingController::class, 'checkOut']); // 办理退房

        // 预订偏好管理
//        Route::get('/{id}/preferences', [app\controller\BookingController::class, 'preferences']); // 获取预订偏好信息
        Route::put('/{id}/preferences', [app\controller\BookingController::class, 'updatePreferences']); // 更新预订偏好信息

        // 预订同意管理
        Route::get('/{id}/consents', [app\controller\BookingController::class, 'consents']); // 获取预订同意信息
        Route::put('/{id}/consents', [app\controller\BookingController::class, 'updateConsents']); // 更新预订同意信息

        // 预订财务管理
        Route::get('/{id}/financial', [app\controller\BookingController::class, 'financial']); // 获取预订财务信息
        Route::put('/{id}/financial', [app\controller\BookingController::class, 'updateFinancial']); // 更新预订财务信息

        // 预订验证管理
        Route::get('/{id}/verification-status', [app\controller\BookingController::class, 'verificationStatus']); // 获取预订验证状态
        Route::put('/{id}/verification-status', [app\controller\BookingController::class, 'updateVerificationStatus']); // 更新预订验证状态

        // 预订押金管理
        Route::get('/{id}/deposits', [app\controller\BookingController::class, 'deposits']); // 获取预订押金信息
        Route::put('/{id}/deposits', [app\controller\BookingController::class, 'updateDeposits']); // 更新预订押金信息
    });

    // 手动订单管理路由
    Route::group('/manual-bookings', function () {
        // 获取可用选项
        Route::get('/available-options', [app\controller\ManualBookingController::class, 'getAvailableOptions']); // 获取可用房型和价格

        // 订单操作
        Route::post('', [app\controller\ManualBookingController::class, 'create']); // 创建手动订单
        Route::put('/{id}', [app\controller\ManualBookingController::class, 'modify']); // 修改手动订单
        Route::post('/{id}/cancel', [app\controller\ManualBookingController::class, 'cancel']); // 取消手动订单
        Route::post('/{id}/confirm', [app\controller\ManualBookingController::class, 'confirm']); // 确认手动订单
    })->middleware([app\middleware\AuthMiddleware::class]);

    // OTA数据同步管理路由
    Route::group('/ota-sync', function () {
        // 渠道管理
        Route::get('/channels', [app\controller\OtaSyncController::class, 'getChannels']); // 获取OTA渠道列表
        Route::post('/channels', [app\controller\OtaSyncController::class, 'createChannel']); // 创建OTA渠道配置
        Route::put('/channels/{id}', [app\controller\OtaSyncController::class, 'updateChannel']); // 更新OTA渠道配置
        Route::delete('/channels/{id}', [app\controller\OtaSyncController::class, 'deleteChannel']); // 删除OTA渠道配置
        Route::post('/channels/{id}/test', [app\controller\OtaSyncController::class, 'testConnection']); // 测试渠道连接

        // 数据同步
        Route::post('/hotel-info', [app\controller\OtaSyncController::class, 'syncHotelInfo']); // 同步酒店信息
        Route::post('/room-types', [app\controller\OtaSyncController::class, 'syncRoomTypes']); // 同步房型信息
        Route::post('/inventory', [app\controller\OtaSyncController::class, 'syncInventory']); // 同步库存数据
        Route::post('/rates', [app\controller\OtaSyncController::class, 'syncRates']); // 同步价格数据

        // 同步日志和统计
        Route::get('/logs', [app\controller\OtaSyncController::class, 'getSyncLogs']); // 获取同步日志
        Route::get('/stats', [app\controller\OtaSyncController::class, 'getSyncStats']); // 获取同步统计
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 系统配置管理路由
    Route::group('/system-config', function () {
        // 配置管理
        Route::get('', [app\controller\SystemConfigController::class, 'index']); // 获取所有配置
        Route::post('', [app\controller\SystemConfigController::class, 'store']); // 创建配置
        Route::get('/groups', [app\controller\SystemConfigController::class, 'getGroups']); // 获取配置分组
        Route::get('/types', [app\controller\SystemConfigController::class, 'getTypes']); // 获取配置类型
        Route::get('/public', [app\controller\SystemConfigController::class, 'getPublicConfigs']); // 获取公开配置
        Route::post('/batch-update', [app\controller\SystemConfigController::class, 'batchUpdate']); // 批量更新配置
        Route::post('/validate', [app\controller\SystemConfigController::class, 'validateValue']); // 验证配置值

        // 分组配置
        Route::get('/group/{group}', [app\controller\SystemConfigController::class, 'getGroupConfigs']); // 获取分组配置

        // 单个配置操作
        Route::get('/{key}', [app\controller\SystemConfigController::class, 'show']); // 获取单个配置
        Route::put('/{key}', [app\controller\SystemConfigController::class, 'update']); // 更新配置
        Route::delete('/{key}', [app\controller\SystemConfigController::class, 'destroy']); // 删除配置
        Route::post('/{key}/reset', [app\controller\SystemConfigController::class, 'reset']); // 重置配置
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 供应商管理路由
    Route::group('/suppliers', function () {
        Route::get('', [app\controller\SupplierController::class, 'index']); // 获取供应商列表
        Route::post('', [app\controller\SupplierController::class, 'store']); // 创建供应商
        Route::get('/{id}', [app\controller\SupplierController::class, 'show']); // 获取供应商详情
        Route::put('/{id}', [app\controller\SupplierController::class, 'update']); // 更新供应商信息
        Route::delete('/{id}', [app\controller\SupplierController::class, 'destroy']); // 删除供应商
        Route::post('/{id}/test-connection', [app\controller\SupplierController::class, 'testConnection']); // 测试连接
        Route::post('/{id}/enable', [app\controller\SupplierController::class, 'enable']); // 启用供应商
        Route::post('/{id}/disable', [app\controller\SupplierController::class, 'disable']); // 禁用供应商
        Route::get('/{id}/hotel-mappings', [app\controller\SupplierController::class, 'hotelMappings']); // 获取酒店映射
        Route::post('/{id}/hotel-mappings', [app\controller\SupplierController::class, 'createHotelMapping']); // 创建酒店映射
        Route::put('/{id}/hotel-mappings/{mapping_id:\d+}', [app\controller\SupplierController::class, 'updateHotelMapping']); // 更新酒店映射
        Route::delete('/{id}/hotel-mappings/{mapping_id:\d+}', [app\controller\SupplierController::class, 'deleteHotelMapping']); // 删除酒店映射
    });

    // 健康检查路由（无需认证）
    Route::group('/health', function () {
        Route::get('/ping', [app\controller\HealthController::class, 'ping']); // 简单ping检查
        Route::get('/basic', [app\controller\HealthController::class, 'basic']); // 基础健康检查
        Route::get('/full', [app\controller\HealthController::class, 'full']); // 完整健康检查
        Route::get('/info', [app\controller\HealthController::class, 'info']); // 系统信息
        Route::get('/version', [app\controller\HealthController::class, 'version']); // 版本信息
        Route::get('/dependencies', [app\controller\HealthController::class, 'dependencies']); // 依赖检查
        Route::get('/metrics', [app\controller\HealthController::class, 'metrics']); // 性能指标
        Route::get('/component/{component}', [app\controller\HealthController::class, 'component']); // 单个组件检查
    });

    // 兼容旧的健康检查路由
    Route::get('/health', [app\controller\HealthController::class, 'basic']); // 兼容旧版本

    // 地区管理路由
    Route::group('/regions', function () {
        Route::get('', [app\controller\RegionController::class, 'index']); // 获取地区列表
        Route::post('', [app\controller\RegionController::class, 'store']); // 创建地区
        Route::get('/tree', [app\controller\RegionController::class, 'tree']); // 获取地区树形结构
        Route::get('/provinces', [app\controller\RegionController::class, 'provinces']); // 获取省份列表
        Route::get('/cities', [app\controller\RegionController::class, 'cities']); // 获取城市列表
        Route::get('/districts', [app\controller\RegionController::class, 'districts']); // 获取区县列表
        Route::get('/{id}', [app\controller\RegionController::class, 'show']); // 获取地区详情
        Route::put('/{id}', [app\controller\RegionController::class, 'update']); // 更新地区
        Route::delete('/{id}', [app\controller\RegionController::class, 'destroy']); // 删除地区
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 房型设施设备管理
    Route::group('/room-facilities', function () {
        // 设施分类
        Route::get('/categories', [app\controller\RoomFacilityController::class, 'categories']);
        Route::post('/categories', [app\controller\RoomFacilityController::class, 'createCategory']);
        Route::put('/categories', [app\controller\RoomFacilityController::class, 'updateCategory']);
        Route::delete('/categories', [app\controller\RoomFacilityController::class, 'deleteCategory']);

        // 设施管理
        Route::get('', [app\controller\RoomFacilityController::class, 'facilities']);
        Route::post('', [app\controller\RoomFacilityController::class, 'createFacility']);
        Route::put('', [app\controller\RoomFacilityController::class, 'updateFacility']);
        Route::delete('', [app\controller\RoomFacilityController::class, 'deleteFacility']);
        Route::post('/batch', [app\controller\RoomFacilityController::class, 'batchFacility']);
        Route::get('/stats', [app\controller\RoomFacilityController::class, 'facilityStats']);

        // 房型设施关联
        Route::get('/room-type', [app\controller\RoomFacilityController::class, 'getRoomTypeFacilities']);
        Route::post('/room-type', [app\controller\RoomFacilityController::class, 'setRoomTypeFacilities']);
        Route::post('/room-type/add', [app\controller\RoomFacilityController::class, 'addRoomTypeFacility']);
        Route::post('/room-type/remove', [app\controller\RoomFacilityController::class, 'removeRoomTypeFacility']);
        Route::post('/room-type/order', [app\controller\RoomFacilityController::class, 'updateFacilityOrder']);
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 房型图片管理
    Route::group('/room-type-images', function () {
        Route::get('', [app\controller\RoomTypeImageController::class, 'getRoomTypeImages']);
        Route::post('/upload', [app\controller\RoomTypeImageController::class, 'uploadImage']);
        Route::post('/batch-upload', [app\controller\RoomTypeImageController::class, 'batchUpload']);
        Route::put('', [app\controller\RoomTypeImageController::class, 'updateImage']);
        Route::post('/primary', [app\controller\RoomTypeImageController::class, 'setPrimary']);
        Route::post('/order', [app\controller\RoomTypeImageController::class, 'updateOrder']);
        Route::delete('', [app\controller\RoomTypeImageController::class, 'deleteImage']);
        Route::post('/batch-delete', [app\controller\RoomTypeImageController::class, 'batchDelete']);
        Route::get('/types', [app\controller\RoomTypeImageController::class, 'getImageTypes']);
        Route::get('/stats', [app\controller\RoomTypeImageController::class, 'getImageStats']);
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 床型类型管理路由
    Route::group('/bed-types', function () {
        Route::get('', [app\controller\BedTypeController::class, 'index']); // 获取床型类型列表
        Route::post('', [app\controller\BedTypeController::class, 'store']); // 创建床型类型
        Route::get('/options', [app\controller\BedTypeController::class, 'options']); // 获取床型类型选项（用于下拉选择）
        Route::get('/categories', [app\controller\BedTypeController::class, 'categories']); // 获取床型分类列表
        Route::get('/sizes', [app\controller\BedTypeController::class, 'sizes']); // 获取床型尺寸列表
        Route::get('/locales', [app\controller\BedTypeController::class, 'locales']); // 获取支持的语言列表
        Route::get('/statistics', [app\controller\BedTypeController::class, 'statistics']); // 获取床型类型统计信息
        Route::post('/batch-update-status', [app\controller\BedTypeController::class, 'batchUpdateStatus']); // 批量更新床型类型状态
        Route::get('/{id}', [app\controller\BedTypeController::class, 'show']); // 获取床型类型详情
        Route::put('/{id}', [app\controller\BedTypeController::class, 'update']); // 更新床型类型
        Route::delete('/{id}', [app\controller\BedTypeController::class, 'destroy']); // 删除床型类型
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 景观类型管理路由
    Route::group('/view-types', function () {
        Route::get('', [app\controller\ViewTypeController::class, 'index']); // 获取景观类型列表
        Route::post('', [app\controller\ViewTypeController::class, 'store']); // 创建景观类型
        Route::get('/options', [app\controller\ViewTypeController::class, 'options']); // 获取景观类型选项（用于下拉选择）
        Route::post('/batch-update-status', [app\controller\ViewTypeController::class, 'batchUpdateStatus']); // 批量更新景观类型状态
        Route::post('/update-sort', [app\controller\ViewTypeController::class, 'updateSort']); // 更新排序
        Route::get('/{id}', [app\controller\ViewTypeController::class, 'show']); // 获取景观类型详情
        Route::put('/{id}', [app\controller\ViewTypeController::class, 'update']); // 更新景观类型
        Route::delete('/{id}', [app\controller\ViewTypeController::class, 'destroy']); // 删除景观类型
    })->middleware([app\middleware\AuthMiddleware::class]);

    // 房价计划管理路由
    Route::group('/price-plans', function () {
        Route::get('', [app\controller\PricePlanController::class, 'index']); // 获取房价计划列表（按房型分组）
        Route::get('/{id}', [app\controller\PricePlanController::class, 'show']); // 获取房价计划详情
        Route::get('/room-type/{roomTypeId}', [app\controller\PricePlanController::class, 'getByRoomType']); // 根据房型获取房价计划
        Route::post('/price-info', [app\controller\PricePlanController::class, 'getPriceInfo']); // 根据房价计划ID和日期获取房价信息
    })->middleware([app\middleware\AuthMiddleware::class]);
});

// 房价计划管理路由组
Route::group('/api/hotels/{hotel_id}/rate-plans', function () {

    // 基础CRUD操作
    Route::get('', [\app\controller\RatePlanController::class, 'index']);           // 获取价格计划列表
    Route::post('', [\app\controller\RatePlanController::class, 'store']);          // 创建价格计划
    Route::get('/{id}', [\app\controller\RatePlanController::class, 'show']);       // 获取价格计划详情
    Route::put('/{id}', [\app\controller\RatePlanController::class, 'update']);     // 更新价格计划
    Route::delete('/{id}', [\app\controller\RatePlanController::class, 'destroy']); // 删除价格计划

    // 复制价格计划
    Route::post('/{id}/copy', [\app\controller\RatePlanController::class, 'copy']); // 复制价格计划

    // 状态管理
    Route::post('/{id}/publish', [\app\controller\RatePlanController::class, 'publish']);     // 发布价格计划
    Route::post('/{id}/unpublish', [\app\controller\RatePlanController::class, 'unpublish']); // 取消发布价格计划
    Route::post('/{id}/activate', [\app\controller\RatePlanController::class, 'activate']);   // 激活价格计划
    Route::post('/{id}/deactivate', [\app\controller\RatePlanController::class, 'deactivate']); // 停用价格计划

    // 价格数据
    Route::get('/{id}/rates', [\app\controller\RatePlanController::class, 'rates']); // 获取价格计划的价格数据

    // 统计和日志
    Route::get('/{id}/statistics', [\app\controller\RatePlanController::class, 'statistics']); // 获取价格计划统计数据
    Route::get('/{id}/logs', [\app\controller\RatePlanController::class, 'logs']);             // 获取价格计划操作日志

})->middleware([\app\middleware\AuthMiddleware::class]);



// 公开接口（无需认证）
Route::group('/api/public/rate-plans', function () {

    // 公开的基础数据接口
    Route::get('/breakfast-types', [\app\controller\RatePlanController::class, 'breakfastTypes']);        // 获取早餐类型列表
    Route::get('/cancellation-policies', [\app\controller\RatePlanController::class, 'cancellationPolicies']); // 获取取消政策列表

});

// CORS 预检请求处理
Route::options('[{path:.+}]', function () {
    return response('', 200, [
        'Access-Control-Allow-Origin' => '*',
        'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With, X-API-Key',
        'Access-Control-Max-Age' => '86400'
    ]);
});




