-- Migration: create room_type_policies table if not exists
CREATE TABLE IF NOT EXISTS `room_type_policies` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  -- 取消政策
  `cancellation_type` varchar(50) DEFAULT 'free' COMMENT '取消政策类型: free/partial/non_refundable/custom',
  `free_cancellation_hours` int NULL DEFAULT 24 COMMENT '免费取消小时数',
  `cancellation_fee_rate` decimal(5,2) NULL DEFAULT 0 COMMENT '取消手续费率(%)',
  `cancellation_policy` text NULL COMMENT '取消政策说明',
  -- 入住政策
  `min_check_in_age` int NULL DEFAULT 18 COMMENT '最低入住年龄',
  `id_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '需要身份证明',
  `credit_card_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '需要信用卡担保',
  `deposit_amount` decimal(10,2) NULL DEFAULT 0 COMMENT '押金金额',
  `deposit_type` varchar(20) DEFAULT 'none' COMMENT '押金类型: cash/credit_card/both/none',
  -- 住客限制
  `max_guests` int NULL DEFAULT 2 COMMENT '最大住客数',
  `visitors_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '允许访客',
  `visitor_hours` varchar(100) NULL DEFAULT NULL COMMENT '访客时间限制',
  `parties_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许聚会',
  `smoking_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许吸烟',
  `quiet_hours` varchar(100) NULL DEFAULT NULL COMMENT '安静时间',
  -- 儿童与宠物
  `children_free_age` int NULL DEFAULT 12 COMMENT '儿童免费年龄',
  `pets_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许宠物',
  `pet_fee_per_night` decimal(10,2) NULL DEFAULT 0 COMMENT '宠物费用(每晚)',
  `pet_restrictions` varchar(200) NULL DEFAULT NULL COMMENT '宠物限制',
  `children_facilities` varchar(200) NULL DEFAULT NULL COMMENT '儿童设施',
  -- 其他
  `early_check_in_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '允许提前入住',
  `late_check_out_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '允许延迟退房',
  `luggage_storage` tinyint(1) NOT NULL DEFAULT 1 COMMENT '行李寄存',
  `special_policies` text NULL COMMENT '其他特殊政策',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态: active/inactive',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_room_type_policy` (`room_type_id`) USING BTREE,
  KEY `idx_room_type_id` (`room_type_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='房型政策表';
