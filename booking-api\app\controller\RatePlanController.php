<?php

namespace app\controller;

use app\service\RateService;
use support\Request;
use support\Response;

/**
 * 价格计划管理控制器
 */
class RatePlanController extends BaseController
{
    /**
     * 价格服务
     *
     * @var RateService
     */
    private $rateService;

    public function __construct()
    {
        $this->rateService = new RateService();
    }

    /**
     * 获取价格计划列表
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function index(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);
            $result = $this->rateService->getRatePlans($hotelId, $params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划列表');
        }
    }

    /**
     * 按房型分组获取价格计划列表
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function indexByRoomType(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);
            $result = $this->rateService->getRatePlansByRoomType($hotelId, $params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '按房型获取价格计划列表');
        }
    }

    /**
     * 创建价格计划
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function store(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);
            $data['code'] =   uniqid();
            // 验证必需参数
            $errors = $this->validateRequired($data, [ 'name', 'plan_type']);
            if ($errors) {
                return $this->error($errors[0]['message'], 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'advance_booking_days' => 'integer',
                'min_stay' => 'integer',
                'max_stay' => 'integer',
                'valid_from' => 'date',
                'valid_to' => 'date',
                'sort_order' => 'integer',
                'base_price' => 'numeric',
                'minimum_revenue_requirements' => 'numeric',
                'maximum_discount_allowed' => 'numeric',
                'min_advance_booking' => 'integer',
                'max_advance_booking' => 'integer',
                'max_occupancy' => 'integer',
                'max_adults' => 'integer',
                'max_children' => 'integer',
                'cancellation_deadline' => 'integer',
                'cancellation_fee_amount' => 'numeric',
                'modification_deadline' => 'integer',
                'modification_fee' => 'numeric',
                'prepayment_amount' => 'numeric',
                'prepayment_deadline' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证计划类型
            $validPlanTypes = ['postpaid', 'prepaid'];
            if (!in_array($data['plan_type'], $validPlanTypes)) {
                return $this->error('无效的计划类型，必须是现付(postpaid)或预付(prepaid)');
            }

            // 验证枚举值
            $enumValidations = [
                'rate_plan_category' => ['standard', 'promotion', 'package', 'corporate', 'government', 'group', 'loyalty', 'last_minute', 'early_bird', 'seasonal', 'weekend', 'weekday', 'holiday'],
                'rate_plan_grade' => ['basic', 'standard', 'premium', 'luxury', 'vip', 'exclusive'],
                'market_segment' => ['leisure', 'business', 'group', 'corporate', 'government', 'wedding', 'conference', 'event', 'family', 'couple', 'solo', 'senior', 'youth', 'luxury', 'budget'],
                'booking_source' => ['direct', 'ota', 'gds', 'corporate', 'travel_agent', 'phone', 'walk_in', 'group', 'event', 'package'],
                'rate_calculation_method' => ['fixed', 'dynamic', 'seasonal', 'demand_based', 'competition_based', 'cost_plus', 'value_based', 'penetration', 'skimming', 'bundle'],
                'price_type' => ['per_night', 'per_person', 'per_room', 'total'],
                'cancellation_fee_type' => ['fixed', 'percentage', 'nights'],
                'prepayment_type' => ['full', 'partial', 'deposit']
            ];

            foreach ($enumValidations as $field => $validValues) {
                if (!empty($data[$field]) && !in_array($data[$field], $validValues)) {
                    return $this->error("字段 {$field} 的值无效");
                }
            }

            // 验证布尔值字段
            $booleanFields = [
                'breakfast_included', 'is_refundable', 'modification_allowed',
                'prepayment_required', 'member_only', 'is_active', 'is_published',
                'dynamic_pricing_enabled', 'transportation_included'
            ];

            foreach ($booleanFields as $field) {
                if (isset($data[$field]) && !is_bool($data[$field]) && !in_array($data[$field], [0, 1, '0', '1', 'true', 'false'])) {
                    return $this->error("字段 {$field} 必须是布尔值");
                }
            }

            // 验证JSON字段格式
            $jsonFields = [
                'includes', 'channel_restrictions', 'distribution_channels', 'seasonal_adjustments',
                'day_of_week_adjustments', 'length_of_stay_discounts', 'early_booking_discounts',
                'last_minute_discounts', 'group_booking_discounts', 'loyalty_program_benefits',
                'corporate_rates', 'government_rates', 'package_inclusions', 'addon_services',
                'upgrade_options', 'meal_plan_options', 'activity_packages', 'spa_packages',
                'business_packages', 'family_packages', 'rate_restrictions', 'booking_window',
                'stay_date_restrictions', 'blackout_dates', 'commission_structure', 'tax_inclusion_rules'
            ];

            foreach ($jsonFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            // 验证OTA渠道绑定参数
            $otaValidationResult = $this->validateOtaChannelBinding($data);
            if ($otaValidationResult !== true) {
                return $this->error($otaValidationResult);
            }

            // 验证有效期
            if (!empty($data['valid_from']) && !empty($data['valid_to'])) {
                if ($data['valid_from'] > $data['valid_to']) {
                    return $this->error('有效开始日期不能晚于结束日期');
                }
            }

            // 验证促销期
            if (!empty($data['promotion_start_date']) && !empty($data['promotion_end_date'])) {
                if ($data['promotion_start_date'] > $data['promotion_end_date']) {
                    return $this->error('促销开始日期不能晚于结束日期');
                }
            }

            // 验证入住天数限制
            if (!empty($data['min_stay']) && !empty($data['max_stay'])) {
                if ($data['min_stay'] > $data['max_stay']) {
                    return $this->error('最少入住天数不能大于最多入住天数');
                }
            }

            $result = $this->rateService->createRatePlan($hotelId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建价格计划');
        }
    }

    /**
     * 获取价格计划详情
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $ratePlan = \app\model\RatePlan::where('hotel_id', $hotelId)
                ->with(['hotel'])
                ->find($ratePlanId);

            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            return $this->success($ratePlan);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划详情');
        }
    }

    /**
     * 更新价格计划
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'advance_booking_days' => 'integer',
                'min_stay' => 'integer',
                'max_stay' => 'integer',
                'valid_from' => 'date',
                'valid_to' => 'date',
                'sort_order' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证计划类型
            if (!empty($data['plan_type'])) {
                $validPlanTypes = ['postpaid', 'prepaid'];
                if (!in_array($data['plan_type'], $validPlanTypes)) {
                    return $this->error('无效的计划类型，必须是现付(postpaid)或预付(prepaid)');
                }
            }

            // 验证有效期
            if (!empty($data['valid_from']) && !empty($data['valid_to'])) {
                if ($data['valid_from'] > $data['valid_to']) {
                    return $this->error('有效开始日期不能晚于结束日期');
                }
            }

            // 验证入住天数限制
            if (!empty($data['min_stay']) && !empty($data['max_stay'])) {
                if ($data['min_stay'] > $data['max_stay']) {
                    return $this->error('最少入住天数不能大于最多入住天数');
                }
            }

            $result = $this->rateService->updateRatePlan($hotelId, $ratePlanId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新价格计划');
        }
    }

    /**
     * 删除价格计划
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->deleteRatePlan($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除价格计划');
        }
    }

    /**
     * 复制价格计划
     *
     * @param Request $request
     * @return Response
     */
    public function copy(Request $request)
    {
        try {
            $hotelId = (int)$request->route('hotel_id');
            $ratePlanId = (int)$request->route('id');
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['name', 'code']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 准备复制数据
            $copyData = [
                'name' => $data['name'],
                'code' => $data['code'],
            ];

            if (!empty($data['description'])) {
                $copyData['description'] = $data['description'];
            }

            $result = $this->rateService->copyRatePlan($hotelId, $ratePlanId, $copyData);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '复制价格计划');
        }
    }

    /**
     * 获取价格计划的价格数据
     *
     * @param Request $request
     * @return Response
     */
    public function rates(Request $request)
    {
        try {
            $hotelId = (int)$request->route('hotel_id');
            $ratePlanId = (int)$request->route('id');

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $params = $this->getInput($request);

            // 设置默认日期范围
            $startDate = $params['start_date'] ?? date('Y-m-d');
            $endDate = $params['end_date'] ?? date('Y-m-d', strtotime('+30 days'));

            // 验证日期格式
            $dateErrors = $this->validateFormat([
                'start_date' => $startDate,
                'end_date' => $endDate
            ], [
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($dateErrors) {
                return $this->error('日期格式错误', 400, $dateErrors);
            }

            $queryParams = [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ];

            if (!empty($params['room_type_id'])) {
                $queryParams['room_type_id'] = $params['room_type_id'];
            }

            $result = $this->rateService->queryRates($queryParams);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划价格数据');
        }
    }

    /**
     * 批量操作价格计划
     *
     * @param Request $request
     * @return Response
     */
    public function batchAction(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['action', 'ids']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['ids']) || empty($data['ids'])) {
                return $this->error('ids必须是非空数组');
            }

            $validActions = ['publish', 'unpublish', 'activate', 'deactivate', 'delete'];
            if (!in_array($data['action'], $validActions)) {
                return $this->error('无效的操作类型');
            }

            $result = $this->rateService->batchActionRatePlans($data['action'], $data['ids']);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量操作价格计划');
        }
    }

    /**
     * 发布价格计划
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function publish(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->publishRatePlan($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '发布价格计划');
        }
    }

    /**
     * 取消发布价格计划
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function unpublish(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->unpublishRatePlan($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '取消发布价格计划');
        }
    }

    /**
     * 激活价格计划
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function activate(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->activateRatePlan($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '激活价格计划');
        }
    }

    /**
     * 停用价格计划
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function deactivate(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->deactivateRatePlan($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '停用价格计划');
        }
    }


    /**
     * 获取价格计划模板
     *
     * @param Request $request
     * @return Response
     */
    public function templates(Request $request)
    {
        try {
            $result = $this->rateService->getRatePlanTemplates();
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划模板');
        }
    }

    /**
     * 获取早餐类型列表
     *
     * @param Request $request
     * @return Response
     */
    public function breakfastTypes(Request $request)
    {
        try {
            $result = $this->rateService->getBreakfastTypes();
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取早餐类型列表');
        }
    }

    /**
     * 获取取消政策列表
     *
     * @param Request $request
     * @return Response
     */
    public function cancellationPolicies(Request $request)
    {
        try {
            $result = $this->rateService->getCancellationPolicies();
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取取消政策列表');
        }
    }

    /**
     * 获取价格计划操作日志
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function logs(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->getRatePlanLogs($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划操作日志');
        }
    }

    /**
     * 获取价格计划统计数据
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function statistics(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $params = $this->getInput($request);
            $result = $this->rateService->getRatePlanStatistics($hotelId, $ratePlanId, $params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划统计数据');
        }
    }

    /**
     * 导出价格计划数据
     *
     * @param Request $request
     * @return Response
     */
    public function export(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $result = $this->rateService->exportRatePlans($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '导出价格计划数据');
        }
    }

    /**
     * 导入价格计划数据
     *
     * @param Request $request
     * @return Response
     */
    public function import(Request $request)
    {
        try {
            $file = $request->file('file');
            if (!$file) {
                return $this->error('请选择要导入的文件');
            }

            $result = $this->rateService->importRatePlans($file);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '导入价格计划数据');
        }
    }

    /**
     * 获取价格计划套餐信息
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function packages(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->getRatePlanPackages($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划套餐信息');
        }
    }

    /**
     * 更新价格计划套餐信息
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function updatePackages(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $data = $this->getInput($request);

            // 验证套餐数据格式
            $packageFields = [
                'package_inclusions', 'addon_services', 'upgrade_options',
                'meal_plan_options', 'activity_packages', 'spa_packages',
                'business_packages', 'family_packages', 'honeymoon_packages',
                'special_occasion_packages', 'seasonal_packages'
            ];

            foreach ($packageFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->rateService->updateRatePlanPackages($hotelId, $ratePlanId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新价格计划套餐信息');
        }
    }

    /**
     * 获取价格计划折扣信息
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function discounts(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->getRatePlanDiscounts($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划折扣信息');
        }
    }

    /**
     * 更新价格计划折扣信息
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function updateDiscounts(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $data = $this->getInput($request);

            // 验证折扣数据格式
            $discountFields = [
                'length_of_stay_discounts', 'early_booking_discounts',
                'last_minute_discounts', 'group_booking_discounts',
                'loyalty_program_benefits', 'seasonal_adjustments',
                'day_of_week_adjustments'
            ];

            foreach ($discountFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->rateService->updateRatePlanDiscounts($hotelId, $ratePlanId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新价格计划折扣信息');
        }
    }

    /**
     * 获取价格计划限制条件
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function restrictions(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->getRatePlanRestrictions($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格计划限制条件');
        }
    }

    /**
     * 更新价格计划限制条件
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function updateRestrictions(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $data = $this->getInput($request);

            // 验证限制条件数据格式
            $restrictionFields = [
                'rate_restrictions', 'booking_window', 'stay_date_restrictions',
                'blackout_dates', 'distribution_channels'
            ];

            foreach ($restrictionFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            // 验证数值字段
            $numericFields = ['minimum_revenue_requirements', 'maximum_discount_allowed'];
            foreach ($numericFields as $field) {
                if (!empty($data[$field]) && !is_numeric($data[$field])) {
                    return $this->error("字段 {$field} 必须是数值");
                }
            }

            $result = $this->rateService->updateRatePlanRestrictions($hotelId, $ratePlanId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新价格计划限制条件');
        }
    }

    /**
     * 获取房价计划关联的OTA渠道
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function getOtaChannels(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $result = $this->rateService->getRatePlanOtaChannels($hotelId, $ratePlanId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取OTA渠道关联');
        }
    }

    /**
     * 更新房价计划的OTA渠道关联
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    public function updateOtaChannels(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $ratePlanId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($ratePlanId <= 0) {
                return $this->error('价格计划ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['ota_channel_ids']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['ota_channel_ids'])) {
                return $this->error('ota_channel_ids必须是数组');
            }

            $result = $this->rateService->updateRatePlanOtaChannels($hotelId, $ratePlanId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新OTA渠道关联');
        }
    }

    /**
     * 验证OTA渠道绑定参数
     *
     * @param array $data
     * @return bool|string
     */
    private function validateOtaChannelBinding(array $data)
    {
        // 如果没有OTA渠道数据，跳过验证
        if (empty($data['ota_channel_ids']) && empty($data['channel_configs'])) {
            return true;
        }

        // 验证ota_channel_ids格式
        if (!empty($data['ota_channel_ids'])) {
            if (!is_array($data['ota_channel_ids'])) {
                return 'ota_channel_ids必须是数组格式';
            }

            // 验证每个渠道ID是否为有效的整数
            foreach ($data['ota_channel_ids'] as $channelId) {
                if (!is_numeric($channelId) || $channelId <= 0) {
                    return 'OTA渠道ID必须是有效的正整数';
                }
            }
        }

        // 验证channel_configs格式
        if (!empty($data['channel_configs'])) {
            if (!is_array($data['channel_configs'])) {
                return 'channel_configs必须是数组格式';
            }

            foreach ($data['channel_configs'] as $channelId => $config) {
                // 验证渠道ID
                if (!is_numeric($channelId) || $channelId <= 0) {
                    return "渠道配置中的渠道ID {$channelId} 无效";
                }

                // 验证配置是否为数组
                if (!is_array($config)) {
                    return "渠道 {$channelId} 的配置必须是数组格式";
                }

                // 验证数值字段
                $numericFields = [
                    'commission_rate' => [0, 100],
                    'markup_rate' => [0, 1000],
                    'markup_amount' => [0, null],
                    'min_stay_override' => [1, null],
                    'max_stay_override' => [1, null],
                    'booking_window_override' => [0, null],
                    'priority' => [0, null]
                ];

                foreach ($numericFields as $field => $range) {
                    if (isset($config[$field])) {
                        if (!is_numeric($config[$field])) {
                            return "渠道 {$channelId} 的 {$field} 必须是数值";
                        }

                        $value = (float)$config[$field];
                        if ($range[0] !== null && $value < $range[0]) {
                            return "渠道 {$channelId} 的 {$field} 不能小于 {$range[0]}";
                        }
                        if ($range[1] !== null && $value > $range[1]) {
                            return "渠道 {$channelId} 的 {$field} 不能大于 {$range[1]}";
                        }
                    }
                }

                // 验证日期字段
                $dateFields = ['effective_from', 'effective_to'];
                foreach ($dateFields as $field) {
                    if (!empty($config[$field])) {
                        if (!$this->isValidDate($config[$field])) {
                            return "渠道 {$channelId} 的 {$field} 日期格式无效";
                        }
                    }
                }

                // 验证生效日期逻辑
                if (!empty($config['effective_from']) && !empty($config['effective_to'])) {
                    if ($config['effective_from'] > $config['effective_to']) {
                        return "渠道 {$channelId} 的生效开始日期不能晚于结束日期";
                    }
                }

                // 验证布尔字段
                $booleanFields = ['is_active'];
                foreach ($booleanFields as $field) {
                    if (isset($config[$field]) && !is_bool($config[$field]) && !in_array($config[$field], [0, 1, '0', '1', 'true', 'false'])) {
                        return "渠道 {$channelId} 的 {$field} 必须是布尔值";
                    }
                }

                // 验证字符串字段长度
                $stringFields = [
                    'channel_rate_plan_code' => 100,
                    'cancellation_policy_override' => 1000,
                    'sync_error_message' => 1000
                ];

                foreach ($stringFields as $field => $maxLength) {
                    if (!empty($config[$field])) {
                        if (!is_string($config[$field])) {
                            return "渠道 {$channelId} 的 {$field} 必须是字符串";
                        }
                        if (strlen($config[$field]) > $maxLength) {
                            return "渠道 {$channelId} 的 {$field} 长度不能超过 {$maxLength} 个字符";
                        }
                    }
                }

                // 验证枚举字段
                if (!empty($config['sync_status'])) {
                    $validSyncStatuses = ['pending', 'syncing', 'success', 'failed'];
                    if (!in_array($config['sync_status'], $validSyncStatuses)) {
                        return "渠道 {$channelId} 的同步状态无效，必须是: " . implode(', ', $validSyncStatuses);
                    }
                }
            }

            // 验证channel_configs中的渠道ID是否都在ota_channel_ids中
            if (!empty($data['ota_channel_ids'])) {
                $configChannelIds = array_keys($data['channel_configs']);
                $missingChannelIds = array_diff($configChannelIds, $data['ota_channel_ids']);
                if (!empty($missingChannelIds)) {
                    return '渠道配置中包含未选择的渠道ID: ' . implode(', ', $missingChannelIds);
                }
            }
        }

        return true;
    }

    /**
     * 验证日期格式
     *
     * @param string $date
     * @return bool
     */
    private function isValidDate($date)
    {
        if (!is_string($date)) {
            return false;
        }

        // 支持多种日期格式
        $formats = ['Y-m-d', 'Y-m-d H:i:s', 'Y/m/d', 'Y/m/d H:i:s'];

        foreach ($formats as $format) {
            $dateTime = \DateTime::createFromFormat($format, $date);
            if ($dateTime && $dateTime->format($format) === $date) {
                return true;
            }
        }

        return false;
    }
}
