<?php

namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use support\Log;
use Throwable;

/**
 * 全局错误处理中间件
 */
class ErrorHandlerMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     *
     * @param Request $request
     * @param callable $handler
     * @return Response
     */
    public function process(Request $request, callable $handler): Response
    {
        try {
            return $handler($request);
        } catch (Throwable $e) {
            return $this->handleException($e, $request);
        }
    }

    /**
     * 处理异常
     *
     * @param Throwable $e
     * @param Request $request
     * @return Response
     */
    private function handleException(Throwable $e, Request $request): Response
    {
        // 记录错误日志
        Log::error('Unhandled exception', [
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString(),
            'url' => $request->url(),
            'method' => $request->method(),
            'ip' => $request->getRealIp(),
            'user_agent' => $request->header('User-Agent'),
            'request_data' => $this->getRequestData($request)
        ]);

        // 根据异常类型返回不同的响应
        $statusCode = $this->getStatusCode($e);
        $message = $this->getErrorMessage($e);

        return json([
            'code' => $statusCode,
            'message' => $message,
            'timestamp' => time(),
            'request_id' => $this->generateRequestId(),
            'debug' => config('app.debug', false) ? [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => explode("\n", $e->getTraceAsString())
            ] : null
        ], $statusCode >= 500 ? 500 : 400);
    }

    /**
     * 获取HTTP状态码
     *
     * @param Throwable $e
     * @return int
     */
    private function getStatusCode(Throwable $e): int
    {
        // 根据异常类型返回相应的HTTP状态码
        switch (true) {
            case $e instanceof \InvalidArgumentException:
                return 400;
            case $e instanceof \UnauthorizedHttpException:
                return 401;
            case $e instanceof \ForbiddenHttpException:
                return 403;
            case $e instanceof \NotFoundHttpException:
                return 404;
            case $e instanceof \MethodNotAllowedHttpException:
                return 405;
            case $e instanceof \ValidationException:
                return 422;
            case $e instanceof \TooManyRequestsHttpException:
                return 429;
            default:
                return 500;
        }
    }

    /**
     * 获取错误消息
     *
     * @param Throwable $e
     * @return string
     */
    private function getErrorMessage(Throwable $e): string
    {
        // 在生产环境下隐藏敏感错误信息
        if (!config('app.debug', false)) {
            switch (true) {
                case $e instanceof \InvalidArgumentException:
                    return '请求参数错误';
                case $e instanceof \UnauthorizedHttpException:
                    return '未授权访问';
                case $e instanceof \ForbiddenHttpException:
                    return '禁止访问';
                case $e instanceof \NotFoundHttpException:
                    return '资源不存在';
                case $e instanceof \MethodNotAllowedHttpException:
                    return '请求方法不允许';
                case $e instanceof \ValidationException:
                    return '数据验证失败';
                case $e instanceof \TooManyRequestsHttpException:
                    return '请求过于频繁';
                default:
                    return '服务器内部错误';
            }
        }

        return $e->getMessage();
    }

    /**
     * 获取请求数据
     *
     * @param Request $request
     * @return array
     */
    private function getRequestData(Request $request): array
    {
        $data = [];

        // GET参数
        if ($request->get()) {
            $data['get'] = $request->get();
        }

        // POST参数
        if ($request->post()) {
            $data['post'] = $this->filterSensitiveData($request->post());
        }

        // JSON数据
        $contentType = $request->header('content-type', '');
        if (strpos($contentType, 'application/json') !== false) {
            $rawBody = $request->rawBody();
            $jsonData = json_decode($rawBody, true);
            if (is_array($jsonData)) {
                $data['json'] = $this->filterSensitiveData($jsonData);
            }
        }

        return $data;
    }

    /**
     * 过滤敏感数据
     *
     * @param array $data
     * @return array
     */
    private function filterSensitiveData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_hash',
            'password_confirmation',
            'token',
            'api_key',
            'api_secret',
            'access_token',
            'refresh_token',
            'credit_card',
            'card_number',
            'cvv',
            'ssn',
            'id_card'
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***FILTERED***';
            }
        }

        return $data;
    }

    /**
     * 生成请求ID
     *
     * @return string
     */
    private function generateRequestId(): string
    {
        return 'req_' . uniqid() . '_' . mt_rand(1000, 9999);
    }
}
