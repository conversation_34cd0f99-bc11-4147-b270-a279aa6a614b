<?php

namespace app\model;

use support\Model;

/**
 * 房型设施设备模型
 */
class RoomFacility extends Model
{
    protected $table = 'room_facilities';
    
    protected $fillable = [
        'code',
        'name',
        'name_en',
        'category_id',
        'icon',
        'sort_order',
        'is_active',
        'is_chargeable',
        'charge_amount',
        'charge_unit',
        'description'
    ];

    protected $casts = [
        'category_id' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'is_chargeable' => 'boolean',
        'charge_amount' => 'decimal:2',
    ];

    /**
     * 获取所属分类
     */
    public function category()
    {
        return $this->belongsTo(RoomFacilityCategory::class, 'category_id', 'id');
    }



    /**
     * 获取房型关联
     */
    public function roomTypes()
    {
        return $this->belongsToMany(
            RoomType::class,
            'room_type_facilities',
            'facility_id',
            'room_type_id'
        )->withPivot(['quantity', 'specification', 'is_highlight', 'sort_order']);
    }

    /**
     * 获取按分类分组的设施列表
     */
    public static function getByCategory()
    {
        $facilities = self::with('category')
                         ->where('is_active', true)
                         ->orderBy('sort_order')
                         ->get()
                         ->groupBy('category.name')
                         ->toArray();

        return $facilities;
    }

    /**
     * 获取扁平化的设施列表
     */
    public static function getFlat()
    {
        return self::with('category')
                  ->where('is_active', true)
                  ->orderBy('sort_order')
                  ->get()
                  ->toArray();
    }

    /**
     * 获取树形结构的设施列表（按分类分组）
     */
    public static function getTree()
    {
        $facilities = self::with('category')
                         ->where('is_active', true)
                         ->orderBy('sort_order')
                         ->get()
                         ->groupBy('category.name')
                         ->toArray();

        return $facilities;
    }

    /**
     * 根据分类ID获取设施
     */
    public static function getByCategoryId($categoryId)
    {
        return self::where('category_id', $categoryId)
                  ->where('is_active', true)
                  ->orderBy('sort_order')
                  ->get()
                  ->toArray();
    }

    /**
     * 搜索设施
     */
    public static function search($keyword)
    {
        return self::with('category')
                  ->where('is_active', true)
                  ->where(function($query) use ($keyword) {
                      $query->where('name', 'like', "%{$keyword}%")
                            ->orWhere('name_en', 'like', "%{$keyword}%")
                            ->orWhere('code', 'like', "%{$keyword}%")
                            ->orWhere('description', 'like', "%{$keyword}%");
                  })
                  ->orderBy('sort_order')
                  ->get()
                  ->toArray();
    }

    /**
     * 检查设施代码是否存在
     */
    public static function codeExists($code, $excludeId = null)
    {
        $query = self::where('code', $code);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 获取下一个排序号
     */
    public static function getNextSortOrder($categoryId)
    {
        return self::where('category_id', $categoryId)->max('sort_order') + 1;
    }

    /**
     * 启用/禁用设施
     */
    public function toggleActive()
    {
        $this->is_active = !$this->is_active;
        return $this->save();
    }

    /**
     * 删除设施（软删除，设为禁用）
     */
    public function softDelete()
    {
        // 禁用所有子设施
        self::where('parent_id', $this->id)->update(['is_active' => false]);
        
        // 删除房型关联
        RoomTypeFacility::where('facility_id', $this->id)->delete();
        
        // 禁用当前设施
        $this->is_active = false;
        return $this->save();
    }

    /**
     * 验证是否可以删除
     */
    public function canDelete()
    {
        // 检查是否有子设施
        $hasChildren = self::where('parent_id', $this->id)->where('is_active', true)->exists();
        
        // 检查是否有房型关联
        $hasRoomTypes = RoomTypeFacility::where('facility_id', $this->id)->exists();
        
        return !$hasChildren && !$hasRoomTypes;
    }

    /**
     * 获取使用统计
     */
    public function getUsageStats()
    {
        $roomTypeCount = RoomTypeFacility::where('facility_id', $this->id)->count();
        
        return [
            'room_type_count' => $roomTypeCount,
            'is_popular' => $roomTypeCount > 10,
        ];
    }
}
