<?php

namespace app\model;

use support\Model;

/**
 * 酒店宠物政策模型
 */
class HotelPoliciesPetPolicy extends Model
{
    /**
     * 表名
     */
    protected $table = 'hotel_policies_pet_policies';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'hotel_id',
        'pets_allowed',
        'pet_types_allowed',
        'max_pets_per_room',
        'pet_weight_limit',
        'pet_fee_type',
        'pet_fee',
        'pet_deposit',
        'vaccination_required',
        'pet_areas_allowed',
        'pet_restrictions',
        'pet_services',
        'pet_amenities',
        'pet_notes',
        'status',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'pets_allowed' => 'boolean',
        'pet_types_allowed' => 'array',
        'max_pets_per_room' => 'integer',
        'pet_weight_limit' => 'decimal:2',
        'pet_fee' => 'decimal:2',
        'pet_deposit' => 'decimal:2',
        'vaccination_required' => 'boolean',
        'pet_areas_allowed' => 'array',
        'pet_restrictions' => 'array',
        'pet_services' => 'array',
        'pet_amenities' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(): array
    {
        return [
            'hotel_id' => 'required|integer|min:1',
            'pets_allowed' => 'boolean',
            'max_pets_per_room' => 'nullable|integer|min:0',
            'pet_weight_limit' => 'nullable|numeric|min:0',
            'pet_fee_type' => 'nullable|string|in:per_night,per_stay,one_time',
            'pet_fee' => 'nullable|numeric|min:0',
            'pet_deposit' => 'nullable|numeric|min:0',
            'vaccination_required' => 'boolean',
            'pet_notes' => 'nullable|string',
            'status' => 'in:active,inactive',
        ];
    }

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id', 'id');
    }

    /**
     * 作用域：启用的政策
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 获取政策类型
     */
    public function getPolicyType(): string
    {
        return 'pet';
    }

    /**
     * 获取政策显示名称
     */
    public function getPolicyDisplayName(): string
    {
        return '宠物政策';
    }

    /**
     * 获取政策英文名称
     */
    public function getPolicyEnglishName(): string
    {
        return 'Pet Policy';
    }

    /**
     * 获取宠物费用类型显示文本
     */
    public function getPetFeeTypeText(): string
    {
        $types = [
            'per_night' => '每晚',
            'per_stay' => '每次住宿',
            'one_time' => '一次性',
        ];
        return $types[$this->pet_fee_type] ?? $this->pet_fee_type ?? '每晚';
    }

    /**
     * 格式化宠物费用显示
     */
    public function getFormattedPetFee(): string
    {
        if (!$this->pet_fee) {
            return '免费';
        }
        return $this->pet_fee . '元/' . $this->getPetFeeTypeText();
    }

    /**
     * 获取允许的宠物类型显示文本
     */
    public function getPetTypesText(): string
    {
        if (!$this->pet_types_allowed || !is_array($this->pet_types_allowed)) {
            return '-';
        }
        
        $typeMap = [
            'dog' => '狗',
            'cat' => '猫',
            'bird' => '鸟',
            'fish' => '鱼',
            'rabbit' => '兔子',
            'hamster' => '仓鼠',
        ];
        
        $types = array_map(function($type) use ($typeMap) {
            return $typeMap[$type] ?? $type;
        }, $this->pet_types_allowed);
        
        return implode('、', $types);
    }

    /**
     * 创建默认政策
     */
    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId,
            'pets_allowed' => false,
            'max_pets_per_room' => 1,
            'pet_fee_type' => 'per_night',
            'vaccination_required' => true,
            'status' => 'active',
        ]);
    }
}
