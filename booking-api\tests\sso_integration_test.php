<?php

/**
 * SSO集成测试脚本
 * 测试预订系统与IAM系统的SSO集成
 */

require_once __DIR__ . '/../vendor/autoload.php';

class SSOIntegrationTest
{
    private $bookingApiUrl = 'http://localhost:9000';
    private $ssoApiUrl = 'http://localhost:8890';
    private $testUsername = 'test_user';
    private $testPassword = 'test_password';
    
    public function __construct()
    {
        echo "=== 预订系统SSO集成测试 ===\n\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        try {
            $this->testTraditionalLoginRedirect();
            $this->testSSOLogin();
            $this->testTokenValidation();
            $this->testUserInfoRetrieval();
            $this->testLogout();
            
            echo "\n✅ 所有测试通过！\n";
        } catch (Exception $e) {
            echo "\n❌ 测试失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试传统登录重定向
     */
    private function testTraditionalLoginRedirect()
    {
        echo "1. 测试传统登录重定向...\n";
        
        $response = $this->makeRequest('POST', '/api/v1/auth/login', [
            'username' => $this->testUsername,
            'password' => $this->testPassword
        ]);
        
        if ($response['code'] === 410) {
            echo "   ✅ 传统登录正确重定向到SSO\n";
        } else {
            throw new Exception("传统登录应该返回410状态码");
        }
    }
    
    /**
     * 测试SSO登录流程
     */
    private function testSSOLogin()
    {
        echo "2. 测试SSO登录流程...\n";
        
        // 模拟SSO登录获取token
        $ssoResponse = $this->makeRequest('POST', '/sso/login', [
            'username' => $this->testUsername,
            'password' => $this->testPassword
        ], $this->ssoApiUrl);
        
        if (isset($ssoResponse['data']['access_token'])) {
            $this->accessToken = $ssoResponse['data']['access_token'];
            echo "   ✅ SSO登录成功，获取到access token\n";
        } else {
            echo "   ⚠️  SSO登录测试跳过（需要真实的SSO服务）\n";
            // 使用模拟token进行后续测试
            $this->accessToken = $this->generateMockToken();
        }
    }
    
    /**
     * 测试token验证
     */
    private function testTokenValidation()
    {
        echo "3. 测试token验证...\n";
        
        if (!$this->accessToken) {
            echo "   ⚠️  跳过token验证测试（无有效token）\n";
            return;
        }
        
        $response = $this->makeRequest('GET', '/api/v1/auth/me', [], $this->bookingApiUrl, [
            'Authorization: Bearer ' . $this->accessToken
        ]);
        
        if ($response['code'] === 200 || $response['code'] === 401) {
            echo "   ✅ Token验证中间件正常工作\n";
        } else {
            throw new Exception("Token验证异常");
        }
    }
    
    /**
     * 测试用户信息获取
     */
    private function testUserInfoRetrieval()
    {
        echo "4. 测试用户信息获取...\n";
        
        $response = $this->makeRequest('GET', '/api/v1/auth/sso/status', [], $this->bookingApiUrl);
        
        if ($response['code'] === 401) {
            echo "   ✅ 未认证用户正确返回401\n";
        } else {
            echo "   ⚠️  用户信息接口响应: " . json_encode($response) . "\n";
        }
    }
    
    /**
     * 测试登出功能
     */
    private function testLogout()
    {
        echo "5. 测试登出功能...\n";
        
        $response = $this->makeRequest('POST', '/api/v1/auth/logout', [], $this->bookingApiUrl);
        
        if ($response['code'] === 200) {
            echo "   ✅ 登出接口正常响应\n";
        } else {
            throw new Exception("登出接口异常");
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeRequest($method, $path, $data = [], $baseUrl = null, $headers = [])
    {
        $url = ($baseUrl ?: $this->bookingApiUrl) . $path;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge([
            'Content-Type: application/json',
            'Accept: application/json'
        ], $headers));
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            return ['code' => 0, 'message' => 'Connection failed'];
        }
        
        $decoded = json_decode($response, true);
        return $decoded ?: ['code' => $httpCode, 'raw' => $response];
    }
    
    /**
     * 生成模拟token用于测试
     */
    private function generateMockToken()
    {
        // 这里应该生成一个有效的JWT token用于测试
        // 实际项目中可以使用测试专用的密钥
        return 'mock_jwt_token_for_testing';
    }
}

// 运行测试
$test = new SSOIntegrationTest();
$test->runAllTests();
