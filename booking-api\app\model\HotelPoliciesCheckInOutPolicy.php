<?php

namespace app\model;

use support\Model;

/**
 * 酒店入离政策模型
 */
class HotelPoliciesCheckInOutPolicy extends Model
{
    /**
     * 表名
     */
    protected $table = 'hotel_policies_check_in_out_policies';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'hotel_id',
        'check_in_time_from',
        'check_in_time_to',
        'check_out_time_from',
        'check_out_time_to',
        'early_check_in_available',
        'early_check_in_fee',
        'late_check_out_available',
        'late_check_out_fee',
        'express_check_in',
        'express_check_out',
        'minimum_age',
        'id_required',
        'credit_card_required',
        'special_instructions',
        'status',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'early_check_in_available' => 'boolean',
        'early_check_in_fee' => 'decimal:2',
        'late_check_out_available' => 'boolean',
        'late_check_out_fee' => 'decimal:2',
        'express_check_in' => 'boolean',
        'express_check_out' => 'boolean',
        'minimum_age' => 'integer',
        'id_required' => 'boolean',
        'credit_card_required' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(): array
    {
        return [
            'hotel_id' => 'required|integer|min:1',
            'check_in_time_from' => 'required|date_format:H:i:s',
            'check_in_time_to' => 'required|date_format:H:i:s',
            'check_out_time_from' => 'required|date_format:H:i:s',
            'check_out_time_to' => 'required|date_format:H:i:s',
            'early_check_in_available' => 'boolean',
            'early_check_in_fee' => 'nullable|numeric|min:0',
            'late_check_out_available' => 'boolean',
            'late_check_out_fee' => 'nullable|numeric|min:0',
            'express_check_in' => 'boolean',
            'express_check_out' => 'boolean',
            'minimum_age' => 'integer|min:0|max:100',
            'id_required' => 'boolean',
            'credit_card_required' => 'boolean',
            'special_instructions' => 'nullable|string|max:1000',
            'status' => 'in:active,inactive',
        ];
    }

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id', 'id');
    }

    /**
     * 作用域：启用的政策
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 获取政策类型
     */
    public function getPolicyType(): string
    {
        return 'check_in_out';
    }

    /**
     * 获取政策显示名称
     */
    public function getPolicyDisplayName(): string
    {
        return '入离政策';
    }

    /**
     * 获取政策英文名称
     */
    public function getPolicyEnglishName(): string
    {
        return 'Check-in/Check-out Policy';
    }

    /**
     * 格式化时间显示
     */
    public function getFormattedCheckInTime(): string
    {
        return date('H:i', strtotime($this->check_in_time_from)) . ' - ' . date('H:i', strtotime($this->check_in_time_to));
    }

    /**
     * 格式化时间显示
     */
    public function getFormattedCheckOutTime(): string
    {
        return date('H:i', strtotime($this->check_out_time_from)) . ' - ' . date('H:i', strtotime($this->check_out_time_to));
    }

    /**
     * 创建默认政策
     */
    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId,
            'check_in_time_from' => '14:00:00',
            'check_in_time_to' => '23:59:59',
            'check_out_time_from' => '00:00:00',
            'check_out_time_to' => '12:00:00',
            'early_check_in_available' => false,
            'late_check_out_available' => false,
            'express_check_in' => false,
            'express_check_out' => false,
            'minimum_age' => 18,
            'id_required' => true,
            'credit_card_required' => false,
            'status' => 'active',
        ]);
    }
}
