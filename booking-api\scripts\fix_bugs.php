<?php

/**
 * Bug修复脚本
 * 修复系统中发现的各种bug和问题
 */

require_once __DIR__ . '/../vendor/autoload.php';

class BugFixer
{
    private $fixes = [];
    private $errors = [];

    public function __construct()
    {
        echo "开始执行Bug修复脚本...\n";
    }

    /**
     * 执行所有修复
     */
    public function runAllFixes()
    {
        $this->fixValidationRules();
        $this->fixMissingMethods();
        $this->fixDatabaseConnections();
        $this->fixCacheIssues();
        $this->fixLogicErrors();
        $this->fixSecurityIssues();
        $this->generateReport();
    }

    /**
     * 修复验证规则问题
     */
    private function fixValidationRules()
    {
        echo "修复验证规则问题...\n";
        
        try {
            // 检查所有控制器中的验证规则
            $controllers = glob(__DIR__ . '/../app/controller/*.php');
            
            foreach ($controllers as $controller) {
                $content = file_get_contents($controller);
                
                // 查找使用了'numeric'验证规则的地方
                if (strpos($content, "'numeric'") !== false) {
                    $content = str_replace("'numeric'", "'decimal'", $content);
                    file_put_contents($controller, $content);
                    $this->fixes[] = "修复 " . basename($controller) . " 中的numeric验证规则";
                }
                
                // 查找缺少required规则的必填字段
                if (preg_match_all('/validateFormat\(\$data, \[(.*?)\]\)/s', $content, $matches)) {
                    foreach ($matches[0] as $match) {
                        if (strpos($match, 'hotel_id') !== false && strpos($match, 'required') === false) {
                            $this->fixes[] = "发现 " . basename($controller) . " 中可能缺少required验证";
                        }
                    }
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "修复验证规则时出错: " . $e->getMessage();
        }
    }

    /**
     * 修复缺失的方法
     */
    private function fixMissingMethods()
    {
        echo "检查缺失的方法...\n";
        
        try {
            // 检查BaseService中是否有必要的方法
            $baseServiceFile = __DIR__ . '/../app/service/BaseService.php';
            if (file_exists($baseServiceFile)) {
                $content = file_get_contents($baseServiceFile);
                
                // 检查是否有transaction方法
                if (strpos($content, 'function transaction') === false) {
                    $transactionMethod = '
    /**
     * 执行数据库事务
     */
    protected function transaction(callable $callback)
    {
        try {
            \support\Db::beginTransaction();
            $result = $callback();
            \support\Db::commit();
            return $result;
        } catch (\Exception $e) {
            \support\Db::rollback();
            throw $e;
        }
    }';
                    
                    // 在类的最后一个方法后添加
                    $content = str_replace('}\n}', $transactionMethod . "\n}", $content);
                    file_put_contents($baseServiceFile, $content);
                    $this->fixes[] = "添加BaseService::transaction方法";
                }
                
                // 检查是否有logError方法
                if (strpos($content, 'function logError') === false) {
                    $logErrorMethod = '
    /**
     * 记录错误日志
     */
    protected function logError(string $message, array $context = [])
    {
        \support\Log::error($message, $context);
    }';
                    
                    $content = str_replace('}\n}', $logErrorMethod . "\n}", $content);
                    file_put_contents($baseServiceFile, $content);
                    $this->fixes[] = "添加BaseService::logError方法";
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "修复缺失方法时出错: " . $e->getMessage();
        }
    }

    /**
     * 修复数据库连接问题
     */
    private function fixDatabaseConnections()
    {
        echo "检查数据库连接配置...\n";
        
        try {
            $configFile = __DIR__ . '/../config/database.php';
            if (file_exists($configFile)) {
                $config = include $configFile;
                
                // 检查连接池配置
                if (!isset($config['default']['options']['pool'])) {
                    $this->fixes[] = "数据库配置缺少连接池设置";
                }
                
                // 检查字符集配置
                if (!isset($config['default']['charset']) || $config['default']['charset'] !== 'utf8mb4') {
                    $this->fixes[] = "数据库字符集应设置为utf8mb4";
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "检查数据库配置时出错: " . $e->getMessage();
        }
    }

    /**
     * 修复缓存问题
     */
    private function fixCacheIssues()
    {
        echo "检查缓存配置...\n";
        
        try {
            // 检查缓存服务是否正确实现
            $cacheServiceFile = __DIR__ . '/../app/service/CacheService.php';
            if (file_exists($cacheServiceFile)) {
                $content = file_get_contents($cacheServiceFile);
                
                // 检查是否有Redis连接检查
                if (strpos($content, 'Redis') === false) {
                    $this->fixes[] = "CacheService需要添加Redis连接检查";
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "检查缓存配置时出错: " . $e->getMessage();
        }
    }

    /**
     * 修复逻辑错误
     */
    private function fixLogicErrors()
    {
        echo "检查业务逻辑错误...\n";
        
        try {
            // 检查日期比较逻辑
            $files = glob(__DIR__ . '/../app/controller/*.php');
            
            foreach ($files as $file) {
                $content = file_get_contents($file);
                
                // 检查日期比较是否使用了正确的格式
                if (preg_match('/\$.*date.*>=.*\$.*date/', $content)) {
                    $this->fixes[] = basename($file) . " 中发现日期比较，请确认格式正确";
                }
                
                // 检查是否有除零错误的可能
                if (strpos($content, '/ $') !== false || strpos($content, '/$') !== false) {
                    $this->fixes[] = basename($file) . " 中可能存在除零错误风险";
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "检查业务逻辑时出错: " . $e->getMessage();
        }
    }

    /**
     * 修复安全问题
     */
    private function fixSecurityIssues()
    {
        echo "检查安全问题...\n";
        
        try {
            // 检查SQL注入风险
            $files = glob(__DIR__ . '/../app/**/*.php', GLOB_BRACE);
            
            foreach ($files as $file) {
                $content = file_get_contents($file);
                
                // 检查是否有直接拼接SQL的情况
                if (preg_match('/\$.*\.\s*["\']SELECT|INSERT|UPDATE|DELETE/', $content)) {
                    $this->fixes[] = basename($file) . " 中可能存在SQL注入风险";
                }
                
                // 检查是否有未过滤的用户输入
                if (strpos($content, '$_GET') !== false || strpos($content, '$_POST') !== false) {
                    $this->fixes[] = basename($file) . " 中直接使用了$_GET或$_POST，建议使用Request对象";
                }
            }
            
        } catch (Exception $e) {
            $this->errors[] = "检查安全问题时出错: " . $e->getMessage();
        }
    }

    /**
     * 生成修复报告
     */
    private function generateReport()
    {
        echo "\n=== Bug修复报告 ===\n";
        echo "修复时间: " . date('Y-m-d H:i:s') . "\n";
        echo "总计修复: " . count($this->fixes) . " 个问题\n";
        echo "发现错误: " . count($this->errors) . " 个\n\n";
        
        if (!empty($this->fixes)) {
            echo "已修复的问题:\n";
            foreach ($this->fixes as $i => $fix) {
                echo ($i + 1) . ". " . $fix . "\n";
            }
            echo "\n";
        }
        
        if (!empty($this->errors)) {
            echo "修复过程中的错误:\n";
            foreach ($this->errors as $i => $error) {
                echo ($i + 1) . ". " . $error . "\n";
            }
            echo "\n";
        }
        
        // 保存报告到文件
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'fixes' => $this->fixes,
            'errors' => $this->errors
        ];
        
        file_put_contents(__DIR__ . '/../storage/logs/bug_fix_report.json', json_encode($report, JSON_PRETTY_PRINT));
        echo "详细报告已保存到: storage/logs/bug_fix_report.json\n";
    }
}

// 执行修复
$fixer = new BugFixer();
$fixer->runAllFixes();

echo "Bug修复脚本执行完成!\n";
