<?php

namespace app\controller;

use app\model\HotelBrand;
use app\model\Hotel;
use support\Request;
use support\Response;
use support\Db;

/**
 * 酒店品牌管理控制器
 */
class HotelBrandController extends BaseController
{
    /**
     * 获取酒店品牌列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $page = (int)$request->get('page', 1);
            $perPage = (int)$request->get('per_page', 10);
            $name = $request->get('name');
            $code = $request->get('code');
            $brandType = $request->get('brand_type');
            $status = $request->get('status');

            $query = HotelBrand::query();

            // 搜索条件
            if ($name) {
                $query->where('name', 'like', "%{$name}%");
            }
            if ($code) {
                $query->where('code', 'like', "%{$code}%");
            }
            if ($brandType) {
                $query->where('brand_type', $brandType);
            }
            if ($status) {
                $query->where('status', $status);
            }

            // 分页查询
            $total = $query->count();
            $brands = $query->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            $items = $brands->map(function ($brand) {
                return [
                    'id' => $brand->id,
                    'name' => $brand->name,
                    'code' => $brand->code,
                    'name_en' => $brand->name_en,
                    'brand_type' => $brand->brand_type,
                    'description' => $brand->description,
                    'logo_url' => $brand->logo_url,
                    'website' => $brand->website,
                    'contact_email' => $brand->contact_email,
                    'contact_phone' => $brand->contact_phone,
                    'status' => $brand->status,
                    'created_at' => $brand->created_at,
                    'updated_at' => $brand->updated_at,
                    'hotel_count' => Hotel::where('brand_id', $brand->id)->count(),
                    'group' => $brand->group->first([
                            "id",
                            "name",
                            "code",
                            "name_en",
                            "description",
                            "website",
                            "address",
                            "contact_person",
                            "contact_phone",
                            "contact_email",
                            "established_year",
                            "logo_url",
                            "status",
                        ]),
                    'group_id' => $brand->group_id,
                ];
            });

            return $this->success([
                'items' => $items,
                'pagination' => [
                    'total' => $total,
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'last_page' => ceil($total / $perPage),
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店品牌列表');
        }
    }

    /**
     * 获取酒店品牌详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request, $id)
    {

        try {
            $brandId = (int)$id;
            
            if ($brandId <= 0) {
                return $this->error('酒店品牌ID无效');
            }

            $brand = HotelBrand::with(['group' => function ($query) {
                return $query->first([
                    "id",
                    "name",
                    "code",
                    "name_en",
                    "description",
                    "website",
                    "address",
                    "contact_person",
                    "contact_phone",
                    "contact_email",
                    "established_year",
                    "logo_url",
                    "status",
                ]);
            }])->find($brandId);

            if (!$brand) {
                return $this->error('酒店品牌不存在', 404);
            }

            return $this->success($brand->toArray());

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店品牌详情');
        }
    }

    /**
     * 创建酒店品牌
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['name', 'code', 'brand_type']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'contact_email' => 'email',
                'website' => 'url'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查品牌名称是否已存在
            if (HotelBrand::where('name', $data['name'])->exists()) {
                return $this->error('品牌名称已存在');
            }

            // 检查品牌代码是否已存在
            if (HotelBrand::where('code', $data['code'])->exists()) {
                return $this->error('品牌代码已存在');
            }

            // 创建酒店品牌
            $brandData = [
                'name' => $data['name'],
                'code' => $data['code'],
                'name_en' => $data['name_en'] ?? null,
                'brand_type' => $data['brand_type'],
                'description' => $data['description'] ?? null,
                'logo_url' => $data['logo_url'] ?? null,
                'website' => $data['website'] ?? null,
                'contact_email' => $data['contact_email'] ?? null,
                'contact_phone' => $data['contact_phone'] ?? null,
                'status' => $data['status'] ?? HotelBrand::STATUS_ACTIVE,
            ];

            $brand = HotelBrand::create($brandData);

            return $this->success([
                'id' => $brand->id,
                'name' => $brand->name,
                'code' => $brand->code,
                'name_en' => $brand->name_en,
                'brand_type' => $brand->brand_type,
                'status' => $brand->status,
            ], '酒店品牌创建成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '创建酒店品牌');
        }
    }

    /**
     * 更新酒店品牌
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $brandId = (int)$id;
            
            if ($brandId <= 0) {
                return $this->error('酒店品牌ID无效');
            }

            $brand = HotelBrand::find($brandId);
            
            if (!$brand) {
                return $this->error('酒店品牌不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'contact_email' => 'email',
                'website' => 'url'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查品牌名称是否已存在（排除当前品牌）
            if (!empty($data['name']) && 
                HotelBrand::where('name', $data['name'])->where('id', '!=', $brandId)->exists()) {
                return $this->error('品牌名称已存在');
            }

            // 检查品牌代码是否已存在（排除当前品牌）
            if (!empty($data['code']) && 
                HotelBrand::where('code', $data['code'])->where('id', '!=', $brandId)->exists()) {
                return $this->error('品牌代码已存在');
            }

            // 更新酒店品牌信息
            $updateData = [];

            // 字段映射：前端字段名 => 后端字段名
            $fieldMapping = [
                'name' => 'name',
                'code' => 'code',
                'name_en' => 'name_en',
                'description' => 'description',
                'group_id' => 'group_id',
                'target_market' => 'target_market',
                'positioning' => 'brand_positioning',
                'brand_type' => 'price_range', // 映射到价格范围字段
                'logo_url' => 'logo_url',
                'website' => 'website',
                'contact_email' => 'contact_email',
                'contact_phone' => 'contact_phone',
                'status' => 'status'
            ];

            foreach ($fieldMapping as $frontendField => $backendField) {
                if (isset($data[$frontendField])) {
                    $updateData[$backendField] = $data[$frontendField];
                }
            }

            if ($updateData) {
                $brand->update($updateData);
            }

            // 重新加载品牌数据以获取最新信息
            $brand->refresh();

            return $this->success([
                'id' => $brand->id,
                'name' => $brand->name,
                'code' => $brand->code,
                'name_en' => $brand->name_en,
                'description' => $brand->description,
                'group_id' => $brand->group_id,
                'brand_type' => $brand->price_range,
                'price_range' => $brand->price_range,
                'target_market' => $brand->target_market,
                'positioning' => $brand->brand_positioning,
                'brand_positioning' => $brand->brand_positioning,
                'status' => $brand->status,
                'logo_url' => $brand->logo_url,
                'website' => $brand->website,
                'contact_email' => $brand->contact_email,
                'contact_phone' => $brand->contact_phone,
                'updated_at' => $brand->updated_at,
            ], '酒店品牌更新成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店品牌');
        }
    }

    /**
     * 删除酒店品牌
     *
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $brandId = (int)$id;
            
            if ($brandId <= 0) {
                return $this->error('酒店品牌ID无效');
            }

            $brand = HotelBrand::find($brandId);
            
            if (!$brand) {
                return $this->error('酒店品牌不存在', 404);
            }

            // 检查是否有酒店属于此品牌
            $hotelCount = Hotel::where('brand_id', $brandId)->count();
            if ($hotelCount > 0) {
                return $this->error('该品牌下还有酒店，无法删除');
            }

            $brand->delete();

            return $this->success(null, '酒店品牌删除成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '删除酒店品牌');
        }
    }

    /**
     * 获取所有酒店品牌（用于下拉选择）
     *
     * @param Request $request
     * @return Response
     */
    public function all(Request $request)
    {
        try {
            $brands = HotelBrand::where('status', HotelBrand::STATUS_ACTIVE)
                ->orderBy('name')
                ->get()
                ->map(function ($brand) {
                    return [
                        'id' => $brand->id,
                        'name' => $brand->name,
                        'code' => $brand->code,
                        'name_en' => $brand->name_en,
                        'brand_type' => $brand->brand_type,
                    ];
                });

            return $this->success($brands);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取所有酒店品牌');
        }
    }
}
