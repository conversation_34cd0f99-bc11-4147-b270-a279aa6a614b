/*
 Navicat Premium Dump SQL

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : localhost:3306
 Source Schema         : booking_system

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 15/08/2025 15:33:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for audit_logs
-- ----------------------------
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '表名',
  `record_id` bigint(20) UNSIGNED NOT NULL COMMENT '记录ID',
  `action` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：create-创建，update-更新，delete-删除',
  `old_values` json NULL COMMENT '旧值',
  `new_values` json NULL COMMENT '新值',
  `changed_fields` json NULL COMMENT '变更字段',
  `user_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '操作用户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `request_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求ID',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话ID',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作原因',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_table_record`(`table_name`, `record_id`) USING BTREE,
  INDEX `idx_action`(`action`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '操作审计日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bed_type_statistics
-- ----------------------------
DROP TABLE IF EXISTS `bed_type_statistics`;
CREATE TABLE `bed_type_statistics`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bed_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '床型类型ID',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `room_type_count` int(11) NOT NULL DEFAULT 0 COMMENT '关联房型数量',
  `hotel_count` int(11) NOT NULL DEFAULT 0 COMMENT '关联酒店数量',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_bed_type_id`(`bed_type_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '床型类型统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bed_type_translations
-- ----------------------------
DROP TABLE IF EXISTS `bed_type_translations`;
CREATE TABLE `bed_type_translations`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `bed_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '床型类型ID',
  `locale` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '语言代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_bed_type_locale`(`bed_type_id`, `locale`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '床型类型翻译表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for bed_types
-- ----------------------------
DROP TABLE IF EXISTS `bed_types`;
CREATE TABLE `bed_types`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '床型代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '描述',
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '英文描述',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'standard' COMMENT '分类：standard-标准,sofa-沙发,tatami-榻榻米,other-其他',
  `size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '尺寸：single-单人,double-双人,queen-女王,king-特大,twin-双床',
  `width` int(11) NULL DEFAULT NULL COMMENT '宽度(厘米)',
  `length` int(11) NULL DEFAULT NULL COMMENT '长度(厘米)',
  `max_occupancy` int(11) NOT NULL DEFAULT 1 COMMENT '最大入住人数',
  `is_standard` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否标准床型',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '床型类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for booking_logs
-- ----------------------------
DROP TABLE IF EXISTS `booking_logs`;
CREATE TABLE `booking_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `booking_id` bigint(20) UNSIGNED NOT NULL COMMENT '订单ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：create-创建，update-更新，confirm-确认，cancel-取消，checkin-入住，checkout-退房',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '操作描述',
  `user_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '操作用户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `request_data` json NULL COMMENT '请求数据',
  `response_data` json NULL COMMENT '响应数据',
  `execution_time` decimal(8, 3) NULL DEFAULT NULL COMMENT '执行时间(秒)',
  `memory_usage` int(11) NULL DEFAULT NULL COMMENT '内存使用量(字节)',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `stack_trace` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '堆栈跟踪',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_booking_id`(`booking_id`) USING BTREE,
  INDEX `idx_action`(`action`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for bookings
-- ----------------------------
DROP TABLE IF EXISTS `bookings`;
CREATE TABLE `bookings`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `booking_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `rate_plan_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '价格计划ID',
  `user_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '用户ID',
  `check_in_date` date NOT NULL COMMENT '入住日期',
  `check_out_date` date NOT NULL COMMENT '退房日期',
  `nights` int(11) NOT NULL COMMENT '入住晚数',
  `rooms` int(11) NOT NULL DEFAULT 1 COMMENT '房间数量',
  `adults` int(11) NOT NULL DEFAULT 1 COMMENT '成人数量',
  `children` int(11) NOT NULL DEFAULT 0 COMMENT '儿童数量',
  `guest_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客人姓名',
  `guest_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客人电话',
  `guest_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客人邮箱',
  `guest_id_card` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客人身份证',
  `guest_nationality` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客人国籍',
  `guest_details` json NULL COMMENT '客人详细信息',
  `room_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '房间价格',
  `service_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '服务费',
  `tax_fee` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '税费',
  `discount_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '折扣金额',
  `total_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `paid_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '已付金额',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `special_requests` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '特殊要求',
  `arrival_time` time NULL DEFAULT NULL COMMENT '预计到店时间',
  `source_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'direct' COMMENT '预订来源渠道',
  `source_reference` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '来源参考号',
  `booking_platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'web' COMMENT '预订平台：web-网站，mobile-手机，app-应用，phone-电话，walk_in-现场',
  `status` enum('pending','confirmed','checked_in','checked_out','cancelled','no_show') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_status` enum('unpaid','partial','paid','refunded','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'unpaid' COMMENT '支付状态',
  `confirmation_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '确认号',
  `confirmed_at` timestamp NULL DEFAULT NULL COMMENT '确认时间',
  `confirmed_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '确认人ID',
  `cancelled_at` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `cancelled_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '取消人ID',
  `cancellation_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '取消原因',
  `cancellation_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '取消费用',
  `checked_in_at` timestamp NULL DEFAULT NULL COMMENT '入住时间',
  `checked_out_at` timestamp NULL DEFAULT NULL COMMENT '退房时间',
  `actual_room_numbers` json NULL COMMENT '实际房间号',
  `early_checkin_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '早到费用',
  `late_checkout_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '延迟退房费用',
  `rating` tinyint(4) NULL DEFAULT NULL COMMENT '评分（1-5）',
  `review` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价内容',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '评价时间',
  `booking_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'individual' COMMENT '预订类型：individual-个人，group-团队，corporate-企业',
  `booking_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'direct' COMMENT '预订来源：direct-直销，ota-OTA，gds-GDS，agent-代理',
  `booking_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'direct' COMMENT '预订渠道',
  `external_booking_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '外部预订ID',
  `ota_booking_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'OTA预订ID',
  `agent_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '代理商ID',
  `corporate_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '企业客户ID',
  `group_booking_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '团队预订ID',
  `promotion_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '促销代码',
  `loyalty_program` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '忠诚度计划',
  `loyalty_points_earned` int(11) NULL DEFAULT 0 COMMENT '获得积分',
  `loyalty_points_used` int(11) NULL DEFAULT 0 COMMENT '使用积分',
  `terms_accepted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否同意条款',
  `privacy_accepted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否同意隐私政策',
  `marketing_consent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否同意营销',
  `internal_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内部备注',
  `guest_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '客人备注',
  `staff_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '员工备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_booking_no`(`booking_no`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_rate_plan_id`(`rate_plan_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_guest_phone`(`guest_phone`) USING BTREE,
  INDEX `idx_guest_email`(`guest_email`) USING BTREE,
  INDEX `idx_check_in_date`(`check_in_date`) USING BTREE,
  INDEX `idx_check_out_date`(`check_out_date`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_payment_status`(`payment_status`) USING BTREE,
  INDEX `idx_source_channel`(`source_channel`) USING BTREE,
  INDEX `idx_booking_platform`(`booking_platform`) USING BTREE,
  INDEX `idx_confirmation_no`(`confirmation_no`) USING BTREE,
  INDEX `idx_external_booking_id`(`external_booking_id`) USING BTREE,
  INDEX `idx_ota_booking_id`(`ota_booking_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for breakfast_types
-- ----------------------------
DROP TABLE IF EXISTS `breakfast_types`;
CREATE TABLE `breakfast_types`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '早餐类型代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '早餐类型名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '早餐描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '早餐类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cancellation_policies
-- ----------------------------
DROP TABLE IF EXISTS `cancellation_policies`;
CREATE TABLE `cancellation_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '政策代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '政策名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '政策描述',
  `policy_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'flexible' COMMENT '政策类型：flexible-灵活，moderate-适中，strict-严格',
  `free_cancellation_hours` int(11) NOT NULL DEFAULT 24 COMMENT '免费取消小时数',
  `cancellation_fee_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'percentage' COMMENT '取消费用类型：fixed-固定金额，percentage-百分比，nights-房晚数',
  `cancellation_fee_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '取消费用金额',
  `no_show_fee_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'percentage' COMMENT '未到店费用类型',
  `no_show_fee_amount` decimal(10, 2) NOT NULL DEFAULT 100.00 COMMENT '未到店费用金额',
  `modification_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否允许修改',
  `modification_fee` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '修改费用',
  `terms_conditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '条款和条件',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_policy_type`(`policy_type`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '取消政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for facility_categories
-- ----------------------------
DROP TABLE IF EXISTS `facility_categories`;
CREATE TABLE `facility_categories`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '父分类ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '设施分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for facility_templates
-- ----------------------------
DROP TABLE IF EXISTS `facility_templates`;
CREATE TABLE `facility_templates`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name_zh` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '中文名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设施分类：basic-基础设施，dining-餐饮设施，entertainment-娱乐设施，business-商务设施，sports-运动设施，health-健康美容，fitness-健身设施，recreation-休闲设施，children-儿童设施，transport-交通服务，service-服务设施，safety-安全设施，accessibility-无障碍设施，shopping-购物设施，policy-政策规定，other-其他设施',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '设施描述',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category`(`category`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '设施设备模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hotel_brands
-- ----------------------------
DROP TABLE IF EXISTS `hotel_brands`;
CREATE TABLE `hotel_brands`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '所属集团ID',
  `parent_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '父品牌ID，支持品牌层级结构',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '品牌代码，全局唯一',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '品牌名称',
  `name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌简称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '品牌描述',
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '英文描述',
  `brand_positioning` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌定位：luxury-奢华，upscale-高档，midscale-中档，economy-经济，budget-预算',
  `target_market` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '目标市场：business-商务，leisure-休闲，family-家庭，boutique-精品',
  `price_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '价格区间：high-高价，medium-中价，low-低价',
  `star_rating` decimal(2, 1) NULL DEFAULT NULL COMMENT '星级标准（1.0-5.0）',
  `brand_features` json NULL COMMENT '品牌特色（JSON数组）',
  `service_standards` json NULL COMMENT '服务标准（JSON数组）',
  `amenities` json NULL COMMENT '标准设施（JSON数组）',
  `logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Logo图片URL',
  `brand_color_primary` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌主色调',
  `brand_color_secondary` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌辅助色',
  `brand_font` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌字体',
  `brand_slogan` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌口号',
  `brand_story` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '品牌故事',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '官方网站',
  `origin_country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌发源地',
  `operating_regions` json NULL COMMENT '运营区域（JSON数组）',
  `established_year` int(11) NULL DEFAULT NULL COMMENT '品牌创立年份',
  `business_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'franchise' COMMENT '经营模式：owned-自营，managed-管理，franchise-特许经营，mixed-混合',
  `hotel_count` int(11) NOT NULL DEFAULT 0 COMMENT '旗下酒店数量',
  `room_count` int(11) NOT NULL DEFAULT 0 COMMENT '总房间数量',
  `market_share` decimal(5, 2) NULL DEFAULT NULL COMMENT '市场份额（%）',
  `growth_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '增长率（%）',
  `customer_satisfaction` decimal(3, 2) NULL DEFAULT NULL COMMENT '客户满意度（1.00-5.00）',
  `brand_value` decimal(15, 2) NULL DEFAULT NULL COMMENT '品牌价值（万元）',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '币种',
  `certifications` json NULL COMMENT '认证信息（JSON数组）',
  `awards` json NULL COMMENT '获奖信息（JSON数组）',
  `quality_standards` json NULL COMMENT '质量标准（JSON数组）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-活跃，inactive-停用，pending-待审核，suspended-暂停',
  `is_public` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否公开显示',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已认证',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为推荐品牌',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '认证时间',
  `verified_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '认证人ID',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '品牌层级，1为顶级',
  `path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '层级路径，如：1,2,3',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_brand_positioning`(`brand_positioning`) USING BTREE,
  INDEX `idx_target_market`(`target_market`) USING BTREE,
  INDEX `idx_price_range`(`price_range`) USING BTREE,
  INDEX `idx_star_rating`(`star_rating`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_business_model`(`business_model`) USING BTREE,
  INDEX `idx_hotel_count`(`hotel_count`) USING BTREE,
  INDEX `idx_is_public`(`is_public`) USING BTREE,
  INDEX `idx_is_verified`(`is_verified`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店品牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_facilities
-- ----------------------------
DROP TABLE IF EXISTS `hotel_facilities`;
CREATE TABLE `hotel_facilities`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `facility_id` bigint(20) UNSIGNED NOT NULL COMMENT '设施ID',
  `is_available` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可用',
  `custom_charge_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '自定义收费金额',
  `custom_operating_hours` json NULL COMMENT '自定义营业时间',
  `custom_capacity` int(11) NULL DEFAULT NULL COMMENT '自定义容量',
  `location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '位置描述',
  `booking_required` tinyint(1) NULL DEFAULT NULL COMMENT '是否需要预订（覆盖标准设置）',
  `advance_booking_hours` int(11) NULL DEFAULT NULL COMMENT '提前预订小时数（覆盖标准设置）',
  `special_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '特殊说明',
  `images` json NULL COMMENT '设施图片',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_facility`(`hotel_id`, `facility_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_facility_id`(`facility_id`) USING BTREE,
  INDEX `idx_is_available`(`is_available`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店设施关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_groups
-- ----------------------------
DROP TABLE IF EXISTS `hotel_groups`;
CREATE TABLE `hotel_groups`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '父集团ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '集团代码',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '集团名称',
  `name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `short_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '集团简称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '集团描述',
  `description_en` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '英文描述',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '官方网站',
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '国家',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '详细地址',
  `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `established_year` int(11) NULL DEFAULT NULL COMMENT '成立年份',
  `business_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型',
  `scale_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '规模等级',
  `hotel_count` int(11) NOT NULL DEFAULT 0 COMMENT '旗下酒店数量',
  `room_count` int(11) NOT NULL DEFAULT 0 COMMENT '总房间数量',
  `logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Logo图片URL',
  `brand_color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌色彩',
  `brand_slogan` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '品牌口号',
  `annual_revenue` decimal(15, 2) NULL DEFAULT NULL COMMENT '年营收（万元）',
  `market_value` decimal(15, 2) NULL DEFAULT NULL COMMENT '市值（万元）',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '币种',
  `business_license` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业执照号',
  `tax_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税务登记号',
  `legal_representative` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '法定代表人',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-活跃，inactive-停用，pending-待审核，suspended-暂停',
  `is_public` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否公开显示',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已认证',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '认证时间',
  `verified_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '认证人ID',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序权重',
  `level` int(11) NOT NULL DEFAULT 1 COMMENT '集团层级，1为顶级',
  `path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '层级路径，如：1,2,3',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_public`(`is_public`) USING BTREE,
  INDEX `idx_is_verified`(`is_verified`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店集团表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_host_info
-- ----------------------------
DROP TABLE IF EXISTS `hotel_host_info`;
CREATE TABLE `hotel_host_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `info_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '房东信息ID（关联其他系统）',
  `host_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '房东姓名',
  `host_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '房东介绍',
  `host_photo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '房东头像',
  `host_gender` enum('Male','Female','Other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '性别',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `province_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '省份ID',
  `city_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '城市ID',
  `auth_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '认证状态',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `is_accept_dishonesty` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否接受失信记录',
  `career` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '职业',
  `hobby` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '爱好',
  `background_pic` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景图片',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_info_id`(`info_id`) USING BTREE,
  INDEX `idx_auth_status`(`auth_status`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店房东信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hotel_images
-- ----------------------------
DROP TABLE IF EXISTS `hotel_images`;
CREATE TABLE `hotel_images`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `image_category_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '图片分类ID',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `image_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片分类',
  `image_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片标题',
  `image_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '图片描述',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_category`(`image_category`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店图片表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hotel_licenses
-- ----------------------------
DROP TABLE IF EXISTS `hotel_licenses`;
CREATE TABLE `hotel_licenses`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `license_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资质类型：business-营业执照，tax-税务登记，fire-消防许可，health-卫生许可，tourism-旅游经营许可',
  `license_data` json NOT NULL COMMENT '资质数据（JSON格式）',
  `license_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '证件号码',
  `issue_date` date NULL DEFAULT NULL COMMENT '发证日期',
  `expiration_date` date NULL DEFAULT NULL COMMENT '过期日期',
  `issuing_authority` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '发证机关',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-有效，expired-过期，revoked-吊销，inactive-停用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_license`(`hotel_id`, `license_type`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_license_type`(`license_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_expiration_date`(`expiration_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店资质证照表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hotel_payment_methods
-- ----------------------------
DROP TABLE IF EXISTS `hotel_payment_methods`;
CREATE TABLE `hotel_payment_methods`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `payment_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '标准支付方式ID',
  `payment_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式名称',
  `payment_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付方式代码',
  `payment_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付类型',
  `bookable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可预订时使用',
  `cvc_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要CVC验证',
  `payable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可支付',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-禁用',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '描述',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认支付方式',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_payment_id`(`payment_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_payment_type`(`payment_type`) USING BTREE,
  INDEX `idx_is_default`(`is_default`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店支付方式表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for hotel_policies_check_in_method_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_check_in_method_policies`;
CREATE TABLE `hotel_policies_check_in_method_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `front_desk_checkin` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持前台入住',
  `self_checkin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持自助入住',
  `mobile_checkin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持手机入住',
  `keyless_entry` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持无钥匙入住',
  `contactless_checkin` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持无接触入住',
  `online_checkin_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持在线入住',
  `checkin_instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '入住说明',
  `key_collection_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '钥匙领取方式',
  `access_code_provided` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供门禁密码',
  `staff_assistance_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '工作人员协助时间',
  `emergency_contact` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '紧急联系方式',
  `checkin_method_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '入住方式备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_checkin_method`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店入住方式政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_check_in_out_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_check_in_out_policies`;
CREATE TABLE `hotel_policies_check_in_out_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `check_in_time_from` time NOT NULL DEFAULT '14:00:00' COMMENT '入住时间开始',
  `check_in_time_to` time NOT NULL DEFAULT '23:59:59' COMMENT '入住时间结束',
  `check_out_time_from` time NOT NULL DEFAULT '00:00:00' COMMENT '退房时间开始',
  `check_out_time_to` time NOT NULL DEFAULT '12:00:00' COMMENT '退房时间结束',
  `early_check_in_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持提前入住',
  `early_check_in_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '提前入住费用',
  `late_check_out_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持延迟退房',
  `late_check_out_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '延迟退房费用',
  `express_check_in` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持快速入住',
  `express_check_out` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持快速退房',
  `minimum_age` int(11) NOT NULL DEFAULT 18 COMMENT '最低入住年龄',
  `id_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要身份证件',
  `credit_card_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要信用卡',
  `special_instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '特殊说明',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_check_in_out`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店入离政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_children_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_children_policies`;
CREATE TABLE `hotel_policies_children_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `children_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否允许儿童入住',
  `free_child_age_limit` int(11) NULL DEFAULT 12 COMMENT '免费儿童年龄限制',
  `max_children_per_room` int(11) NULL DEFAULT NULL COMMENT '每间房最多儿童数',
  `child_bed_policy` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '儿童床位政策',
  `extra_bed_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供加床',
  `extra_bed_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '加床费用',
  `crib_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供婴儿床',
  `crib_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '婴儿床费用',
  `child_meal_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '儿童餐食政策',
  `babysitting_service` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供托儿服务',
  `children_activities` json NULL COMMENT '儿童活动',
  `children_facilities` json NULL COMMENT '儿童设施',
  `supervision_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要成人监护',
  `children_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '儿童政策备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_children`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店儿童政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_deposit_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_deposit_policies`;
CREATE TABLE `hotel_policies_deposit_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `deposit_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要押金',
  `deposit_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'cash' COMMENT '押金类型：cash-现金，credit_card-信用卡，both-两者皆可',
  `deposit_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '押金金额',
  `deposit_amount_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'fixed' COMMENT '押金金额类型：fixed-固定，per_night-每晚，percentage-百分比',
  `deposit_currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '押金币种',
  `deposit_collection_time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'check_in' COMMENT '押金收取时间',
  `deposit_refund_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '押金退还时间',
  `deposit_refund_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '押金退还方式',
  `damage_assessment_policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '损坏评估政策',
  `incidental_charges_policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '杂费政策',
  `authorization_hold` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否预授权冻结',
  `hold_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '冻结金额',
  `deposit_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '押金政策备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_deposit`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店押金政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_guest_restriction_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_guest_restriction_policies`;
CREATE TABLE `hotel_policies_guest_restriction_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `max_guests_per_room` int(11) NULL DEFAULT NULL COMMENT '每间房最多客人数',
  `additional_guest_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '额外客人费用',
  `visitor_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '访客政策',
  `visitor_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '访客时间',
  `overnight_visitors` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许过夜访客',
  `party_events_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许聚会活动',
  `noise_restrictions` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '噪音限制',
  `quiet_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '安静时间',
  `smoking_policy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'non_smoking' COMMENT '吸烟政策',
  `alcohol_policy` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '酒精政策',
  `age_restrictions` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '年龄限制',
  `group_booking_restrictions` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '团体预订限制',
  `restriction_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '限制备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_guest_restriction`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店住客限制政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_in_hotel_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_in_hotel_policies`;
CREATE TABLE `hotel_policies_in_hotel_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `housekeeping_service` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否提供客房清洁',
  `housekeeping_frequency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'daily' COMMENT '清洁频率',
  `towel_change_policy` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '毛巾更换政策',
  `linen_change_policy` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '床单更换政策',
  `do_not_disturb_respected` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否尊重请勿打扰',
  `maintenance_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '维护时间',
  `facility_access_hours` json NULL COMMENT '设施开放时间',
  `common_area_rules` json NULL COMMENT '公共区域规则',
  `wifi_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'WiFi政策',
  `laundry_service` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供洗衣服务',
  `concierge_service` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供礼宾服务',
  `lost_and_found_policy` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '失物招领政策',
  `in_hotel_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '在店政策备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_in_hotel`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店在店政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_meal_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_meal_policies`;
CREATE TABLE `hotel_policies_meal_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `breakfast_included` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否包含早餐',
  `breakfast_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '早餐类型',
  `breakfast_time_from` time NULL DEFAULT NULL COMMENT '早餐开始时间',
  `breakfast_time_to` time NULL DEFAULT NULL COMMENT '早餐结束时间',
  `breakfast_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '早餐地点',
  `lunch_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供午餐',
  `lunch_time_from` time NULL DEFAULT NULL COMMENT '午餐开始时间',
  `lunch_time_to` time NULL DEFAULT NULL COMMENT '午餐结束时间',
  `dinner_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供晚餐',
  `dinner_time_from` time NULL DEFAULT NULL COMMENT '晚餐开始时间',
  `dinner_time_to` time NULL DEFAULT NULL COMMENT '晚餐结束时间',
  `room_service_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供客房送餐',
  `room_service_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客房送餐时间',
  `dietary_restrictions_supported` json NULL COMMENT '支持的饮食限制',
  `special_meal_requests` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否接受特殊餐食要求',
  `meal_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '餐食备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_meal`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店餐食政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_parking_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_parking_policies`;
CREATE TABLE `hotel_policies_parking_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `parking_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供停车',
  `parking_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '停车类型：free-免费，paid-收费，valet-代客泊车',
  `parking_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '停车费用',
  `parking_fee_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'per_night' COMMENT '费用单位：per_hour-每小时，per_night-每晚，per_day-每天',
  `parking_spaces` int(11) NULL DEFAULT NULL COMMENT '停车位数量',
  `parking_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '停车位置',
  `valet_service` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供代客泊车',
  `valet_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '代客泊车费用',
  `electric_charging` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否提供电动车充电',
  `charging_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '充电费用',
  `reservation_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要预约',
  `height_restriction` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '高度限制',
  `parking_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '停车备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_parking`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店停车场政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotel_policies_pet_policies
-- ----------------------------
DROP TABLE IF EXISTS `hotel_policies_pet_policies`;
CREATE TABLE `hotel_policies_pet_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `pets_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许宠物',
  `pet_types_allowed` json NULL COMMENT '允许的宠物类型',
  `max_pets_per_room` int(11) NULL DEFAULT 1 COMMENT '每间房最多宠物数',
  `pet_weight_limit` decimal(5, 2) NULL DEFAULT NULL COMMENT '宠物重量限制(kg)',
  `pet_fee_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'per_night' COMMENT '宠物费用类型',
  `pet_fee` decimal(10, 2) NULL DEFAULT NULL COMMENT '宠物费用',
  `pet_deposit` decimal(10, 2) NULL DEFAULT NULL COMMENT '宠物押金',
  `vaccination_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要疫苗证明',
  `pet_areas_allowed` json NULL COMMENT '宠物允许区域',
  `pet_restrictions` json NULL COMMENT '宠物限制',
  `pet_services` json NULL COMMENT '宠物服务',
  `pet_amenities` json NULL COMMENT '宠物设施',
  `pet_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '宠物政策备注',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_pet`(`hotel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店宠物政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hotels
-- ----------------------------
DROP TABLE IF EXISTS `hotels`;
CREATE TABLE `hotels`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '所属集团ID',
  `brand_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '所属品牌ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '酒店代码',
  `hotel_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应商酒店代码',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '酒店名称',
  `name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '酒店描述',
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'China' COMMENT '国家',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `district` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区县',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `latitude` decimal(10, 8) NULL DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(11, 8) NULL DEFAULT NULL COMMENT '经度',
  `star_rating` tinyint(4) NULL DEFAULT 0 COMMENT '星级评定',
  `opening_date` date NULL DEFAULT NULL COMMENT '开业日期',
  `renovation_date` date NULL DEFAULT NULL COMMENT '最近装修日期',
  `total_rooms` int(11) NULL DEFAULT 0 COMMENT '总房间数',
  `total_floors` int(11) NULL DEFAULT 0 COMMENT '总楼层数',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '酒店电话',
  `fax` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '传真',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '官网',
  `business_license` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业执照号',
  `tax_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税务登记号',
  `legal_representative` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '法定代表人',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人邮箱',
  `emergency_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '紧急联系电话',
  `business_hours` json NULL COMMENT '营业时间',
  `timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Asia/Shanghai' COMMENT '时区',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '币种',
  `languages` json NULL COMMENT '支持语言',
  `payment_methods` json NULL COMMENT '支付方式',
  `check_in_time` time NULL DEFAULT '14:00:00' COMMENT '入住时间',
  `check_out_time` time NULL DEFAULT '12:00:00' COMMENT '退房时间',
  `language_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'zh-CN' COMMENT '语言代码',
  `active` tinyint(1) NULL DEFAULT 1 COMMENT '酒店启用状态',
  `hotel_names` json NULL COMMENT '多语言酒店名称',
  `hotel_briefs` json NULL COMMENT '多语言酒店简介',
  `hotel_descriptions` json NULL COMMENT '多语言酒店描述',
  `important_informations` json NULL COMMENT '重要通知信息',
  `when_built` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '建造年月',
  `last_renovation` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后装修年月',
  `hotel_category` json NULL COMMENT '酒店类别信息',
  `positions` json NULL COMMENT '经纬度信息',
  `addresses` json NULL COMMENT '多语言地址信息',
  `phones` json NULL COMMENT '酒店电话信息',
  `emails` json NULL COMMENT '酒店邮箱信息',
  `total_room_quantity` int(11) NULL DEFAULT 0 COMMENT '酒店房间总数量',
  `star_licence` tinyint(1) NULL DEFAULT 0 COMMENT '是否挂牌星级',
  `hotel_building_area` int(11) NULL DEFAULT NULL COMMENT '酒店建筑面积(平方米)',
  `receive_foreign_guest` tinyint(4) NULL DEFAULT 5 COMMENT '接待资质(3:仅大陆,4:大陆+港澳台,5:全部)',
  `virtual_tour_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '虚拟游览URL',
  `status` enum('active','inactive','maintenance','closed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '状态',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已认证',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '认证时间',
  `verified_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '认证人ID',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '查看次数',
  `booking_count` int(11) NOT NULL DEFAULT 0 COMMENT '预订次数',
  `review_count` int(11) NOT NULL DEFAULT 0 COMMENT '评价数量',
  `average_rating` decimal(3, 2) NULL DEFAULT NULL COMMENT '平均评分',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_group_id`(`group_id`) USING BTREE,
  INDEX `idx_brand_id`(`brand_id`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_city`(`city`) USING BTREE,
  INDEX `idx_star_rating`(`star_rating`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_is_verified`(`is_verified`) USING BTREE,
  INDEX `idx_location`(`latitude`, `longitude`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '酒店表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for image_categories
-- ----------------------------
DROP TABLE IF EXISTS `image_categories`;
CREATE TABLE `image_categories`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` bigint(20) UNSIGNED NOT NULL COMMENT '分类ID',
  `parent_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父分类ID',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `category_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类组',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_id`(`category_id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_category_group`(`category_group`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '图片分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inventory_logs
-- ----------------------------
DROP TABLE IF EXISTS `inventory_logs`;
CREATE TABLE `inventory_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `date` date NOT NULL COMMENT '日期',
  `change_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更类型：manual-手动，booking-预订，cancellation-取消，system-系统',
  `change_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '变更原因',
  `before_available` int(11) NULL DEFAULT NULL COMMENT '变更前可售数量',
  `after_available` int(11) NULL DEFAULT NULL COMMENT '变更后可售数量',
  `change_amount` int(11) NULL DEFAULT NULL COMMENT '变更数量',
  `operator_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `source_system` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'api' COMMENT '来源系统',
  `reference_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '关联ID(如订单ID)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_date`(`date`) USING BTREE,
  INDEX `idx_change_type`(`change_type`) USING BTREE,
  INDEX `idx_operator_id`(`operator_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '库存变更日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ota_channels
-- ----------------------------
DROP TABLE IF EXISTS `ota_channels`;
CREATE TABLE `ota_channels`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道代码',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '渠道描述',
  `logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道Logo URL',
  `api_endpoint` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API接口地址',
  `api_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API版本',
  `api_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API密钥',
  `api_secret` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API密钥',
  `api_config` json NULL COMMENT 'API配置信息',
  `commission_rate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金比例（%）',
  `commission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'percentage' COMMENT '佣金类型：percentage-百分比，fixed-固定金额',
  `settlement_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'monthly' COMMENT '结算方式：daily-日结，weekly-周结，monthly-月结，quarterly-季结',
  `settlement_currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '结算币种',
  `payment_terms` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '付款条款',
  `contact_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人姓名',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '联系地址',
  `supported_currencies` json NULL COMMENT '支持的币种列表',
  `supported_languages` json NULL COMMENT '支持的语言列表',
  `booking_window` int(11) NULL DEFAULT 365 COMMENT '预订窗口期（天）',
  `cancellation_window` int(11) NULL DEFAULT 24 COMMENT '取消窗口期（小时）',
  `modification_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否允许修改',
  `instant_confirmation` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否支持即时确认',
  `min_advance_booking` int(11) NOT NULL DEFAULT 0 COMMENT '最少提前预订天数',
  `max_advance_booking` int(11) NULL DEFAULT NULL COMMENT '最多提前预订天数',
  `blackout_dates` json NULL COMMENT '禁售日期',
  `rate_restrictions` json NULL COMMENT '价格限制规则',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级，数值越大优先级越高',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `total_bookings` int(11) NOT NULL DEFAULT 0 COMMENT '总预订数',
  `total_revenue` decimal(15, 2) NOT NULL DEFAULT 0.00 COMMENT '总收入',
  `last_sync_at` timestamp NULL DEFAULT NULL COMMENT '最后同步时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  INDEX `idx_commission_rate`(`commission_rate`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'OTA渠道表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for payment_methods
-- ----------------------------
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `payment_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式名称',
  `payment_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付方式代码',
  `payment_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付类型',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '描述',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `cvc_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要CVC验证',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_payment_code`(`payment_code`) USING BTREE,
  INDEX `idx_payment_type`(`payment_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '常用支付方式表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for rate_plan_ota_channels
-- ----------------------------
DROP TABLE IF EXISTS `rate_plan_ota_channels`;
CREATE TABLE `rate_plan_ota_channels`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rate_plan_id` bigint(20) UNSIGNED NOT NULL COMMENT '房价计划ID',
  `ota_channel_id` bigint(20) UNSIGNED NOT NULL COMMENT 'OTA渠道ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `channel_rate_plan_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道内部房价计划代码',
  `commission_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '特定佣金比例（覆盖渠道默认值）',
  `markup_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '加价比例（%）',
  `markup_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '固定加价金额',
  `min_stay_override` int(11) NULL DEFAULT NULL COMMENT '最少入住天数覆盖',
  `max_stay_override` int(11) NULL DEFAULT NULL COMMENT '最多入住天数覆盖',
  `booking_window_override` int(11) NULL DEFAULT NULL COMMENT '预订窗口期覆盖（天）',
  `cancellation_policy_override` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '取消政策覆盖',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `effective_from` date NULL DEFAULT NULL COMMENT '生效开始日期',
  `effective_to` date NULL DEFAULT NULL COMMENT '生效结束日期',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_rate_plan_channel`(`rate_plan_id`, `ota_channel_id`) USING BTREE,
  INDEX `idx_rate_plan_id`(`rate_plan_id`) USING BTREE,
  INDEX `idx_ota_channel_id`(`ota_channel_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_effective_dates`(`effective_from`, `effective_to`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_rate_plan_ota_channel` FOREIGN KEY (`ota_channel_id`) REFERENCES `ota_channels` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_rate_plan_ota_rate_plan` FOREIGN KEY (`rate_plan_id`) REFERENCES `rate_plans` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房价计划OTA渠道关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for rate_plans
-- ----------------------------
DROP TABLE IF EXISTS `rate_plans`;
CREATE TABLE `rate_plans`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '价格计划代码',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '价格计划名称',
  `name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '价格计划描述',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `price_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'per_room' COMMENT '价格类型：per_room-按房间，per_person-按人',
  `breakfast_included` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否含早餐',
  `breakfast_count` int(11) NULL DEFAULT NULL COMMENT '早餐份数',
  `min_stay` int(11) NOT NULL DEFAULT 1 COMMENT '最少入住天数',
  `max_stay` int(11) NULL DEFAULT NULL COMMENT '最多入住天数',
  `min_advance_booking` int(11) NOT NULL DEFAULT 0 COMMENT '最少提前预订天数',
  `max_advance_booking` int(11) NULL DEFAULT NULL COMMENT '最多提前预订天数',
  `check_in_time_start` time NULL DEFAULT NULL COMMENT '最早入住时间',
  `check_in_time_end` time NULL DEFAULT NULL COMMENT '最晚入住时间',
  `max_occupancy` int(11) NULL DEFAULT NULL COMMENT '最大入住人数（覆盖房型设置）',
  `max_adults` int(11) NULL DEFAULT NULL COMMENT '最大成人数（覆盖房型设置）',
  `max_children` int(11) NULL DEFAULT NULL COMMENT '最大儿童数（覆盖房型设置）',
  `child_age_limit` int(11) NULL DEFAULT 12 COMMENT '儿童年龄限制',
  `cancellation_policy_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '取消政策ID',
  `cancellation_policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '自定义取消政策',
  `plan_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'standard' COMMENT '计划类型：standard-标准，promotion-促销，package-套餐',
  `cancellation_deadline` int(11) NULL DEFAULT 24 COMMENT '取消截止时间（小时）',
  `cancellation_fee_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'percentage' COMMENT '取消费用类型：fixed-固定金额，percentage-百分比，nights-按夜数',
  `cancellation_fee_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '取消费用金额',
  `modification_allowed` tinyint(1) NULL DEFAULT 1 COMMENT '是否允许修改',
  `modification_deadline` int(11) NULL DEFAULT 24 COMMENT '修改截止时间（小时）',
  `prepayment_required` tinyint(1) NULL DEFAULT 0 COMMENT '是否需要预付',
  `prepayment_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'full' COMMENT '预付类型：full-全额，partial-部分，deposit-定金',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `is_published` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发布',
  `priority` int(11) NOT NULL DEFAULT 0 COMMENT '优先级，数值越大优先级越高',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `unpublished_at` timestamp NULL DEFAULT NULL COMMENT '取消发布时间',
  `activated_at` timestamp NULL DEFAULT NULL COMMENT '激活时间',
  `deactivated_at` timestamp NULL DEFAULT NULL COMMENT '停用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_room_code`(`hotel_id`, `room_type_id`, `code`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_cancellation_policy_id`(`cancellation_policy_id`) USING BTREE,
  INDEX `idx_status`(`is_active`, `is_published`) USING BTREE,
  INDEX `idx_priority`(`priority`) USING BTREE,
  INDEX `idx_breakfast`(`breakfast_included`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房价计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for regions
-- ----------------------------
DROP TABLE IF EXISTS `regions`;
CREATE TABLE `regions`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区编码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `parent_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '父级地区ID',
  `level` tinyint(4) NOT NULL DEFAULT 1 COMMENT '层级：1省份，2城市，3区县',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '地区表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '显示名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '角色描述',
  `permissions` json NULL COMMENT '权限列表',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_facilities
-- ----------------------------
DROP TABLE IF EXISTS `room_facilities`;
CREATE TABLE `room_facilities`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` bigint(20) UNSIGNED NOT NULL COMMENT '分类ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设施代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设施名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '设施描述',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `is_chargeable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否收费',
  `charge_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '收费金额',
  `charge_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收费单位',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_active`(`is_active`) USING BTREE,
  INDEX `idx_sort`(`sort_order`) USING BTREE,
  CONSTRAINT `fk_room_facilities_category` FOREIGN KEY (`category_id`) REFERENCES `room_facility_categories` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房间设施表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_facility_categories
-- ----------------------------
DROP TABLE IF EXISTS `room_facility_categories`;
CREATE TABLE `room_facility_categories`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `parent_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '父分类ID',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_active`(`is_active`) USING BTREE,
  INDEX `idx_sort`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房间设施分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_inventory
-- ----------------------------
DROP TABLE IF EXISTS `room_inventory`;
CREATE TABLE `room_inventory`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `date` date NOT NULL COMMENT '日期',
  `total_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '总房间数',
  `available_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '可售房间数',
  `sold_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '已售房间数',
  `booked_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '已预订房间数',
  `blocked_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '锁定房间数',
  `maintenance_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '维修房间数',
  `overbooking_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '超售房间数',
  `oversell_limit` int(11) NULL DEFAULT 0 COMMENT '超售限制',
  `is_closed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关房',
  `close_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关房原因',
  `is_stop_sale` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否停售',
  `stop_sale_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '停售原因',
  `min_stay` int(11) NULL DEFAULT NULL COMMENT '最少入住天数（覆盖计划设置）',
  `max_stay` int(11) NULL DEFAULT NULL COMMENT '最多入住天数（覆盖计划设置）',
  `min_advance_booking` int(11) NULL DEFAULT NULL COMMENT '最少提前预订天数（覆盖计划设置）',
  `max_advance_booking` int(11) NULL DEFAULT NULL COMMENT '最多提前预订天数（覆盖计划设置）',
  `arrival_restriction` tinyint(1) NOT NULL DEFAULT 0 COMMENT '到店限制（CTA）',
  `departure_restriction` tinyint(1) NOT NULL DEFAULT 0 COMMENT '离店限制（CTD）',
  `max_occupancy` int(11) NULL DEFAULT NULL COMMENT '最大入住人数（覆盖房型设置）',
  `max_adults` int(11) NULL DEFAULT NULL COMMENT '最大成人数（覆盖房型设置）',
  `max_children` int(11) NULL DEFAULT NULL COMMENT '最大儿童数（覆盖房型设置）',
  `base_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '基础价格',
  `min_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低价格',
  `max_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高价格',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '币种',
  `booking_count` int(11) NOT NULL DEFAULT 0 COMMENT '预订数量',
  `checkin_count` int(11) NOT NULL DEFAULT 0 COMMENT '入住数量',
  `checkout_count` int(11) NOT NULL DEFAULT 0 COMMENT '退房数量',
  `cancellation_count` int(11) NOT NULL DEFAULT 0 COMMENT '取消数量',
  `last_updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '最后更新人ID',
  `last_updated_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后更新原因',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_room_date`(`hotel_id`, `room_type_id`, `date`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_date`(`date`) USING BTREE,
  INDEX `idx_available_rooms`(`available_rooms`) USING BTREE,
  INDEX `idx_is_closed`(`is_closed`) USING BTREE,
  INDEX `idx_is_stop_sale`(`is_stop_sale`) USING BTREE,
  INDEX `idx_booking_count`(`booking_count`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房态房量表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_inventory_batch_edits
-- ----------------------------
DROP TABLE IF EXISTS `room_inventory_batch_edits`;
CREATE TABLE `room_inventory_batch_edits`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `room_type_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '房型ID，NULL表示所有房型',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：set-设置，increase-增加，decrease-减少，close-关房，open-开房',
  `inventory_adjustment` int(11) NULL DEFAULT NULL COMMENT '库存调整值',
  `close_sales` tinyint(1) NULL DEFAULT NULL COMMENT '是否关房',
  `stop_sales` tinyint(1) NULL DEFAULT NULL COMMENT '是否停售',
  `min_stay_adjustment` int(11) NULL DEFAULT NULL COMMENT '最少入住天数调整',
  `max_stay_adjustment` int(11) NULL DEFAULT NULL COMMENT '最多入住天数调整',
  `arrival_restriction` tinyint(1) NULL DEFAULT NULL COMMENT '到店限制',
  `departure_restriction` tinyint(1) NULL DEFAULT NULL COMMENT '离店限制',
  `days_of_week` json NULL COMMENT '星期限制：[1,2,3,4,5,6,7]',
  `exclude_dates` json NULL COMMENT '排除日期',
  `include_dates` json NULL COMMENT '包含日期',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作原因',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
  `processed_count` int(11) NULL DEFAULT 0 COMMENT '已处理记录数',
  `total_count` int(11) NULL DEFAULT 0 COMMENT '总记录数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_date_range`(`start_date`, `end_date`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_by`(`created_by`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '库存批量编辑表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_rate_batch_edits
-- ----------------------------
DROP TABLE IF EXISTS `room_rate_batch_edits`;
CREATE TABLE `room_rate_batch_edits`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `room_type_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '房型ID，NULL表示所有房型',
  `rate_plan_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '价格计划ID，NULL表示所有计划',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：set-设置，increase-增加，decrease-减少，percentage-百分比调整',
  `price_adjustment` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格调整值',
  `percentage_adjustment` decimal(5, 2) NULL DEFAULT NULL COMMENT '百分比调整值',
  `days_of_week` json NULL COMMENT '星期限制：[1,2,3,4,5,6,7]',
  `exclude_dates` json NULL COMMENT '排除日期',
  `include_dates` json NULL COMMENT '包含日期',
  `min_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最低价格限制',
  `max_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最高价格限制',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
  `processed_count` int(11) NULL DEFAULT 0 COMMENT '已处理记录数',
  `total_count` int(11) NULL DEFAULT 0 COMMENT '总记录数',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_rate_plan_id`(`rate_plan_id`) USING BTREE,
  INDEX `idx_date_range`(`start_date`, `end_date`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_by`(`created_by`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房价批量编辑表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_rates
-- ----------------------------
DROP TABLE IF EXISTS `room_rates`;
CREATE TABLE `room_rates`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `rate_plan_id` bigint(20) UNSIGNED NOT NULL COMMENT '价格计划ID',
  `date` date NOT NULL COMMENT '日期',
  `sell_price` decimal(10, 2) NOT NULL COMMENT '售价',
  `base_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '底价',
  `market_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '市场价',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `breakfast_included` tinyint(1) NULL DEFAULT NULL COMMENT '是否含早餐',
  `breakfast_count` int(11) NULL DEFAULT NULL COMMENT '早餐份数',
  `extra_adult_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '额外成人价格',
  `extra_child_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '额外儿童价格',
  `min_stay` int(11) NULL DEFAULT NULL COMMENT '最少入住天数',
  `max_stay` int(11) NULL DEFAULT NULL COMMENT '最多入住天数',
  `min_advance_booking` int(11) NULL DEFAULT NULL COMMENT '最少提前预订天数',
  `max_advance_booking` int(11) NULL DEFAULT NULL COMMENT '最多提前预订天数',
  `is_closed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否关闭销售',
  `closed_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关闭原因',
  `arrival_restriction` tinyint(1) NOT NULL DEFAULT 0 COMMENT '到店限制（CTA）',
  `departure_restriction` tinyint(1) NOT NULL DEFAULT 0 COMMENT '离店限制（CTD）',
  `channel_restrictions` json NULL COMMENT '渠道限制',
  `commission_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '佣金比例（%）',
  `markup_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '加价比例（%）',
  `is_promotion` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为促销价格',
  `promotion_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '促销名称',
  `promotion_discount_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '促销折扣类型',
  `promotion_discount_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '促销折扣金额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_room_plan_date`(`hotel_id`, `room_type_id`, `rate_plan_id`, `date`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_rate_plan_id`(`rate_plan_id`) USING BTREE,
  INDEX `idx_date`(`date`) USING BTREE,
  INDEX `idx_sell_price`(`sell_price`) USING BTREE,
  INDEX `idx_is_closed`(`is_closed`) USING BTREE,
  INDEX `idx_is_promotion`(`is_promotion`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_type_bed_configs
-- ----------------------------
DROP TABLE IF EXISTS `room_type_bed_configs`;
CREATE TABLE `room_type_bed_configs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `bed_type` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '主床型ID',
  `bed_count` int(11) NOT NULL DEFAULT 1 COMMENT '主床数量',
  `bed_size` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '床尺寸',
  `extra_beds` json NULL COMMENT '额外床位配置',
  `bedding_material` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'cotton' COMMENT '床单材质',
  `pillow_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'down' COMMENT '枕头类型',
  `pillow_count` int(11) NOT NULL DEFAULT 2 COMMENT '枕头数量',
  `duvet_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'down' COMMENT '被子类型',
  `blanket_provided` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否提供毯子',
  `bedding_change_frequency` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'daily' COMMENT '床品更换频率',
  `bed_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '床位说明',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_bed_type`(`bed_type`) USING BTREE,
  INDEX `idx_bedding_material`(`bedding_material`) USING BTREE,
  INDEX `idx_pillow_type`(`pillow_type`) USING BTREE,
  INDEX `idx_duvet_type`(`duvet_type`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_updated_at`(`updated_at`) USING BTREE,
  CONSTRAINT `fk_room_type_bed_configs_bed_type` FOREIGN KEY (`bed_type`) REFERENCES `bed_types` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_room_type_bed_configs_room_type_id` FOREIGN KEY (`room_type_id`) REFERENCES `room_types` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房型床位配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_type_bed_types
-- ----------------------------
DROP TABLE IF EXISTS `room_type_bed_types`;
CREATE TABLE `room_type_bed_types`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `bed_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '床型ID',
  `bed_count` int(11) NOT NULL DEFAULT 1 COMMENT '床数量',
  `is_primary` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否主要床型',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_room_type_bed_type`(`room_type_id`, `bed_type_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_bed_type_id`(`bed_type_id`) USING BTREE,
  INDEX `idx_is_primary`(`is_primary`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房型床型关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_type_facilities
-- ----------------------------
DROP TABLE IF EXISTS `room_type_facilities`;
CREATE TABLE `room_type_facilities`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `facility_id` bigint(20) UNSIGNED NOT NULL COMMENT '设施ID',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '数量',
  `specification` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '规格说明',
  `is_highlight` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否重点展示',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_room_type_facility`(`room_type_id`, `facility_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_facility_id`(`facility_id`) USING BTREE,
  INDEX `idx_is_highlight`(`is_highlight`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  CONSTRAINT `fk_room_type_facilities_facility` FOREIGN KEY (`facility_id`) REFERENCES `room_facilities` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_room_type_facilities_room_type` FOREIGN KEY (`room_type_id`) REFERENCES `room_types` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房型设施关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for room_type_images
-- ----------------------------
DROP TABLE IF EXISTS `room_type_images`;
CREATE TABLE `room_type_images`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `image_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片标题',
  `image_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '图片描述',
  `image_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'room' COMMENT '图片类型：room-房间，bathroom-浴室，view-景观，amenity-设施',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件名',
  `file_size` int(11) NULL DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件类型',
  `width` int(11) NULL DEFAULT NULL COMMENT '图片宽度',
  `height` int(11) NULL DEFAULT NULL COMMENT '图片高度',
  `display_order` int(11) NOT NULL DEFAULT 0 COMMENT '显示顺序',
  `is_primary` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否主图',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_image_type`(`image_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_display_order`(`display_order`) USING BTREE,
  INDEX `idx_is_primary`(`is_primary`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房型图片表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_type_policies
-- ----------------------------
DROP TABLE IF EXISTS `room_type_policies`;
CREATE TABLE `room_type_policies`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_type_id` bigint(20) UNSIGNED NOT NULL COMMENT '房型ID',
  `cancellation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'free' COMMENT '取消政策类型: free/partial/non_refundable/custom',
  `free_cancellation_hours` int(11) NULL DEFAULT 24 COMMENT '免费取消小时数',
  `cancellation_fee_rate` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '取消手续费率(%)',
  `cancellation_policy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '取消政策说明',
  `min_check_in_age` int(11) NULL DEFAULT 18 COMMENT '最低入住年龄',
  `id_required` tinyint(1) NOT NULL DEFAULT 1 COMMENT '需要身份证明',
  `credit_card_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '需要信用卡担保',
  `deposit_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '押金金额',
  `deposit_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'none' COMMENT '押金类型: cash/credit_card/both/none',
  `max_guests` int(11) NULL DEFAULT 2 COMMENT '最大住客数',
  `visitors_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '允许访客',
  `visitor_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '访客时间限制',
  `parties_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许聚会',
  `smoking_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许吸烟',
  `quiet_hours` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '安静时间',
  `children_free_age` int(11) NULL DEFAULT 12 COMMENT '儿童免费年龄',
  `pets_allowed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '允许宠物',
  `pet_fee_per_night` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '宠物费用(每晚)',
  `pet_restrictions` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '宠物限制',
  `children_facilities` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '儿童设施',
  `early_check_in_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '允许提前入住',
  `late_check_out_allowed` tinyint(1) NOT NULL DEFAULT 1 COMMENT '允许延迟退房',
  `luggage_storage` tinyint(1) NOT NULL DEFAULT 1 COMMENT '行李寄存',
  `special_policies` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '其他特殊政策',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态: active/inactive',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_room_type_policy`(`room_type_id`) USING BTREE,
  INDEX `idx_room_type_id`(`room_type_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房型政策表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for room_types
-- ----------------------------
DROP TABLE IF EXISTS `room_types`;
CREATE TABLE `room_types`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '房型代码',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '房型名称',
  `name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '房型名称(英文)',
  `english_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '房型描述',
  `area` decimal(8, 2) NULL DEFAULT NULL COMMENT '房间面积（平方米）',
  `max_occupancy` int(11) NOT NULL DEFAULT 2 COMMENT '最大入住人数',
  `max_adults` int(11) NOT NULL DEFAULT 2 COMMENT '最大成人数',
  `max_children` int(11) NOT NULL DEFAULT 0 COMMENT '最大儿童数',
  `bed_count` int(11) NOT NULL DEFAULT 1 COMMENT '床数量',
  `floor_range` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '楼层范围',
  `total_rooms` int(11) NOT NULL DEFAULT 0 COMMENT '房间总数',
  `room_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '房型分类：standard-标准，suite-套房，villa-别墅',
  `smoking_policy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'non_smoking' COMMENT '吸烟政策：smoking-可吸烟，non_smoking-禁烟，designated-指定区域',
  `bathroom_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '浴室类型：private-独立，shared-共享，ensuite-套内',
  `window_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'standard' COMMENT '窗户类型：floor_to_ceiling-落地窗，standard-标准窗，bay-飘窗',
  `balcony_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'none' COMMENT '阳台类型：private-私人阳台，shared-共享阳台，none-无阳台',
  `decoration_style` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '装修风格：modern-现代，classic-古典，minimalist-简约',
  `room_orientation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '房间朝向：north-北，south-南，east-东，west-西',
  `room_dimensions` json NULL COMMENT '房间尺寸',
  `turndown_service` tinyint(1) NULL DEFAULT 0 COMMENT '是否提供开夜床服务',
  `base_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '基础价格',
  `extra_bed_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '加床价格',
  `child_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '儿童价格',
  `status` enum('active','inactive','maintenance','closed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '状态',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否推荐',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `view_count` int(11) NOT NULL DEFAULT 0 COMMENT '查看次数',
  `booking_count` int(11) NOT NULL DEFAULT 0 COMMENT '预订次数',
  `review_count` int(11) NOT NULL DEFAULT 0 COMMENT '评价数量',
  `average_rating` decimal(3, 2) NULL DEFAULT NULL COMMENT '平均评分',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_hotel_code`(`hotel_id`, `code`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_room_category`(`room_category`) USING BTREE,
  INDEX `idx_max_occupancy`(`max_occupancy`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_base_price`(`base_price`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for standard_facilities
-- ----------------------------
DROP TABLE IF EXISTS `standard_facilities`;
CREATE TABLE `standard_facilities`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` bigint(20) UNSIGNED NOT NULL COMMENT '分类ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设施代码',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设施名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '设施描述',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设施图标',
  `is_chargeable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否收费',
  `charge_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收费类型：fixed-固定，hourly-按小时，daily-按天',
  `charge_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '收费金额',
  `is_bookable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要预订',
  `booking_advance_hours` int(11) NULL DEFAULT NULL COMMENT '提前预订小时数',
  `operating_hours` json NULL COMMENT '营业时间',
  `capacity` int(11) NULL DEFAULT NULL COMMENT '容量/人数限制',
  `age_restriction` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '年龄限制',
  `special_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '特殊要求',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_is_chargeable`(`is_chargeable`) USING BTREE,
  INDEX `idx_is_bookable`(`is_bookable`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '标准设施表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for suppliers
-- ----------------------------
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '供应商代码',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '供应商名称',
  `name_en` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '英文名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '供应商描述',
  `supplier_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'hotel' COMMENT '供应商类型：hotel-酒店，chain-连锁，group-集团，agent-代理商',
  `business_license` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营业执照号',
  `tax_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税务登记号',
  `legal_representative` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '法定代表人',
  `contact_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地址',
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '国家',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市',
  `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '官方网站',
  `api_endpoint` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API接口地址',
  `api_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API版本',
  `api_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API密钥',
  `api_secret` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API密钥',
  `commission_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '佣金比例（%）',
  `currency` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '结算币种',
  `payment_terms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '付款条件',
  `settlement_period` int(11) NULL DEFAULT 30 COMMENT '结算周期（天）',
  `credit_limit` decimal(15, 2) NULL DEFAULT NULL COMMENT '信用额度',
  `quality_score` decimal(3, 2) NULL DEFAULT NULL COMMENT '质量评分（1.00-5.00）',
  `cooperation_start_date` date NULL DEFAULT NULL COMMENT '合作开始日期',
  `contract_end_date` date NULL DEFAULT NULL COMMENT '合同结束日期',
  `sync_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用同步',
  `last_sync_at` timestamp NULL DEFAULT NULL COMMENT '最后同步时间',
  `sync_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '同步状态',
  `hotel_count` int(11) NOT NULL DEFAULT 0 COMMENT '酒店数量',
  `room_count` int(11) NOT NULL DEFAULT 0 COMMENT '房间数量',
  `booking_count` int(11) NOT NULL DEFAULT 0 COMMENT '预订数量',
  `total_revenue` decimal(15, 2) NOT NULL DEFAULT 0.00 COMMENT '总营收',
  `status` enum('active','inactive','suspended','terminated') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '状态',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已认证',
  `verified_at` timestamp NULL DEFAULT NULL COMMENT '认证时间',
  `verified_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '认证人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_supplier_type`(`supplier_type`) USING BTREE,
  INDEX `idx_name`(`name`) USING BTREE,
  INDEX `idx_city`(`city`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_verified`(`is_verified`) USING BTREE,
  INDEX `idx_sync_enabled`(`sync_enabled`) USING BTREE,
  INDEX `idx_quality_score`(`quality_score`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '供应商表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sync_logs
-- ----------------------------
DROP TABLE IF EXISTS `sync_logs`;
CREATE TABLE `sync_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sync_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步类型：hotel-酒店，room_type-房型，inventory-库存，rate-房价，booking-订单',
  `sync_direction` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步方向：push-推送，pull-拉取，bidirectional-双向',
  `channel_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '渠道ID',
  `supplier_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '供应商ID',
  `hotel_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '酒店ID',
  `external_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '外部ID',
  `sync_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '同步状态：pending-待处理，processing-处理中，success-成功，failed-失败，partial-部分成功',
  `request_data` json NULL COMMENT '请求数据',
  `response_data` json NULL COMMENT '响应数据',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '错误代码',
  `retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '重试次数',
  `max_retries` int(11) NOT NULL DEFAULT 3 COMMENT '最大重试次数',
  `next_retry_at` timestamp NULL DEFAULT NULL COMMENT '下次重试时间',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `execution_time` decimal(8, 3) NULL DEFAULT NULL COMMENT '执行时间(秒)',
  `records_processed` int(11) NULL DEFAULT 0 COMMENT '处理记录数',
  `records_success` int(11) NULL DEFAULT 0 COMMENT '成功记录数',
  `records_failed` int(11) NULL DEFAULT 0 COMMENT '失败记录数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sync_type`(`sync_type`) USING BTREE,
  INDEX `idx_sync_direction`(`sync_direction`) USING BTREE,
  INDEX `idx_channel_id`(`channel_id`) USING BTREE,
  INDEX `idx_supplier_id`(`supplier_id`) USING BTREE,
  INDEX `idx_hotel_id`(`hotel_id`) USING BTREE,
  INDEX `idx_sync_status`(`sync_status`) USING BTREE,
  INDEX `idx_external_id`(`external_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_next_retry_at`(`next_retry_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '数据同步日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_configs
-- ----------------------------
DROP TABLE IF EXISTS `system_configs`;
CREATE TABLE `system_configs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置值',
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string' COMMENT '配置类型：string-字符串，int-整数，float-浮点数，bool-布尔值，json-JSON',
  `config_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'general' COMMENT '配置分组：general-通用，hotel-酒店，booking-预订，payment-支付，notification-通知',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置描述',
  `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否公开（前端可访问）',
  `is_editable` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可编辑',
  `validation_rules` json NULL COMMENT '验证规则',
  `default_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '默认值',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updated_by` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key`) USING BTREE,
  INDEX `idx_config_group`(`config_group`) USING BTREE,
  INDEX `idx_is_public`(`is_public`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for system_logs
-- ----------------------------
DROP TABLE IF EXISTS `system_logs`;
CREATE TABLE `system_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志级别：debug-调试，info-信息，warning-警告，error-错误，critical-严重',
  `channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'application' COMMENT '日志通道：application-应用，database-数据库，api-接口，sync-同步',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志消息',
  `context` json NULL COMMENT '上下文数据',
  `user_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户代理',
  `request_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求ID',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话ID',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求URL',
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '请求方法',
  `execution_time` decimal(8, 3) NULL DEFAULT NULL COMMENT '执行时间(秒)',
  `memory_usage` int(11) NULL DEFAULT NULL COMMENT '内存使用量(字节)',
  `file` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件路径',
  `line` int(11) NULL DEFAULT NULL COMMENT '行号',
  `stack_trace` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '堆栈跟踪',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_level`(`level`) USING BTREE,
  INDEX `idx_channel`(`channel`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_request_id`(`request_id`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_roles
-- ----------------------------
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) UNSIGNED NOT NULL COMMENT '角色ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_role`(`user_id`, `role_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_role_id`(`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `gender` enum('male','female','unknown') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'unknown' COMMENT '性别',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `user_type` enum('admin','hotel_manager','staff','customer') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'customer' COMMENT '用户类型',
  `status` enum('active','inactive','banned') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT '状态',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `phone_verified_at` timestamp NULL DEFAULT NULL COMMENT '手机验证时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username`) USING BTREE,
  UNIQUE INDEX `uk_email`(`email`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  INDEX `idx_user_type`(`user_type`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for view_types
-- ----------------------------
DROP TABLE IF EXISTS `view_types`;
CREATE TABLE `view_types`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '景观代码',
  `name_cn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '中文名称',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '英文名称',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用，inactive-禁用',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '景观类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Triggers structure for table hotel_payment_methods
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_hotel_payment_methods_default_check`;
delimiter ;;
CREATE TRIGGER `tr_hotel_payment_methods_default_check` BEFORE UPDATE ON `hotel_payment_methods` FOR EACH ROW BEGIN
    -- 如果设置为默认支付方式，取消同一酒店的其他默认支付方式
    IF NEW.`is_default` = 1 AND OLD.`is_default` = 0 THEN
        UPDATE `hotel_payment_methods` 
        SET `is_default` = 0 
        WHERE `hotel_id` = NEW.`hotel_id` AND `id` != NEW.`id`;
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
