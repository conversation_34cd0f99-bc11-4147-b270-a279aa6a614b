<?php

namespace app\service;

use app\model\SyncLog;
use Carbon\Carbon;

/**
 * 同步日志服务
 */
class SyncLogService extends BaseService
{
    /**
     * 获取同步日志列表
     *
     * @param array $params
     * @return array
     */
    public function getSyncLogList(array $params = [])
    {
        try {
            $query = SyncLog::with(['channel', 'supplier', 'hotel']);

            // 筛选条件
            if (!empty($params['sync_type'])) {
                $query->where('sync_type', $params['sync_type']);
            }

            if (!empty($params['sync_direction'])) {
                $query->where('sync_direction', $params['sync_direction']);
            }

            if (!empty($params['sync_status'])) {
                $query->where('sync_status', $params['sync_status']);
            }

            if (!empty($params['channel_id'])) {
                $query->where('channel_id', $params['channel_id']);
            }

            if (!empty($params['supplier_id'])) {
                $query->where('supplier_id', $params['supplier_id']);
            }

            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            if (!empty($params['external_id'])) {
                $query->where('external_id', $params['external_id']);
            }

            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }

            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            // 排序
            $sortField = $params['sort_field'] ?? 'created_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $page = $params['page'] ?? 1;
            $pageSize = $params['page_size'] ?? 20;
            $result = $query->paginate($pageSize, ['*'], 'page', $page);

            return $this->success([
                'list' => $result->items(),
                'total' => $result->total(),
                'page' => $result->currentPage(),
                'page_size' => $result->perPage(),
                'total_pages' => $result->lastPage()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取同步日志列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取同步日志详情
     *
     * @param int $logId
     * @return array
     */
    public function getSyncLogDetail(int $logId)
    {
        try {
            $log = SyncLog::with(['channel', 'supplier', 'hotel'])
                ->find($logId);

            if (!$log) {
                return $this->error('同步日志不存在');
            }

            return $this->success($log);

        } catch (\Exception $e) {
            return $this->error('获取同步日志详情失败：' . $e->getMessage());
        }
    }

    /**
     * 创建同步日志
     *
     * @param array $data
     * @return array
     */
    public function createSyncLog(array $data)
    {
        try {
            // 设置默认值
            $data['sync_status'] = $data['sync_status'] ?? SyncLog::STATUS_PENDING;
            $data['retry_count'] = $data['retry_count'] ?? 0;
            $data['max_retries'] = $data['max_retries'] ?? 3;

            $log = SyncLog::create($data);

            return $this->success($log, '同步日志创建成功');

        } catch (\Exception $e) {
            return $this->error('创建同步日志失败：' . $e->getMessage());
        }
    }

    /**
     * 更新同步日志
     *
     * @param int $logId
     * @param array $data
     * @return array
     */
    public function updateSyncLog(int $logId, array $data)
    {
        try {
            $log = SyncLog::find($logId);
            if (!$log) {
                return $this->error('同步日志不存在');
            }

            $log->update($data);

            return $this->success($log, '同步日志更新成功');

        } catch (\Exception $e) {
            return $this->error('更新同步日志失败：' . $e->getMessage());
        }
    }

    /**
     * 删除同步日志
     *
     * @param int $logId
     * @return array
     */
    public function deleteSyncLog(int $logId)
    {
        try {
            $log = SyncLog::find($logId);
            if (!$log) {
                return $this->error('同步日志不存在');
            }

            $log->delete();

            return $this->success(null, '同步日志删除成功');

        } catch (\Exception $e) {
            return $this->error('删除同步日志失败：' . $e->getMessage());
        }
    }

    /**
     * 获取同步统计信息
     *
     * @param array $params
     * @return array
     */
    public function getSyncStatistics(array $params = [])
    {
        try {
            $query = SyncLog::query();

            // 时间范围
            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }
            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            // 渠道筛选
            if (!empty($params['channel_id'])) {
                $query->where('channel_id', $params['channel_id']);
            }

            // 供应商筛选
            if (!empty($params['supplier_id'])) {
                $query->where('supplier_id', $params['supplier_id']);
            }

            $statistics = [
                'total_syncs' => $query->count(),
                'success_syncs' => $query->where('sync_status', SyncLog::STATUS_SUCCESS)->count(),
                'failed_syncs' => $query->where('sync_status', SyncLog::STATUS_FAILED)->count(),
                'pending_syncs' => $query->where('sync_status', SyncLog::STATUS_PENDING)->count(),
                'processing_syncs' => $query->where('sync_status', SyncLog::STATUS_PROCESSING)->count(),
                'partial_syncs' => $query->where('sync_status', SyncLog::STATUS_PARTIAL)->count(),
            ];

            // 计算成功率
            $statistics['success_rate'] = $statistics['total_syncs'] > 0 
                ? round(($statistics['success_syncs'] / $statistics['total_syncs']) * 100, 2) 
                : 0;

            // 按同步类型统计
            $typeStats = $query->selectRaw('sync_type, COUNT(*) as count')
                ->groupBy('sync_type')
                ->pluck('count', 'sync_type')
                ->toArray();

            $statistics['type_stats'] = $typeStats;

            // 按同步方向统计
            $directionStats = $query->selectRaw('sync_direction, COUNT(*) as count')
                ->groupBy('sync_direction')
                ->pluck('count', 'sync_direction')
                ->toArray();

            $statistics['direction_stats'] = $directionStats;

            // 平均执行时间
            $avgExecutionTime = $query->whereNotNull('execution_time')
                ->avg('execution_time');
            $statistics['avg_execution_time'] = round($avgExecutionTime, 3);

            return $this->success($statistics);

        } catch (\Exception $e) {
            return $this->error('获取同步统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 重试同步日志
     *
     * @param int $logId
     * @return array
     */
    public function retrySyncLog(int $logId)
    {
        try {
            $log = SyncLog::find($logId);
            if (!$log) {
                return $this->error('同步日志不存在');
            }

            if (!$log->canRetry()) {
                return $this->error('该同步日志不能重试');
            }

            // 重置状态
            $log->update([
                'sync_status' => SyncLog::STATUS_PENDING,
                'error_message' => null,
                'error_code' => null,
                'next_retry_at' => null,
                'started_at' => null,
                'completed_at' => null,
                'execution_time' => null
            ]);

            return $this->success($log, '同步日志已重置，可以重新执行');

        } catch (\Exception $e) {
            return $this->error('重试同步日志失败：' . $e->getMessage());
        }
    }

    /**
     * 批量重试同步日志
     *
     * @param array $logIds
     * @return array
     */
    public function batchRetrySyncLogs(array $logIds)
    {
        try {
            $logs = SyncLog::whereIn('id', $logIds)->get();
            
            $retryCount = 0;
            $skipCount = 0;
            $errors = [];

            foreach ($logs as $log) {
                if ($log->canRetry()) {
                    $log->update([
                        'sync_status' => SyncLog::STATUS_PENDING,
                        'error_message' => null,
                        'error_code' => null,
                        'next_retry_at' => null,
                        'started_at' => null,
                        'completed_at' => null,
                        'execution_time' => null
                    ]);
                    $retryCount++;
                } else {
                    $skipCount++;
                    $errors[] = "日志ID {$log->id} 不能重试";
                }
            }

            return $this->success([
                'retry_count' => $retryCount,
                'skip_count' => $skipCount,
                'errors' => $errors
            ], "成功重置 {$retryCount} 个同步日志");

        } catch (\Exception $e) {
            return $this->error('批量重试同步日志失败：' . $e->getMessage());
        }
    }

    /**
     * 清理过期日志
     *
     * @param int $retentionDays
     * @return array
     */
    public function cleanupExpiredLogs(int $retentionDays = 30)
    {
        try {
            $cutoffDate = Carbon::now()->subDays($retentionDays);
            
            $deletedCount = SyncLog::where('created_at', '<', $cutoffDate)
                ->where('sync_status', '!=', SyncLog::STATUS_PROCESSING)
                ->delete();

            return $this->success([
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toDateTimeString()
            ], "成功清理 {$deletedCount} 条过期日志");

        } catch (\Exception $e) {
            return $this->error('清理过期日志失败：' . $e->getMessage());
        }
    }

    /**
     * 导出同步日志
     *
     * @param array $params
     * @return array
     */
    public function exportSyncLogs(array $params = [])
    {
        try {
            $query = SyncLog::with(['channel', 'supplier', 'hotel']);

            // 应用筛选条件
            if (!empty($params['sync_type'])) {
                $query->where('sync_type', $params['sync_type']);
            }

            if (!empty($params['sync_status'])) {
                $query->where('sync_status', $params['sync_status']);
            }

            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }

            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            $logs = $query->orderBy('created_at', 'desc')->get();

            // 格式化导出数据
            $exportData = $logs->map(function ($log) {
                return [
                    'ID' => $log->id,
                    '同步类型' => $log->sync_type_name,
                    '同步方向' => $log->sync_direction_name,
                    '同步状态' => $log->sync_status_name,
                    '渠道' => $log->channel->name ?? '',
                    '供应商' => $log->supplier->name ?? '',
                    '酒店' => $log->hotel->name ?? '',
                    '外部ID' => $log->external_id,
                    '执行时间' => $log->formatted_execution_time,
                    '处理记录数' => $log->records_processed,
                    '成功记录数' => $log->records_success,
                    '失败记录数' => $log->records_failed,
                    '成功率' => $log->success_rate . '%',
                    '错误信息' => $log->error_message,
                    '创建时间' => $log->created_at->format('Y-m-d H:i:s'),
                    '完成时间' => $log->completed_at ? $log->completed_at->format('Y-m-d H:i:s') : '',
                ];
            });

            $format = $params['format'] ?? 'csv';
            $filename = 'sync_logs_' . date('Y-m-d_H-i-s') . '.' . $format;

            return $this->success([
                'data' => $exportData,
                'filename' => $filename,
                'format' => $format,
                'count' => $exportData->count()
            ], '同步日志导出成功');

        } catch (\Exception $e) {
            return $this->error('导出同步日志失败：' . $e->getMessage());
        }
    }

    /**
     * 获取同步趋势分析
     *
     * @param array $params
     * @return array
     */
    public function getSyncTrends(array $params = [])
    {
        try {
            $timeRange = $params['time_range'] ?? '7d';
            
            // 计算时间范围
            switch ($timeRange) {
                case '1d':
                    $startDate = Carbon::now()->subDay();
                    $groupBy = 'HOUR(created_at)';
                    break;
                case '7d':
                    $startDate = Carbon::now()->subDays(7);
                    $groupBy = 'DATE(created_at)';
                    break;
                case '30d':
                    $startDate = Carbon::now()->subDays(30);
                    $groupBy = 'DATE(created_at)';
                    break;
                case '90d':
                    $startDate = Carbon::now()->subDays(90);
                    $groupBy = 'DATE(created_at)';
                    break;
                default:
                    $startDate = Carbon::now()->subDays(7);
                    $groupBy = 'DATE(created_at)';
            }

            $query = SyncLog::where('created_at', '>=', $startDate);

            // 按时间分组统计
            $trends = $query->selectRaw("
                {$groupBy} as time_period,
                COUNT(*) as total_count,
                SUM(CASE WHEN sync_status = 'success' THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed_count,
                AVG(execution_time) as avg_execution_time
            ")
            ->groupBy('time_period')
            ->orderBy('time_period')
            ->get();

            return $this->success([
                'time_range' => $timeRange,
                'trends' => $trends,
                'summary' => [
                    'total_syncs' => $trends->sum('total_count'),
                    'total_success' => $trends->sum('success_count'),
                    'total_failed' => $trends->sum('failed_count'),
                    'avg_execution_time' => round($trends->avg('avg_execution_time'), 3)
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error('获取同步趋势分析失败：' . $e->getMessage());
        }
    }
}
