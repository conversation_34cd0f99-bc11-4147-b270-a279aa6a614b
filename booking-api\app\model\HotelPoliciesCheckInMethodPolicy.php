<?php

namespace app\model;

use support\Model;

/**
 * 酒店入住方式政策模型
 */
class HotelPoliciesCheckInMethodPolicy extends Model
{
    protected $table = 'hotel_policies_check_in_method_policies';
    protected $primaryKey = 'id';

    protected $fillable = [
        'hotel_id', 'front_desk_checkin', 'self_checkin', 'mobile_checkin', 'keyless_entry',
        'contactless_checkin', 'online_checkin_available', 'checkin_instructions', 'key_collection_method',
        'access_code_provided', 'staff_assistance_hours', 'emergency_contact', 'checkin_method_notes', 'status',
    ];

    protected $casts = [
        'id' => 'integer', 'hotel_id' => 'integer', 'front_desk_checkin' => 'boolean',
        'self_checkin' => 'boolean', 'mobile_checkin' => 'boolean', 'keyless_entry' => 'boolean',
        'contactless_checkin' => 'boolean', 'online_checkin_available' => 'boolean',
        'access_code_provided' => 'boolean', 'created_at' => 'datetime', 'updated_at' => 'datetime',
    ];

    public function hotel() { return $this->belongsTo(Hotel::class, 'hotel_id', 'id'); }
    public function scopeActive($query) { return $query->where('status', 'active'); }
    public function scopeByHotel($query, $hotelId) { return $query->where('hotel_id', $hotelId); }
    public function getPolicyType(): string { return 'check_in_method'; }
    public function getPolicyDisplayName(): string { return '入住方式政策'; }

    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId, 'front_desk_checkin' => true, 'self_checkin' => false,
            'mobile_checkin' => false, 'keyless_entry' => false, 'contactless_checkin' => false,
            'online_checkin_available' => false, 'access_code_provided' => false, 'status' => 'active'
        ]);
    }
}
