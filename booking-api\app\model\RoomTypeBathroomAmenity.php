<?php

namespace app\model;

/**
 * 房型浴室设施模型
 * 对应数据库表：room_type_bathroom_amenities
 */
class RoomTypeBathroomAmenity extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_type_bathroom_amenities';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'room_type_id',
        'amenity_name',
        'amenity_description',
        'quantity',
        'specification',
        'is_highlight',
        'is_chargeable',
        'charge_amount',
        'icon',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'integer',
        'is_highlight' => 'boolean',
        'is_chargeable' => 'boolean',
        'charge_amount' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * 关联房型
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type_id');
    }

    /**
     * 获取重点展示设施
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHighlight($query)
    {
        return $query->where('is_highlight', true);
    }

    /**
     * 获取收费设施
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeChargeable($query)
    {
        return $query->where('is_chargeable', true);
    }

    /**
     * 按排序排序
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取价格信息
     *
     * @return string
     */
    public function getPriceInfoAttribute()
    {
        if (!$this->is_chargeable) {
            return '免费';
        }
        
        if ($this->charge_amount) {
            return "{$this->charge_amount}元";
        }
        
        return '收费';
    }

    /**
     * 获取设施完整信息
     *
     * @return array
     */
    public function getAmenityInfoAttribute()
    {
        return [
            'id' => $this->id,
            'amenity_name' => $this->amenity_name,
            'amenity_description' => $this->amenity_description,
            'quantity' => $this->quantity,
            'specification' => $this->specification,
            'is_highlight' => $this->is_highlight,
            'is_chargeable' => $this->is_chargeable,
            'charge_amount' => $this->charge_amount,
            'price_info' => $this->price_info,
            'icon' => $this->icon,
        ];
    }

    /**
     * 验证设施
     *
     * @param array $data
     * @return array
     */
    public static function validateAmenity($data)
    {
        $rules = [
            'room_type_id' => 'required|integer|exists:room_types,id',
            'amenity_name' => 'required|string|max:100',
            'amenity_description' => 'nullable|string',
            'quantity' => 'required|integer|min:1',
            'specification' => 'nullable|string|max:255',
            'is_highlight' => 'boolean',
            'is_chargeable' => 'boolean',
            'charge_amount' => 'nullable|numeric|min:0',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'integer|min:0',
        ];

        $messages = [
            'room_type_id.required' => '房型ID不能为空',
            'room_type_id.exists' => '房型不存在',
            'amenity_name.required' => '设施名称不能为空',
            'amenity_name.max' => '设施名称不能超过100个字符',
            'quantity.required' => '数量不能为空',
            'quantity.min' => '数量必须大于0',
            'specification.max' => '规格说明不能超过255个字符',
            'charge_amount.min' => '收费金额不能为负数',
            'icon.max' => '图标不能超过100个字符',
            'sort_order.min' => '排序不能为负数',
        ];

        return [
            'rules' => $rules,
            'messages' => $messages,
        ];
    }

    /**
     * 创建浴室设施
     *
     * @param array $data
     * @return static
     */
    public static function createAmenity($data)
    {
        $validation = self::validateAmenity($data);
        
        // 这里应该使用验证器，暂时简化处理
        $amenity = new self($data);
        $amenity->save();
        
        return $amenity;
    }

    /**
     * 更新浴室设施
     *
     * @param array $data
     * @return bool
     */
    public function updateAmenity($data)
    {
        $validation = self::validateAmenity($data);
        
        // 这里应该使用验证器，暂时简化处理
        return $this->update($data);
    }

    /**
     * 获取房型的所有浴室设施
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的重点展示浴室设施
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getHighlightAmenitiesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('is_highlight', true)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的收费浴室设施
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getChargeableAmenitiesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('is_chargeable', true)
            ->ordered()
            ->get();
    }
}
