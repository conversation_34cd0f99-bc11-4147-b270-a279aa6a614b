<?php

namespace app\model;

use support\Model;

/**
 * 设施分类模型
 */
class FacilityCategory extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'facility_categories';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'parent_id',
        'code',
        'name',
        'name_en',
        'description',
        'icon',
        'sort_order',
        'is_active',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取父分类
     */
    public function parent()
    {
        return $this->belongsTo(FacilityCategory::class, 'parent_id');
    }

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(FacilityCategory::class, 'parent_id');
    }

    /**
     * 获取分类下的设施
     */
    public function facilities()
    {
        return $this->hasMany(StandardFacility::class, 'category_id');
    }

    /**
     * 作用域：启用的分类
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：顶级分类
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取分类树形结构
     *
     * @return array
     */
    public static function getTree()
    {
        $categories = static::active()->ordered()->get();
        return static::buildTree($categories);
    }

    /**
     * 构建树形结构
     *
     * @param \Illuminate\Support\Collection $categories
     * @param int|null $parentId
     * @return array
     */
    private static function buildTree($categories, $parentId = null)
    {
        $tree = [];
        
        foreach ($categories->where('parent_id', $parentId) as $category) {
            $node = [
                'id' => $category->id,
                'code' => $category->code,
                'name' => $category->name,
                'name_en' => $category->name_en,
                'icon' => $category->icon,
                'children' => static::buildTree($categories, $category->id),
            ];
            
            $tree[] = $node;
        }
        
        return $tree;
    }

    /**
     * 获取所有启用的分类（用于下拉选择）
     *
     * @return array
     */
    public static function getActiveOptions()
    {
        return static::active()->ordered()->get()->map(function ($category) {
            return [
                'value' => $category->id,
                'label' => $category->name,
                'code' => $category->code,
                'icon' => $category->icon,
            ];
        })->toArray();
    }
}
