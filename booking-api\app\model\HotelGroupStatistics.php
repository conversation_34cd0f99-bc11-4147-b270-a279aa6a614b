<?php

namespace app\model;

use support\Model;

/**
 * 酒店集团统计模型
 */
class HotelGroupStatistics extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'hotel_group_statistics';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_group_id',
        'hotel_count',
        'room_count',
        'active_hotel_count',
        'total_bookings',
        'total_revenue',
        'average_rating',
        'review_count',
        'market_share',
        'growth_rate',
        'last_calculated_at',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_group_id' => 'integer',
        'hotel_count' => 'integer',
        'room_count' => 'integer',
        'active_hotel_count' => 'integer',
        'total_bookings' => 'integer',
        'total_revenue' => 'decimal:2',
        'average_rating' => 'decimal:2',
        'review_count' => 'integer',
        'market_share' => 'decimal:2',
        'growth_rate' => 'decimal:2',
        'last_calculated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取关联的酒店集团
     */
    public function hotelGroup()
    {
        return $this->belongsTo(HotelGroup::class);
    }
}
