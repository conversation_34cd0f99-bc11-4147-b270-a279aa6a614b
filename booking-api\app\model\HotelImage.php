<?php

namespace app\model;

use support\Model;

/**
 * 酒店图片模型
 */
class HotelImage extends Model
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'hotel_images';

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'image_category_id',
        'image_url',
        'image_category',
        'image_title',
        'image_description',
        'sort_order',
        'status',
    ];

    /**
     * 属性类型转换
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'image_category_id' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id');
    }

    /**
     * 关联图片分类
     */
    public function category()
    {
        return $this->belongsTo(ImageCategory::class, 'image_category_id', 'category_id');
    }

    /**
     * 作用域：启用的图片
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $categoryId)
    {
        if ($categoryId) {
            return $query->where('category_id', $categoryId);
        }
        return $query;
    }

    /**
     * 作用域：按父分类筛选
     */
    public function scopeByParentCategory($query, $parentCategoryId)
    {
        if ($parentCategoryId) {
            return $query->where('parent_category_id', $parentCategoryId);
        }
        return $query;
    }

    /**
     * 作用域：排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取酒店的所有图片按分类分组
     */
    public static function getByHotelGrouped(int $hotelId): array
    {
        $images = self::byHotel($hotelId)
            ->active()
            ->with(['category', 'parentCategory'])
            ->ordered()
            ->get();

        $grouped = [];
        foreach ($images as $image) {
            $parentCategoryName = $image->parentCategory ? $image->parentCategory->category_name : '未分类';
            $categoryName = $image->category ? $image->category->category_name : '未分类';
            
            $groupKey = $parentCategoryName;
            if (!isset($grouped[$groupKey])) {
                $grouped[$groupKey] = [
                    'parent_category' => $parentCategoryName,
                    'parent_category_id' => $image->parent_category_id,
                    'subcategories' => [],
                ];
            }

            $subKey = $categoryName;
            if (!isset($grouped[$groupKey]['subcategories'][$subKey])) {
                $grouped[$groupKey]['subcategories'][$subKey] = [
                    'category' => $categoryName,
                    'category_id' => $image->category_id,
                    'images' => [],
                ];
            }

            $grouped[$groupKey]['subcategories'][$subKey]['images'][] = $image;
        }

        return array_values($grouped);
    }

    /**
     * 批量更新排序
     */
    public static function updateOrder(array $orders): bool
    {
        try {
            foreach ($orders as $order) {
                self::where('id', $order['id'])->update(['sort_order' => $order['order']]);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取图片完整URL
     */
    public function getFullUrlAttribute(): string
    {
        if (str_starts_with($this->image_url, 'http')) {
            return $this->image_url;
        }
        
        $baseUrl = config('app.url', 'http://localhost:8890');
        return rtrim($baseUrl, '/') . '/' . ltrim($this->image_url, '/');
    }

    /**
     * 获取图片缩略图URL
     */
    public function getThumbnailUrlAttribute(): string
    {
        $url = $this->getFullUrlAttribute();
        $pathInfo = pathinfo($url);
        
        return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
    }

    /**
     * 删除图片文件
     */
    public function deleteFile(): bool
    {
        try {
            $publicPath = config('app.public_path', base_path() . '/public');
            $filePath = $publicPath . '/' . ltrim($this->image_url, '/');
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // 删除缩略图
            $thumbnailPath = $publicPath . '/' . ltrim(str_replace('.', '_thumb.', $this->image_url), '/');
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 批量删除图片
     */
    public static function batchDelete(array $imageIds): array
    {
        $deleted = 0;
        $errors = [];

        foreach ($imageIds as $imageId) {
            try {
                $image = self::find($imageId);
                if ($image) {
                    $image->deleteFile();
                    $image->delete();
                    $deleted++;
                }
            } catch (\Exception $e) {
                $errors[] = "删除图片失败: ID {$imageId} - {$e->getMessage()}";
            }
        }

        return [
            'deleted' => $deleted,
            'errors' => $errors,
        ];
    }
}
