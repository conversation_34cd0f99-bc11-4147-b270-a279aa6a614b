<?php

namespace app\model;

use support\Model;

/**
 * 床型类型模型
 * 基于携程开放平台床型类型标准
 */
class BedType extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'bed_types';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name',
        'name_en',
        'description',
        'description_en',
        'category',
        'size',
        'width',
        'length',
        'max_occupancy',
        'is_standard',
        'is_active',
        'sort_order',
        'icon',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'width' => 'integer',
        'length' => 'integer',
        'max_occupancy' => 'integer',
        'is_standard' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取床型类型的多语言翻译
     */
    public function translations()
    {
        return $this->hasMany(BedTypeTranslation::class);
    }

    /**
     * 获取床型类型的统计信息
     */
    public function statistics()
    {
        return $this->hasOne(BedTypeStatistics::class);
    }

    /**
     * 获取关联的房型
     */
    public function roomTypes()
    {
        return $this->belongsToMany(RoomType::class, 'room_type_bed_types')
            ->withPivot(['bed_count', 'is_primary', 'sort_order'])
            ->withTimestamps();
    }

    /**
     * 作用域：启用的床型
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：标准床型
     */
    public function scopeStandard($query)
    {
        return $query->where('is_standard', true);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：按尺寸筛选
     */
    public function scopeBySize($query, $size)
    {
        return $query->where('size', $size);
    }

    /**
     * 作用域：按最大容纳人数筛选
     */
    public function scopeByMaxOccupancy($query, $maxOccupancy)
    {
        return $query->where('max_occupancy', '>=', $maxOccupancy);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取床型分类列表
     *
     * @return array
     */
    public static function getCategories()
    {
        return [
            'standard' => '标准床',
            'sofa' => '沙发床',
            'tatami' => '榻榻米',
            'other' => '其他',
        ];
    }

    /**
     * 获取床型尺寸列表
     *
     * @return array
     */
    public static function getSizes()
    {
        return [
            'single' => '单人',
            'double' => '双人',
            'queen' => '大床',
            'king' => '特大床',
            'twin' => '双床',
        ];
    }

    /**
     * 获取床型分类名称
     *
     * @return string
     */
    public function getCategoryNameAttribute()
    {
        $categories = static::getCategories();
        return $categories[$this->category] ?? $this->category;
    }

    /**
     * 获取床型尺寸名称
     *
     * @return string
     */
    public function getSizeNameAttribute()
    {
        $sizes = static::getSizes();
        return $sizes[$this->size] ?? $this->size;
    }

    /**
     * 获取床型尺寸描述
     *
     * @return string
     */
    public function getSizeDescriptionAttribute()
    {
        $description = '';
        
        if ($this->width && $this->length) {
            $description = "{$this->width}cm × {$this->length}cm";
        }
        
        if ($this->max_occupancy) {
            $description .= $description ? " (最多{$this->max_occupancy}人)" : "最多{$this->max_occupancy}人";
        }
        
        return $description;
    }

    /**
     * 获取指定语言的名称
     *
     * @param string $locale
     * @return string
     */
    public function getLocalizedName($locale = 'zh-CN')
    {
        if ($locale === 'zh-CN') {
            return $this->name;
        }
        
        if ($locale === 'en-US' && $this->name_en) {
            return $this->name_en;
        }
        
        $translation = $this->translations()->where('locale', $locale)->first();
        return $translation ? $translation->name : $this->name;
    }

    /**
     * 获取指定语言的描述
     *
     * @param string $locale
     * @return string
     */
    public function getLocalizedDescription($locale = 'zh-CN')
    {
        if ($locale === 'zh-CN') {
            return $this->description;
        }
        
        if ($locale === 'en-US' && $this->description_en) {
            return $this->description_en;
        }
        
        $translation = $this->translations()->where('locale', $locale)->first();
        return $translation ? $translation->description : $this->description;
    }

    /**
     * 获取所有启用的床型（用于下拉选择）
     *
     * @param string $locale
     * @return array
     */
    public static function getActiveOptions($locale = 'zh-CN')
    {
        $bedTypes = static::active()->ordered()->get();
        
        return $bedTypes->map(function ($bedType) use ($locale) {
            return [
                'value' => $bedType->id,
                'label' => $bedType->getLocalizedName($locale),
                'code' => $bedType->code,
                'category' => $bedType->category,
                'category_name' => $bedType->category_name,
                'size' => $bedType->size,
                'size_name' => $bedType->size_name,
                'size_description' => $bedType->size_description,
                'max_occupancy' => $bedType->max_occupancy,
                'is_standard' => $bedType->is_standard,
                'icon' => $bedType->icon,
            ];
        })->toArray();
    }

    /**
     * 获取按分类分组的床型选项
     *
     * @param string $locale
     * @return array
     */
    public static function getGroupedOptions($locale = 'zh-CN')
    {
        $bedTypes = static::active()->ordered()->get();
        $categories = static::getCategories();
        
        $grouped = [];
        
        foreach ($categories as $categoryCode => $categoryName) {
            $categoryBedTypes = $bedTypes->where('category', $categoryCode);
            
            if ($categoryBedTypes->isNotEmpty()) {
                $grouped[] = [
                    'label' => $categoryName,
                    'options' => $categoryBedTypes->map(function ($bedType) use ($locale) {
                        return [
                            'value' => $bedType->id,
                            'label' => $bedType->getLocalizedName($locale),
                            'code' => $bedType->code,
                            'size_description' => $bedType->size_description,
                            'max_occupancy' => $bedType->max_occupancy,
                            'icon' => $bedType->icon,
                        ];
                    })->values()->toArray()
                ];
            }
        }
        
        return $grouped;
    }

    /**
     * 更新使用统计
     */
    public function updateStatistics()
    {
        $statistics = $this->statistics ?: new BedTypeStatistics(['bed_type_id' => $this->id]);
        
        $statistics->usage_count = ($statistics->usage_count ?? 0) + 1;
        $statistics->room_type_count = $this->roomTypes()->count();
        $statistics->hotel_count = $this->roomTypes()->distinct('hotel_id')->count('hotel_id');
        $statistics->last_used_at = now();
        
        $statistics->save();
    }
}
