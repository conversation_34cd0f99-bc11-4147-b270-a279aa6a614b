<?php

namespace app\model;

use support\Model;

/**
 * 酒店押金政策模型
 */
class HotelPoliciesDepositPolicy extends Model
{
    protected $table = 'hotel_policies_deposit_policies';
    protected $primaryKey = 'id';

    protected $fillable = [
        'hotel_id', 'deposit_required', 'deposit_type', 'deposit_amount', 'deposit_amount_type',
        'deposit_currency', 'deposit_collection_time', 'deposit_refund_time', 'deposit_refund_method',
        'damage_assessment_policy', 'incidental_charges_policy', 'authorization_hold', 'hold_amount',
        'deposit_notes', 'status',
    ];

    protected $casts = [
        'id' => 'integer', 'hotel_id' => 'integer', 'deposit_required' => 'boolean',
        'deposit_amount' => 'decimal:2', 'authorization_hold' => 'boolean', 'hold_amount' => 'decimal:2',
        'created_at' => 'datetime', 'updated_at' => 'datetime',
    ];

    public function hotel() { return $this->belongsTo(Hotel::class, 'hotel_id', 'id'); }
    public function scopeActive($query) { return $query->where('status', 'active'); }
    public function scopeByHotel($query, $hotelId) { return $query->where('hotel_id', $hotelId); }
    public function getPolicyType(): string { return 'deposit'; }
    public function getPolicyDisplayName(): string { return '押金政策'; }

    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId, 'deposit_required' => false, 'deposit_type' => 'cash',
            'deposit_amount_type' => 'fixed', 'deposit_currency' => 'CNY',
            'deposit_collection_time' => 'check_in', 'authorization_hold' => false, 'status' => 'active'
        ]);
    }
}
