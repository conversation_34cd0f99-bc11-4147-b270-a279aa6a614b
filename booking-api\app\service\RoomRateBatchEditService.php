<?php

namespace app\service;

use app\model\RoomRateBatchEdit;
use app\model\RoomRate;
use app\model\Hotel;
use app\model\RoomType;
use app\model\RatePlan;
use Carbon\Carbon;

/**
 * 房价批量编辑服务
 */
class RoomRateBatchEditService extends BaseService
{
    /**
     * 获取批量编辑任务列表
     *
     * @param array $params
     * @return array
     */
    public function getBatchEditList(array $params = [])
    {
        try {
            $query = RoomRateBatchEdit::with(['hotel', 'roomType', 'ratePlan', 'creator']);

            // 筛选条件
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            if (!empty($params['room_type_id'])) {
                $query->where('room_type_id', $params['room_type_id']);
            }

            if (!empty($params['rate_plan_id'])) {
                $query->where('rate_plan_id', $params['rate_plan_id']);
            }

            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            if (!empty($params['operation_type'])) {
                $query->where('operation_type', $params['operation_type']);
            }

            if (!empty($params['created_by'])) {
                $query->where('created_by', $params['created_by']);
            }

            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }

            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            // 排序
            $sortField = $params['sort_field'] ?? 'created_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $page = $params['page'] ?? 1;
            $pageSize = $params['page_size'] ?? 20;
            $result = $query->paginate($pageSize, ['*'], 'page', $page);

            return $this->success([
                'list' => $result->items(),
                'total' => $result->total(),
                'page' => $result->currentPage(),
                'page_size' => $result->perPage(),
                'total_pages' => $result->lastPage()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取批量编辑任务列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取批量编辑任务详情
     *
     * @param int $taskId
     * @return array
     */
    public function getBatchEditDetail(int $taskId)
    {
        try {
            $task = RoomRateBatchEdit::with(['hotel', 'roomType', 'ratePlan', 'creator'])
                ->find($taskId);

            if (!$task) {
                return $this->error('批量编辑任务不存在');
            }

            return $this->success($task);

        } catch (\Exception $e) {
            return $this->error('获取批量编辑任务详情失败：' . $e->getMessage());
        }
    }

    /**
     * 创建批量编辑任务
     *
     * @param array $data
     * @return array
     */
    public function createBatchEdit(array $data)
    {
        try {
            // 验证酒店是否存在
            $hotel = Hotel::find($data['hotel_id']);
            if (!$hotel) {
                return $this->error('酒店不存在');
            }

            // 验证房型（如果指定）
            if (!empty($data['room_type_id'])) {
                $roomType = RoomType::where('hotel_id', $data['hotel_id'])
                    ->where('id', $data['room_type_id'])
                    ->first();
                if (!$roomType) {
                    return $this->error('房型不存在或不属于该酒店');
                }
            }

            // 验证价格计划（如果指定）
            if (!empty($data['rate_plan_id'])) {
                $ratePlan = RatePlan::where('hotel_id', $data['hotel_id'])
                    ->where('id', $data['rate_plan_id'])
                    ->first();
                if (!$ratePlan) {
                    return $this->error('价格计划不存在或不属于该酒店');
                }
            }

            // 计算影响的日期数量
            $affectedDates = $this->calculateAffectedDates($data);
            $data['total_count'] = count($affectedDates);

            // 创建批量编辑任务
            $task = RoomRateBatchEdit::create($data);

            return $this->success($task, '批量编辑任务创建成功');

        } catch (\Exception $e) {
            return $this->error('创建批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 执行批量编辑任务
     *
     * @param int $taskId
     * @return array
     */
    public function executeBatchEdit(int $taskId)
    {
        try {
            $task = RoomRateBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('批量编辑任务不存在');
            }

            if ($task->status !== RoomRateBatchEdit::STATUS_PENDING) {
                return $this->error('任务状态不允许执行');
            }

            // 标记为处理中
            $task->markAsProcessing();

            // 获取影响的日期
            $affectedDates = $task->getAffectedDates();
            $processedCount = 0;
            $successCount = 0;
            $errorMessages = [];

            foreach ($affectedDates as $date) {
                try {
                    $this->processDateRates($task, $date);
                    $successCount++;
                } catch (\Exception $e) {
                    $errorMessages[] = "日期 {$date}: " . $e->getMessage();
                }
                
                $processedCount++;
                
                // 更新进度
                $task->updateProgress($processedCount, count($affectedDates));
            }

            // 标记完成状态
            if ($successCount === count($affectedDates)) {
                $task->markAsCompleted();
            } else {
                $task->markAsFailed(implode('; ', $errorMessages));
            }

            return $this->success([
                'task_id' => $taskId,
                'processed_count' => $processedCount,
                'success_count' => $successCount,
                'error_count' => count($errorMessages),
                'status' => $task->status
            ], '批量编辑任务执行完成');

        } catch (\Exception $e) {
            // 标记任务失败
            if (isset($task)) {
                $task->markAsFailed($e->getMessage());
            }
            return $this->error('执行批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 取消批量编辑任务
     *
     * @param int $taskId
     * @return array
     */
    public function cancelBatchEdit(int $taskId)
    {
        try {
            $task = RoomRateBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('批量编辑任务不存在');
            }

            if (!in_array($task->status, [RoomRateBatchEdit::STATUS_PENDING, RoomRateBatchEdit::STATUS_PROCESSING])) {
                return $this->error('任务状态不允许取消');
            }

            $task->markAsFailed('用户取消');

            return $this->success(null, '批量编辑任务已取消');

        } catch (\Exception $e) {
            return $this->error('取消批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 获取批量编辑进度
     *
     * @param int $taskId
     * @return array
     */
    public function getBatchEditProgress(int $taskId)
    {
        try {
            $task = RoomRateBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('批量编辑任务不存在');
            }

            return $this->success([
                'task_id' => $taskId,
                'status' => $task->status,
                'status_name' => $task->status_name,
                'processed_count' => $task->processed_count,
                'total_count' => $task->total_count,
                'progress_percentage' => $task->progress_percentage,
                'error_message' => $task->error_message,
                'created_at' => $task->created_at,
                'processed_at' => $task->processed_at
            ]);

        } catch (\Exception $e) {
            return $this->error('获取批量编辑进度失败：' . $e->getMessage());
        }
    }

    /**
     * 预览批量编辑影响
     *
     * @param array $data
     * @return array
     */
    public function previewBatchEdit(array $data)
    {
        try {
            // 计算影响的日期
            $affectedDates = $this->calculateAffectedDates($data);
            
            // 获取当前价格数据
            $query = RoomRate::where('hotel_id', $data['hotel_id'])
                ->whereIn('date', $affectedDates);

            if (!empty($data['room_type_id'])) {
                $query->where('room_type_id', $data['room_type_id']);
            }

            if (!empty($data['rate_plan_id'])) {
                $query->where('rate_plan_id', $data['rate_plan_id']);
            }

            $currentRates = $query->get();

            // 计算预览数据
            $preview = [];
            foreach ($currentRates as $rate) {
                $originalPrice = $rate->price;
                $newPrice = $this->calculateNewPrice($originalPrice, $data);
                
                $preview[] = [
                    'date' => $rate->date,
                    'room_type_id' => $rate->room_type_id,
                    'rate_plan_id' => $rate->rate_plan_id,
                    'original_price' => $originalPrice,
                    'new_price' => $newPrice,
                    'change_amount' => $newPrice - $originalPrice,
                    'change_percentage' => $originalPrice > 0 ? round((($newPrice - $originalPrice) / $originalPrice) * 100, 2) : 0
                ];
            }

            return $this->success([
                'affected_dates_count' => count($affectedDates),
                'affected_rates_count' => count($preview),
                'preview_data' => $preview,
                'summary' => [
                    'total_dates' => count($affectedDates),
                    'total_rates' => count($preview),
                    'operation_type' => $data['operation_type'],
                    'adjustment_value' => $data['price_adjustment'] ?? $data['percentage_adjustment'] ?? 0
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error('预览批量编辑影响失败：' . $e->getMessage());
        }
    }

    /**
     * 删除批量编辑任务
     *
     * @param int $taskId
     * @return array
     */
    public function deleteBatchEdit(int $taskId)
    {
        try {
            $task = RoomRateBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('批量编辑任务不存在');
            }

            if ($task->status === RoomRateBatchEdit::STATUS_PROCESSING) {
                return $this->error('正在处理中的任务不能删除');
            }

            $task->delete();

            return $this->success(null, '批量编辑任务删除成功');

        } catch (\Exception $e) {
            return $this->error('删除批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 获取批量编辑统计信息
     *
     * @param array $params
     * @return array
     */
    public function getBatchEditStatistics(array $params = [])
    {
        try {
            $query = RoomRateBatchEdit::query();

            // 时间范围
            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }
            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            // 酒店筛选
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            $statistics = [
                'total_tasks' => $query->count(),
                'pending_tasks' => $query->where('status', RoomRateBatchEdit::STATUS_PENDING)->count(),
                'processing_tasks' => $query->where('status', RoomRateBatchEdit::STATUS_PROCESSING)->count(),
                'completed_tasks' => $query->where('status', RoomRateBatchEdit::STATUS_COMPLETED)->count(),
                'failed_tasks' => $query->where('status', RoomRateBatchEdit::STATUS_FAILED)->count(),
            ];

            // 按操作类型统计
            $operationStats = $query->selectRaw('operation_type, COUNT(*) as count')
                ->groupBy('operation_type')
                ->pluck('count', 'operation_type')
                ->toArray();

            $statistics['operation_stats'] = $operationStats;

            return $this->success($statistics);

        } catch (\Exception $e) {
            return $this->error('获取批量编辑统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 重试失败的批量编辑任务
     *
     * @param int $taskId
     * @return array
     */
    public function retryBatchEdit(int $taskId)
    {
        try {
            $task = RoomRateBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('批量编辑任务不存在');
            }

            if ($task->status !== RoomRateBatchEdit::STATUS_FAILED) {
                return $this->error('只能重试失败的任务');
            }

            // 重置任务状态
            $task->update([
                'status' => RoomRateBatchEdit::STATUS_PENDING,
                'processed_count' => 0,
                'error_message' => null,
                'processed_at' => null
            ]);

            return $this->success($task, '任务已重置，可以重新执行');

        } catch (\Exception $e) {
            return $this->error('重试批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 计算影响的日期
     *
     * @param array $data
     * @return array
     */
    private function calculateAffectedDates(array $data)
    {
        $dates = [];
        $current = Carbon::parse($data['start_date']);
        $end = Carbon::parse($data['end_date']);

        while ($current <= $end) {
            // 检查是否在包含日期中（如果设置了）
            if (!empty($data['include_dates']) && !in_array($current->toDateString(), $data['include_dates'])) {
                $current = $current->addDay();
                continue;
            }

            // 检查是否在排除日期中
            if (!empty($data['exclude_dates']) && in_array($current->toDateString(), $data['exclude_dates'])) {
                $current = $current->addDay();
                continue;
            }

            // 检查星期限制
            if (!empty($data['days_of_week']) && !in_array($current->dayOfWeek, $data['days_of_week'])) {
                $current = $current->addDay();
                continue;
            }

            $dates[] = $current->toDateString();
            $current = $current->addDay();
        }

        return $dates;
    }

    /**
     * 处理指定日期的价格
     *
     * @param RoomRateBatchEdit $task
     * @param string $date
     * @return void
     */
    private function processDateRates(RoomRateBatchEdit $task, string $date)
    {
        $query = RoomRate::where('hotel_id', $task->hotel_id)
            ->where('date', $date);

        if ($task->room_type_id) {
            $query->where('room_type_id', $task->room_type_id);
        }

        if ($task->rate_plan_id) {
            $query->where('rate_plan_id', $task->rate_plan_id);
        }

        $rates = $query->get();

        foreach ($rates as $rate) {
            $newPrice = $task->calculateAdjustedPrice($rate->price);
            $rate->update(['price' => $newPrice]);
        }
    }

    /**
     * 计算新价格
     *
     * @param float $originalPrice
     * @param array $data
     * @return float
     */
    private function calculateNewPrice(float $originalPrice, array $data)
    {
        switch ($data['operation_type']) {
            case 'set':
                $newPrice = $data['price_adjustment'];
                break;
            case 'increase':
                $newPrice = $originalPrice + $data['price_adjustment'];
                break;
            case 'decrease':
                $newPrice = $originalPrice - $data['price_adjustment'];
                break;
            case 'percentage':
                $newPrice = $originalPrice * (1 + $data['percentage_adjustment'] / 100);
                break;
            default:
                $newPrice = $originalPrice;
        }

        // 应用价格限制
        if (!empty($data['min_price']) && $newPrice < $data['min_price']) {
            $newPrice = $data['min_price'];
        }

        if (!empty($data['max_price']) && $newPrice > $data['max_price']) {
            $newPrice = $data['max_price'];
        }

        return round($newPrice, 2);
    }
}
