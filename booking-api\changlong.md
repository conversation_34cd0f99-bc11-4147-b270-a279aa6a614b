长隆集团酒店接口规范
长隆酒店接口规范
项目名称：TA/OTA 接口对接
文件编号：
【广东长隆集团有限公司】
1 / 73
长隆集团酒店接口规范
2 / 73
目录
文档历史记录 3
1 引言 8
2 范围 8
1) 术语解释 8
2) 接口应用范围 8
   3 系统功能 8
1) 对接双方约定 8
2) 交易流程 9
   4 公共接口说明 11
1) 接口协议和编码 11
2) OTA 信息 11
3) 公共请求参数 11
4) 公共响应参数 12
5) 公共错误码 12
6) 业务错误码 12
7) 请求和响应参数加密算法 12
   5 酒店信息接口 14
1) 拉取酒店信息（chimelong.hotel.hotel.info.get） 14
2) 拉取酒店餐厅信息（chimelong.hotel.restaurant.info.get） 19
   6 产品信息接口 22
1) 拉取协议信息（chimelong.hotel.proxy.info.get） 22
2) 拉取产品信息（chimelong.hotel.product.info.get） 25
3) 拉取产品价格日历（chimelong.hotel.product.price.get） 37
4) 拉取产品库存日历（chimelong.hotel.product.stock.get） 42
5) 产品变化通知（chimelong.hotel.product.change.notice） 47
   7 订单信息接口 49
1) 订单验证（chimelong.hotel.order.verify） 49
2) 订单创建（chimelong.hotel.order.create） 52
3) 订单关闭（chimelong.hotel.order.close） 57
4) 订单支付（chimelong.hotel.order.pay） 57
5) 订单出确认码通知（chimelong.hotel.order.voucher.notice） 57
6) 订单取消（chimelong.hotel.order.cancel） 59
7) 订单修改（chimelong.hotel.order.change） 60
8) 订单查询（chimelong.hotel.order.query） 66
   8 辅助接口 API 68
1) 查询账户余额（chimelong.hotel.support.balance.query） 68
   9 附录 70
1) 证件类型定义 70
2) Region 定义 70
3) 错误码 70
   长隆集团酒店接口规范
   3 / 73
   文档历史记录
   编号与名称 版本 日期 编写者 修改说明
   长隆酒店接口规范 V1.0 2018/7/22 WXC 创建接口规范
   长隆酒店接口规范 V1.1 2018/8/6 WXC 修改接口定义和签名加密方法
   长隆酒店接口规范 V1.2 2018/8/6 WXC 增加响应示例
   部分属性支持国际化调整
   产品补充组合属性
   长隆酒店接口规范 V1.5 2018/8/17 理想 调整接口内容
   长隆酒店接口规范 V1.6 2018/9/3 理想 1、调整签名规则 2、增加订单
   验证、调整订单创建接口
   长隆酒店接口规范 V1.7 2018/9/13 理想 1、修改订单的价格数据结构。
   2、修改产品的餐的定义。
   3、修改产品的退改规则。
   长隆酒店接口规范 V1.8 2018/9/29 理想 1、修改一些接口的字段取值。
   长隆酒店接口规范 V1.9 2018/10/11 理想 1、修改可定状态检查接口，增
   加返回值。
   2、
   长隆酒店接口规范 V2.0 2018/10/24 吴烨烨 1、修改产品价格和产品库存接口数据格
   式
   2、去掉修改订单通知接口
   3、修改订单接口去掉 changeId 字段
   长隆集团酒店接口规范
   4 / 73
   长隆酒店接口规范 V2.1 2018/11/21 吴烨烨 1、产品接口中增加
   canBuyOnday 字段，定义是否当
   日可卖
   2、创建订单接口中，产品数据
   中增加 entryNumber 字段，用于
   指定订单行，若接修改订单接口
   必填
   3、修改订单接口，产品数据中
   增加 entryNumber 字段，必填。
   4、修改订单接口，增加描述。
   长隆酒店接口规范 V2.2 2018/12/3 吴烨烨 1、修改订单查询接口中订单状
   态的定义
   长隆酒店接口规范 V2.3 2018/12/21 吴烨烨 1、酒店订单允许修改联系人信
   息，增加相关字段
   长隆酒店接口规范 V2.4 2019/01/06 吴烨烨 1、接口及参数增加一些说明
   2、补充详细的错误码
   长隆酒店接口规范 V2.5 2019/01/20 吴烨烨 1、业务规定，酒店产品也不可
   以修改联系人，去掉修改接口中
   相关参数。
   2、酒店订单中，房包票产品的
   开始结束时间要一致
   长隆酒店接口规范 V2.6 2019/02/01 吴烨烨 1、调整拉取产品信息接口，增
   加加购产品的查询参数和产品属
   性返回值
   2、验证接口中联系人和入住人
   信息可为非必填
   长隆酒店接口规范 V2.7 2019/02/26 吴烨烨 调整文档文字描述、一些字段的
   必填属性及接口数据示例
   长隆集团酒店接口规范
   5 / 73
   长隆酒店接口规范 V2.8 2019/02/27 吴烨烨 1、调整交易流程图
   2、拉取产品信息接口增加
   eatTimeType 字段的枚举值
   长隆酒店接口规范 V2.9 2019/03/28 吴烨烨 增加一些接口说明信息及双方约
   定。
   长隆酒店接口规范 V3.0 2019/04/02 吴烨烨 产品信息接口中增加字段：是否
   是促销产品
   长隆酒店接口规范 V3.1 2019/04/04 吴烨烨 产品信息接口中增加字段：房型
   编号、床型编号
   长隆酒店接口规范 V3.2 2019/04/08 吴烨烨 产品信息接口中增加字段：
   region—产品所属区域
   长隆酒店接口规范 V3.3 2019/04/12 吴烨烨 增加证件类型-回乡证
   长隆酒店接口规范 V3.4 2019/04/19 朱佳慧 1. 增加代理商协议接口
2. 增加拉取产品信息、价格、
   库存接口调用频次限制，以
   及调用标准
3. 订单出确认凭证码接口说明
   修改
4. 订单取消接口说明修改
5. 订单修改接口说明修改
   长隆酒店接口规范 V3.5 2019/04/23 朱佳慧 修改拉取协议信息借楼，增加
   isDeBlock 字段，标识协议是否
   可以扣减配额数量, needFirst 是否
   必须优先售卖
   长隆酒店接口规范 V3.6 长隆酒店接口
   规范
   吴烨烨 1、协议接口增加请求参数：
   purchaseType
   2、产品变化通知接口增加参数
   ：productCategory
   长隆酒店接口规范 V3.7 2019/04/26 朱佳慧 1、协议接口优先级控制字
   段&状态字段描述更新
   长隆酒店接口规范 V3.8 2019/05/04 吴烨烨 协议接口返回协议加购类型字段:
   purchaseType
   长隆集团酒店接口规范
   6 / 73
   长隆酒店接口规范 V3.9 2019/05/22 吴烨烨 1、若某天因为关房或禁售等操作，无价
   格时，settlePrice 返回-1
   2、双方约定：当价格大于 0 且库存大于
   0，两者都满足时，产品才可售卖
   长隆酒店接口规范 V4.0 2019/06/26 吴烨烨 创建订单接口增加姓名拆分的规则说明
   长隆酒店接口规范 V4.1 2019/07/18 吴烨烨 调整库存变化的推送机制描述
   长隆酒店接口规范 V4.2 2019/09/17 赵亚平 1、 拉取协议信息增加协议开始时间字
   段；
   2、 餐饮分时段，增加 0 类型。
   长隆酒店接口规范 V4.3 2019/09/24 赵亚平 订单校验和创建接口增加随行人数据
   长隆酒店接口规范 V4.4 2019/10/21 叶子元 产品信息接口新增房包票产品的包价子
   产品售卖起始时间,结束时间和相应的售
   卖周控时间策略等
   长隆酒店接口规范 V4.5 2020/05/28 彭文迪 拉取产品信息接口，增加协议产品开始
   售卖时间
   长隆酒店接口规范 V4.6 2020/05/26 赵亚平 增加错误码说明
   长隆酒店接口规范 v4.9 2020/08/23 唐石彪 修改证件类型名称
   长隆酒店接口规范 V5.0 2023/05/24 唐石彪 修改证件类型名称：
   （回乡证--港澳居民来往内地通行证
   台胞证--台湾居民来往大陆通行证）
   新增证件类型：
   （外国人永久居留身份证）
   长隆酒店接口规范 V5.1 2023/07/27 唐石彪 增加错误码说明
   ERROR-ORD-134 身份证信息不合法
   长隆集团酒店接口规范
   7 / 73
   长隆酒店接口规范 V5.2 2023/12/28 唐石彪 1、拉取产品信息接口，packageInfo
   数据增加“是否每晚赠送（isNightDay）
   ”字段；
   2、拉 取 产 品 信 息 接 口 ，
   productDescription 字段增加描述内容；
   3、拉取产品信息接口，ticketData
   数据增加“房包票数量（ticketNum）”
   字段
   长隆酒店接口规范 V5.3 2024/5/29 赵亚平 订单创建接口，返回结果增加亲子秀产
   品 的 观 演 时 间 字 段
   （secondCriusUseTime）。
   长隆酒店接口规范 V5.4 2024/6/5 赵亚平 订单创建接口，返回结果中的亲子秀产
   品的观演时间字段修改。
   长隆门票接口规范 V5.5 2025/02/25 唐石彪 增加错误码说明：
   ERROR-ORD-010
   当前代理商创建订单限制中
   长隆集团酒店接口规范
   8 / 73
   1 引言
   2 范围
1) 术语解释
    长隆：本文档出现的供应商即指长隆。
    Price 类型：接口中出现的类型为 Price，表示精确到 2 位小数的数字。
    Localized 类型：接口中出现的类型为 Localized，表示多语言字符类型，在本文中的接口示例，该类型
   字段可能使用 String 显示。
   格式如下：
   "name": {
   "en": "parasis adult weekendsday ticket",
   "zh": "欢乐世界成人特定日全票",
   "zh_TW": "歡樂世界成人特定日全票"
   }
    保留字段：接口中标识位保留字段的参数，表示当前不会返回该字段，仅作为以后的扩展使用。
2) 接口应用范围
    长隆集团所有区域（广州、珠海及以后清远等）的酒店 OTA 对接均按本规范进行。
   3 系统功能
1) 对接双方约定
   【产品相关】
    产品、价格信息的变化会通过“产品变化通知”接口通知 OTA 拉取；
    拉取产品价格和库存时，日期范围≤60 天；
    产品库存变化的推送机制为跳跃式推送，20->9->5->0，即库存剩余 20 会推送一次，剩余 9 会推一次，
   剩余 5 会推一次，剩余 0 再推送一次，且推送为并发推送。OTA 可根据平台自身需要进行拉取；
    保留房和非保留房会以独立产品形式推送，一个产品只有一种库存类型
    当价格大于 0 且库存大于 0，两者都满足时，产品才可售卖
   【订单相关】
    为避免游客在 OTA 下单和支付成功但长隆订单创建失败，所有订单采用 2 步下单的方式，在申请创建
   长隆订单成功后再下 OTA 订单并调用支付接口
    未支付订单游客主动取消时，由 OTA 发起调用长隆订单关闭接口。
   长隆在一定时间（酒餐产品 15 分钟，酒店产品 60 分钟）后自动取消，并解锁库存、预付款资金占用
   等。
   长隆集团酒店接口规范
   9 / 73
    在产品属性中【产品的加购类型】为“加购”的商品不能单独下单，需与其他“非加购”产品合并下
   单；
    产品订单创建时，如预订多个房间则需要多个客人姓名和身份证号，一间房需一个入住人信息。
    订单扣款时，会自动按订购产品所属区域的财务账户来扣除，一个订单中支持包含多个区域的产品。
    下单成功后异步回传确认码，一个入住人一个凭证码
    一个订单可以有多个产品。
    订单支持整单取消和按订单行退改，具体退改原则按产品退改规则进行。
    订单下单后符合取消规则可以整单取消，同步返回取消结果及收取的手续费。
    订单下单后符合修改规则可以修改，同步返回修改结果及收取的手续费。
    订单修改仅可以修改入住人信息。
    订单下每个产品（订单行）修改只能 1 次。（具体的修改规则根据业务沟通的结果为准）
   【其他】
    OTA 提供一个通知地址，用于接收产品变化通知和出凭证码通知接口。通过 method 可判断分别是哪
   个接口。
    幂等：1）创建订单：若传来的 ota 订单号相同，则不会重新创建订单。例如 ota 订单号 A001 创建了
   长隆订单 CL001，创建成功。则第二次继续用 ota 订单号 A001 调用创建订单接口，返回的还是长隆订
   单 CL001。2）支付订单和取消订单等：若已经支付或已经取消的订单，再调接口的话，不会重复扣钱
   或退钱。
2) 交易流程
   长隆集团酒店接口规范
   10 / 73
   OTA订单流转
   客人 代理商平台（如：携程） 供应商系统（长隆）
   支付订单（预存款扣款）
   请求代理商
   支付 下OTA订单
   选择产品下
   单
   申请订单取
   消
   向供应商发
   起取消
   供应商订单
   取消
   接收并处理
   取消信息
   发起通知
   查询供应商
   订单信息
   查询供应商
   订单信息
   成功 接收结果
   确认改变
   接收取消信
   息
   状态改变（取
   票）
   返回订单信息
   检验参数是否正确（价
   格、库存等）
   请求供应商
   校验订单
   供应商下
   单成功？
   是
   下单失败 否
   成功返回订单号（未支
   付）或者失败返回失败原
   因
   通知用户
   订单出票通知
   （兑换凭证码）
   成功？
   取消订单
   通知用户 是
   否
   申请订单修
   改
   向供应商发
   起修改
   供应商订单
   修改
   接收并处理
   修改信息
   接收修改信
   息
   返回校验结果，若失败会
   返回正确的价格和库存
   请求供应商
   创建订单
   检验参数是否正确（价
   格、库存等），并锁定库
   存、资金等
   供应商校验订
   单成功？
   是
   否
   代理商调整
   下单数据
   订单下单（2步下单） 订单核销 订单取消 订单查询 订单出兑换凭证 订单修改
   长隆集团酒店接口规范
   11 / 73
   4 公共接口说明
1) 接口协议和编码
   采用 HTTP 协议的 POST 方式，编码格式统一为 UTF-8。
2) OTA 信息
   长隆分配给 OTA 的信息：
   参数 名称 描述
   mer_no 商户 ID 用于识别请求的 OTA
   key 加密 key 用于数据传输数据加密，OTA 需要妥善保管
   chimeLongPublicKey 长隆公钥 用于数据签名，OTA 需要妥善保管
   OTA 分配给长隆的信息：
   参数 名称 描述
   otaPublicKey ota 公钥 用于数据签名
3) 公共请求参数
   参数 类型 是否必填 最大长度 描述 示例值
   mer_no String 是 32 长隆分配给 OTA 的商户 ID 0001
   method String 是 128 接口名称 chimelong.trip.ord
   er.create
   sign String 是 32 OTA 请求的签名串
   nonce_str String 是 32 随机字符串，推荐使用guid生成。
   该字段用于幂等。
   所有接口使用该参数幂等，若
   OTA 需要根据业务 Id 幂等，需要
   保证业务 Id 与该参数值始终一
   一对应。【例如：同一订单号在
   创建订单、取消订单、查询订单
   等订单相关操作中，该参数不能
   重复，否则就会进入幂等】
   timestamp String 是 19 发送请求的时间 格式为 yyyy�MM-dd HH:mm:ss
   东八区时区。
   2014-07-24
   03:07:50
   ver_no String 是 3 调用的接口版本，固定为 1.0 1.0
   body String 是 请求参数的 JSON 内容，最大长
   度不限，除公共参数外所有请求
   参数都必须放到这个参数中传
   递
   若 body 没有数据，
   则返回空字符串。
   长隆集团酒店接口规范
   12 / 73
4) 公共响应参数
   参数 类型 是否必填 最大长度 描述 示例值
   code String 是 返回码，请见相关内容
   msg String 是 返回码描述，请见相关内容 签名错误
   sign String 是 32 OTA 请求的签名串
   nonce_str String 是 32 随机字符串，使用 guid 生成，用
   于签名。
   body String 是 响应参数的 JSON 加密后内容，
   最大长度不限，除公共参数外所
   有响应参数都放到这个参数中
   传递
   若 body 没有数据，
   则返回空字符串。
5) 公共错误码
   code msg 解决方案
   10000 成功
   10001 缺少请求参数【xxx】
   10002 请求参数【xxx】无效
   10003 OTA 未授权当前接口
   10004 签名错误
   10005 数据解密失败
   10006 非法时间戳 检查时间是否与当前时间
   相差太多
   10007 未解析出 OTA 账号
   10099 服务暂不可用 稍后重试
   1111 接口请求已达上限 联系长隆技术，优化请求
   方式
6) 业务错误码
   见附录。
7) 请求和响应参数加密算法
   长隆集团酒店接口规范
   13 / 73
   (1) 生成签名，使用各自语言对应的 SHA256WithRSA 签名函数利用发送方私钥对待签名字符串进行
   签名，并进行 Base64 编码。
   请求签名字符串：mer_no+method+nonce_str+timestamp+ver_no+body
   响应签名字符串：code+msg+nonce_str+body
   (2) 传输数据加密：AES 算法 ECB 方式加密，密钥长度 128 位，块长 128 位。
   对加密 key 取 16 位 MD5。
   请求要加密的数据（JSON 格式），body 里为字符串：
   {
   "method": "chimelong.trip.park.info.get",
   "sign": "xxxxx",
   "nonce_str": "b734e97d74404d16b3b33d462a932ab9",
   "timestamp": "2018-08-05 22:12:00",
   "ver_no": "1.0"
   "body": "{.....}"
   }
   加密后数据：
   Djksfk2432sdfs…..
   传输数据：
   {
   "mer_no": "xxxx",
   "data": " Djksfk2432sdfs….."
   }
   响应数据（JSON 格式）：
   {
   "code": "10000",
   "msg": "xxxx",
   AES加密
   （BASE64编码）
   SHA256WithRSA签名
   （BASE64编码）
   传输内容形成签
   名字符串 使用请求方私钥 传输内容和签名加密
   AES解密
   （BASE64编码）
   SHA256WithRSA验签
   （BASE64编码）
   接收内容 传输内容和签名解密 使用请求方公钥
   长隆集团酒店接口规范
   14 / 73
   "sign": "xxxxx",
   "nonce_str": "531c1de4ba9f48d88b8f1f829b5b5f67",
   "body: "{.....}"
   }
   加密后数据：
   Djksfk2432sdfs…..
   传输数据：
   Djksfk2432sdfs…..
   (3) 发送请求
   将加密后的放到 http body 流和 response 流中发送。
   (4) 接收方得到数据后先解密，然后使用发送方公钥验签，过程与上述相反，在此不再赘述。
   5 酒店信息接口
1) 拉取酒店信息（chimelong.hotel.hotel.info.get）
   接口名 chimelong.hotel.hotel.info.get
   类型 OTA ->长隆
   场景
   OTA 获取长隆酒店信息
   说明
   调用频率：建议每天凌晨更新一次
   输入
   参数 类型 是 否
   必填
   最 大
   长度
   描述 示例值
   无
   输出
   参数 类型 是 否
   必填
   最 大
   长度
   描述 示例值
   hotelData Object[] 是 酒店对象
   hotelData 数据结构
   hotelCode String 是 酒店 code
   hotelName Localized 是 酒店名称（支持国际
   化）
   address Localized 是 地址（支持国际化）
   phone String 是 联系电话
   regionId Integer 是 区域 ID
   starLevel Integer 否 星级，酒店星级,参数
   为(1 至 5)
   长隆集团酒店接口规范
   15 / 73
   operatingStatus String 否 运 营 状 态 ，
   PREPARE/PRESALE/O
   PEN/CLOSE,
   (PRESALE/OPEN 可售,
   :PREPARE/CLOSE不可
   售)
   Intitude String 否 经度 保留字段
   latitude String 否 纬度 保留字段
   openDate String 否 开业时间，格式：
   yyyy-MM-dd
   保留字段
   themeTag Integer 否 酒店主题标签
   0：购物便捷
   1：培训学习
   2：蜜月出行
   3：休闲情调
   4：交通便利
   5：离医院近
   6：商旅之家
   7：四合院
   8：园林庭院
   9：安静优雅
   10：特色建筑
   11：周边美景
   12：家有萌宠
   13：文艺范儿
   14：观景露台
   15：古色古香
   保留字段
   decorationDate String 否 装修时间，格式：
   yyyy-MM-dd
   保留字段
   officialBrand String 否 官方品牌
   以下为扩展属性
   保留字段
   floorNum Integer 否 楼高层数 保留字段
   roomNum Integer 否 房间总数 保留字段
   foregiftType Integer 否 押金类型
   0：入住需要押金，金
   额以前台为准
   1：入住需要押金，每
   间房固定押金
   2：入住不需要押金
   保留字段
   poiType String 否 酒店类型
   0：经济型
   1：快捷酒店
   2：商务酒店
   3：主题酒店
   保留字段
   长隆集团酒店接口规范
   16 / 73
   4：情侣酒店
   5：公寓
   6：客栈
   7：民宿
   8：青年旅社
   9：农家院
   10：家庭旅馆
   11：招待所
   12：度假酒店
   13：别墅
   wifi Integer 否 wifi，1：有，0：无 保留字段
   chineseRestaurant Integer 否 中式餐厅，1：有，0：
   无
   保留字段
   westernRestaurant Integer 否 西式餐厅，1：有，0：
   无
   保留字段
   deskSafe Integer 否 前台保险柜，1：有，
   0：无
   保留字段
   teaRoom Integer 否 茶室，1：有，0：无 保留字段
   cafe Integer 否 咖啡厅，1：有，0：
   无
   保留字段
   bar Integer 否 酒吧，1：有，0：无 保留字段
   businessCenter Integer 否 商务中心，1：有，0：
   无
   保留字段
   banquetHall Integer 否 宴会厅，1：有，0：
   无
   保留字段
   meetingRoom Integer 否 会议室，1：有，0：
   无
   保留字段
   fitnessRoom Integer 否 健身中心，1：有，0：
   无
   保留字段
   parking Integer 否 停车场，1：有，0：
   无
   保留字段
   luggageStorage Integer 否 行李寄存，1：有，0：
   无
   保留字段
   wakeUpCall Integer 否 叫醒服务，1：有，0：
   无
   保留字段
   deliverMeal Integer 否 送餐服务，1：有，0：
   无
   保留字段
   cardCharge Integer 否 信用卡/银联卡收费，
   1：有，0：无
   保留字段
   laundry Integer 否 洗衣服务，1：有，0：
   无
   保留字段
   twentyFourHour Integer 否 24 小时前台接待服
   务，1：有，0：无
   保留字段
   pickUp Integer 否 接送机服务，1：有， 保留字段
   长隆集团酒店接口规范
   17 / 73
   0：无
   petAllow Integer 否 宠物携带，1：有，0：
   无
   保留字段
   regionType Integer 否 区域，1：内地 2：海
   外
   保留字段
   响应示例
   {
   "hotelData": [
   {
   "hotelCode": "GZH001",
   "hotelname": {
   "en": "Chimelong Hotel Guangzhou",
   "zh": "广州长隆酒店",
   "zh_TW": "廣州長隆酒店"
   },
   "address": {
   "en": "Chimelong Tourist Resort Area,Panyu Avenue ,Panyu District,Guangzhou,P.R China",
   "zh": "中国广东省广州市番禺区番禺大道长隆旅游度假区内",
   "zh_TW": "中國廣東省廣州市番禺區番禺大道長隆旅遊度假區內"
   },
   "phone": "(020)84722888",
   "regionId": "GZ",
   "starLevel": 5,
   "operatingStatus": "OPEN",
   "openDate": "",
   "longitude": "",
   "latitude": "",
   "themeTag": null,
   "decorationDate": "",
   "officialBrand": "",
   "floorNum": null,
   "roomNum": null,
   "foregiftType": null,
   "poiType": "",
   "wifi": null,
   "chineseRestaurant": null,
   "westernRestaurant": null,
   "deskSafe": null,
   "teaRoom": null,
   "cafe": null,
   "bar": null,
   "businessCenter": null,
   "banquetHall": null,
   "meetingRoom": null,
   长隆集团酒店接口规范
   18 / 73
   "fitnessRoom": null,
   "parking": null,
   "luggageStorage": null,
   "wakeUpCall": null,
   "deliverMeal": null,
   "cardCharge": null,
   "laundry": null,
   "twentyFourHour": null,
   "pickUp": null,
   "petAllow": null,
   "regionType": null
   },
   {
   "hotelCode": "GZH002",
   "hotelname": {
   "en": "Panda Hotel Guangzhou",
   "zh": "广州熊猫酒店",
   "zh_TW": "廣州長隆酒店"
   },
   "address": {
   "en": "Chimelong Tourist Resort Area,Panyu Avenue ,Panyu District,Guangzhou,P.R China",
   "zh": "中国广东省广州市番禺区番禺大道长隆旅游度假区内",
   "zh_TW": "中國廣東省廣州市番禺區番禺大道長隆旅遊度假區內"
   },
   "phone": "(020)84722888",
   "regionId": "GZ",
   "starLevel": 5,
   "operatingStatus": "OPEN",
   "openDate": "",
   "longitude": "",
   "latitude": "",
   "themeTag": null,
   "decorationDate": "",
   "officialBrand": "",
   "floorNum": null,
   "roomNum": null,
   "foregiftType": null,
   "poiType": "",
   "wifi": null,
   "chineseRestaurant": null,
   "westernRestaurant": null,
   "deskSafe": null,
   "teaRoom": null,
   "cafe": null,
   长隆集团酒店接口规范
   19 / 73
   "bar": null,
   "businessCenter": null,
   "banquetHall": null,
   "meetingRoom": null,
   "fitnessRoom": null,
   "parking": null,
   "luggageStorage": null,
   "wakeUpCall": null,
   "deliverMeal": null,
   "cardCharge": null,
   "laundry": null,
   "twentyFourHour": null,
   "pickUp": null,
   "petAllow": null,
   "regionType": null
   }
   ]
   }
2) 拉取酒店餐厅信息（
   chimelong.hotel.restaurant.info.get）
   接口名 chimelong.hotel.restaurant.info.get
   类型 OTA ->长隆
   场景
   OTA 拉取长隆酒店餐厅信息
   说明
   调用频率：建议每天凌晨更新一次
   输入
   参数 类型 是否
   必填
   最 大
   长度
   描述 示例值
   hotelCode String 是
   输出
   参数 类型 是否
   必填
   最 大
   长度
   描述 示例值
   hotelRestaurantD
   ata
   Object[] 酒店餐厅
   hotelRestaurantData 数据结构
   hotelRestaurantC
   ode
   String 是 酒店餐厅代码
   name String 是 酒店餐厅名称
   description String 否 酒店餐厅描述
   hotelCode String 是 所在酒店代码
   长隆集团酒店接口规范
   20 / 73
   openTime String 否 酒店餐厅营业时间
   address String 否 酒店地址
   operatingStatus String 否 运 营 状 态 ，
   PREPARE/PRESALE/OPE
   N/CLOSE
   (PRESALE/OPEN 可售,
   PREPARE/CLOSE不可售)
   响应示例
   {
   "hotelRestaurants": [
   {
   "address": {
   "en": "Chimelong Tourist Resort Area,Panyu Avenue ,Panyu District,Guangzhou,P.R China",
   "zh": "广州市番禺区番禺大道长隆旅游度假区",
   "zh_TW": "廣州市番禺區番禺大道長隆旅遊度假區"
   },
   "hotelRestaurantCode": "CL001",
   "description": {
   "en": "Unicorn Restaurant",
   "zh": "麒麟中餐厅",
   "zh_TW": "麒麟中餐廳"
   },
   "hotelCode": "gzcljd",
   "name": {
   "en": "Unicorn Restaurant",
   "zh": "麒麟中餐厅",
   "zh_TW": "麒麟中餐廳"
   },
   "openTime": "08:00-11:00,11:00-14:30,17:00-21:00",
   "operatingStatus": "PREPARE"
   },
   {
   "address": {
   "en": "Chimelong Tourist Resort Area,Panyu Avenue ,Panyu District,Guangzhou,P.R China",
   "zh": "广州市番禺区番禺大道长隆旅游度假区",
   "zh_TW": "廣州市番禺區番禺大道長隆旅遊度假區"
   },
   "hotelRestaurantCode": "CL004",
   "description": {
   "en": "Teppanyaki Restaurant",
   "zh": "怀石料理",
   "zh_TW": "懷石料理"
   },
   "hotelCode": "gzcljd",
   长隆集团酒店接口规范
   21 / 73
   "name": {
   "en": "Teppanyaki Restaurant",
   "zh": "怀石料理",
   "zh_TW": "懷石料理"
   },
   "openTime": "11:30-14:30,17:00-22:30",
   "operatingStatus": "PREPARE"
   },
   {
   "address": {
   "en": "Chimelong Tourist Resort Area,Panyu Avenue ,Panyu District,Guangzhou,P.R China",
   "zh": "广州市番禺区番禺大道长隆旅游度假区",
   "zh_TW": "廣州市番禺區番禺大道長隆旅遊度假區"
   },
   "hotelRestaurantCode": "CL006",
   "description": {
   "en": "Petrus Grill Room",
   "zh": "帕图斯扒房",
   "zh_TW": "帕圖斯扒房"
   },
   "hotelCode": "gzcljd",
   "name": {
   "en": "Petrus Grill Room",
   "zh": "帕图斯扒房",
   "zh_TW": "帕圖斯扒房"
   },
   "openTime": "17:30-23:00",
   "operatingStatus": "PREPARE"
   },
   {
   "address": {
   "en": "Chimelong Tourist Resort Area,Panyu Avenue ,Panyu District,Guangzhou,P.R China",
   "zh": "广州市番禺区番禺大道长隆旅游度假区",
   "zh_TW": "廣州市番禺區番禺大道長隆旅遊度假區"
   },
   "hotelRestaurantCode": "CL002",
   "description": {
   "en": "White Tiger Restaurant",
   "zh": "白虎自助餐厅",
   "zh_TW": "白虎自助餐廳"
   },
   "hotelCode": "gzcljd",
   "name": {
   "en": "White Tiger Restaurant",
   长隆集团酒店接口规范
   22 / 73
   "zh": "白虎自助餐厅",
   "zh_TW": "白虎自助餐廳"
   },
   "openTime": "06:30-10:00,11:30-14:30,17:30-21:00",
   "operatingStatus": "PREPARE"
   },
   {
   "address": {
   "en": "Chimelong Tourist Resort Area,Panyu Avenue ,Panyu District,Guangzhou,P.R China",
   "zh": "广州市番禺区番禺大道长隆旅游度假区",
   "zh_TW": "廣州市番禺區番禺大道長隆旅遊度假區"
   },
   "hotelRestaurantCode": "CL003",
   "description": {
   "en": "Butterfly Gourmet Court",
   "zh": "彩蝶谷美食廊",
   "zh_TW": "彩蝶穀美食廊"
   },
   "hotelCode": "gzcljd",
   "name": {
   "en": "Butterfly Gourmet Court",
   "zh": "彩蝶谷美食廊",
   "zh_TW": "彩蝶穀美食廊"
   },
   "openTime": "11:00-24:00",
   "operatingStatus": "PREPARE"
   }
   ]
   }
   6 产品信息接口
1) 拉取协议信息（chimelong.hotel.proxy.info.get）
   接口名 chimelong.hotel. proxy.info.get
   类型 OTA ->长隆
   场景
1. 定期拉取长隆协议信息，用于判断协议中产品售卖规则，（拉取频率需与长隆技术沟通确定）
2. 接口返回协议均为代理商未失效的协议
   说明
1. 代理商可以通过协议编码和产品编号“_“后半部分做匹配，若相同可以认定该产品属于当前协议。
   长隆集团酒店接口规范
   23 / 73
2. 产品的售卖需要根据协议相关信息做调整。长隆会根据协议类型控制代理商售卖顺序，包销协议
   用完后才能使用常规协议。
   输入
   参数 类型 是 否
   必填
   最 大
   长度
   描述 示例值
   pageNo Int 否 当前页（从 1 开始），默认 1 1
   pageSize Int 否 每页记录数，默认 20 20
   proxyCodes String[] 否 协议 ID 列表（协议 ID 或页码必
   传一个）
   ["100","200","300"]
   purchaseType String 否 协议的加购类型。加购协议下的
   产品均不可单独购买
   0-非加购
   1-加购
   2-全部
   默认为 0
   输出
   参数 类型 是 否
   必填
   最 大
   长度
   描述 示例值
   totalCnt Int 否 协议总数，当分页查询时必传 1000
   proxyDatas Object[] 协议数据列表
   proxyData 数据结构
   proxyCode String 是 协议代码, 协议唯一标识符
   proxyName Localized 是 协议名称（支持国际化）
   proxyStatus String 是 协议状态（现阶段默认该值为 0，
   有变化长隆会另行通知）
   0- 激活
   1- 暂停（暂停状态下的协
   议暂时不可售卖，待重
   新激活后才可售卖）
   proxyType String 是 协议类型
   0- 包销协议
   1- 常规协
   purchaseType String 是 协议加购类型
   0-非加购
   1-加购
   proxyStartDate String 是 协议开始时间，格式：yyyy-MM�dd（协议激活时间）
   trueStartDate String 是 协议产品售卖开始时间，格式：
   yyyy-MM-dd
   proxyEndDate String 是 协议结束时间，格式：yyyy-MM-
   长隆集团酒店接口规范
   24 / 73
   dd
   isDeBlock Int 是 是否扣减配额
   0- 否
   1- 是
   包销协议的是否扣减配额字段
   为：1
   isPromotion Int 是 是否促销协议,促销协议产品不
   能取消
   0 – 否
   1 – 是
   needFirst Int 是 是否必须优先售卖（现阶段默认
   该值为 0，有变化长隆会另行通
   知）
   0- 否
   1- 是
   0
   priority String Int 协议的优先级,数值约小，优先级
   越高
   1
   specialChannel String 否 特殊售卖渠道 1 日历房；2 直播预
   售；3 商旅；4 机酒
   /铁酒
   响应示例
   {
   "proxyDatas": [
   {
   "proxyCode": "100002001",
   "proxyName": {
   "en": "xxxxxx",
   "zh": "xxxxxxx",
   "zh_TW": "xxxxxxx"
   },
   "proxyStatus": "1",
   "proxyType": "1",
   "proxyStartDate": "2019-03-07",
   "proxyEndDate": "2019-05-07",
   "isDeBlock": "0",
   "isPromotion": "0",
   “needFirst”:”0”,
   "priority": "1"
   },
   {
   "proxyCode": "100002002",
   "proxyName": {
   "en": "xxxxxx",
   "zh": "xxxxxxx",
   长隆集团酒店接口规范
   25 / 73
   "zh_TW": "xxxxxxx"
   },
   "proxyStatus": "1",
   "proxyType": "1",
   "proxyStartDate": "2019-04-07",
   "proxyEndDate": "2019-04-30",
   "isPromotion": "0",
   " isDeBlock ": "0",
   “needFirst”:”1”,
   "priority": "1"
   }
   ]
   }
2) 拉取产品信息（chimelong.hotel.product.info.get）
   接口名 chimelong.hotel.product.info.get
   类型 OTA ->长隆
   场景
   1.首次对接时 OTA 调用此接口获取全量产品信息；
2. 当长隆有产品新增或数据更新时，会通过“产品变化通知”接口告知 OTA（包含具体的
   productCode），由 OTA 实时调用此接口更新指定的产品日期数据。长隆通知代理商为批次的数据，代
   理商请求长隆查询接口也需按批次请求，减少接口的交互次数。
   3.长隆会在查询接口上做请求次数限制，遇到错误 code 为‘1111’的时候，需要调整代理商请求频率。
   说明
   1、当天是否可买、预定提前天数、售卖/上架/下架时间等与售卖时间相关的参数需要 OTA 做控制
   3、产品所属酒店，由 hotelCode 字段确定；酒餐产品所属酒店餐厅，由 hotelRestaurantCode 字段确
   定
   4、增加了 purchaseType 请求参数，默认查找非加购的
   5、一个订单多产品有两种情况：
   ①普通非加购产品任意组合购买
   ②非加购的产品和加购的产品组合购买
   6、净房是 containHotel=1&containDinner=0&containTicket=0
   房包票是 containHotel=1&(containDinner=1||containTicket=1)
   餐饮是 containHotel=0&containDinner=1&containTicket=0
   门票是 containHotel=0&containDinner=0&containTicket=1
   7、如果是房包产品，包含的子产品，需要验证子产品的适用时间，如房包含早餐的产品，平日含早，
   节假日不含早。
   输入
   参数 productCodes 优先级较高，若 productCodes 和 page 参数同时传递，则取 productCodes。
   当不传 productCodes 时，purchaseType 才有效。传递了 productCodes 则通过响应数据中 purchaseType
   判断产品的加购属性
   参数 类型 是 否
   必填
   最 大
   长度
   描述 示例值
   长隆集团酒店接口规范
   26 / 73
   pageNo Int 否 当前页（从 1 开始），默认 1 1
   pageSize Int 否 每页记录数，默认 20 20
   productCodes String[] 否 产品 ID 列表 ["100","200","300"]
   purchaseType String 否 查询的产品加购属性。非加购的
   产品可以单独购买，加购的产品
   需要和非加购的产品一起组合
   购买
   0-非加购
   1-加购
   2-全部
   默认为 0
   输出
   参数 类型 是 否
   必填
   最 大
   长度
   描述 示例值
   totalCnt Int 否 产品总数，当分页查询时必传 1000
   productData Object[] 产品数据列表
   productData 数据结构
   productCode String 是 产品代码(包含净房和房+X), 产
   品唯一标识符
   productName Localize
   d
   是 产品名称（支持国际化）
   productDescripti
   on
   Localize
   d
   否 产品描述（支持国际化）
   每个描述组前增加编码区分：
   PD01：置顶描述
   PD02：产品描述
   PD03：礼遇描述
   PD04：权益描述
   PD05：须知描述
   purchaseType String 是 产品的加购类型
   0-非加购
   1-加购
   加购的产品需要和非加购的产
   品一起组合购买，不可单独购买
   promotionType Int 否 是否促销，默认 0
   0-否
   1-是
   region String 是 产品所属区域 GZ/ZH
   productDays Int 是 产品天数（连续的天数），默认
   1
   若是套餐产品（3 天 2 晚，则为
   2），则可能大于 1。
   scheduleOnline String 否 产品上架时间，格式：yyyy-MM�dd HH:mm:ss，若不传或值小于
   当前时间，则产品立即上架
   长隆集团酒店接口规范
   27 / 73
   saleBegDate String 否 开始售卖时间，格式：yyyy-MM�dd HH:mm:ss。
   若不传该参数，则产品上架后立
   即可以售票。
   trueStartDate String 是 协议产品开始时间，格式：yyyy�MM-dd
   scheduleOffline String 否 产品下架时间，格式：yyyy-MM�dd HH:mm:ss，若不传则产品永
   远不下架，若值小于当前时间，
   则产品立即下架
   若 值 与
   scheduleOnline冲突，
   则该参数优先级高。
   containHotel Int 否 是否包含酒店产品，仅在单独餐
   饮产品时不包含
   0- 不包含
   1- 包含
   containDinner Int 否 是否包含餐饮产品
   0- 不包含
   1- 包含
   dinnerDesc String 否 餐饮描述 保留字段
   containTicket Int 否 是否包含门票产品
   0- 不包含
   1- 包含
   seasonType String 否 是否包含场次票
   0- 否
   1- 是
   hotelCode String 否 酒店代码 加购的门票没有该
   属性
   hotelRestaurantC
   ode
   String 否 酒店餐厅代码
   payType Int 否 支付类型，默认 0
   0- 线上支付
   1- 景区到付
   固定值：0
   maxBuyDays Int 否 预售天数，若不传该值则没有限
   制 ； 对 应 中 台 的 字 段
   （leadSalesDays）。
   aheadBuyDays Int 否 预订提前天数，默认 1；对应中
   台的早鸟天数（earlyBirdDays）。
   若当日可购买，则为 0。
   aheadBuyTime String 否 预订结束时间，格式：HHmm，
   默认 2359，订当天产品的结束时
   间。
   aheadBuyDays=3 ，
   aheadBuyTime="1530"，若购买
   7-27 的产品，最晚购买时间为 7-
   保留字段
   长隆集团酒店接口规范
   28 / 73
   24 15:30:00
   canBuyOnday Boolean 否 当日是否可买
   orderMinBooking
   Num
   Int 否 订单最小预定数 保留参数
   orderMaxBooking
   Num
   Int 否 订单最大预定数
   accountMaxBooki
   ngNum
   Int 否 账号最大预定数
   monday Boolean 否 星期一是否可用，默认 true
   tuesday Boolean 否 星期二是否可用，默认 true
   wednesday Boolean 否 星期三是否可用，默认 true
   thursday Boolean 否 星期四是否可用，默认 true
   friday Boolean 否 星期五是否可用，默认 true
   saturday Boolean 否 星期六是否可用，默认 true
   sunday Boolean 否 星期日是否可用，默认 true
   useRangeData Object[] 否 使用的日期时间范围
   productData.useRangeData 数据结构
   useBegDate String 否 开始时间，格式：yyyy-MM-dd
   HH:mm:ss。
   useEndDate String 否 结束时间，格式：yyyy-MM-dd
   HH:mm:ss。
   changeRule Object 否 退改规则
   productData.changeRule 数据结构
   changeType Int 否 已支付订单的是否可修改（包括
   改期、改数量等），默认 0
   0- 可以
   1- 不可以
   aheadDays Int 否 修改订单提前天数，按门票的有
   效开始日期计算，默认 0。
   可以是负数，表示过期可退。
   保留字段
   aheadTime String 否 修改订单结束时间，格式：
   HHmm，默认 2359
   aheadDays=3，aheadTime="1530"，
   若过期日期为 7-27 的票，最晚
   修改时间为 7-24 15:30:00
   保留字段
   cancelType Int 否 已支付订单的是否可取消，默认
   0
   0- 可以
   1- 不可以
   cancelAheadDays Int 否 取消订单提前天数，按门票的有
   效开始日期计算，默认 0。
   可以是负数，表示过期可退。
   保留字段
   cancelAheadTime String 否 取消订单结束时间，格式： 保留字段
   长隆集团酒店接口规范
   29 / 73
   HHmm，默认 2359
   cancelAheadDays =3 ，
   cancelAheadTime ="1530"，若过
   期日期为 7-27 的票，最晚取消
   时间为 7-24 15:30:00
   roomData Object[] 否 房间属性，若单独的餐饮产品没
   有该属性
   productData.roomData 数据结构
   productCode String 是 产品编号
   productName Localize
   d
   是 产品名称（支持国际化）
   roomCategoryNa
   me
   Localize
   d
   是 房型类别名称 高级房、野趣房
   roomTypeCode String 是 房型编号 GZH001_TD
   roomTypeName Localize
   d
   是 房床型类型名称 高级大床房、野趣双
   床房
   bedTypeCode String 是 床型
   bedTypeName Localize
   d
   是 床型名称 大床、双床、三床
   commonInfos String[] 否 房间设施 [空调,电视,洗衣机]
   hasInternet Boolean 否 是否包含免费宽带仅返回 true
   (是)或者 false(不是)
   adultNum Int 否 最大可住成人人数
   childNum Int 否 最大可住儿童数
   stayNight Int 否 入住晚数，默认 1。
   restaurantData Object[] 否 餐饮属性，单独餐饮或房加餐时
   有效
   productData.restaurantData 数据结构
   productCode String 是 产品编号
   productName Localize
   d
   是 产品名称（支持国际化）
   eatTimeType Int 是 餐饮分时段: 早 午 晚 （1代表
   为早餐,2代表为午餐,3代表为晚
   餐,4代表为早午茶,5代表为下午
   茶,9 代表园餐，0 代表其他）
   eatTimeDetail String 否 使用时间（例:8:30-10:00）
   eatType Int 否 餐饮类型，默认 1，值：
   0- 首日餐饮
   1- 每天餐饮
   对于产品天数 productDays>1 时
   有效。
   eatNum Int 否 餐饮数量，默认 1
   isNightDay Boolean 是 是否每晚赠送 新增
   长隆集团酒店接口规范
   30 / 73
   packageInfo Object[] 否 房包票产品中包含子产品的售
   卖起始时间,结束时间和相应的
   售卖周控时间策略等
   新增
   ticketData Object[] 否 房包票时有效，同一个产品中包
   含 1-n 个门票产品
   productData.ticketData 数据结构
   productCode String 是 产品 Code
   productName Localize
   d
   是 产品名称
   productDescripti
   on
   Localize
   d
   否 产品描述
   ticketNum Int 否 房包票数量 新增
   subValidityType String 否 房包票入园有效期 新增
   isNightDay Boolean 是 是否每晚赠送 新增
   packageInfo Object[] 否 房包票产品中包价子产品的售
   卖起始时间,结束时间和相应的
   售卖周控时间策略等
   新增
   packageInfo 数据结构
   startDate String 是 包价子产品开始售卖时间
   "2019-04-01"
   endDate String 是 包价子产品售卖结束时间
   "2019-09-01"
   dayOfWeeks Object[] 是 包价子产品销售周控时间策略
   ["SUNDAY","MONDAY","TUESDA
   Y","WEDNESDAY"]
   响应示例
   {
   "totalCnt": "1",
   "productData": [
   {
   "productCode": "302TSTAF00008_1000001655",
   "productName": {
   "en": "Three Days Two Nights Couple Package with lunch buffet,Circus and Ocean Kingdom
   tickets",
   "zh": "3 天 2 晚双人双园游(海洋王国+马戏城)+自助午餐套票",
   "zh_TW": "3 天 2 晚雙人雙園游(海洋王國+馬戲城)套票+自助午餐套票"
   },
   "productDescription": {
   "en": "<p>1. Two nights stay in a Room（breakfast not included）<br />2. Ocean Kingdom
   tickets for two persons (two-day&#39;s multi entry to Ocean Kingdom since arrival day)<br />3. Circus tickets
   for two persons and only applicable on the arrival day<br />4. Lunch Buffet for two persons (applicable on the
   arrival day or next day)<br />5. Hengqin Bay Water World tickets for two persons (once entry during stay from
   长隆集团酒店接口规范
   31 / 73
   1st July to 31th August)</p>",
   "zh": "<p style=\"margin-left:0cm; margin-right:0cm\">1.客房1间2晚（不含早餐）<br />2.
   2 张海洋王国门票（入住当日起两日内多次入园）<br />3. 2 张马戏普通座门票（仅限入住当天观看<br
   />4. 2 位自助午餐（入住当天或次日使用）<br />5. 2 张水世界门票（7-8 月入住赠送，入住期间一次入园
   有效）</p>",
   "zh_TW": "<p style=\"margin-left:0cm; margin-right:0cm\">1. 客房1間2晚（不含早餐）<br
   />2. 2 張海洋王國門票（入住當日起兩日內多次入園）<br />3. 2 張馬戲普通座門票（僅限入住當天觀看）
   <br />4. 2 位自助午餐（入住當天或次日使用）<br />5. 2 張水世界門票（7-8 月入住贈送，入住期間壹次
   入園有效）</p>"
   },
   "region": "ZH",
   "purchaseType": "0",
   "promotionType": "0",
   "productDays": 2,
   "scheduleOnline": "2019-01-29 00:00:01",
   "saleBegDate": "2019-07-15 00:00:01",
   "scheduleOffline": "2019-12-31 23:59:59",
   "containHotel": 1,
   "containDinner": 1,
   "containTicket": 1,
   "seasonType": 1,
   "hotelCode": "ZHH002",
   "payType": 0,
   "maxBuyDays": 365,
   "aheadBuyDays": 0,
   "canBuyOnday": true,
   "orderMaxBookingNum": 8,
   "accountMaxBookingNum": null,
   "monday": true,
   "tuesday": true,
   "wednesday": true,
   "thursday": true,
   "friday": true,
   "saturday": true,
   "sunday": true,
   "useRangeData": [
   {
   "useBegDate": "2018-11-01 00:00:00",
   "useEndDate": "2019-01-01 00:00:00"
   },
   {
   "useBegDate": "2019-01-01 00:00:00",
   "useEndDate": "2019-03-31 00:00:00"
   },
   长隆集团酒店接口规范
   32 / 73
   {
   "useBegDate": "2019-04-01 00:00:00",
   "useEndDate": "2019-12-31 00:00:00"
   }
   ],
   "changeRule": {
   "changeType": 1,
   "cancelType": 0
   },
   "roomData": [
   {
   "productCode": "302TSTAF00008_1000001655",
   "productName": {
   "en": "Three Days Two Nights Couple Package with lunch buffet,Circus and Ocean
   Kingdom tickets",
   "zh": "3 天 2 晚双人双园游(海洋王国+马戏城)+自助午餐套票",
   "zh_TW": "3 天 2 晚雙人雙園游(海洋王國+馬戲城)套票+自助午餐套票"
   },
   "roomCategoryName": {
   "en": "Fancy Room",
   "zh": "杂技房",
   "zh_TW": "雜技房"
   },
   "roomTypeCode": "ZHH002_TS",
   "roomTypeName": {
   "en": "Fancy Twin Bed Room",
   "zh": "杂技双床房",
   "zh_TW": "雜技雙床房"
   },
   "bedTypeCode": "T",
   "bedTypeName": {
   "en": "Twin Bed",
   "zh": "双床",
   "zh_TW": "雙床"
   },
   "commonInfo": [
   "42 寸液晶电视",
   "超过 60 个国内、国际电视频道",
   "有线、无线上网",
   "独立可调控中央空调私人保险箱",
   "安全电子门锁",
   "拖鞋",
   "风筒",
   "沐浴用品",
   长隆集团酒店接口规范
   33 / 73
   "24 小时礼宾服务",
   "非吸烟客房",
   "可按要求提供无障碍客房"
   ],
   "hasInternet": true,
   "adultNum": 0,
   "childNum": 0,
   "stayNight": 2
   }
   ],
   "restaurantData": [
   {
   "productCode": "3025100013",
   "productName": {
   "en": "Adult Lunch (Circus Hotel）",
   "zh": "马戏自助餐厅自助午餐（房包餐）",
   "zh_TW": "成人午餐（馬戲酒店）"
   },
   "eatTimeType": "2",
   "eatTimeDetail": "11:30-14:30",
   "eatType": 0,
   "eatNum": 2,
   " packageInfo": [
   [
   {
   "dayOfWeeks": [
   "MONDAY",
   "TUESDAY",
   "WEDNESDAY",
   "THURSDAY",
   "FRIDAY",
   "SATURDAY",
   "SUNDAY"
   ],
   "endDate": "2018-12-31",
   "startDate": "2018-09-01"
   },
   {
   "dayOfWeeks": [
   "SUNDAY",
   "MONDAY",
   "TUESDAY",
   "WEDNESDAY",
   "THURSDAY",
   长隆集团酒店接口规范
   34 / 73
   "FRIDAY",
   "SATURDAY"
   ],
   "endDate": "2019-03-31",
   "startDate": "2019-01-29"
   },
   {
   "dayOfWeeks": [
   "MONDAY",
   "TUESDAY",
   "WEDNESDAY",
   "THURSDAY",
   "FRIDAY",
   "SATURDAY",
   "SUNDAY"
   ],
   "endDate": "2019-06-30",
   "startDate": "2019-04-01"
   },
   {
   "dayOfWeeks": [
   "SUNDAY",
   "MONDAY",
   "TUESDAY",
   "WEDNESDAY",
   "THURSDAY",
   "FRIDAY",
   "SATURDAY"
   ],
   "endDate": "2019-12-31",
   "startDate": "2019-07-01"
   }
   ]
   ]
   }
   ],
   "ticketData": [
   {
   "productCode": "3581000001",
   "productName": {
   "en": "Hengqinwang water word Package ticket",
   "zh": "水世界票(房包)",
   "zh_TW": "橫琴灣水世界房包票"
   },
   长隆集团酒店接口规范
   35 / 73
   "productDescription": {
   "en": "<p>Hengqinwang water word Package ticket</p>",
   "zh": "",
   "zh_TW": "<p>橫琴灣水世界房包票</p>"
   },
   " packageInfo": [
   [
   {
   "dayOfWeeks": [
   "MONDAY",
   "TUESDAY",
   "WEDNESDAY",
   "THURSDAY",
   "FRIDAY",
   "SATURDAY",
   "SUNDAY"
   ],
   "endDate": "2019-12-31",
   "startDate": "2019-07-01"
   }
   ]
   ]
   },
   {
   "productCode": "3561000001",
   "productName": {
   "en": "海洋王国两天票(房包）",
   "zh": "海洋王国两天票(房包）",
   "zh_TW": "海洋王国两天票(房包）"
   },
   "productDescription": {
   "en": "海洋王国两天多次房包票",
   "zh": "<p>海洋王国两天多次房包票 1</p>",
   "zh_TW": "海洋王国两天多次房包票"
   },
   " packageInfo": [
   [
   {
   "dayOfWeeks": [
   "MONDAY",
   "TUESDAY",
   "WEDNESDAY",
   "THURSDAY",
   "FRIDAY"
   长隆集团酒店接口规范
   36 / 73
   ],
   "endDate": "2018-12-29",
   "startDate": "2018-10-08"
   }
   ]
   ]
   },
   {
   "productCode": "3591000001",
   "productName": {
   "en": "国际马戏城房包票",
   "zh": "长隆剧院普通座(房包)",
   "zh_TW": "国际马戏城房包票"
   },
   "productDescription": {
   "en": "珠海国际马戏城房包票门票（适用平日及特定日）",
   "zh": "珠海国际马戏城房包票门票（适用平日及特定日）",
   "zh_TW": "珠海国际马戏城房包票门票（适用平日及特定日）"
   },
   " packageInfo": [
   [
   {
   "dayOfWeeks": [
   "MONDAY",
   "TUESDAY",
   "WEDNESDAY",
   "THURSDAY",
   "FRIDAY"
   ],
   "endDate": "2018-12-29",
   "startDate": "2018-10-08"
   },
   {
   "dayOfWeeks": [
   "SATURDAY",
   "SUNDAY"
   ],
   "endDate": "2018-12-29",
   "startDate": "2018-10-08"
   }
   ]
   ]
   }
   ]
   长隆集团酒店接口规范
   37 / 73
   }
   ]
   }
3) 拉取产品价格日历（
   chimelong.hotel.product.price.get）
   接口名 chimelong.hotel.product.price.get
   类型 OTA->长隆
   场景
   OTA 主动拉取长隆的价格信息
   说明
   输入
   参数 类型 是否
   必填
   最大
   长度
   描述 示例值
   getData Object[] 是 获取数据数组
   该数组元素最多 20 个
   getData 的数据结构
   productCode String 是 产品 Code
   begDate String 是 开始日期，格式：yyyy�MM-dd
   endDate String 是 结束日期，格式：yyyy�MM-dd
   输出
   1、 在开始日期和结束日期范围内，输出中没有的日期，表示当天的产品不能出售。
   2、 productCode+productDate 为唯一键，若为场次票，当天可售的所有场次从 seasonData 数组中获取。
   3、 若多天的套餐产品，每天的价格取平均数四舍五入到个位，最后 1 天的价格为总价减去前面的天的
   汇总数。【此说明是针对需要对房包票产品（如 3 天 2 晚）需要进行日拆价的 OTA，在显示每日价
   格时的计算公式】
   4、 长隆在产品价格的返回数据中已经做好了产品使用区间和周控的控制，如周一-周五可售卖的产品，
   价格接口中只返回周一-周五的价格。
   5、 当长隆有产品新增或数据更新时，会通过“产品变化通知”接口告知 OTA（包含具体的
   productCode），由 OTA 实时调用此接口更新指定的产品日期数据。长隆通知代理商为批次的数据，
   代理商请求长隆查询接口也需按批次请求，减少接口的交互次数。
   6、 长隆会在查询接口上做请求次数限制，遇到错误 code 为‘1111’的时候，需要调整代理商请求频率。
   7、 若某天因为关房或禁售等操作，无价格时，settlePrice 返回-1
   参数 类型 是否
   必填
   最大
   长度
   描述 示例值
   priceData Object[] 否 产品的价格数据
   priceData 数据结构
   长隆集团酒店接口规范
   38 / 73
   productCode String 是 产品 Code
   productDate String 是 日期，格式：yyyy-MM-dd
   promotionType Int 否 是否促销，默认 0
   0- 否
   1- 是
   保留字段
   currency String 是 币种，CNY-人民币 固定值：CNY
   fixPrice Price 否 定价，即市场价
   otaPrice Price 否 OTA 价格，OTA 的售卖价
   格
   保留字段
   settlePrice Price 否 结算价，OTA 与长隆之间
   的结算价。
   若是场次票，则结算价从
   seasonData. settlePrice 中
   获取（不同场次的价格可
   能不一致）
   非场次票，则该字段必填
   seasonData Object[] 否 公园场次数据
   priceData.seasonData 数据结构
   seasonCode String 是 场次 Code，若该公园含有
   场次信息。
   seasonName String 否 场次名称 上午场
   seasonBegTime String 是 场次开始时间，格式：
   HHmm
   seasonEndTime String 是 场次结束时间，格式：
   HHmm
   fixPrice Price 否 定价，即门市价
   settlePrice Price 是 结算价，OTA 与长隆之间
   的结算价。
   响应示例
   {
   "priceData": [{
   "productCode": "202512153",
   "productDate": "2019-03-09",
   "currency": "CNY",
   "settlePrice": "148"
   }, {
   "productCode": "202512153",
   "productDate": "2019-03-15",
   "currency": "CNY",
   "settlePrice": "148"
   }, {
   "productCode": "202512153",
   "productDate": "2019-03-16",
   "currency": "CNY",
   长隆集团酒店接口规范
   39 / 73
   "settlePrice": "148"
   }, {
   "productCode": "202512153",
   "productDate": "2019-03-22",
   "currency": "CNY",
   "settlePrice": "148"
   }, {
   "productCode": "202512153",
   "productDate": "2019-03-23",
   "currency": "CNY",
   "settlePrice": "148"
   }, {
   "productCode": "202512153",
   "productDate": "2019-03-29",
   "currency": "CNY",
   "settlePrice": "148"
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-02-27",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3009",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "5610"
   }, {
   "seasonCode": "2GZ7054",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "settlePrice": "5610"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-02-28",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3010",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "5748"
   }, {
   "seasonCode": "2GZ7055",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   长隆集团酒店接口规范
   40 / 73
   "settlePrice": "5748"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-03-01",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3011",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "6024"
   }, {
   "seasonCode": "2GZ7056",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "settlePrice": "6024"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-03-02",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3012",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "6024"
   }, {
   "seasonCode": "2GZ7057",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "settlePrice": "6024"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-03-03",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3013",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "5748"
   }, {
   "seasonCode": "2GZ7058",
   "seasonBegTime": "1500",
   长隆集团酒店接口规范
   41 / 73
   "seasonEndTime": "1630",
   "settlePrice": "5748"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-03-04",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3014",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "5610"
   }, {
   "seasonCode": "2GZ7059",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "settlePrice": "5610"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-03-05",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3015",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "5610"
   }, {
   "seasonCode": "2GZ7060",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "settlePrice": "5610"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-03-06",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3016",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "5610"
   }, {
   "seasonCode": "2GZ7061",
   长隆集团酒店接口规范
   42 / 73
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "settlePrice": "5610"
   }]
   }, {
   "productCode": "201TCTA248",
   "productDate": "2019-03-07",
   "currency": "CNY",
   "seasonData": [{
   "seasonCode": "2GZ3017",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "settlePrice": "5748"
   }, {
   "seasonCode": "2GZ7062",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "settlePrice": "5748"
   }]
   }]
   }
4) 拉取产品库存日历（
   chimelong.hotel.product.stock.get）
   接口名 chimelong.hotel.product.stock.get
   类型 OTA->长隆
   场景
   OTA 主动拉取长隆的库存信息
   说明
   1.首次对接时 OTA 调用此接口获取全量产品库存,全量拉取每天只能一次，并且拉取时间需跟长隆技术确
   认；
2. 当 长 隆 有 产 品 新 增 或 数 据 更 新 时 ， 会 通 过 “ 产 品 变 化 通 知 ” 接 口 告 知 OTA （ 包 含 具 体 的
   productCode），由 OTA 实时调用此接口更新指定的产品库存数据。长隆通知代理商为批次的数据，代理
   商请求长隆查询接口也需按批次请求，减少接口的交互次数。
   3.长隆会在查询接口上做请求次数限制，遇到错误 code 为‘1111’的时候，需要调整代理商请求频
   率。
   输入
   参数 类型 是否
   必填
   最大
   长度
   描述 示例值
   getData Object[] 是 获取数据数组
   该数组元素最多 20 个
   长隆集团酒店接口规范
   43 / 73
   getData 的数据结构
   productCode String 是 产品 Code
   begDate String 是 开始日期，格式：yyyy-MM�dd
   endDate String 是 结束日期，格式：yyyy-MM�dd
   输出
   1、在开始日期和结束日期范围内，输出中没有的日期，表示当天的产品不能出售。
   2、productCode+productDate 为唯一键。若为场次票，当天可售的所有场次信息从 seasonData 数组中获
   取。
   3、长隆在产品库存的返回数据中已经做好了产品使用区间和周控的控制，如周一-周五可售卖的产品，
   库存接口中只返回周一-周五的库存。
   参数 类型 是否
   必填
   最大
   长度
   描述 示例值
   stockData Object[] 否 产品的价格库存数据
   stockData 数据结构
   productCode String 是 产品 Code
   productDate String 是 日期，格式：yyyy-MM-dd
   stockLevel Int 否 日库存，库存级别。
   0- 库存充足
   1- 库存紧张
   2- 无库存
   若是场次票，则结算价从
   seasonData. stockLevel 中获
   取（不同场次的库存可能不
   一致）
   非场次票，则 stockLevel、
   stock 至少有一个。
   stock Int 否 日库存，剩余可售卖库存。
   若是场次票，则结算价从
   seasonData. stock 中 获 取
   （不同场次的库存可能不
   一致）
   非场次票，则 stockLevel、
   stock 至少有一个。
   seasonData Object[] 否 公园场次数据
   stockData.seasonData 数据结构
   seasonCode String 否 场次 Code，若该公园含有
   场次信息。
   17:00 场
   seasonName String 否 场次名称 保留字段
   seasonBegTime String 是 场 次 开 始 时 间 ， 格 式 ：
   HHmm
   seasonEndTime String 是 场 次 结 束 时 间 ， 格 式 ：
   HHmm
   长隆集团酒店接口规范
   44 / 73
   stockLevel Int 否 日库存，库存级别。
   0-库存充足
   1-库存紧张
   2-无库存
   stockLevel、stock 至少有一
   个。
   stock Int 否 日库存，剩余可售卖库存。
   stockLevel、stock 至少有一
   个。
   响应示例
   {
   "stockData": [{
   "productCode": "ZHH0022471",
   "productDate": "2019-02-26",
   "stockLevel": 0,
   "stock": 203
   }, {
   "productCode": "ZHH0022471",
   "productDate": "2019-02-27",
   "stockLevel": 0,
   "stock": 207
   }, {
   "productCode": "ZHH0022471",
   "productDate": "2019-02-28",
   "stockLevel": 0,
   "stock": 204
   }, {
   "productCode": "20252153",
   "productDate": "2019-03-08",
   "stockLevel": 0,
   "stock": 10000000
   }, {
   "productCode": "20252153",
   "productDate": "2019-03-09",
   "stockLevel": 0,
   "stock": 10000000
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-02-26",
   "seasonData": [{
   "seasonCode": "2GZ540043008",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 0,
   长隆集团酒店接口规范
   45 / 73
   "stock": 244
   }, {
   "seasonCode": "2GZ540017053",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 0,
   "stock": 148
   }]
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-02-27",
   "seasonData": [{
   "seasonCode": "2GZ540043009",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 0,
   "stock": 242
   }, {
   "seasonCode": "2GZ540017054",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 0,
   "stock": 148
   }]
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-02-28",
   "seasonData": [{
   "seasonCode": "2GZ540043010",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 0,
   "stock": 245
   }, {
   "seasonCode": "2GZ540017055",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 0,
   "stock": 148
   }]
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-03-01",
   "seasonData": [{
   长隆集团酒店接口规范
   46 / 73
   "seasonCode": "2GZ540043011",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 0,
   "stock": 250
   }, {
   "seasonCode": "2GZ540017056",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 0,
   "stock": 149
   }]
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-03-02",
   "seasonData": [{
   "seasonCode": "2GZ540043012",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 0,
   "stock": 249
   }, {
   "seasonCode": "2GZ540017057",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 0,
   "stock": 150
   }]
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-03-03",
   "seasonData": [{
   "seasonCode": "2GZ540043013",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 0,
   "stock": 250
   }, {
   "seasonCode": "2GZ540017058",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 0,
   "stock": 150
   }]
   长隆集团酒店接口规范
   47 / 73
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-03-04",
   "seasonData": [{
   "seasonCode": "2GZ540043014",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 0,
   "stock": 250
   }, {
   "seasonCode": "2GZ540017059",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 0,
   "stock": 150
   }]
   }, {
   "productCode": "201TCT2248",
   "productDate": "2019-03-05",
   "seasonData": [{
   "seasonCode": "2GZ540043015",
   "seasonBegTime": "1930",
   "seasonEndTime": "2100",
   "stockLevel": 1,
   "stock": 7
   }, {
   "seasonCode": "2GZ540017060",
   "seasonBegTime": "1500",
   "seasonEndTime": "1630",
   "stockLevel": 1,
   "stock": 7
   }]
   }]
   }
5) 产品变化通知（
   chimelong.hotel.product.change.notice）
   接口名 chimelong.hotel.product.change.notice
   类型 长隆->OTA
   场景
   长隆通知 OTA 产品信息发生变更、新增、删除，变化信息包括：
   1、 产品信息（chimelong.trip.product.info.get 接口返回的信息），包括产品的上下架等信息变化了，会
   长隆集团酒店接口规范
   48 / 73
   推送该通知。
   2、 产品价格，当产品价格变化了，会推送改通知，需 OTA 调用价格接口查询最新的实时价格。
   3、 产品库存变化的推送机制为跳跃式推送，20->9->5->0，即库存剩余 20 会推送一次，剩余 9 会推一次，
   剩余 5 会推一次，剩余 0 再推送一次。且推送为并发推送。
   说明
   1、产品信息变化：建议收到改变化通知后调用产品信息接口获取最新产品信息
   2、新增产品：建议收到该通知后，上架该产品，并调用价格/库存接口获取该产品的价格/库存，最多 2
   个月一次。
   3、删除产品：建议收到该通知后下架产品
   4、价格变化：建议收到该通知后，调用价格接口获取并更新该产品、该天的价格
   5、库存变化：建议收到该通知后，调用价格接口获取并更新该产品、该天的库存
   输入
   参数 类型 是否必填 最大长度 描述 示例值
   notice Object[] 是 通知的对象
   notice 数据结构
   productCode String 是 产品 Code
   status Int 是 产品状态：
   0- 产品信息变化
   1- 产品价格变化
   2- 产品新增
   3- 产品删除
   4- 产品库存变化
   productDate String 否 产品的日期，格式：yyyy-MM�dd
   当 status=1 或 4 时有值
   productCategor
   y
   String 否 产品类型
   T-门票
   H-净房
   BH-房包票
   PF-园餐
   HF-酒餐
   输出
   参数 类型 是否必填 最大长度 描述 示例值
   请求示例
   {
   "notice": [
   {
   "productCode": 2222222,
   "status": 0
   },
   {
   "productCode": 2222222,
   "status": 1,
   长隆集团酒店接口规范
   49 / 73
   "productDate": "2018-08-15"
   }
   ]
   }
   7 订单信息接口
1) 订单验证（chimelong.hotel.order.verify）
   接口名 chimelong.hotel.order.verify
   类型 OTA->长隆
   场景
   OTA 调用此接口请求长隆验证订单（通过价格日历和库存日历校验价格和库存）
   说明
   1、酒餐产品，下单传递的开始、结束时间要一致，即定义为酒餐的使用时间
   2、房包票产品，下单传递的开始、结束时间要一致，因为房包票产品为包装好的组合产品，只要定
   义一个时间，其中子产品会根据子产品属性计算出具体的时间，无需代理商计算。
   输入
   参数 类型 是否必
   填
   最 大 长
   度
   描述 示例值
   remark String 否 备注
   name String 否 联系人姓名
   mobile String 否 联系人手机号
   email String 否 联系人电子邮箱
   currency String 是 币种，CNY-人民币 固定值：CNY
   productData Object[] 是 产品数据
   productData 数据结构（productCode+begDate+seasonCode 唯一）
   productCode String 是 长隆的产品 Code
   begDate String 是 入住日期，格式：yyyy-MM�dd
   酒餐和房包票产品，为使用
   日期
   endDate String 是 离店日期，格式：yyyy-MM�dd，
   酒餐和房包票产品，为使用
   日期，且和 begDate 一致。
   seasonCode String 否 场次 Code，若该产品带有
   马戏场次，则该值必填。
   settlePrice Price 否 结算价，OTA 与长隆之间的
   结算价，所有天的汇总价。
   注意每天的价
   格可能不一样
   otaPrice Price 否 OTA 售卖价格
   quantity Int 是 购买数量
   长隆集团酒店接口规范
   50 / 73
   totalMoney Price 否 产品金额
   settlePrice×quantity
   addBedNum String 否 加床数量
   adults String 否 成人数
   children String 否 小孩数量
   dayPriceData Object[] 否 每日价格数据。非必填。
   productData.dayPriceData 数据结构
   roomDate String 是 日期，格式：yyyy-MM-dd。
   settlePrice Price 是 结算价，OTA 与长隆之间的
   结算价
   otaPrice Price 否 OTA 售卖价格
   breakFastNum Int 否 早餐数
   personInfoData Object[] 否 入住人数据 入住人信息的
   个数需和购买
   数量保持一致，
   且证件号不能
   重复。
   productData.personInfoData 数据结构
   name String 是 入住人姓名或拼音
   mobile String 否 入住人手机号。
   是否必填按照产品信息定
   义。
   certCardType Int 是 证件类型，证件类型列表见
   附录。
   是否必填按照产品信息定
   义。
   certCardNo String 是 证件号码。
   是否必填按照产品信息定
   义。
   email String 否 入住人电子邮箱。
   是否必填按照产品信息定
   义。
   touristAccompanyings Object[] 否 随行人数据 随行人信息只
   能一个。即一
   间房一个入住
   人，最多一个
   随行人
   productData.personInfoData. touristAccompanyings 数据结构
   name String 是 随行人姓名或拼音
   mobile String 是 随行人手机号。
   visitorData Object[] 否 游玩人数据
   目前请确保该数据与入住
   人相同。
   保留字段
   长隆集团酒店接口规范
   51 / 73
   productData.visitorData 数据结构
   name String 否 游客姓名或拼音
   mobile String 否 游客手机号。
   是否必填按照产品信息定
   义。
   certCardType Int 否 证件类型，证件类型列表见
   附录。
   是否必填按照产品信息定
   义。
   certCardNo String 否 证件号码。
   是否必填按照产品信息定
   义。
   email String 否 游客电子邮箱。
   是否必填按照产品信息定
   义。
   输出
   参数 类型 是否必
   填
   最 大 长
   度
   描述 示例值
   verifyResult Int 是 验证结果。
   0- 验证失败
   1- 验证成功
   dayPriceData Object[] 否 正确的每日价格数据。
   dayPriceData 数据结构
   productCode String 是 长隆的产品 Code
   roomDate String 是 日期，格式：yyyy-MM-dd。
   settlePrice Price 是 结算价，OTA 与长隆之间的
   结算价
   otaPrice Price 否 OTA 售卖价格
   breakFastNum Int 否 早餐数
   stockLevel Int 否 日库存，库存级别。
   0- 库存充足
   1- 库存紧张
   2- 无库存
   stockLevel、stock 至少有一
   个。
   stock Int 否 库存，剩余可售卖库存
   stockLevel、stock 至少有一
   个。
   请求示例
   {
   "remark": "aaaaaa",
   "name": "xx",
   "mobile": "13666641818",
   "email": "<EMAIL>",
   长隆集团酒店接口规范
   52 / 73
   "productData": [{
   "entryNumber": "1",
   "productCode": "ZHH0020921",
   "begDate": "2018-12-18",
   "endDate": "2018-12-19",
   "settlePrice": 29.8,
   "seasonCode": "2GZ3030",
   "quantity": 1,
   "totalMoney": 29.8,
   "addBedNum": 0,
   "adults": 1,
   "children": 0,
   "dayPriceData": null,
   "personInfoData": [{
   "name": "XiAO",
   "mobile": "075593864321",
   "certCardType": 1,
   "certCardNo": "******************",
   "email": "<EMAIL>",
   "touristAccompanyings": [{
   "name": "RR",
   "mobile": "17612154669"
   }]
   }]
   }]
   }
2) 订单创建（chimelong.hotel.order.create）
   接口名 chimelong.hotel.order.create
   类型 OTA->长隆
   场景
   OTA 调用此接口请求长隆创建订单
   说明
   1、酒餐产品，下单传递的开始、结束时间要一致，即定义为酒餐的使用时间
   2、房包票产品，下单传递的开始、结束时间要一致，因为房包票产品为包装好的组合产品，只要定
   义一个时间，其中子产品会根据子产品属性计算出具体的时间，无需代理商计算。
   3、入住人/联系人姓名，在下游系统中会需要拆分姓和名，按照以下规则进行拆分：
   ① 姓和名中间有一个空格的，按空格截，不区分中英文，例如 zhang san 就会截成 姓：zhang 名：
   san；
   ② 若传来的名字中带有复姓，则截取复姓为姓，剩余的是名
   ③ 若以上 2 个都不满足，则截取第一个，例如传来中文的张三，就会截成 姓：张 名：三；传来英文
   的 zhangsan，就截取成 姓：z 名 hangsan
   输入
   参数 类型 是 否 必 最 大 长 描述 示例值
   长隆集团酒店接口规范
   53 / 73
   填 度
   otaOrderId String 是 OTA 的订单 Id
   remark String 否 备注
   name String 是 联系人姓名
   mobile String 是 联系人手机号
   email String 否 联系人电子邮箱
   orderMoney Price 是 订单总金额【订单行的
   totalMoney 的总和】
   payed Int 是 是否已支付
   0-未支付（再调用支付接口
   支付）
   1-已支付
   currency String 是 币种，CNY-人民币 固定值：CNY
   productData Object[] 是 产品数据
   productData 数据结构（productCode+begDate+seasonCode 唯一）
   productCode String 是 长隆的产品 Code
   begDate String 是 入住日期，格式：yyyy-MM�dd。
   酒餐和房包票产品，为使用
   日期
   endDate String 是 离店日期，格式：yyyy-MM�dd。
   酒餐和房包票产品，为使用
   日期，且和 begDate 一致。
   如果为房包票
   产品，房晚为固
   定值，本字段无
   效。切勿把房包
   票价格拆分成
   多个房晚。
   settlePrice Price 是 结算价，OTA 与长隆之间的
   结算价，所有天的汇总价。
   注意每天的价
   格可能不一样
   otaPrice Price 否 OTA 售卖价格，所有天的汇
   总价。
   seasonCode String 否 场次 Code，若该产品含有
   场次信息。若该产品带有马
   戏场次，则该值必填。
   entryNumber String 否 订单行。用于指定订单行，
   若需要接修改订单接口，必
   填。
   quantity Int 是 购买数量
   totalMoney Price 是 产品金额
   settlePrice×quantity
   addBedNum Int 否 加床数量
   adults Int 否 成人数
   children Int 否 小孩数量
   dayPriceData Object[] 否 每日价格数据。非必填。
   长隆集团酒店接口规范
   54 / 73
   productData.dayPriceData 数据结构
   roomDate String 是 日期，格式：yyyy-MM-dd。
   settlePrice Price 是 结算价，OTA 与长隆之间的
   结算价
   otaPrice Price 否 OTA 售卖价格
   breakFastNum Int 否 早餐数
   personInfoData Object[] 是 入住人数据 入住人信息的
   个数需和购买
   数量保持一致，
   且证件号不能
   重复。
   productData.personInfoData 数据结构
   name String 是 入住人姓名或拼音
   mobile String 否 入住人手机号。
   是否必填按照产品信息定
   义。
   certCardType Int 是 证件类型，证件类型列表见
   附录。
   是否必填按照产品信息定
   义。
   certCardNo String 是 证件号码。
   是否必填按照产品信息定
   义。
   email String 否 入住人电子邮箱。
   是否必填按照产品信息定
   义。
   touristAccompanyings Object[] 否 随行人数据 随行人信息只
   能一个。即一间
   房一个入住人，
   最多一个随行
   人
   productData.personInfoData. touristAccompanyings 数据结构
   name String 是 随行人姓名或拼音
   mobile String 是 随行人手机号。
   visitorData Object[] 否 游玩人数据
   目前若传递，请确保该数据
   与入住人相同。
   保留字段
   productData.visitorData 数据结构
   name String 否 游客姓名或拼音
   mobile String 否 游客手机号。
   是否必填按照产品信息定
   义。
   certCardType Int 否 证件类型，证件类型列表见
   附录。
   长隆集团酒店接口规范
   55 / 73
   是否必填按照产品信息定
   义。
   certCardNo String 否 证件号码。
   是否必填按照产品信息定
   义。
   email String 否 游客电子邮箱。
   是否必填按照产品信息定
   义。
   输出
   参数 类型 是 否 必
   填
   最 大 长
   度
   描述 示例值
   chimeOrderId String 是 长隆订单 Id
   secondCriusUseTimeDat
   a
   Object[] 否 亲子秀产品场次时间，数组
   对象
   secondCriusUseTimeData 数据结构
   productCode String 否 长隆的产品 Code
   begDate String 否 入住日期，格式：yyyy-MM�dd。
   酒餐和房包票产品，为使用
   日期
   seasonCode String 否 场次 Code，若该产品含有
   场次信息
   secondCriusUseTime String 否 亲子秀产品场次时间，格式
   为：亲子秀场次:2024-05-28
   10:00~11:30
   直接展示就可
   以
   业务错误码
   错误码 错误描述 解决方案
   请求示例
   {
   "otaOrderId": "OTA0001",
   "remark": "酒店产品订单 0001",
   "name": "张三",
   "mobile": 18888888888,
   "email": "<EMAIL>",
   "orderMoney":3800,
   "productData": [
   {
   "productCode": "BARN5",
   "begDate": "2018-09-01",
   "endDate": "2018-09-03",
   "settlePrice": 1900,
   "otaPrice": 2100,
   长隆集团酒店接口规范
   56 / 73
   "quantity": 2,
   "totalMoney": 3800,
   "personInfoData": [
   {
   "name": "张三",
   "mobile": 18888888888,
   "certCardType": 1,
   "certCardNo": 230124199909011230,
   "email": "<EMAIL>",
   "touristAccompanyings": [{
   "name": "RR",
   "mobile": "17612154669"
   }]
   },
   {
   "name": "李四",
   "mobile": 13666666666,
   "certCardType": 1,
   "certCardNo": 230124199909011231,
   "email": "<EMAIL>"
   }
   ],
   "visitorData": [
   {
   "name": "张三",
   "mobile": 18888888888,
   "certCardType": 1,
   "certCardNo": 230124199909011230,
   "email": "<EMAIL>"
   },
   {
   "name": "四",
   "mobile": 13666666666,
   "certCardType": 1,
   "certCardNo": 230124199909011231,
   "email": "<EMAIL>"
   }
   ]
   }
   ]
   }
3) 订单关闭（chimelong.hotel.order.close）
   长隆集团酒店接口规范
   57 / 73
   接口名 chimelong.hotel.order.close
   类型 OTA->长隆
   场景
   若未支付订单主动关闭，则由 OTA 调用该接口通知长隆；
   说明
   长隆也会在一定时间内自动关闭订单（酒餐产品 15 分钟，酒店产品 60 分钟）
   输入
   参数 类型 是否必填 最大长度 描述 示例值
   otaOrderId String 否 OTA 的订单 Id，otaOrderId 与
   chimeOrderId 至少传递一个。
   chimeOrderId String 否 长 隆 订 单 Id ， otaOrderId 与
   chimeOrderId 至少传递一个。
   输出
   参数 类型 是否必填 最大长度 描述 示例值
   仅公共响应参数（参见 4.4）
   响应示例
4) 订单支付（chimelong.hotel.order.pay）
   接口名 chimelong.hotel.order.pay
   类型 OTA->长隆
   场景
   OTA 调用此接口请求长隆订单。
   说明
   接口返回成功，表示订单已确认，但不代表已经出票，由于长隆内部系统都是异步处理，所以需要
   等待出凭证码通知得到最终结果。
   输入
   参数 类型 是否必填 最大长度 描述 示例值
   otaOrderId String 否 OTA 的订单 Id，otaOrderId 与
   chimeOrderId 至少传递一个。
   chimeOrderId String 否 长 隆 订 单 Id ， otaOrderId 与
   chimeOrderId 至少传递一个。
   输出
   参数 类型 是否必填 最大长度 描述 示例值
   仅公共响应参数（参见 4.4）
   响应示例
5) 订单出确认码通知（
   chimelong.hotel.order.voucher.notice）
   长隆集团酒店接口规范
   58 / 73
   接口名 chimelong.hotel.order.voucher.notice
   类型 长隆->OTA
   场景
   当订单支付成功后，异步返回兑换凭证。长隆调用 OTA 接口传递确认码。
   说明
   可能有多个兑换码。
   若通知没有得到正确的回复，hub 会按照一定的频率重试请求。
   若超过 5 分钟还没收到凭证码通知，建议 ota 调用查询接口主动查询订单数据，若查询也查不到凭
   证码，需联系长隆客服。
   注意：只有订单出码才算交易真正成立。出码失败，代理商平台需取消订单。
   长隆集团将推行身份证入园，有如下变更点：
   1、 预订的房间依然推送 PMS 确认码作为订单确认凭证，与原流程保持不变；
   2、 房包票办理入住时需要进行身份证绑定，绑定后凭身份证入园（预订时填写的入住人身份证绑定房
   包票中一张门票，不可修改）；
   3、 净房加购门票办理入住时需要进行身份证绑定，绑定后凭身份证入园（预订时填写的入住人身份证
   绑定加购门票中一张门票，不可修改）；
   4、 房包票或加购门票取消线下凭身份证件取票；
   5、 入住人证件类型为非身份证的，工作人员会核实预订时填写的入住人证件与现场提供的证件是否一
   致，如不一致将不予绑定身份证。
   输入
   参数 类型 是否必填 最大长度 描述 示例值
   otaOrderId String 否 OTA 的订单 Id，otaOrderId 与
   chimeOrderId 至少传递一个。
   chimeOrderId String 否 长 隆 订 单 Id ， otaOrderId 与
   chimeOrderId 至少传递一个。
   status Int 是 兑换码通知状态，值如下：
   0- 出凭证码失败
   1- 出凭证码成功
   voucherData Object[] 否 兑换码数据
   voucherData 数据结构（productCode+begDate+seasonCode 不唯一，可能有多行，即多个兑换码）
   productCode String 是 产品 Code
   begDate String 是 入住日期，格式：yyyy-MM-dd
   seasonCode String 否 场次 Code，若该公园含有场次
   信息。
   上午场
   voucherCode String 是 确认码。
   voucherType String 否 兑换码说明性文字
   TICKET-门票
   HOTEL-酒店
   保留字段
   certCardNo String 是 证件号，酒店时有效
   输出
   参数 类型 是否必填 最大长度 描述 示例值
   仅公共响应参数（参见 4.4）
   长隆集团酒店接口规范
   59 / 73
   请求示例
   {
   "otaOrderId": 1111111,
   "chimeOrderId": 2222222,
   "status": 0,
   "voucherData": [
   {
   "productCode": "P001",
   "begDate": "2018-08-09",
   "voucherCode": "aaaaaaa",
   "voucherType": "HOTEL",
   "certCardNo": "1234567890"
   },
   {
   "productCode": "P002",
   "begDate": "2018-08-09",
   "seasonCode": "上午场",
   "voucherCode": "bbbbbb",
   "voucherType": "TICKET"
   }
   ]
   }
6) 订单取消（chimelong.hotel.order.cancel）
   接口名 chimelong.hotel.order.cancel
   类型 OTA->长隆
   场景
   已付款的订单，OTA 调用此接口请求退款申请。（整单取消）
   说明
   注意：订单是否可取消必须已长隆返回结果为准。
   输入
   参数 类型 是否必填 最大长度 描述 示例值
   otaOrderId String 否 OTA 的订单 Id，otaOrderId 与
   chimeOrderId 至少传递一个。
   chimeOrderId String 否 长 隆 订 单 Id ， otaOrderId 与
   chimeOrderId 至少传递一个。
   refundFee Price 否 平台退款手续费（以平台手续费
   为准的渠道必传，保留两位小数）
   输出
   参数 类型 是否必填 最大长度 描述 示例值
   refundFee Price 否 退款手续费。
   当该参数返回 0，则表示没有手
   续费；当该参数不传或 null，表
   暂时线下处理时不
   传内容
   长隆集团酒店接口规范
   60 / 73
   示线下结算手续费。
   响应示例
   略
7) 订单修改（chimelong.hotel.order.change）
   接口名 chimelong.hotel.order.change
   类型 OTA->长隆
   场景
   订单修改内容。
   说明
   注意：
   1、修改结果同步返回。
   2、产品编号不可以修改
   3、可以修改产品数量增加、数量减少、改期，接口会同步返回修改费用。
   4、订单修改次数有限制，具体由线下业务沟通规定。
   5、修改订单后，若是加购的餐饮或门票产品，会重新出新的凭证码。房的确认码不会重新出。
   6、原订单数据可以不传，但 target 开头的新订单数据，必须全量传递。
   7、订单是否可修改必须已长隆返回结果为准。
   输入
   参数 类型 是否必
   填
   最大长
   度
   描述 示例值
   otaOrderId String 否 OTA 的订单 Id，otaOrderId
   与 chimeOrderId 至少传递
   一个。
   chimeOrderId String 否 长隆订单 Id，otaOrderId
   与 chimeOrderId 至少传递
   一个。
   changeType Int 是 是否仅仅修改联系人信
   息，值：
   0- 否
   1- 是
   若修改了产品数据，包括
   修改游客信息，值就为 1。
   新业务规定，
   不 可 以 修 改
   联系人
   name String 否 原联系人姓名或拼音
   email String 否 原联系人电子邮箱。
   targetName String 是 目标联系人姓名
   targetMobile String 否 目标联系人手机号 保留字段
   targetEmail String 否 目标联系人电子邮箱。
   productData Object[] 是 产品数据
   productData 数据结构
   productCode String 否 产品 Code
   长隆集团酒店接口规范
   61 / 73
   begDate String 否 入住日期，格式：yyyy�MM-dd，
   酒餐产品，为使用日期
   endDate String 否 离店日期，格式：yyyy�MM-dd，
   酒餐产品，为使用日期，
   且和 begDate 一致。
   entryNumber String 是 订单行。用于指定订单行，
   需与创建订单时的订单
   行一致。
   seasonCode String 否 场次 Code，若该公园含有
   场次信息。
   若该产品带有马戏场次，
   则该值必填。
   otaPrice Price 否 OTA 价格，OTA 的售卖价
   格
   settlePrice Price 否 结算价，OTA 与长隆之间
   的结算价。
   quantity Int 否 购买数量。
   addBedNum String 否 加床数量
   adults String 否 成人数
   children String 否 小孩数量
   targetProductCode String 是 产品 Code 产品 Code 不
   可修改
   targetBegDate String 是 目标入住日期，格式：
   yyyy-MM-dd，
   酒餐产品，为使用日期
   targetEndDate String 是 目标离店日期，格式：
   yyyy-MM-dd，
   酒餐产品，为使用日期，
   且和 begDate 一致。
   targetSeasonCode String 否 场次 Code，若该公园含有
   场次信息。
   若该产品带有马戏场次，
   则该值必填。
   targetOtaPrice Price 否 目标 OTA 价格，OTA 的售
   卖价格
   targetSettlePrice Price 是 目标结算价，OTA 与长隆
   之间的结算价，所有天的
   汇总价。
   注 意 每 天 的
   价 格 可 能 不
   一样
   targetQuantity Int 是 目标购买数量。
   targetAddBedNum String 否 目标加床数量
   targetAdults String 否 目标成人数
   targetChildren String 否 目标小孩数量
   长隆集团酒店接口规范
   62 / 73
   personInfoData Object[] 否 原入住人数据
   若修改，则传递该参数。
   修改需把所有入住人数
   据全部传递
   productData.personInfoData 数据结构
   name String 是 入住人姓名或拼音
   mobile String 否 入住人手机号。
   是否必填按照产品信息
   定义。
   certCardType Int 是 证件类型，证件类型列表
   见附录。
   是否必填按照产品信息
   定义。
   certCardNo String 是 证件号码。
   是否必填按照产品信息
   定义。
   email String 否 入住人电子邮箱。
   是否必填按照产品信息
   定义。
   visitorData Object[] 否 游客数据。
   若修改，则传递该参数。
   修改需把所有游客数据
   全部传递。
   保留字段
   productData.visitorData 数据结构
   name String 否 游客姓名或拼音
   mobile String 否 游客手机号。
   是否必填按照产品信息
   定义。
   certCardType Int 否 证件类型，证件类型列表
   见附录。
   是否必填按照产品信息
   定义。
   certCardNo String 否 证件号码。
   是否必填按照产品信息
   定义。
   email String 否 游客电子邮箱。
   是否必填按照产品信息
   定义。
   targetPersonInfoData Object[] 是 目标入住人数据
   若修改，则传递该参数。
   修改需把所有入住人数
   据全部传递
   若 修 改 了 产
   品信息，该节
   点也必填，和
   targetQuantit
   y要保持一致，
   且 身 份 证 号
   长隆集团酒店接口规范
   63 / 73
   不能重复
   productData.targetPersonInfoData 数据结构
   name String 是 入住人姓名或拼音
   mobile String 否 入住人手机号。
   是否必填按照产品信息
   定义。
   certCardType Int 是 证件类型，证件类型列表
   见附录。
   是否必填按照产品信息
   定义。
   certCardNo String 是 证件号码。
   是否必填按照产品信息
   定义。
   email String 否 入住人电子邮箱。
   是否必填按照产品信息
   定义。
   targetVisitorData Object[] 是 目标游客数据。
   若修改，则传递该参数。
   修改需把所有游客数据
   全部传递。
   保留字段
   productData.targetVisitorData 数据结构
   name String 否 游客姓名或拼音
   mobile String 否 游客手机号。
   是否必填按照产品信息
   定义。
   certCardType Int 否 证件类型，证件类型列表
   见附录。
   是否必填按照产品信息
   定义。
   certCardNo String 否 证件号码。
   是否必填按照产品信息
   定义。
   email String 否 游客电子邮箱。
   是否必填按照产品信息
   定义。
   输出
   参数 类型 是否必
   填
   最大长
   度
   描述 示例值
   status Int 是 修改状态，值如下：
   0- 修改中（异步，需要
   等待通知得到结果）
   1- 修改成功
   2- 修改失败
   changeFee Price 否 若 status=1 时有效，修改
   长隆集团酒店接口规范
   64 / 73
   手续费。
   当该参数返回 0，则表示
   没有手续费；当该参数不
   传或 null，表示线下结算
   手续费。
   业务错误码
   错误码 错误描述 解决方案
   请求示例
   {
   "otaOrderId": "OTA0001",
   "chimeOrderId": "CD0000120003",
   "changeType": 0,
   "targetName": "王五",
   "productData": [
   {
   "entryNumber": "0",
   "productCode": "BARN5",
   "begDate": "2018-09-01",
   "endDate": "2018-09-03",
   "settlePrice": 1900,
   "otaPrice": 2100,
   "quantity": 2,
   "addBedNum": 0,
   "adults": 2,
   "children": 1,
   "targetProductCode": "BARN3",
   "targetBegDate": "2018-09-01",
   "targetEndDate": "2018-09-03",
   "targetOtaPrice": 2500,
   "targetSettlePrice": 2800,
   "targetQuantity": 1,
   "targetAddBedNum": 0,
   "targetAdults": 1,
   "targetChildren": 1,
   "targetPersonInfoData": [
   {
   "name": "王五",
   "mobile": 13333333333,
   "certCardType": 1,
   "certCardNo": 230124199909011230,
   "email": "<EMAIL>"
   }
   ]
   长隆集团酒店接口规范
   65 / 73
   },
   {
   "entryNumber": "1",
   "productCode": "BARN3",
   "begDate": "2018-09-01",
   "endDate": "2018-09-03",
   "settlePrice": 1900,
   "otaPrice": 2100,
   "quantity": 1,
   "addBedNum": 0,
   "adults": 2,
   "children": 1,
   "targetProductCode": "BARN3",
   "targetBegDate": "2018-09-01",
   "targetEndDate": "2018-09-03",
   "targetOtaPrice": 2500,
   "targetSettlePrice": 2800,
   "targetQuantity": 2,
   "targetAddBedNum": 0,
   "targetAdults": 1,
   "targetChildren": 1,
   "targetPersonInfoData": [
   {
   "name": "王五",
   "mobile": 13333333333,
   "certCardType": 1,
   "certCardNo": 230124199909011230,
   "email": "<EMAIL>"
   },
   {
   "name": "赵六",
   "mobile": 13456789012,
   "certCardType": 1,
   "certCardNo": 230124199909011260,
   "email": "<EMAIL>"
   }
   ],
   "visitorData": [
   {
   "name": "王五",
   "mobile": 13333333333,
   "certCardType": 1,
   "certCardNo": 230124199909011230,
   "email": "<EMAIL>"
   长隆集团酒店接口规范
   66 / 73
   },
   {
   "name": "赵六",
   "mobile": 13456789012,
   "certCardType": 1,
   "certCardNo": 230124199909011260,
   "email": "<EMAIL>"
   }
   ]
   }
   ]
   }
8) 订单查询（chimelong.hotel.order.query）
   接口名 chimelong.hotel.order.query
   类型 OTA->长隆
   场景
   OTA 调用此接口请求长隆查询订单信息，包括订单状况和确认码。
   说明
   1、订单出凭证码，订单状态仍为 2-已确认，voucherData 里会返回凭证码。即订单出凭证码没有特
   别的订单状态。订单状态为 2 且 voucherCode 不为空才代表订单确认成功。
   2、若不接出凭证码通知接口，也可通过该接口主动查询订单的数据，建议：出凭证码的主动查询频
   率为 1 分钟或 30 秒一次
   输入
   参数 类型 是 否 必
   填
   最 大 长
   度
   描述 示例值
   otaOrderId String 否 OTA 的订单 Id，otaOrderId
   与 chimeOrderId 至少传递一
   个。
   chimeOrderId String 否 长隆订单 Id，otaOrderId 与
   chimeOrderId至少传递一个。
   输出
   参数 类型 是 否 必
   填
   最 大 长
   度
   描述 示例值
   otaOrderId String 是 OTA 的订单 Id
   chimeOrderId String 是 长隆订单 Id
   orderMoney Price 是 订单总金额
   orderStatus Int 是 订单状态。
   1- 订单创建成功（未支付）
   （已创建）
   2- 订单支付成功（已确认）
   3- 订单入住（执行中）
   4- 订单退房（已完成）
   长隆集团酒店接口规范
   67 / 73
   5- 订单全部取消（未支付
   或已支付取消）（已取消）
   6- 预订异常（出凭证码失
   败等）（预订异常）
   7- 已挂起（等待下一步处
   理）
   productData Object[] 是 产品数据
   productData 数据结构（productCode+begDate+seasonCode 唯一）
   productCode String 是 长隆的产品 Code
   begDate String 是 游玩开始日期，格式：yyyy�MM-dd，若产品需要填写游
   玩日期必填。
   若为期票，传递购买日期。
   endDate String 是 游玩结束日期，格式：yyyy�MM-dd，若产品需要填写游
   玩日期必填。
   若为期票，传递购买日期。
   seasonCode String 否 场次 Code，若该公园含有场
   次信息。
   19:00 场
   settlePrice Price 是 结算价，OTA 与长隆之间的
   结算价（分销平台【总价
   /quantity】）
   totalMoney Price 是 产品金额
   quantity Int 是 购买数量
   voucherData Object[] 否 兑换码数据。
   productData.voucherData 数据结构
   voucherCode String 是 兑换码。
   voucherType String 否 兑换码说明性文字
   TICKET-门票
   HOTEL-酒店
   保留字段
   certCardNo String 是 证件号，酒店时有效
   响应示例
   {
   "otaOrderId": "",
   "chimeOrderId": "CL0000013",
   "orderMoney": 11768,
   "orderStatus": 3,
   "productData": [{
   "productCode": "303TDK2513",
   "begDate": "2019-02-26",
   "endDate": "2019-02-26",
   "seasonCode": "3ZH5026",
   "settlePrice": "5578",
   "quantity": "1",
   长隆集团酒店接口规范
   68 / 73
   "totalMoney": 5578,
   "voucherData": [{
   "voucherCode": "1775309",
   "certCardNo": "1111"
   }]
   }, {
   "productCode": "303TDK2513",
   "begDate": "2019-02-25",
   "endDate": "2019-02-25",
   "seasonCode": "3ZH5025",
   "settlePrice": "5578",
   "quantity": "1",
   "totalMoney": 5578,
   "voucherData": [{
   "voucherCode": "1775310",
   "certCardNo": "1112"
   }]
   }, {
   "productCode": "3572523",
   "begDate": "2019-02-21",
   "endDate": "2019-02-21",
   "seasonCode": "3ZH5021",
   "settlePrice": "612",
   "quantity": "1",
   "totalMoney": 612,
   "voucherData": [{
   "voucherCode": "5021639"
   }]
   }]
   }
   8 辅助接口 API
1) 查询账户余额（
   chimelong.hotel.support.balance.query）
   接口名 chimelong.hotel.support.balance.query
   类型 OTA->长隆
   场景
   查询账户的余额
   长隆集团酒店接口规范
   69 / 73
   说明
   输入
   参数 类型 是否必填 最大长度 描述 示例值
   regionId String 否 区域 Id，取值见附录。
   若传递该参数，则查询与区域
   相关的账户。
   若不传递，则返回该 OTA 所有
   账户。
   输出
   参数 类型 是否必填 最大长度 描述 示例值
   balanceData Object[] 是 账户余额数据
   balanceData 数据结构
   accountId String 否 账户 Id
   accountName String 否 账户名称
   accountRemai
   n
   Price 是 账户余额
   regionId String 是 区域 Id
   业务错误码
   错误码 错误描述 解决方案
   响应示例
   {
   "balanceData": [
   {
   "accountId": 2222222,
   "accountName": "携程广州长隆账户",
   "accountRemain": 12345.67,
   "regionId": "GZ"
   },
   {
   "accountId": 3333333,
   "accountName": "携程珠海长隆账户",
   "accountRemain": 2345.67,
   "regionId": "GZ"
   }
   ]
   }
   长隆集团酒店接口规范
   70 / 73
   9 附录
1) 证件类型定义
   选择相应的证件后，相应的证件还需要输入相应的名称，如身份证需要输入姓名，护照需要输入英文名或
   拼音。
   证件编号 证件类型 描述 示例值
   1 身份证
   2 护照
   3 台湾居民来往大陆通行证
   5 港澳台居民居住证
   6 港澳居民来往内地通行证
   7 外国人永久居留身份证
2) Region 定义
   Region 编号 Region 名称 描述 示例值
   GZ 广州长隆
   ZH 珠海长隆
   QY 清远长隆
3) 错误码
   code msg
   1111 达到请求上限
   ERROR-UNKNOWN 系统错误，请联系管理员
   ERROR-HYBRIS_ERROR 系统错误，请联系中台管理员
   ERROR-CONCURRENT_ERROR 系统繁忙，请稍后重试
   ERROR-ACCESS_DENIED 您没有权限访问，请联系管理员
   ERROR-ERR-003 日期范围不正确%s - %s
   ERROR-PRD-001 找不到库存记录
   ERROR-PRD-002 商品库存校验失败
   ERROR-PRD-003 找不到产品%s[%s]@%s 价格记录
   ERROR-PRD-004 找不到等级价格
   ERROR-PRD-005 商品库存不足
   ERROR-PRX-001 协议不存在
   ERROR-PRX-002 未找到匹配的协议
   ERROR-PRX-003 缺少协议
   ERROR-PRX-004 协议未激活
   ERROR-PRX-005 协议未到生效时间
   ERROR-PRX-006 协议已过期
   ERROR-PRX-007 产品%s 未找到匹配的协议行
   ERROR-PRX-008 blockId：%s 未找到
   长隆集团酒店接口规范
   71 / 73
   ERROR-PRX-101 产品%s 超过产品最大购买量
   ERROR-PRX-102 产品%s 少于产品最小购买量
   ERROR-PRX-103 超过%s 协议中的订单最大购买量
   ERROR-PRX-104 协议行定义错误导致无法计算协议价%s
   ERROR-ORD-001 订单不存在
   ERROR-ORD-002 产品不存在
   ERROR-ORD-051 产品%s 不可销售
   ERROR-ORD-052 产品%s 所属的酒店已关闭
   ERROR-ORD-054 产品%s 不在可销售时间范围
   ERROR-ORD-055 产品%s 不允许当天购买
   ERROR-ORD-056 产品%s 不在预售范围
   ERROR-ORD-057 产品%s 不在早鸟购买范围
   ERROR-ORD-058 产品%s 不在可使用时间范围
   ERROR-ORD-059 产品%s 不在可使用周控范围
   ERROR-ORD-060 产品%s 没有价格记录
   ERROR-ORD-061 产品%s 没有足够的（限购/包销）库存
   ERROR-ORD-102 订单内容为空
   ERROR-ORD-104 未找到订单行
   ERROR-ORD-105 订单行内容为空
   ERROR-ORD-106 产品 code 为空
   ERROR-ORD-107 产品数量非法
   ERROR-ORD-110 OTA 不支持团队订单
   ERROR-ORD-111 订单行产品原始价格错误
   ERROR-ORD-112 订单行产品成交价格错误
   ERROR-ORD-113 订单行金额错误
   ERROR-ORD-114 订单总金额错误
   ERROR-ORD-115 订单缺少必要的联系人证件信息
   ERROR-ORD-116 住客信息重复
   ERROR-ORD-117 OTA 订单号和代理商信息必填
   ERROR-ORD-121 净房单产品最大购买数溢出, 限购：%s
   ERROR-ORD-122 房包票单产品最大购买数溢出, 限购：%s
   ERROR-ORD-123 订单总数最大购买数溢出, 限购：%s
   ERROR-ORD-131 预付款余额不足
   ERROR-ORD-132 专款专用余额不足
   ERROR-ORD-142 产品包销数量扣除失败
   ERROR-ORD-143 预存款账户冻结失败
   ERROR-ORD-144 专款余额不足
   ERROR-ORD-145 账户已禁用
   ERROR-ORD-146 专款协议已失效
   ERROR-ORD-201 执行中和已完成的订单不可取消
   ERROR-ORD-202 执行中的订单不能整单取消
   ERROR-ORD-203 产品不能退单
   ERROR-ORD-204 促销协议订单不能退单
   长隆集团酒店接口规范
   72 / 73
   ERROR-ORD-205 订单中包含了不支持的产品
   ERROR-ORD-206 无修改历史的订单才能取消
   ERROR-ORD-207 只能取消分销商自己的订单
   ERROR-ORD-208 订单已达到修改次数上线，不能取消
   ERROR-ORD-209 OTA 订单号或者分销订单号不能为空值
   ERROR-ORD-211 平时和周末的产品需要提前 2 天 17 点前才能取消
   ERROR-ORD-212 普通节假日的产品需要提前 7 天 17 点前才能取消
   ERROR-ORD-213 特殊节假日的产品不允许取消
   ERROR-ORD-231 订单超时无法取消，提前一天的 17：00 之前可以取消
   ERROR-ORD-232 用餐当天不允许取消
   ERROR-ORD-251 平时和周末的产品取消应该提前 1 天
   ERROR-ORD-252 普通节假日的产品取消应该提前 7 天
   ERROR-ORD-253 当特殊节假日的产品不能退
   ERROR-ORD-271 订单超出可取消/修改时间范围
   ERROR-ORD-291 中台订单取消失败
   ERROR-ORD-292 中台订单取消确认失败
   ERROR-ORD-300 订单未修改任何内容
   ERROR-ORD-301 订单状态不允许修改
   ERROR-ORD-302 非确认状态订单不允许修改
   ERROR-ORD-303 入住人只有分配了 PMS code 才能退改
   ERROR-ORD-305 超过订单最大修改次数
   ERROR-ORD-307 找不到历史订单
   ERROR-ORD-308 只能修改分销商自己的订单
   ERROR-ORD-311 平时和周末的产品需要提前 2 天 17 点前才能取消
   ERROR-ORD-312 普通节假日的产品需要提前 7 天 17 点前才能取消
   ERROR-ORD-313 特殊节假日的产品不允许修改
   ERROR-ORD-314 促销产品不允许修改
   ERROR-ORD-315 订单行修改不允许修改产品
   ERROR-ORD-316 修改订单业态和老订单业态不一致
   ERROR-ORD-317 修改订单产品类型和老订单产品类型不一致
   ERROR-ORD-318 找不到 OTA 修改订单
   ERROR-ORD-319 OTA 修改订单号或者分销订单号不能为空值
   ERROR-ORD-320 %s 协议已终止或过期，不允许退改
   ERROR-ORD-321 %s 产品不能修改日期
   ERROR-ORD-331 净房订单修改失败
   ERROR-ORD-332 房包票订单修改失败
   ERROR-ORD-333 订单修改失败
   ERROR-ORD-334 促销产品订单修改失败
   ERROR-ORD-335 特定节假日（国庆、春节、暑假中段），非净房不可修改
   ERROR-ORD-336 当前时间距离订单日期应该提前 7 天 17:00 前
   ERROR-ORD-337 当前时间距离订单日期应该提前 7 天 17:00 前
   ERROR-ORD-338 当前时间距离订单日期应该提前 2 天 17:00 前
   ERROR-ORD-339 特定节假日（国庆、春节、暑假中段），净房只允许改期
   长隆集团酒店接口规范
   73 / 73
   ERROR-ORD-341 散客订单修改次数不能大于 1
   ERROR-ORD-351 订单超时无法修改，用餐当天 9：30 之前可以修改
   ERROR-ORD-352 订单超时无法修改，用餐当天 9：30 之前可以修改
   ERROR-ORD-353 用餐当天 8：00 - 9：30 不可减少
   ERROR-ORD-354 订单超时无法修改，提前一天的 17：00 之前可以修改
   ERROR-ORD-355 订单超时无法修改，用餐当天 10：00 之前可以修改
   ERROR-ORD-356 订单超时无法修改，用餐当天 14：00 之前可以修改
   ERROR-ORD-357 用餐当天不允许修改
   ERROR-ORD-358 只允许修改一次
   ERROR-ORD-359 只支持修改日期,修改产品数量和删除产品
   ERROR-ORD-360 只支持同等价格修改
   ERROR-ORD-361 订单只允许可修改两次
   ERROR-ORD-362 数量不可改为 0
   ERROR-ORD-363 餐次提前两个小时之前可以修改
   ERROR-ORD-364 散客净酒餐不能修改
   ERROR-ORD-365 促销酒餐或园餐不能修改
   ERROR-ORD-367 只支持修改产品数量和删除产品
   ERROR-ORD-368 执行中的订单修改失败
   ERROR-ORD-369 非后台管理账号不能修改执行中订单
   ERROR-ORD-370 订单号不能为空
   ERROR-ORD-371 非执行中订单，订单行不能取消
   ERROR-ORD-372 非确认状态订单行无法取消
   ERROR-ORD-373 订单行已取消，不能重复取消
   ERROR-ORD-374 执行中订单行取消，必须取消所有未使用订单行
   ERROR-ORD-501 订单支付已超时
   ERROR-ORD-503 酒店订单已自动取消，无法支付
   ERROR-ORD-505 餐食订单已自动取消，无法支付
   ERROR-ORD-506 订单不是可支付状态
   ERROR-ORD-514 订单支付操作处理中，请稍后（一分钟内重复调用支付接
   口时返回，建议隔一分钟再用订单查询接口查询订单状态
   后再进行退款处理）
   ERROR-RLF-001 分布式锁获取失败（重复创建订单，当第一个请求未处理
   完，第二个相同 OTA 订单号的请求又过来时返回）
   1100 自定义错误
   1111 限流
   ERROR-ORD-134 身份证信息不合法
   ERROR-ORD-010 当前代理商创建订单限制中
