<?php

namespace app\model;

/**
 * 系统日志模型
 * 对应数据库表：system_logs
 */
class SystemLog extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'system_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'level',
        'channel',
        'message',
        'context',
        'user_id',
        'ip_address',
        'user_agent',
        'request_id',
        'session_id',
        'url',
        'method',
        'execution_time',
        'memory_usage',
        'file',
        'line',
        'stack_trace',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'context' => 'array',
        'user_id' => 'integer',
        'execution_time' => 'decimal:3',
        'memory_usage' => 'integer',
        'line' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * 日志级别常量
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * 日志通道常量
     */
    const CHANNEL_APPLICATION = 'application';
    const CHANNEL_DATABASE = 'database';
    const CHANNEL_API = 'api';
    const CHANNEL_SYNC = 'sync';
    const CHANNEL_SECURITY = 'security';
    const CHANNEL_PERFORMANCE = 'performance';

    /**
     * 获取所属用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 作用域：按级别筛选
     */
    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * 作用域：按通道筛选
     */
    public function scopeByChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：调试级别
     */
    public function scopeDebug($query)
    {
        return $query->where('level', self::LEVEL_DEBUG);
    }

    /**
     * 作用域：信息级别
     */
    public function scopeInfo($query)
    {
        return $query->where('level', self::LEVEL_INFO);
    }

    /**
     * 作用域：警告级别
     */
    public function scopeWarning($query)
    {
        return $query->where('level', self::LEVEL_WARNING);
    }

    /**
     * 作用域：错误级别
     */
    public function scopeError($query)
    {
        return $query->where('level', self::LEVEL_ERROR);
    }

    /**
     * 作用域：严重级别
     */
    public function scopeCritical($query)
    {
        return $query->where('level', self::LEVEL_CRITICAL);
    }

    /**
     * 作用域：应用通道
     */
    public function scopeApplication($query)
    {
        return $query->where('channel', self::CHANNEL_APPLICATION);
    }

    /**
     * 作用域：数据库通道
     */
    public function scopeDatabase($query)
    {
        return $query->where('channel', self::CHANNEL_DATABASE);
    }

    /**
     * 作用域：API通道
     */
    public function scopeApi($query)
    {
        return $query->where('channel', self::CHANNEL_API);
    }

    /**
     * 作用域：同步通道
     */
    public function scopeSync($query)
    {
        return $query->where('channel', self::CHANNEL_SYNC);
    }

    /**
     * 作用域：安全通道
     */
    public function scopeSecurity($query)
    {
        return $query->where('channel', self::CHANNEL_SECURITY);
    }

    /**
     * 作用域：性能通道
     */
    public function scopePerformance($query)
    {
        return $query->where('channel', self::CHANNEL_PERFORMANCE);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：按IP地址筛选
     */
    public function scopeByIpAddress($query, $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * 作用域：按请求ID筛选
     */
    public function scopeByRequestId($query, $requestId)
    {
        return $query->where('request_id', $requestId);
    }

    /**
     * 作用域：按会话ID筛选
     */
    public function scopeBySessionId($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * 作用域：按URL筛选
     */
    public function scopeByUrl($query, $url)
    {
        return $query->where('url', 'like', '%' . $url . '%');
    }

    /**
     * 作用域：按HTTP方法筛选
     */
    public function scopeByMethod($query, $method)
    {
        return $query->where('method', $method);
    }

    /**
     * 作用域：慢查询（执行时间超过指定秒数）
     */
    public function scopeSlowQueries($query, $threshold = 1.0)
    {
        return $query->where('execution_time', '>', $threshold);
    }

    /**
     * 作用域：高内存使用（内存使用超过指定字节数）
     */
    public function scopeHighMemoryUsage($query, $threshold = 50 * 1024 * 1024) // 50MB
    {
        return $query->where('memory_usage', '>', $threshold);
    }

    /**
     * 获取级别名称
     */
    public function getLevelNameAttribute()
    {
        $levels = [
            self::LEVEL_DEBUG => '调试',
            self::LEVEL_INFO => '信息',
            self::LEVEL_WARNING => '警告',
            self::LEVEL_ERROR => '错误',
            self::LEVEL_CRITICAL => '严重',
        ];

        return $levels[$this->level] ?? '未知';
    }

    /**
     * 获取通道名称
     */
    public function getChannelNameAttribute()
    {
        $channels = [
            self::CHANNEL_APPLICATION => '应用',
            self::CHANNEL_DATABASE => '数据库',
            self::CHANNEL_API => 'API',
            self::CHANNEL_SYNC => '同步',
            self::CHANNEL_SECURITY => '安全',
            self::CHANNEL_PERFORMANCE => '性能',
        ];

        return $channels[$this->channel] ?? '未知';
    }

    /**
     * 获取级别颜色
     */
    public function getLevelColorAttribute()
    {
        $colors = [
            self::LEVEL_DEBUG => 'gray',
            self::LEVEL_INFO => 'blue',
            self::LEVEL_WARNING => 'orange',
            self::LEVEL_ERROR => 'red',
            self::LEVEL_CRITICAL => 'purple',
        ];

        return $colors[$this->level] ?? 'default';
    }

    /**
     * 获取级别图标
     */
    public function getLevelIconAttribute()
    {
        $icons = [
            self::LEVEL_DEBUG => 'bug',
            self::LEVEL_INFO => 'info-circle',
            self::LEVEL_WARNING => 'exclamation-triangle',
            self::LEVEL_ERROR => 'times-circle',
            self::LEVEL_CRITICAL => 'exclamation-circle',
        ];

        return $icons[$this->level] ?? 'question-circle';
    }

    /**
     * 检查是否为错误级别
     */
    public function isError()
    {
        return in_array($this->level, [self::LEVEL_ERROR, self::LEVEL_CRITICAL]);
    }

    /**
     * 检查是否为警告级别
     */
    public function isWarning()
    {
        return $this->level === self::LEVEL_WARNING;
    }

    /**
     * 检查是否为信息级别
     */
    public function isInfo()
    {
        return $this->level === self::LEVEL_INFO;
    }

    /**
     * 检查是否为调试级别
     */
    public function isDebug()
    {
        return $this->level === self::LEVEL_DEBUG;
    }

    /**
     * 获取格式化的执行时间
     */
    public function getFormattedExecutionTimeAttribute()
    {
        if (!$this->execution_time) {
            return '-';
        }

        if ($this->execution_time < 1) {
            return round($this->execution_time * 1000) . 'ms';
        } else {
            return round($this->execution_time, 3) . 's';
        }
    }

    /**
     * 获取格式化的内存使用量
     */
    public function getFormattedMemoryUsageAttribute()
    {
        if (!$this->memory_usage) {
            return '-';
        }

        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->memory_usage;
        $i = 0;

        while ($bytes >= 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 获取简短的文件路径
     */
    public function getShortFilePathAttribute()
    {
        if (!$this->file) {
            return '-';
        }

        $parts = explode('/', $this->file);
        if (count($parts) > 3) {
            return '.../' . implode('/', array_slice($parts, -3));
        }

        return $this->file;
    }

    /**
     * 获取用户代理信息
     */
    public function getUserAgentInfoAttribute()
    {
        if (!$this->user_agent) {
            return null;
        }

        // 简单的用户代理解析
        $userAgent = $this->user_agent;
        $info = [
            'browser' => 'Unknown',
            'platform' => 'Unknown',
            'is_mobile' => false,
            'is_bot' => false,
        ];

        // 检测浏览器
        if (strpos($userAgent, 'Chrome') !== false) {
            $info['browser'] = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $info['browser'] = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $info['browser'] = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $info['browser'] = 'Edge';
        }

        // 检测平台
        if (strpos($userAgent, 'Windows') !== false) {
            $info['platform'] = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $info['platform'] = 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $info['platform'] = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $info['platform'] = 'Android';
            $info['is_mobile'] = true;
        } elseif (strpos($userAgent, 'iOS') !== false) {
            $info['platform'] = 'iOS';
            $info['is_mobile'] = true;
        }

        // 检测移动设备
        if (strpos($userAgent, 'Mobile') !== false || strpos($userAgent, 'iPhone') !== false) {
            $info['is_mobile'] = true;
        }

        // 检测机器人
        if (strpos($userAgent, 'bot') !== false || strpos($userAgent, 'crawler') !== false || strpos($userAgent, 'spider') !== false) {
            $info['is_bot'] = true;
        }

        return $info;
    }

    /**
     * 创建日志记录
     */
    public static function createLog($level, $channel, $message, $context = [], $additionalData = [])
    {
        $data = array_merge([
            'level' => $level,
            'channel' => $channel,
            'message' => $message,
            'context' => $context,
        ], $additionalData);

        return static::create($data);
    }

    /**
     * 记录调试日志
     */
    public static function debug($channel, $message, $context = [], $additionalData = [])
    {
        return static::createLog(self::LEVEL_DEBUG, $channel, $message, $context, $additionalData);
    }

    /**
     * 记录信息日志
     */
    public static function info($channel, $message, $context = [], $additionalData = [])
    {
        return static::createLog(self::LEVEL_INFO, $channel, $message, $context, $additionalData);
    }

    /**
     * 记录警告日志
     */
    public static function warning($channel, $message, $context = [], $additionalData = [])
    {
        return static::createLog(self::LEVEL_WARNING, $channel, $message, $context, $additionalData);
    }

    /**
     * 记录错误日志
     */
    public static function error($channel, $message, $context = [], $additionalData = [])
    {
        return static::createLog(self::LEVEL_ERROR, $channel, $message, $context, $additionalData);
    }

    /**
     * 记录严重日志
     */
    public static function critical($channel, $message, $context = [], $additionalData = [])
    {
        return static::createLog(self::LEVEL_CRITICAL, $channel, $message, $context, $additionalData);
    }
}
