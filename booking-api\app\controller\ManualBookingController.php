<?php

namespace app\controller;

use app\service\ManualBookingService;
use support\Request;
use support\Response;

/**
 * 手动订单管理控制器
 * 对标携程EBooking的手动订单功能
 */
class ManualBookingController extends BaseController
{
    /**
     * 手动订单服务
     *
     * @var ManualBookingService
     */
    private $manualBookingService;

    public function __construct()
    {
        $this->manualBookingService = new ManualBookingService();
    }

    /**
     * 获取可用房型和价格
     *
     * @param Request $request
     * @return Response
     */
    public function getAvailableOptions(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'check_in_date', 'check_out_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'check_in_date' => 'date',
                'check_out_date' => 'date',
                'rooms' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($params['check_in_date'] >= $params['check_out_date']) {
                return $this->error('入住日期必须早于退房日期');
            }

            // 验证入住日期不能是过去的日期
            if ($params['check_in_date'] < date('Y-m-d')) {
                return $this->error('入住日期不能是过去的日期');
            }

            $result = $this->manualBookingService->getAvailableRoomTypesAndRates(
                $params['hotel_id'],
                $params['check_in_date'],
                $params['check_out_date'],
                $params['rooms'] ?? 1
            );

            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取可用房型和价格');
        }
    }

    /**
     * 创建手动订单
     *
     * @param Request $request
     * @return Response
     */
    public function create(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'room_type_id', 'check_in_date', 'check_out_date',
                'rooms', 'adults', 'guest_name', 'guest_phone'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validate($data, [
                'hotel_id' => 'required|integer',
                'room_type_id' => 'required|integer',
                'rate_plan_id' => 'required|integer',
                'user_id' => 'integer',
                'check_in_date' => 'required|date',
                'check_out_date' => 'required|date',
                'rooms' => 'required|integer|min:1',
                'adults' => 'required|integer|min:1',
                'children' => 'integer|min:0',
                'room_price' => 'decimal|min:0',
                'service_fee' => 'decimal|min:0',
                'tax_fee' => 'decimal|min:0',
                'discount_amount' => 'decimal|min:0',
                'total_amount' => 'required|decimal|min:0'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['check_in_date'] >= $data['check_out_date']) {
                return $this->error('入住日期必须早于退房日期');
            }

            // 验证入住日期不能是过去的日期
            if ($data['check_in_date'] < date('Y-m-d')) {
                return $this->error('入住日期不能是过去的日期');
            }

            // 验证房间数和人数
            if ($data['rooms'] <= 0) {
                return $this->error('房间数必须大于0');
            }

            if ($data['adults'] <= 0) {
                return $this->error('成人数必须大于0');
            }

            if (isset($data['children']) && $data['children'] < 0) {
                return $this->error('儿童数不能小于0');
            }

            // 验证联系信息格式
            if (!empty($data['guest_phone']) && !preg_match('/^1[3-9]\d{9}$/', $data['guest_phone'])) {
                return $this->error('手机号格式不正确');
            }

            if (!empty($data['guest_email']) && !filter_var($data['guest_email'], FILTER_VALIDATE_EMAIL)) {
                return $this->error('邮箱格式不正确');
            }

            // 验证身份证号格式
            if (!empty($data['guest_id_card']) && !preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $data['guest_id_card'])) {
                return $this->error('身份证号格式不正确');
            }

            // 验证枚举值
            $enumValidations = [
                'booking_source' => ['manual', 'phone', 'email', 'walk_in', 'agent', 'corporate'],
                'booking_channel' => ['direct', 'phone', 'email', 'counter', 'agent'],
                'guest_nationality' => ['CN', 'US', 'UK', 'JP', 'KR', 'FR', 'DE', 'AU', 'CA', 'SG'],
                'currency' => ['CNY', 'USD', 'EUR', 'JPY', 'KRW', 'GBP', 'AUD', 'CAD', 'SGD']
            ];

            foreach ($enumValidations as $field => $validValues) {
                if (!empty($data[$field]) && !in_array($data[$field], $validValues)) {
                    return $this->error("字段 {$field} 的值无效");
                }
            }

            // 设置创建者信息
            $data['created_by'] = $this->getCurrentUserId($request);
            $data['booking_source'] = $data['booking_source'] ?? 'manual';
            $data['booking_channel'] = $data['booking_channel'] ?? 'direct';
            $data['currency'] = $data['currency'] ?? 'CNY';

            $result = $this->manualBookingService->createManualBooking($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建手动订单');
        }
    }

    /**
     * 修改手动订单
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function modify(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'check_in_date' => 'date',
                'check_out_date' => 'date',
                'rooms' => 'integer',
                'adults' => 'integer',
                'children' => 'integer',
                'room_price' => 'numeric',
                'service_fee' => 'numeric',
                'tax_fee' => 'numeric',
                'discount_amount' => 'numeric',
                'total_amount' => 'numeric'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if (!empty($data['check_in_date']) && !empty($data['check_out_date'])) {
                if ($data['check_in_date'] >= $data['check_out_date']) {
                    return $this->error('入住日期必须早于退房日期');
                }
            }

            // 验证房间数和人数
            if (isset($data['rooms']) && $data['rooms'] <= 0) {
                return $this->error('房间数必须大于0');
            }

            if (isset($data['adults']) && $data['adults'] <= 0) {
                return $this->error('成人数必须大于0');
            }

            if (isset($data['children']) && $data['children'] < 0) {
                return $this->error('儿童数不能小于0');
            }

            // 验证联系信息格式
            if (!empty($data['guest_phone']) && !preg_match('/^1[3-9]\d{9}$/', $data['guest_phone'])) {
                return $this->error('手机号格式不正确');
            }

            if (!empty($data['guest_email']) && !filter_var($data['guest_email'], FILTER_VALIDATE_EMAIL)) {
                return $this->error('邮箱格式不正确');
            }

            $userId = $this->getCurrentUserId($request);
            $result = $this->manualBookingService->modifyManualBooking($bookingId, $data, $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '修改手动订单');
        }
    }

    /**
     * 取消手动订单
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function cancel(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['reason']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (empty(trim($data['reason']))) {
                return $this->error('取消原因不能为空');
            }

            $userId = $this->getCurrentUserId($request);
            $result = $this->manualBookingService->cancelManualBooking($bookingId, $data['reason'], $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '取消手动订单');
        }
    }

    /**
     * 确认手动订单
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function confirm(Request $request, $id)
    {
        try {
            $bookingId = (int)$id;
            
            if ($bookingId <= 0) {
                return $this->error('订单ID无效');
            }

            $data = $this->getInput($request);
            $confirmationNo = $data['confirmation_no'] ?? null;

            // 如果提供了确认号，验证格式
            if (!empty($confirmationNo) && !preg_match('/^[A-Z0-9]{6,20}$/', $confirmationNo)) {
                return $this->error('确认号格式不正确');
            }

            $userId = $this->getCurrentUserId($request);
            $result = $this->manualBookingService->confirmManualBooking($bookingId, $confirmationNo, $userId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '确认手动订单');
        }
    }

    /**
     * 获取当前用户ID
     */
    protected function getCurrentUserId(Request $request)
    {
        // 从请求头或会话中获取当前用户ID
        // 这里需要根据实际的认证机制来实现
        return $request->header('X-User-Id') ?? $request->session()->get('user_id') ?? null;
    }
}
