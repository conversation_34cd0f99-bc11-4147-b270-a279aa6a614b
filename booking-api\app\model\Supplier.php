<?php

namespace app\model;

/**
 * 供应商模型
 * 对应数据库表：suppliers
 */
class Supplier extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'suppliers';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name',
        'name_en',
        'description',
        'supplier_type',
        'business_license',
        'tax_number',
        'legal_representative',
        'contact_person',
        'contact_phone',
        'contact_email',
        'address',
        'country',
        'province',
        'city',
        'postal_code',
        'website',
        'api_endpoint',
        'api_version',
        'api_key',
        'api_secret',
        'commission_rate',
        'currency',
        'payment_terms',
        'settlement_period',
        'credit_limit',
        'quality_score',
        'cooperation_start_date',
        'contract_end_date',
        'sync_enabled',
        'last_sync_at',
        'sync_status',
        'hotel_count',
        'room_count',
        'booking_count',
        'total_revenue',
        'status',
        'is_verified',
        'verified_at',
        'verified_by',
        'created_by',
        'updated_by'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'commission_rate' => 'decimal:2',
        'settlement_period' => 'integer',
        'credit_limit' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'cooperation_start_date' => 'date',
        'contract_end_date' => 'date',
        'sync_enabled' => 'boolean',
        'last_sync_at' => 'datetime',
        'hotel_count' => 'integer',
        'room_count' => 'integer',
        'booking_count' => 'integer',
        'total_revenue' => 'decimal:2',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'verified_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'created_by' => 'integer',
        'updated_by' => 'integer'
    ];

    /**
     * 应该隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'api_key',
        'api_secret',
    ];

    /**
     * 供应商类型常量
     */
    const TYPE_HOTEL_DIRECT = 'hotel_direct';
    const TYPE_HOTEL = 'hotel';
    const TYPE_TRAVEL_AGENCY = 'travel_agency';
    const TYPE_TOUR_OPERATOR = 'tour_operator';
    const TYPE_WHOLESALER = 'wholesaler';
    const TYPE_CONSOLIDATOR = 'consolidator';
    const TYPE_OTA = 'ota';
    const TYPE_GDS = 'gds';
    const TYPE_BEDBANK = 'bedbank';
    const TYPE_CORPORATE = 'corporate';
    const TYPE_INDIVIDUAL = 'individual';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_TERMINATED = 'terminated';

    /**
     * 风险等级常量
     */
    const RISK_LOW = 'low';
    const RISK_MEDIUM = 'medium';
    const RISK_HIGH = 'high';
    const RISK_CRITICAL = 'critical';

    /**
     * 获取供应商酒店映射
     */
    public function hotelMappings()
    {
        return $this->hasMany(SupplierHotelMapping::class);
    }

    /**
     * 获取供应商订单
     */
    public function supplierBookings()
    {
        return $this->hasMany(SupplierBooking::class);
    }

    /**
     * 获取同步日志
     */
    public function syncLogs()
    {
        return $this->hasMany(SupplierSyncLog::class);
    }

    /**
     * 作用域：活跃的供应商
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * 作用域：按质量评分排序
     */
    public function scopeByQuality($query)
    {
        return $query->orderBy('quality_score', 'desc');
    }

    /**
     * 获取供应商类型名称
     */
    public function getTypeNameAttribute()
    {
        $types = [
            self::TYPE_HOTEL_DIRECT => '酒店直连',
            self::TYPE_OTA => 'OTA平台',
            self::TYPE_WHOLESALER => '批发商',
            self::TYPE_BEDBANK => '床库',
        ];

        return $types[$this->type] ?? '未知';
    }

    /**
     * 检查API配置是否完整
     */
    public function hasValidApiConfig()
    {
        return !empty($this->api_endpoint) && !empty($this->api_key);
    }

    /**
     * 获取API配置项
     */
    public function getApiConfigValue($key, $default = null)
    {
        $config = $this->api_config ?? [];
        return $config[$key] ?? $default;
    }

    /**
     * 设置API配置项
     */
    public function setApiConfigValue($key, $value)
    {
        $config = $this->api_config ?? [];
        $config[$key] = $value;
        $this->api_config = $config;
        return $this;
    }

    /**
     * 获取映射配置项
     */
    public function getMappingConfigValue($key, $default = null)
    {
        $config = $this->mapping_config ?? [];
        return $config[$key] ?? $default;
    }

    /**
     * 设置映射配置项
     */
    public function setMappingConfigValue($key, $value)
    {
        $config = $this->mapping_config ?? [];
        $config[$key] = $value;
        $this->mapping_config = $config;
        return $this;
    }

    /**
     * 更新最后同步时间
     */
    public function updateLastSyncTime()
    {
        $this->last_sync_at = now();
        return $this->save();
    }

    /**
     * 计算佣金
     */
    public function calculateCommission($amount)
    {
        return $amount * ($this->commission_rate / 100);
    }

    /**
     * 检查是否需要同步
     */
    public function needsSync($intervalMinutes = 60)
    {
        if (!$this->last_sync_at) {
            return true;
        }

        $lastSync = $this->last_sync_at;
        $now = now();
        
        return $lastSync->diffInMinutes($now) >= $intervalMinutes;
    }

    /**
     * 获取超时时间（秒）
     */
    public function getTimeoutSeconds()
    {
        return $this->timeout_seconds ?: 30;
    }

    /**
     * 获取重试次数
     */
    public function getRetryTimes()
    {
        return $this->retry_times ?: 3;
    }

    /**
     * 获取结算周期名称
     */
    public function getSettlementCycleNameAttribute()
    {
        $cycles = [
            7 => '周结',
            15 => '半月结',
            30 => '月结',
            90 => '季结',
        ];

        return $cycles[$this->settlement_cycle] ?? $this->settlement_cycle . '天结';
    }

    /**
     * 启用供应商
     */
    public function enable()
    {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }

    /**
     * 禁用供应商
     */
    public function disable()
    {
        $this->status = self::STATUS_INACTIVE;
        return $this->save();
    }

    /**
     * 测试API连接
     */
    public function testApiConnection()
    {
        if (!$this->hasValidApiConfig()) {
            return [
                'success' => false,
                'message' => 'API配置不完整'
            ];
        }

        // 这里应该实现实际的API连接测试
        // 临时返回成功
        return [
            'success' => true,
            'message' => 'API连接正常',
            'response_time' => mt_rand(100, 500) . 'ms'
        ];
    }



    /**
     * 获取供应商类型名称
     */
    public function getSupplierTypeNameAttribute()
    {
        $types = [
            self::TYPE_HOTEL => '酒店',
            self::TYPE_TRAVEL_AGENCY => '旅行社',
            self::TYPE_TOUR_OPERATOR => '旅游运营商',
            self::TYPE_WHOLESALER => '批发商',
            self::TYPE_CONSOLIDATOR => '整合商',
            self::TYPE_OTA => 'OTA平台',
            self::TYPE_GDS => 'GDS系统',
            self::TYPE_CORPORATE => '企业客户',
            self::TYPE_INDIVIDUAL => '个人供应商'
        ];

        return $types[$this->supplier_type ?? $this->type] ?? ($this->supplier_name ?? $this->name);
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute()
    {
        $statuses = [
            self::STATUS_ACTIVE => '正常',
            self::STATUS_INACTIVE => '停用',
            self::STATUS_SUSPENDED => '暂停',
            self::STATUS_TERMINATED => '终止'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * 获取风险等级名称
     */
    public function getRiskLevelNameAttribute()
    {
        $levels = [
            self::RISK_LOW => '低风险',
            self::RISK_MEDIUM => '中等风险',
            self::RISK_HIGH => '高风险',
            self::RISK_CRITICAL => '极高风险'
        ];

        return $levels[$this->risk_level] ?? $this->risk_level;
    }

    /**
     * 获取综合评分
     */
    public function getOverallScoreAttribute()
    {
        $qualityScore = $this->quality_score ?? 0;
        $serviceScore = $this->service_score ?? 0;
        $cooperationScore = $this->cooperation_score ?? 0;

        return round(($qualityScore + $serviceScore + $cooperationScore) / 3, 2);
    }

    /**
     * 获取好评率
     */
    public function getPraiseRateAttribute()
    {
        $totalReviews = $this->praise_count + $this->complaint_count;

        if ($totalReviews === 0) {
            return 0;
        }

        return round(($this->praise_count / $totalReviews) * 100, 2);
    }

    /**
     * 检查是否可以合作
     */
    public function canCooperate()
    {
        return $this->status === self::STATUS_ACTIVE &&
               ($this->is_verified ?? true);
    }

    /**
     * 检查合同是否有效
     */
    public function hasValidContract()
    {
        if (!$this->cooperation_start_date || !$this->cooperation_end_date) {
            return false;
        }

        $today = now()->toDateString();
        return $this->cooperation_start_date <= $today && $this->cooperation_end_date >= $today;
    }

    /**
     * 获取供应商摘要信息
     */
    public function getSummaryInfo()
    {
        return [
            'id' => $this->id,
            'supplier_code' => $this->supplier_code ?? $this->code,
            'supplier_name' => $this->supplier_name ?? $this->name,
            'supplier_type' => $this->supplier_type ?? $this->type,
            'supplier_type_name' => $this->supplier_type_name,
            'status' => $this->status,
            'status_name' => $this->status_name,
            'risk_level' => $this->risk_level,
            'risk_level_name' => $this->risk_level_name,
            'is_active' => $this->is_active,
            'is_verified' => $this->is_verified ?? true,
            'contact_info' => [
                'contact_person' => $this->contact_person,
                'contact_phone' => $this->contact_phone,
                'contact_email' => $this->contact_email,
                'city' => $this->city,
                'province' => $this->province,
                'country' => $this->country
            ],
            'scores' => [
                'quality_score' => $this->quality_score,
                'service_score' => $this->service_score,
                'cooperation_score' => $this->cooperation_score,
                'overall_score' => $this->overall_score,
                'praise_rate' => $this->praise_rate . '%'
            ],
            'cooperation_info' => [
                'commission_rate' => $this->commission_rate,
                'cooperation_start_date' => $this->cooperation_start_date,
                'cooperation_end_date' => $this->cooperation_end_date,
                'has_valid_contract' => $this->hasValidContract(),
                'can_cooperate' => $this->canCooperate()
            ],
            'statistics' => [
                'total_bookings' => $this->total_bookings ?? 0,
                'total_revenue' => $this->total_revenue ?? 0,
                'complaint_count' => $this->complaint_count ?? 0,
                'praise_count' => $this->praise_count ?? 0,
                'last_cooperation_date' => $this->last_cooperation_date
            ]
        ];
    }

    /**
     * 更新统计数据
     */
    public function updateStatistics()
    {
        // 这里需要根据实际的订单关联来计算统计数据
        // 暂时保持现有逻辑
        $this->save();
    }

    /**
     * 计算风险等级
     */
    public function calculateRiskLevel()
    {
        $riskScore = 0;

        // 基于投诉率计算风险
        if ($this->praise_rate < 60) {
            $riskScore += 3;
        } elseif ($this->praise_rate < 80) {
            $riskScore += 2;
        } elseif ($this->praise_rate < 90) {
            $riskScore += 1;
        }

        // 基于综合评分计算风险
        if ($this->overall_score < 3.0) {
            $riskScore += 3;
        } elseif ($this->overall_score < 4.0) {
            $riskScore += 2;
        } elseif ($this->overall_score < 4.5) {
            $riskScore += 1;
        }

        // 基于合同状态计算风险
        if (!$this->hasValidContract()) {
            $riskScore += 2;
        }

        // 基于验证状态计算风险
        if (!($this->is_verified ?? true)) {
            $riskScore += 2;
        }

        // 确定风险等级
        if ($riskScore >= 7) {
            $this->risk_level = self::RISK_CRITICAL;
        } elseif ($riskScore >= 5) {
            $this->risk_level = self::RISK_HIGH;
        } elseif ($riskScore >= 3) {
            $this->risk_level = self::RISK_MEDIUM;
        } else {
            $this->risk_level = self::RISK_LOW;
        }

        $this->save();
    }
}
