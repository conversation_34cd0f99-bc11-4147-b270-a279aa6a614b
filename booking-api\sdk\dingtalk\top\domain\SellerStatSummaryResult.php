<?php

/**
 * 返回结果
 * <AUTHOR> create
 */
class SellerStatSummaryResult
{
	
	/** 
	 * rate平均分
	 **/
	public $avg_rate_score;
	
	/** 
	 * 可售商品数量
	 **/
	public $can_sale_amount;
	
	/** 
	 * 日期
	 **/
	public $date_param;
	
	/** 
	 * 曝光总数
	 **/
	public $exposed_amount;
	
	/** 
	 * 曝光率
	 **/
	public $exposed_percent;
	
	/** 
	 * hid参数
	 **/
	public $hid_param;
	
	/** 
	 * rate最高分
	 **/
	public $max_rate_score;
	
	/** 
	 * rate最低分
	 **/
	public $min_rate_score;
	
	/** 
	 * 选品保留商品数量
	 **/
	public $selected_amount;
	
	/** 
	 * 选品情况
	 **/
	public $selection_message_info;
	
	/** 
	 * 选品情况
	 **/
	public $selection_message_info_json;
	
	/** 
	 * sellerId参数
	 **/
	public $seller_id_param;
	
	/** 
	 * 标准酒店维度曝光总数
	 **/
	public $shid_total_amount;
	
	/** 
	 * supplier参数
	 **/
	public $supplier_param;
	
	/** 
	 * 商品总数
	 **/
	public $total_amount;
	
	/** 
	 * 不可售情况
	 **/
	public $unsale_reason_info_json;
	
	/** 
	 * 不可售情况
	 **/
	public $unsale_reseason_info;
	
	/** 
	 * vendor参数
	 **/
	public $vendor_param;	
}
?>