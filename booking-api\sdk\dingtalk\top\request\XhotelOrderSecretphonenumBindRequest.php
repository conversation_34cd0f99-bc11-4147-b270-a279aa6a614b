<?php
/**
 * TOP API: taobao.xhotel.order.secretphonenum.bind request
 * 
 * <AUTHOR> create
 * @since 1.0, 2024.05.22
 */
class XhotelOrderSecretphonenumBindRequest
{
	/** 
	 * 隐私号绑定参数
	 **/
	private $secretPhoneNumberBindParam;
	
	private $apiParas = array();
	
	public function setSecretPhoneNumberBindParam($secretPhoneNumberBindParam)
	{
		$this->secretPhoneNumberBindParam = $secretPhoneNumberBindParam;
		$this->apiParas["secret_phone_number_bind_param"] = $secretPhoneNumberBindParam;
	}

	public function getSecretPhoneNumberBindParam()
	{
		return $this->secretPhoneNumberBindParam;
	}

	public function getApiMethodName()
	{
		return "taobao.xhotel.order.secretphonenum.bind";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
