<?php

namespace app\service;

use support\Cache;
use support\Log;

/**
 * 缓存服务类
 * 提供统一的缓存管理功能
 */
class CacheService
{
    /**
     * 缓存前缀
     */
    const PREFIX = 'booking_system:';
    
    /**
     * 默认缓存时间（秒）
     */
    const DEFAULT_TTL = 3600;
    
    /**
     * 缓存键分组
     */
    const GROUP_HOTEL = 'hotel';
    const GROUP_ROOM_TYPE = 'room_type';
    const GROUP_RATE_PLAN = 'rate_plan';
    const GROUP_INVENTORY = 'inventory';
    const GROUP_BOOKING = 'booking';
    const GROUP_USER = 'user';
    const GROUP_CONFIG = 'config';
    const GROUP_SUPPLIER = 'supplier';
    const GROUP_OTA = 'ota';

    /**
     * 生成缓存键
     *
     * @param string $group
     * @param string $key
     * @param array $params
     * @return string
     */
    public static function generateKey(string $group, string $key, array $params = []): string
    {
        $keyParts = [self::PREFIX, $group, $key];
        
        if (!empty($params)) {
            $keyParts[] = md5(serialize($params));
        }
        
        return implode(':', $keyParts);
    }

    /**
     * 获取缓存
     *
     * @param string $group
     * @param string $key
     * @param array $params
     * @return mixed
     */
    public static function get(string $group, string $key, array $params = [])
    {
        $cacheKey = self::generateKey($group, $key, $params);
        
        try {
            return Cache::get($cacheKey);
        } catch (\Exception $e) {
            Log::warning('缓存获取失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 设置缓存
     *
     * @param string $group
     * @param string $key
     * @param mixed $value
     * @param int $ttl
     * @param array $params
     * @return bool
     */
    public static function set(string $group, string $key, $value, int $ttl = self::DEFAULT_TTL, array $params = []): bool
    {
        $cacheKey = self::generateKey($group, $key, $params);
        
        try {
            return Cache::set($cacheKey, $value, $ttl);
        } catch (\Exception $e) {
            Log::warning('缓存设置失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param string $group
     * @param string $key
     * @param array $params
     * @return bool
     */
    public static function delete(string $group, string $key, array $params = []): bool
    {
        $cacheKey = self::generateKey($group, $key, $params);
        
        try {
            return Cache::delete($cacheKey);
        } catch (\Exception $e) {
            Log::warning('缓存删除失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取或设置缓存（如果不存在则执行回调函数）
     *
     * @param string $group
     * @param string $key
     * @param callable $callback
     * @param int $ttl
     * @param array $params
     * @return mixed
     */
    public static function remember(string $group, string $key, callable $callback, int $ttl = self::DEFAULT_TTL, array $params = [])
    {
        $value = self::get($group, $key, $params);
        
        if ($value !== null) {
            return $value;
        }
        
        try {
            $value = $callback();
            self::set($group, $key, $value, $ttl, $params);
            return $value;
        } catch (\Exception $e) {
            Log::error('缓存回调执行失败', [
                'group' => $group,
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 清除分组缓存
     *
     * @param string $group
     * @return bool
     */
    public static function clearGroup(string $group): bool
    {
        $pattern = self::PREFIX . $group . ':*';
        
        try {
            // 这里需要根据具体的缓存实现来清除匹配的缓存
            // Redis实现示例：
            // $redis = Cache::getRedis();
            // $keys = $redis->keys($pattern);
            // if (!empty($keys)) {
            //     $redis->del($keys);
            // }
            
            Log::info('清除分组缓存', ['group' => $group, 'pattern' => $pattern]);
            return true;
        } catch (\Exception $e) {
            Log::warning('清除分组缓存失败', [
                'group' => $group,
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 清除所有缓存
     *
     * @return bool
     */
    public static function clearAll(): bool
    {
        $pattern = self::PREFIX . '*';
        
        try {
            // 这里需要根据具体的缓存实现来清除所有缓存
            Log::info('清除所有缓存', ['pattern' => $pattern]);
            return true;
        } catch (\Exception $e) {
            Log::warning('清除所有缓存失败', [
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 缓存酒店信息
     *
     * @param int $hotelId
     * @param array $hotelData
     * @param int $ttl
     * @return bool
     */
    public static function cacheHotel(int $hotelId, array $hotelData, int $ttl = self::DEFAULT_TTL): bool
    {
        return self::set(self::GROUP_HOTEL, 'info', $hotelData, $ttl, ['id' => $hotelId]);
    }

    /**
     * 获取缓存的酒店信息
     *
     * @param int $hotelId
     * @return array|null
     */
    public static function getCachedHotel(int $hotelId): ?array
    {
        return self::get(self::GROUP_HOTEL, 'info', ['id' => $hotelId]);
    }

    /**
     * 缓存房型信息
     *
     * @param int $roomTypeId
     * @param array $roomTypeData
     * @param int $ttl
     * @return bool
     */
    public static function cacheRoomType(int $roomTypeId, array $roomTypeData, int $ttl = self::DEFAULT_TTL): bool
    {
        return self::set(self::GROUP_ROOM_TYPE, 'info', $roomTypeData, $ttl, ['id' => $roomTypeId]);
    }

    /**
     * 获取缓存的房型信息
     *
     * @param int $roomTypeId
     * @return array|null
     */
    public static function getCachedRoomType(int $roomTypeId): ?array
    {
        return self::get(self::GROUP_ROOM_TYPE, 'info', ['id' => $roomTypeId]);
    }

    /**
     * 缓存库存信息
     *
     * @param int $roomTypeId
     * @param string $date
     * @param array $inventoryData
     * @param int $ttl
     * @return bool
     */
    public static function cacheInventory(int $roomTypeId, string $date, array $inventoryData, int $ttl = 1800): bool
    {
        return self::set(self::GROUP_INVENTORY, 'daily', $inventoryData, $ttl, [
            'room_type_id' => $roomTypeId,
            'date' => $date
        ]);
    }

    /**
     * 获取缓存的库存信息
     *
     * @param int $roomTypeId
     * @param string $date
     * @return array|null
     */
    public static function getCachedInventory(int $roomTypeId, string $date): ?array
    {
        return self::get(self::GROUP_INVENTORY, 'daily', [
            'room_type_id' => $roomTypeId,
            'date' => $date
        ]);
    }

    /**
     * 缓存价格信息
     *
     * @param int $ratePlanId
     * @param string $date
     * @param array $priceData
     * @param int $ttl
     * @return bool
     */
    public static function cachePrice(int $ratePlanId, string $date, array $priceData, int $ttl = 1800): bool
    {
        return self::set(self::GROUP_RATE_PLAN, 'daily_price', $priceData, $ttl, [
            'rate_plan_id' => $ratePlanId,
            'date' => $date
        ]);
    }

    /**
     * 获取缓存的价格信息
     *
     * @param int $ratePlanId
     * @param string $date
     * @return array|null
     */
    public static function getCachedPrice(int $ratePlanId, string $date): ?array
    {
        return self::get(self::GROUP_RATE_PLAN, 'daily_price', [
            'rate_plan_id' => $ratePlanId,
            'date' => $date
        ]);
    }

    /**
     * 缓存用户信息
     *
     * @param int $userId
     * @param array $userData
     * @param int $ttl
     * @return bool
     */
    public static function cacheUser(int $userId, array $userData, int $ttl = self::DEFAULT_TTL): bool
    {
        return self::set(self::GROUP_USER, 'info', $userData, $ttl, ['id' => $userId]);
    }

    /**
     * 获取缓存的用户信息
     *
     * @param int $userId
     * @return array|null
     */
    public static function getCachedUser(int $userId): ?array
    {
        return self::get(self::GROUP_USER, 'info', ['id' => $userId]);
    }

    /**
     * 清除相关缓存
     *
     * @param string $type
     * @param int $id
     * @return bool
     */
    public static function clearRelatedCache(string $type, int $id): bool
    {
        switch ($type) {
            case 'hotel':
                self::delete(self::GROUP_HOTEL, 'info', ['id' => $id]);
                self::clearGroup(self::GROUP_ROOM_TYPE); // 清除相关房型缓存
                self::clearGroup(self::GROUP_INVENTORY); // 清除相关库存缓存
                break;
                
            case 'room_type':
                self::delete(self::GROUP_ROOM_TYPE, 'info', ['id' => $id]);
                self::clearGroup(self::GROUP_INVENTORY); // 清除相关库存缓存
                self::clearGroup(self::GROUP_RATE_PLAN); // 清除相关价格缓存
                break;
                
            case 'rate_plan':
                self::clearGroup(self::GROUP_RATE_PLAN); // 清除所有价格缓存
                break;
                
            case 'booking':
                self::clearGroup(self::GROUP_INVENTORY); // 清除库存缓存
                break;
                
            case 'user':
                self::delete(self::GROUP_USER, 'info', ['id' => $id]);
                break;
        }
        
        return true;
    }

    /**
     * 获取缓存统计信息
     *
     * @return array
     */
    public static function getStats(): array
    {
        // 这里需要根据具体的缓存实现来获取统计信息
        return [
            'total_keys' => 0,
            'memory_usage' => 0,
            'hit_rate' => 0,
            'groups' => []
        ];
    }
}
