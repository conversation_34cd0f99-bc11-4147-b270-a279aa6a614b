<?php

namespace app\controller;

use app\model\RoomType;
use app\model\RoomTypePolicy;
use support\Request;
use support\Response;

class RoomTypePolicyController extends BaseController
{
    public function index(Request $request, $id): Response
    {
        try {
            $roomType = RoomType::find($id);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $policy = RoomTypePolicy::where('room_type_id', $id)->first();
            return $this->success($policy ? $policy->toArray() : null);
        } catch (\Exception $e) {
            return $this->error('获取房型政策失败: ' . $e->getMessage());
        }
    }

    public function show(Request $request, $id, $policyId): Response
    {
        try {
            $policy = RoomTypePolicy::where('room_type_id', $id)->where('id', $policyId)->first();
            if (!$policy) {
                return $this->error('政策不存在', 404);
            }
            return $this->success($policy->toArray());
        } catch (\Exception $e) {
            return $this->error('获取政策详情失败: ' . $e->getMessage());
        }
    }

    public function store(Request $request, $id): Response
    {
        try {
            $roomType = RoomType::find($id);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $data = $request->all();
            $data['room_type_id'] = (int)$id;

            $policy = RoomTypePolicy::create($data);
            return $this->success($policy->toArray(), '政策创建成功');
        } catch (\Exception $e) {
            return $this->error('创建政策失败: ' . $e->getMessage());
        }
    }

    public function update(Request $request, $id, $policyId): Response
    {
        try {
            $policy = RoomTypePolicy::where('room_type_id', $id)->where('id', $policyId)->first();
            if (!$policy) {
                return $this->error('政策不存在', 404);
            }

            $data = $request->all();
            $policy->update($data);

            return $this->success($policy->fresh()->toArray(), '政策更新成功');
        } catch (\Exception $e) {
            return $this->error('更新政策失败: ' . $e->getMessage());
        }
    }

    public function destroy(Request $request, $id, $policyId): Response
    {
        try {
            $policy = RoomTypePolicy::where('room_type_id', $id)->where('id', $policyId)->first();
            if (!$policy) {
                return $this->error('政策不存在', 404);
            }

            $policy->delete();
            return $this->success(null, '政策删除成功');
        } catch (\Exception $e) {
            return $this->error('删除政策失败: ' . $e->getMessage());
        }
    }
}

