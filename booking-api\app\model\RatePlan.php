<?php

namespace app\model;

use Carbon\Carbon;

/**
 * 价格计划模型
 * 对应数据库表：rate_plans
 */
class RatePlan extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'rate_plans';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'room_type_id',
        'code',
        'name',
        'description',
        'plan_type',

        // 基础价格设置
        'base_price',
        'currency',
        'price_type',

        // 早餐设置
        'breakfast_included',
        'breakfast_count',
        'breakfast_type',
        'breakfast_description',

        // 销售限制
        'min_stay',
        'max_stay',
        'min_advance_booking',
        'max_advance_booking',

        // 入住时间限制
        'check_in_time_start',
        'check_in_time_end',
        'check_out_time',

        // 客人限制
        'max_occupancy',
        'max_adults',
        'max_children',

        // 取消政策
        'cancellation_policy',
        'cancellation_deadline',
        'cancellation_fee_type',
        'cancellation_fee_amount',

        // 修改政策
        'modification_allowed',
        'modification_deadline',
        'modification_fee',

        // 预付政策
        'prepayment_required',
        'prepayment_type',
        'prepayment_amount',
        'prepayment_deadline',

        // 销售渠道
        'channel_restrictions',
        'member_only',

        // 促销设置
        'promotion_type',
        'promotion_description',
        'promotion_start_date',
        'promotion_end_date',

        // 其他信息
        'terms_conditions',
        'special_requests',

        // 状态信息
        'is_active',
        'is_published',
        'priority',
        'published_at',
        'unpublished_at',
        'activated_at',
        'deactivated_at',

        // 兼容旧字段
        'advance_booking_days',
        'is_refundable',
        'includes',
        'valid_from',
        'valid_to',
        'status',
        'sort_order',

        // 新增字段 - 对标携程EBooking
        'rate_plan_category',
        'rate_plan_grade',
        'market_segment',
        'booking_source',
        'distribution_channels',
        'rate_calculation_method',
        'dynamic_pricing_enabled',
        'seasonal_adjustments',
        'day_of_week_adjustments',
        'length_of_stay_discounts',
        'early_booking_discounts',
        'last_minute_discounts',
        'group_booking_discounts',
        'loyalty_program_benefits',
        'corporate_rates',
        'government_rates',
        'package_inclusions',
        'addon_services',
        'upgrade_options',
        'meal_plan_options',
        'transportation_included',
        'activity_packages',
        'spa_packages',
        'business_packages',
        'family_packages',
        'honeymoon_packages',
        'special_occasion_packages',
        'seasonal_packages',
        'weekend_packages',
        'weekday_packages',
        'holiday_packages',
        'event_packages',
        'conference_packages',
        'wedding_packages',
        'group_packages',
        'extended_stay_packages',
        'pet_friendly_packages',
        'accessibility_packages',
        'eco_friendly_packages',
        'luxury_packages',
        'budget_packages',
        'all_inclusive_packages',
        'half_board_packages',
        'full_board_packages',
        'room_only_packages',
        'bed_breakfast_packages',
        'american_plan_packages',
        'european_plan_packages',
        'continental_plan_packages',
        'modified_american_plan_packages',
        'rate_restrictions',
        'booking_window',
        'stay_date_restrictions',
        'blackout_dates',
        'minimum_revenue_requirements',
        'maximum_discount_allowed',
        'commission_structure',
        'net_rates',
        'commissionable_rates',
        'markup_rates',
        'currency_conversion_rules',
        'tax_inclusion_rules',
        'service_charge_rules',
        'resort_fee_rules',
        'city_tax_rules',
        'environmental_tax_rules',
        'occupancy_tax_rules',
        'additional_person_charges',
        'child_charges',
        'infant_charges',
        'extra_bed_charges',
        'rollaway_bed_charges',
        'crib_charges',
        'pet_charges',
        'parking_charges',
        'wifi_charges',
        'breakfast_charges',
        'lunch_charges',
        'dinner_charges',
        'minibar_charges',
        'laundry_charges',
        'spa_charges',
        'fitness_charges',
        'business_center_charges',
        'airport_transfer_charges',
        'local_transport_charges',
        'tour_charges',
        'activity_charges',
        'equipment_rental_charges',
        'damage_deposit_requirements',
        'security_deposit_requirements',
        'guarantee_requirements',
        'payment_terms',
        'invoice_terms',
        'settlement_terms',
        'reporting_requirements',
        'audit_requirements',
        'compliance_requirements',
        'legal_terms',
        'privacy_terms',
        'data_protection_terms',
        'marketing_permissions',
        'communication_preferences',
        'notification_settings',
        'alert_settings',
        'reporting_settings',
        'integration_settings',
        'api_settings',
        'webhook_settings',
        'sync_settings',
        'backup_settings',
        'archive_settings',
        'retention_settings',
        'deletion_settings',
        'recovery_settings',
        'monitoring_settings',
        'performance_settings',
        'optimization_settings',
        'caching_settings',
        'security_settings',
        'access_control_settings',
        'permission_settings',
        'role_settings',
        'user_settings',
        'admin_settings',
        'system_settings',
        'configuration_settings',
        'customization_settings',
        'localization_settings',
        'internationalization_settings',
        'multi_language_settings',
        'multi_currency_settings',
        'multi_timezone_settings',
        'multi_property_settings',
        'multi_brand_settings',
        'multi_channel_settings',
        'multi_platform_settings',
        'created_by',
        'updated_by'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'room_type_id' => 'integer',

        // 基础价格设置
        'base_price' => 'decimal:2',

        // 早餐设置
        'breakfast_included' => 'boolean',
        'breakfast_count' => 'integer',

        // 销售限制
        'min_stay' => 'integer',
        'max_stay' => 'integer',
        'min_advance_booking' => 'integer',
        'max_advance_booking' => 'integer',

        // 客人限制
        'max_occupancy' => 'integer',
        'max_adults' => 'integer',
        'max_children' => 'integer',

        // 取消政策
        'cancellation_deadline' => 'integer',
        'cancellation_fee_amount' => 'decimal:2',

        // 修改政策
        'modification_allowed' => 'boolean',
        'modification_deadline' => 'integer',
        'modification_fee' => 'decimal:2',

        // 预付政策
        'prepayment_required' => 'boolean',
        'prepayment_amount' => 'decimal:2',
        'prepayment_deadline' => 'integer',

        // 销售渠道
        'channel_restrictions' => 'array',
        'member_only' => 'boolean',

        // 促销设置
        'promotion_start_date' => 'date',
        'promotion_end_date' => 'date',

        // 状态信息
        'is_active' => 'boolean',
        'is_published' => 'boolean',
        'priority' => 'integer',
        'published_at' => 'datetime',
        'unpublished_at' => 'datetime',
        'activated_at' => 'datetime',
        'deactivated_at' => 'datetime',

        // 兼容旧字段
        'advance_booking_days' => 'integer',
        'is_refundable' => 'boolean',
        'includes' => 'array',
        'valid_from' => 'date',
        'valid_to' => 'date',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',

        // 新增字段转换
        'distribution_channels' => 'array',
        'dynamic_pricing_enabled' => 'boolean',
        'seasonal_adjustments' => 'array',
        'day_of_week_adjustments' => 'array',
        'length_of_stay_discounts' => 'array',
        'early_booking_discounts' => 'array',
        'last_minute_discounts' => 'array',
        'group_booking_discounts' => 'array',
        'loyalty_program_benefits' => 'array',
        'corporate_rates' => 'array',
        'government_rates' => 'array',
        'package_inclusions' => 'array',
        'addon_services' => 'array',
        'upgrade_options' => 'array',
        'meal_plan_options' => 'array',
        'transportation_included' => 'boolean',
        'activity_packages' => 'array',
        'spa_packages' => 'array',
        'business_packages' => 'array',
        'family_packages' => 'array',
        'honeymoon_packages' => 'array',
        'special_occasion_packages' => 'array',
        'seasonal_packages' => 'array',
        'weekend_packages' => 'array',
        'weekday_packages' => 'array',
        'holiday_packages' => 'array',
        'event_packages' => 'array',
        'conference_packages' => 'array',
        'wedding_packages' => 'array',
        'group_packages' => 'array',
        'extended_stay_packages' => 'array',
        'pet_friendly_packages' => 'array',
        'accessibility_packages' => 'array',
        'eco_friendly_packages' => 'array',
        'luxury_packages' => 'array',
        'budget_packages' => 'array',
        'all_inclusive_packages' => 'array',
        'half_board_packages' => 'array',
        'full_board_packages' => 'array',
        'room_only_packages' => 'array',
        'bed_breakfast_packages' => 'array',
        'american_plan_packages' => 'array',
        'european_plan_packages' => 'array',
        'continental_plan_packages' => 'array',
        'modified_american_plan_packages' => 'array',
        'rate_restrictions' => 'array',
        'booking_window' => 'array',
        'stay_date_restrictions' => 'array',
        'blackout_dates' => 'array',
        'minimum_revenue_requirements' => 'decimal:2',
        'maximum_discount_allowed' => 'decimal:2',
        'commission_structure' => 'array',
        'net_rates' => 'array',
        'commissionable_rates' => 'array',
        'markup_rates' => 'array',
        'currency_conversion_rules' => 'array',
        'tax_inclusion_rules' => 'array',
        'service_charge_rules' => 'array',
        'resort_fee_rules' => 'array',
        'city_tax_rules' => 'array',
        'environmental_tax_rules' => 'array',
        'occupancy_tax_rules' => 'array',
        'additional_person_charges' => 'array',
        'child_charges' => 'array',
        'infant_charges' => 'array',
        'extra_bed_charges' => 'array',
        'rollaway_bed_charges' => 'array',
        'crib_charges' => 'array',
        'pet_charges' => 'array',
        'parking_charges' => 'array',
        'wifi_charges' => 'array',
        'breakfast_charges' => 'array',
        'lunch_charges' => 'array',
        'dinner_charges' => 'array',
        'minibar_charges' => 'array',
        'laundry_charges' => 'array',
        'spa_charges' => 'array',
        'fitness_charges' => 'array',
        'business_center_charges' => 'array',
        'airport_transfer_charges' => 'array',
        'local_transport_charges' => 'array',
        'tour_charges' => 'array',
        'activity_charges' => 'array',
        'equipment_rental_charges' => 'array',
        'damage_deposit_requirements' => 'array',
        'security_deposit_requirements' => 'array',
        'guarantee_requirements' => 'array',
        'payment_terms' => 'array',
        'invoice_terms' => 'array',
        'settlement_terms' => 'array',
        'reporting_requirements' => 'array',
        'audit_requirements' => 'array',
        'compliance_requirements' => 'array',
        'legal_terms' => 'array',
        'privacy_terms' => 'array',
        'data_protection_terms' => 'array',
        'marketing_permissions' => 'array',
        'communication_preferences' => 'array',
        'notification_settings' => 'array',
        'alert_settings' => 'array',
        'reporting_settings' => 'array',
        'integration_settings' => 'array',
        'api_settings' => 'array',
        'webhook_settings' => 'array',
        'sync_settings' => 'array',
        'backup_settings' => 'array',
        'archive_settings' => 'array',
        'retention_settings' => 'array',
        'deletion_settings' => 'array',
        'recovery_settings' => 'array',
        'monitoring_settings' => 'array',
        'performance_settings' => 'array',
        'optimization_settings' => 'array',
        'caching_settings' => 'array',
        'security_settings' => 'array',
        'access_control_settings' => 'array',
        'permission_settings' => 'array',
        'role_settings' => 'array',
        'user_settings' => 'array',
        'admin_settings' => 'array',
        'system_settings' => 'array',
        'configuration_settings' => 'array',
        'customization_settings' => 'array',
        'localization_settings' => 'array',
        'internationalization_settings' => 'array',
        'multi_language_settings' => 'array',
        'multi_currency_settings' => 'array',
        'multi_timezone_settings' => 'array',
        'multi_property_settings' => 'array',
        'multi_brand_settings' => 'array',
        'multi_channel_settings' => 'array',
        'multi_platform_settings' => 'array',
        'created_by' => 'integer',
        'updated_by' => 'integer',
    ];

    /**
     * 计划类型常量
     */
    const PLAN_TYPE_STANDARD = 'standard';
    const PLAN_TYPE_PROMOTION = 'promotion';
    const PLAN_TYPE_PACKAGE = 'package';
    const PLAN_TYPE_CORPORATE = 'corporate';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取所属房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * 作用域：活跃状态
     * 重写父类方法，使用 is_active 字段而不是 status 字段
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    /**
     * 作用域：非活跃状态
     * 重写父类方法，使用 is_active 字段而不是 status 字段
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', 0);
    }

    /**
     * 关联OTA渠道（多对多）
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function otaChannels()
    {
        return $this->belongsToMany(
            OtaChannel::class,
            'rate_plan_ota_channels',
            'rate_plan_id',
            'ota_channel_id'
        )->withPivot([
            'hotel_id',
            'channel_rate_plan_code',
            'commission_rate',
            'markup_rate',
            'markup_amount',
            'min_stay_override',
            'max_stay_override',
            'booking_window_override',
            'cancellation_policy_override',
            'is_active',
            'effective_from',
            'effective_to',
            'priority',
            'created_at',
            'updated_at'
        ])->withTimestamps();
    }

    /**
     * 关联OTA渠道绑定记录（一对多）
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function ratePlanOtaChannels()
    {
        return $this->hasMany(RatePlanOtaChannel::class, 'rate_plan_id');
    }

    /**
     * 获取房型价格
     */
    public function roomRates()
    {
        return $this->hasMany(RoomRate::class);
    }

    /**
     * 获取订单
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * 作用域：按计划类型筛选
     */
    public function scopeByPlanType($query, $planType)
    {
        return $query->where('plan_type', $planType);
    }

    /**
     * 作用域：有效的计划
     */
    public function scopeValid($query, $date = null)
    {
        $date = $date ?: date('Y-m-d');
        
        return $query->where(function ($q) use ($date) {
            $q->whereNull('valid_from')
              ->orWhere('valid_from', '<=', $date);
        })->where(function ($q) use ($date) {
            $q->whereNull('valid_to')
              ->orWhere('valid_to', '>=', $date);
        });
    }

    /**
     * 作用域：可退款的
     */
    public function scopeRefundable($query)
    {
        return $query->where('is_refundable', true);
    }

    /**
     * 作用域：已发布的
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    /**
     * 作用域：已激活的
     */
    public function scopeActivated($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按房型筛选
     */
    public function scopeByRoomType($query, $roomTypeId)
    {
        return $query->where('room_type_id', $roomTypeId);
    }

    /**
     * 作用域：按价格范围筛选
     */
    public function scopeByPriceRange($query, $minPrice = null, $maxPrice = null)
    {
        if ($minPrice !== null) {
            $query->where('base_price', '>=', $minPrice);
        }
        if ($maxPrice !== null) {
            $query->where('base_price', '<=', $maxPrice);
        }
        return $query;
    }

    /**
     * 作用域：按早餐设置筛选
     */
    public function scopeByBreakfast($query, $breakfastIncluded)
    {
        return $query->where('breakfast_included', $breakfastIncluded);
    }

    /**
     * 作用域：按取消政策筛选
     */
    public function scopeByCancellationPolicy($query, $policy)
    {
        return $query->where('cancellation_policy', $policy);
    }

    /**
     * 作用域：仅限会员
     */
    public function scopeMemberOnly($query)
    {
        return $query->where('member_only', true);
    }

    /**
     * 获取计划类型名称
     */
    public function getPlanTypeNameAttribute()
    {
        $types = [
            self::PLAN_TYPE_STANDARD => '标准价格',
            self::PLAN_TYPE_PROMOTION => '促销价格',
            self::PLAN_TYPE_PACKAGE => '套餐价格',
            self::PLAN_TYPE_CORPORATE => '协议价格',
        ];

        return $types[$this->plan_type] ?? '未知';
    }

    /**
     * 检查是否在有效期内
     */
    public function isValidForDate($date)
    {
        $checkDate = is_string($date) ? $date : $date->toDateString();
        
        $validFrom = $this->valid_from ? $this->valid_from->toDateString() : null;
        $validTo = $this->valid_to ? $this->valid_to->toDateString() : null;
        
        if ($validFrom && $checkDate < $validFrom) {
            return false;
        }
        
        if ($validTo && $checkDate > $validTo) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否满足提前预订要求
     */
    public function meetsAdvanceBookingRequirement($bookingDate = null)
    {
        if (!$this->advance_booking_days) {
            return true;
        }
        
        $bookingDate = $bookingDate ?: date('Y-m-d H:i:s');
        $requiredDate = date('Y-m-d H:i:s', strtotime("+{$this->advance_booking_days} days"));
        
        return $bookingDate >= $requiredDate;
    }

    /**
     * 检查是否满足最少入住天数要求
     */
    public function meetsMinStayRequirement($nights)
    {
        return !$this->min_stay || $nights >= $this->min_stay;
    }

    /**
     * 检查是否超过最多入住天数限制
     */
    public function exceedsMaxStayLimit($nights)
    {
        return $this->max_stay && $nights > $this->max_stay;
    }

    /**
     * 获取指定房型和日期的价格
     */
    public function getRateForRoomTypeAndDate($roomTypeId, $date)
    {
        return $this->roomRates()
            ->where('room_type_id', $roomTypeId)
            ->where('date', $date)
            ->active()
            ->first();
    }

    /**
     * 获取指定房型和日期范围的价格
     */
    public function getRatesForRoomTypeAndDateRange($roomTypeId, $startDate, $endDate)
    {
        return $this->roomRates()
            ->where('room_type_id', $roomTypeId)
            ->whereBetween('date', [$startDate, $endDate])
            ->active()
            ->orderBy('date')
            ->get();
    }

    /**
     * 检查包含的服务
     */
    public function includesService($service)
    {
        return in_array($service, $this->includes ?? []);
    }

    /**
     * 获取包含服务列表
     */
    public function getIncludedServices()
    {
        return $this->includes ?? [];
    }

    /**
     * 获取价格类型名称
     */
    public function getPriceTypeNameAttribute()
    {
        $types = [
            'per_room' => '按房间',
            'per_person' => '按人',
        ];

        return $types[$this->price_type] ?? '未知';
    }

    /**
     * 获取早餐类型名称
     */
    public function getBreakfastTypeNameAttribute()
    {
        $types = [
            'continental' => '欧陆式早餐',
            'american' => '美式早餐',
            'chinese' => '中式早餐',
            'buffet' => '自助早餐',
            'light' => '简易早餐',
        ];

        return $types[$this->breakfast_type] ?? '未知';
    }

    /**
     * 获取取消政策名称
     */
    public function getCancellationPolicyNameAttribute()
    {
        $policies = [
            'flexible' => '灵活取消',
            'moderate' => '适中取消',
            'strict' => '严格取消',
            'non_refundable' => '不可退款',
        ];

        return $policies[$this->cancellation_policy] ?? '未知';
    }

    /**
     * 获取预付类型名称
     */
    public function getPrepaymentTypeNameAttribute()
    {
        $types = [
            'full' => '全额预付',
            'partial' => '部分预付',
            'deposit' => '定金',
        ];

        return $types[$this->prepayment_type] ?? '未知';
    }

    /**
     * 检查是否支持指定销售渠道
     */
    public function supportsChannel($channel)
    {
        if (empty($this->channel_restrictions)) {
            return true; // 无限制表示支持所有渠道
        }

        return in_array($channel, $this->channel_restrictions);
    }

    /**
     * 检查是否在促销期内
     */
    public function isInPromotionPeriod($date = null)
    {
        if (!$this->promotion_start_date || !$this->promotion_end_date) {
            return false;
        }

        $checkDate = $date ? (is_string($date) ? $date : $date->toDateString()) : date('Y-m-d');

        return $checkDate >= $this->promotion_start_date->toDateString()
            && $checkDate <= $this->promotion_end_date->toDateString();
    }

    /**
     * 检查是否满足预付要求
     */
    public function meetsPrepaymentRequirement($bookingDate = null)
    {
        if (!$this->prepayment_required) {
            return true;
        }

        if (!$this->prepayment_deadline) {
            return true;
        }

        $bookingDate = $bookingDate ?: date('Y-m-d H:i:s');
        $requiredDate = date('Y-m-d H:i:s', strtotime("+{$this->prepayment_deadline} hours"));

        return $bookingDate >= $requiredDate;
    }

    /**
     * 检查是否满足修改要求
     */
    public function meetsModificationRequirement($modificationDate = null)
    {
        if (!$this->modification_allowed) {
            return false;
        }

        if (!$this->modification_deadline) {
            return true;
        }

        $modificationDate = $modificationDate ?: date('Y-m-d H:i:s');
        $requiredDate = date('Y-m-d H:i:s', strtotime("+{$this->modification_deadline} hours"));

        return $modificationDate >= $requiredDate;
    }

    /**
     * 计算取消费用
     */
    public function calculateCancellationFee($totalAmount, $cancellationDate = null)
    {
        if (!$this->cancellation_fee_amount) {
            return 0;
        }

        $cancellationDate = $cancellationDate ?: date('Y-m-d H:i:s');
        $deadlineDate = date('Y-m-d H:i:s', strtotime("+{$this->cancellation_deadline} hours"));

        // 如果在免费取消期内
        if ($cancellationDate <= $deadlineDate) {
            return 0;
        }

        switch ($this->cancellation_fee_type) {
            case 'fixed':
                return $this->cancellation_fee_amount;
            case 'percentage':
                return $totalAmount * ($this->cancellation_fee_amount / 100);
            case 'nights':
                // 这里需要根据实际业务逻辑计算
                return $this->cancellation_fee_amount * ($totalAmount / 7); // 假设平均7晚
            default:
                return 0;
        }
    }

    /**
     * 获取状态文本
     */
    public function getStatusText()
    {
        if ($this->is_active && $this->is_published) {
            return '已发布';
        } elseif ($this->is_active && !$this->is_published) {
            return '已激活';
        } elseif (!$this->is_active && $this->is_published) {
            return '已发布(未激活)';
        } else {
            return '未激活';
        }
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColor()
    {
        if ($this->is_active && $this->is_published) {
            return 'green';
        } elseif ($this->is_active && !$this->is_published) {
            return 'orange';
        } elseif (!$this->is_active && $this->is_published) {
            return 'blue';
        } else {
            return 'default';
        }
    }

    /**
     * 获取价格计划分类名称
     */
    public function getRatePlanCategoryNameAttribute()
    {
        $categories = [
            'standard' => '标准价格',
            'promotion' => '促销价格',
            'package' => '套餐价格',
            'corporate' => '企业价格',
            'government' => '政府价格',
            'group' => '团队价格',
            'loyalty' => '会员价格',
            'last_minute' => '最后时刻价格',
            'early_bird' => '早鸟价格',
            'seasonal' => '季节性价格',
            'weekend' => '周末价格',
            'weekday' => '工作日价格',
            'holiday' => '节假日价格'
        ];

        return $categories[$this->rate_plan_category] ?? '标准价格';
    }

    /**
     * 获取价格计划等级名称
     */
    public function getRatePlanGradeNameAttribute()
    {
        $grades = [
            'basic' => '基础版',
            'standard' => '标准版',
            'premium' => '高级版',
            'luxury' => '豪华版',
            'vip' => 'VIP版',
            'exclusive' => '专享版'
        ];

        return $grades[$this->rate_plan_grade] ?? '标准版';
    }

    /**
     * 获取市场细分名称
     */
    public function getMarketSegmentNameAttribute()
    {
        $segments = [
            'leisure' => '休闲旅游',
            'business' => '商务旅行',
            'group' => '团队旅游',
            'corporate' => '企业客户',
            'government' => '政府机构',
            'wedding' => '婚庆活动',
            'conference' => '会议活动',
            'event' => '活动庆典',
            'family' => '家庭度假',
            'couple' => '情侣出行',
            'solo' => '单人旅行',
            'senior' => '银发族',
            'youth' => '青年旅客',
            'luxury' => '奢华旅游',
            'budget' => '经济旅游'
        ];

        return $segments[$this->market_segment] ?? '休闲旅游';
    }

    /**
     * 获取预订来源名称
     */
    public function getBookingSourceNameAttribute()
    {
        $sources = [
            'direct' => '直接预订',
            'ota' => 'OTA平台',
            'gds' => 'GDS系统',
            'corporate' => '企业直订',
            'travel_agent' => '旅行社',
            'phone' => '电话预订',
            'walk_in' => '现场预订',
            'group' => '团队预订',
            'event' => '活动预订',
            'package' => '套餐预订'
        ];

        return $sources[$this->booking_source] ?? '直接预订';
    }

    /**
     * 获取价格计算方法名称
     */
    public function getRateCalculationMethodNameAttribute()
    {
        $methods = [
            'fixed' => '固定价格',
            'dynamic' => '动态定价',
            'seasonal' => '季节定价',
            'demand_based' => '需求定价',
            'competition_based' => '竞争定价',
            'cost_plus' => '成本加成',
            'value_based' => '价值定价',
            'penetration' => '渗透定价',
            'skimming' => '撇脂定价',
            'bundle' => '捆绑定价'
        ];

        return $methods[$this->rate_calculation_method] ?? '固定价格';
    }

    /**
     * 获取套餐包含项信息
     */
    public function getPackageInclusionsInfo()
    {
        return [
            'basic' => $this->package_inclusions ?? [],
            'addon_services' => $this->addon_services ?? [],
            'upgrade_options' => $this->upgrade_options ?? [],
            'meal_plans' => $this->meal_plan_options ?? [],
            'transportation' => $this->transportation_included ?? false,
            'activities' => $this->activity_packages ?? [],
            'spa' => $this->spa_packages ?? [],
            'business' => $this->business_packages ?? [],
            'family' => $this->family_packages ?? [],
            'special_occasions' => $this->special_occasion_packages ?? []
        ];
    }

    /**
     * 获取折扣信息
     */
    public function getDiscountInfo()
    {
        return [
            'length_of_stay' => $this->length_of_stay_discounts ?? [],
            'early_booking' => $this->early_booking_discounts ?? [],
            'last_minute' => $this->last_minute_discounts ?? [],
            'group_booking' => $this->group_booking_discounts ?? [],
            'loyalty_program' => $this->loyalty_program_benefits ?? [],
            'seasonal' => $this->seasonal_adjustments ?? [],
            'day_of_week' => $this->day_of_week_adjustments ?? []
        ];
    }

    /**
     * 获取费用结构信息
     */
    public function getChargeStructureInfo()
    {
        return [
            'additional_person' => $this->additional_person_charges ?? [],
            'child' => $this->child_charges ?? [],
            'infant' => $this->infant_charges ?? [],
            'extra_bed' => $this->extra_bed_charges ?? [],
            'rollaway_bed' => $this->rollaway_bed_charges ?? [],
            'crib' => $this->crib_charges ?? [],
            'pet' => $this->pet_charges ?? [],
            'parking' => $this->parking_charges ?? [],
            'wifi' => $this->wifi_charges ?? [],
            'meals' => [
                'breakfast' => $this->breakfast_charges ?? [],
                'lunch' => $this->lunch_charges ?? [],
                'dinner' => $this->dinner_charges ?? []
            ],
            'services' => [
                'minibar' => $this->minibar_charges ?? [],
                'laundry' => $this->laundry_charges ?? [],
                'spa' => $this->spa_charges ?? [],
                'fitness' => $this->fitness_charges ?? [],
                'business_center' => $this->business_center_charges ?? []
            ],
            'transportation' => [
                'airport_transfer' => $this->airport_transfer_charges ?? [],
                'local_transport' => $this->local_transport_charges ?? []
            ],
            'activities' => [
                'tours' => $this->tour_charges ?? [],
                'activities' => $this->activity_charges ?? [],
                'equipment_rental' => $this->equipment_rental_charges ?? []
            ]
        ];
    }

    /**
     * 获取限制条件信息
     */
    public function getRestrictionsInfo()
    {
        return [
            'rate_restrictions' => $this->rate_restrictions ?? [],
            'booking_window' => $this->booking_window ?? [],
            'stay_date_restrictions' => $this->stay_date_restrictions ?? [],
            'blackout_dates' => $this->blackout_dates ?? [],
            'distribution_channels' => $this->distribution_channels ?? [],
            'minimum_revenue' => $this->minimum_revenue_requirements,
            'maximum_discount' => $this->maximum_discount_allowed
        ];
    }

    /**
     * 获取税费规则信息
     */
    public function getTaxRulesInfo()
    {
        return [
            'tax_inclusion' => $this->tax_inclusion_rules ?? [],
            'service_charge' => $this->service_charge_rules ?? [],
            'resort_fee' => $this->resort_fee_rules ?? [],
            'city_tax' => $this->city_tax_rules ?? [],
            'environmental_tax' => $this->environmental_tax_rules ?? [],
            'occupancy_tax' => $this->occupancy_tax_rules ?? [],
            'currency_conversion' => $this->currency_conversion_rules ?? []
        ];
    }
}
