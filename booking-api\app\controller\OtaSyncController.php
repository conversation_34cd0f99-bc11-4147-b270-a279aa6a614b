<?php

namespace app\controller;

use app\service\OtaSyncService;
use app\model\OtaChannel;
use app\model\OtaSyncLog;
use support\Request;
use support\Response;

/**
 * OTA数据同步控制器
 * 对标携程EBooking的OTA数据同步功能
 */
class OtaSyncController extends BaseController
{
    /**
     * OTA同步服务
     *
     * @var OtaSyncService
     */
    private $otaSyncService;

    public function __construct()
    {
        $this->otaSyncService = new OtaSyncService();
    }

    /**
     * 获取OTA渠道列表
     *
     * @param Request $request
     * @return Response
     */
    public function getChannels(Request $request)
    {
        try {
            $params = $this->getInput($request);

            $query = OtaChannel::query();

            // 按酒店筛选
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            // 按渠道筛选
            if (!empty($params['channel_code'])) {
                $query->where('channel_code', $params['channel_code']);
            }

            // 按状态筛选
            if (isset($params['is_active'])) {
                $query->where('is_active', $params['is_active']);
            }

            // 按测试模式筛选
            if (isset($params['is_test_mode'])) {
                $query->where('is_test_mode', $params['is_test_mode']);
            }

            $channels = $query->with(['hotel'])
                ->orderBy('hotel_id')
                ->orderBy('channel_code')
                ->get();

            $channelList = $channels->map(function($channel) {
                return $channel->getConfigSummary();
            });

            return $this->success([
                'channels' => $channelList,
                'total' => $channels->count()
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取OTA渠道列表');
        }
    }

    /**
     * 创建OTA渠道配置
     *
     * @param Request $request
     * @return Response
     */
    public function createChannel(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'channel_code', 'channel_name'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validate($data, [
                'hotel_id' => 'required|integer',
                'is_active' => 'boolean',
                'is_test_mode' => 'boolean'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查渠道是否已存在
            $existingChannel = OtaChannel::where('hotel_id', $data['hotel_id'])
                ->where('channel_code', $data['channel_code'])
                ->first();

            if ($existingChannel) {
                return $this->error('该酒店的此OTA渠道配置已存在');
            }

            // 验证渠道代码
            $supportedChannels = array_keys(OtaSyncService::SUPPORTED_CHANNELS);
            if (!in_array($data['channel_code'], $supportedChannels)) {
                return $this->error('不支持的OTA渠道代码');
            }

            // 设置默认值
            $data['created_by'] = $this->getCurrentUserId($request);
            $data['sync_settings'] = $data['sync_settings'] ?? [
                'inventory_sync' => true,
                'rate_sync' => true,
                'availability_sync' => true,
                'restriction_sync' => false,
                'booking_sync' => false,
                'auto_sync' => false,
                'sync_interval' => 60
            ];

            $channel = OtaChannel::create($data);

            return $this->success([
                'channel' => $channel->getConfigSummary(),
                'message' => 'OTA渠道配置创建成功'
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建OTA渠道配置');
        }
    }

    /**
     * 更新OTA渠道配置
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateChannel(Request $request, $id)
    {
        try {
            $channelId = (int)$id;
            
            if ($channelId <= 0) {
                return $this->error('渠道ID无效');
            }

            $channel = OtaChannel::find($channelId);
            if (!$channel) {
                return $this->error('OTA渠道配置不存在');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'is_active' => 'boolean',
                'is_test_mode' => 'boolean'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 设置更新者
            $data['updated_by'] = $this->getCurrentUserId($request);

            $channel->update($data);

            return $this->success([
                'channel' => $channel->fresh()->getConfigSummary(),
                'message' => 'OTA渠道配置更新成功'
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新OTA渠道配置');
        }
    }

    /**
     * 删除OTA渠道配置
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function deleteChannel(Request $request, $id)
    {
        try {
            $channelId = (int)$id;
            
            if ($channelId <= 0) {
                return $this->error('渠道ID无效');
            }

            $channel = OtaChannel::find($channelId);
            if (!$channel) {
                return $this->error('OTA渠道配置不存在');
            }

            // 检查是否有正在运行的同步任务
            $runningSyncs = OtaSyncLog::where('hotel_id', $channel->hotel_id)
                ->where('channel_code', $channel->channel_code)
                ->whereIn('status', ['started', 'running'])
                ->count();

            if ($runningSyncs > 0) {
                return $this->error('该渠道有正在运行的同步任务，无法删除');
            }

            $channel->delete();

            return $this->success(['message' => 'OTA渠道配置删除成功']);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除OTA渠道配置');
        }
    }

    /**
     * 测试OTA渠道连接
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function testConnection(Request $request, $id)
    {
        try {
            $channelId = (int)$id;
            
            if ($channelId <= 0) {
                return $this->error('渠道ID无效');
            }

            $channel = OtaChannel::find($channelId);
            if (!$channel) {
                return $this->error('OTA渠道配置不存在');
            }

            $testResult = $channel->testConnection();

            return $this->success([
                'test_result' => $testResult,
                'message' => $testResult['message']
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '测试OTA渠道连接');
        }
    }

    /**
     * 同步酒店信息
     *
     * @param Request $request
     * @return Response
     */
    public function syncHotelInfo(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'channel_code']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $result = $this->otaSyncService->syncHotelInfo(
                $data['hotel_id'],
                $data['channel_code'],
                $data['options'] ?? []
            );

            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '同步酒店信息');
        }
    }

    /**
     * 同步房型信息
     *
     * @param Request $request
     * @return Response
     */
    public function syncRoomTypes(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'channel_code']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $result = $this->otaSyncService->syncRoomTypes(
                $data['hotel_id'],
                $data['channel_code'],
                $data['room_type_ids'] ?? null
            );

            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '同步房型信息');
        }
    }

    /**
     * 同步库存数据
     *
     * @param Request $request
     * @return Response
     */
    public function syncInventory(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'channel_code', 'start_date', 'end_date'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证日期格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['start_date'] > $data['end_date']) {
                return $this->error('开始日期不能晚于结束日期');
            }

            $result = $this->otaSyncService->syncInventory(
                $data['hotel_id'],
                $data['channel_code'],
                $data['start_date'],
                $data['end_date'],
                $data['room_type_ids'] ?? null
            );

            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '同步库存数据');
        }
    }

    /**
     * 同步价格数据
     *
     * @param Request $request
     * @return Response
     */
    public function syncRates(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'channel_code', 'start_date', 'end_date'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证日期格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['start_date'] > $data['end_date']) {
                return $this->error('开始日期不能晚于结束日期');
            }

            $result = $this->otaSyncService->syncRates(
                $data['hotel_id'],
                $data['channel_code'],
                $data['start_date'],
                $data['end_date'],
                $data['room_type_ids'] ?? null,
                $data['rate_plan_ids'] ?? null
            );

            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '同步价格数据');
        }
    }

    /**
     * 获取同步日志
     *
     * @param Request $request
     * @return Response
     */
    public function getSyncLogs(Request $request)
    {
        try {
            $params = $this->getInput($request);

            $query = OtaSyncLog::query();

            // 按酒店筛选
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            // 按渠道筛选
            if (!empty($params['channel_code'])) {
                $query->where('channel_code', $params['channel_code']);
            }

            // 按同步类型筛选
            if (!empty($params['sync_type'])) {
                $query->where('sync_type', $params['sync_type']);
            }

            // 按状态筛选
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 按日期范围筛选
            if (!empty($params['start_date']) && !empty($params['end_date'])) {
                $query->whereBetween('started_at', [$params['start_date'], $params['end_date']]);
            }

            // 分页
            $page = max(1, (int)($params['page'] ?? 1));
            $pageSize = min(100, max(1, (int)($params['page_size'] ?? 20)));

            $total = $query->count();
            $logs = $query->with(['hotel', 'otaChannel'])
                ->orderBy('started_at', 'desc')
                ->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();

            $logList = $logs->map(function($log) {
                return $log->getSyncSummary();
            });

            return $this->success([
                'logs' => $logList,
                'pagination' => [
                    'page' => $page,
                    'page_size' => $pageSize,
                    'total' => $total,
                    'pages' => ceil($total / $pageSize)
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取同步日志');
        }
    }

    /**
     * 获取同步统计
     *
     * @param Request $request
     * @return Response
     */
    public function getSyncStats(Request $request)
    {
        try {
            $params = $this->getInput($request);

            $hotelId = $params['hotel_id'] ?? null;
            $channelCode = $params['channel_code'] ?? null;
            $days = min(365, max(1, (int)($params['days'] ?? 30)));

            $stats = OtaSyncLog::getSyncStats($hotelId, $channelCode, $days);

            return $this->success([
                'stats' => $stats,
                'period' => [
                    'days' => $days,
                    'start_date' => now()->subDays($days)->format('Y-m-d'),
                    'end_date' => now()->format('Y-m-d')
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取同步统计');
        }
    }

    /**
     * 获取当前用户ID
     */
    protected function getCurrentUserId(Request $request)
    {
        return $request->header('X-User-Id') ?? $request->session()->get('user_id') ?? null;
    }
}
