<?php

namespace app\service;

use app\model\RoomType;
use app\model\RoomTypeBedConfig;
use app\model\BedType;

/**
 * 房型床位配置服务类
 */
class RoomTypeBedConfigService extends BaseService
{
    /**
     * 获取房型床位配置
     *
     * @param int $roomTypeId
     * @return array
     */
    public function getBedConfig(int $roomTypeId)
    {
        try {
            // 检查房型是否存在
            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            // 获取床位配置
            $bedConfig = RoomTypeBedConfig::where('room_type_id', $roomTypeId)->first();

            if (!$bedConfig) {
                // 返回默认配置
                return $this->success($this->getDefaultBedConfig());
            }

            // 格式化配置数据
            $config = $this->formatBedConfig($bedConfig);

            return $this->success($config);

        } catch (\Exception $e) {
            $this->logError('获取房型床位配置失败', [
                'room_type_id' => $roomTypeId,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取房型床位配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存房型床位配置
     *
     * @param int $roomTypeId
     * @param array $data
     * @return array
     */
    public function saveBedConfig(int $roomTypeId, array $data)
    {
        try {
            // 检查房型是否存在
            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            // 验证床型是否存在
            if (!empty($data['bed_type'])) {
                $bedType = BedType::find($data['bed_type']);
                if (!$bedType) {
                    return $this->error('主床型不存在');
                }
            }

            // 验证额外床型
            if (!empty($data['extra_beds'])) {
                foreach ($data['extra_beds'] as $extraBed) {
                    if (!empty($extraBed['bed_type'])) {
                        $extraBedType = BedType::find($extraBed['bed_type']);
                        if (!$extraBedType) {
                            return $this->error('额外床型不存在');
                        }
                    }
                }
            }

            return $this->transaction(function () use ($roomTypeId, $data) {
                // 查找现有配置
                $bedConfig = RoomTypeBedConfig::where('room_type_id', $roomTypeId)->first();

                if ($bedConfig) {
                    // 更新现有配置
                    $bedConfig->update($data);
                } else {
                    // 创建新配置
                    $data['room_type_id'] = $roomTypeId;
                    $bedConfig = RoomTypeBedConfig::create($data);
                }

                $this->logInfo('保存房型床位配置成功', [
                    'room_type_id' => $roomTypeId,
                    'bed_type' => $data['bed_type'] ?? null
                ]);

                return $this->success($this->formatBedConfig($bedConfig), '保存床位配置成功');
            });

        } catch (\Exception $e) {
            $this->logError('保存房型床位配置失败', [
                'room_type_id' => $roomTypeId,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            return $this->error('保存房型床位配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除房型床位配置
     *
     * @param int $roomTypeId
     * @return array
     */
    public function deleteBedConfig(int $roomTypeId)
    {
        try {
            $bedConfig = RoomTypeBedConfig::where('room_type_id', $roomTypeId)->first();

            if (!$bedConfig) {
                return $this->error('床位配置不存在', 404);
            }

            $bedConfig->delete();

            $this->logInfo('删除房型床位配置成功', [
                'room_type_id' => $roomTypeId
            ]);

            return $this->success(null, '删除床位配置成功');

        } catch (\Exception $e) {
            $this->logError('删除房型床位配置失败', [
                'room_type_id' => $roomTypeId,
                'error' => $e->getMessage()
            ]);
            return $this->error('删除房型床位配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取床位配置模板
     *
     * @return array
     */
    public function getBedConfigTemplates()
    {
        try {
            $templates = [
                [
                    'id' => 'standard_single',
                    'name' => '标准单人间',
                    'description' => '适合单人入住的标准化配置',
                    'config' => [
                        'bed_type' => null, // 需要根据实际床型ID设置
                        'bed_count' => 1,
                        'bed_size' => '1.2m×2.0m',
                        'extra_beds' => [],
                        'bedding_material' => 'cotton',
                        'pillow_type' => 'down',
                        'pillow_count' => 2,
                        'duvet_type' => 'down',
                        'blanket_provided' => true,
                        'bedding_change_frequency' => 'daily',
                        'bed_notes' => '标准单人间配置，提供舒适的睡眠体验',
                    ]
                ],
                [
                    'id' => 'standard_double',
                    'name' => '标准双人间',
                    'description' => '适合双人入住的标准化配置',
                    'config' => [
                        'bed_type' => null, // 需要根据实际床型ID设置
                        'bed_count' => 1,
                        'bed_size' => '1.8m×2.0m',
                        'extra_beds' => [],
                        'bedding_material' => 'cotton',
                        'pillow_type' => 'down',
                        'pillow_count' => 2,
                        'duvet_type' => 'down',
                        'blanket_provided' => true,
                        'bedding_change_frequency' => 'daily',
                        'bed_notes' => '标准双人间配置，提供舒适的睡眠体验',
                    ]
                ],
                [
                    'id' => 'family_room',
                    'name' => '家庭房',
                    'description' => '适合家庭入住的配置，包含额外床位',
                    'config' => [
                        'bed_type' => null, // 需要根据实际床型ID设置
                        'bed_count' => 1,
                        'bed_size' => '2.0m×2.0m',
                        'extra_beds' => [
                            [
                                'bed_type' => null, // 需要根据实际床型ID设置
                                'count' => 1,
                                'size' => '1.2m×2.0m',
                                'fee' => 100.00,
                            ]
                        ],
                        'bedding_material' => 'cotton',
                        'pillow_type' => 'down',
                        'pillow_count' => 4,
                        'duvet_type' => 'down',
                        'blanket_provided' => true,
                        'bedding_change_frequency' => 'daily',
                        'bed_notes' => '家庭房配置，提供主床和额外床位，适合家庭入住',
                    ]
                ],
                [
                    'id' => 'luxury_suite',
                    'name' => '豪华套房',
                    'description' => '高端豪华配置，提供优质睡眠体验',
                    'config' => [
                        'bed_type' => null, // 需要根据实际床型ID设置
                        'bed_count' => 1,
                        'bed_size' => '2.2m×2.2m',
                        'extra_beds' => [],
                        'bedding_material' => 'silk',
                        'pillow_type' => 'memory_foam',
                        'pillow_count' => 4,
                        'duvet_type' => 'silk',
                        'blanket_provided' => true,
                        'bedding_change_frequency' => 'daily',
                        'bed_notes' => '豪华套房配置，使用高端床品，提供极致睡眠体验',
                    ]
                ]
            ];

            return $this->success($templates);

        } catch (\Exception $e) {
            $this->logError('获取床位配置模板失败', [
                'error' => $e->getMessage()
            ]);
            return $this->error('获取床位配置模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证床位配置
     *
     * @param int $roomTypeId
     * @param array $data
     * @return array
     */
    public function validateBedConfig(int $roomTypeId, array $data)
    {
        try {
            $errors = [];

            // 检查房型是否存在
            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                $errors[] = '房型不存在';
            }

            // 验证主床型
            if (!empty($data['bed_type'])) {
                $bedType = BedType::find($data['bed_type']);
                if (!$bedType) {
                    $errors[] = '主床型不存在';
                }
            } else {
                $errors[] = '主床型不能为空';
            }

            // 验证额外床型
            if (!empty($data['extra_beds'])) {
                foreach ($data['extra_beds'] as $index => $extraBed) {
                    if (!empty($extraBed['bed_type'])) {
                        $extraBedType = BedType::find($extraBed['bed_type']);
                        if (!$extraBedType) {
                            $errors[] = "额外床型[{$index}]不存在";
                        }
                    } else {
                        $errors[] = "额外床型[{$index}]床型不能为空";
                    }
                }
            }

            // 验证床位数量
            if (isset($data['bed_count']) && ($data['bed_count'] < 1 || $data['bed_count'] > 5)) {
                $errors[] = '主床数量必须在1-5之间';
            }

            // 验证枕头数量
            if (isset($data['pillow_count']) && ($data['pillow_count'] < 1 || $data['pillow_count'] > 10)) {
                $errors[] = '枕头数量必须在1-10之间';
            }

            if (empty($errors)) {
                return $this->success(['valid' => true], '配置验证通过');
            } else {
                return $this->error('配置验证失败', 400, ['errors' => $errors]);
            }

        } catch (\Exception $e) {
            $this->logError('验证床位配置失败', [
                'room_type_id' => $roomTypeId,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            return $this->error('验证床位配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 格式化床位配置数据
     *
     * @param RoomTypeBedConfig $bedConfig
     * @return array
     */
    private function formatBedConfig($bedConfig)
    {
        $config = $bedConfig->toArray();

        // 确保额外床位是数组格式
        if (is_string($config['extra_beds'])) {
            $config['extra_beds'] = json_decode($config['extra_beds'], true) ?: [];
        }

        // 添加床型信息
        if (!empty($config['bed_type'])) {
            $bedType = BedType::find($config['bed_type']);
            if ($bedType) {
                $config['bed_type_info'] = [
                    'id' => $bedType->id,
                    'name' => $bedType->name,
                    'code' => $bedType->code,
                    'category' => $bedType->category,
                    'size' => $bedType->size,
                    'size_description' => $bedType->size_description,
                ];
            }
        }

        // 添加额外床型信息
        if (!empty($config['extra_beds'])) {
            foreach ($config['extra_beds'] as &$extraBed) {
                if (!empty($extraBed['bed_type'])) {
                    $extraBedType = BedType::find($extraBed['bed_type']);
                    if ($extraBedType) {
                        $extraBed['bed_type_info'] = [
                            'id' => $extraBedType->id,
                            'name' => $extraBedType->name,
                            'code' => $extraBedType->code,
                            'category' => $extraBedType->category,
                            'size' => $extraBedType->size,
                            'size_description' => $extraBedType->size_description,
                        ];
                    }
                }
            }
        }

        return $config;
    }

    /**
     * 获取默认床位配置
     *
     * @return array
     */
    private function getDefaultBedConfig()
    {
        return [
            'room_type_id' => null,
            'bed_type' => '',
            'bed_count' => 1,
            'bed_size' => '',
            'extra_beds' => [],
            'bedding_material' => 'cotton',
            'pillow_type' => 'down',
            'pillow_count' => 2,
            'duvet_type' => 'down',
            'blanket_provided' => true,
            'bedding_change_frequency' => 'daily',
            'bed_notes' => '',
            'bed_type_info' => null,
        ];
    }
}
