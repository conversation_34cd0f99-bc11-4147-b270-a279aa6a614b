<?php

namespace app\controller;

use app\service\RoomInventoryBatchEditService;
use support\Request;
use support\Response;

/**
 * 库存批量编辑控制器
 */
class RoomInventoryBatchEditController extends BaseController
{
    /**
     * 库存批量编辑服务
     *
     * @var RoomInventoryBatchEditService
     */
    private $batchEditService;

    public function __construct()
    {
        $this->batchEditService = new RoomInventoryBatchEditService();
    }

    /**
     * 获取批量编辑任务列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            $result = $this->batchEditService->getBatchEditList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取库存批量编辑任务列表');
        }
    }

    /**
     * 获取批量编辑任务详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->getBatchEditDetail($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取库存批量编辑任务详情');
        }
    }

    /**
     * 创建库存批量编辑任务
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'start_date', 'end_date', 'operation_type'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date',
                'inventory_adjustment' => 'integer',
                'min_stay_adjustment' => 'integer',
                'max_stay_adjustment' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['start_date'] >= $data['end_date']) {
                return $this->error('开始日期必须早于结束日期');
            }

            // 验证操作类型
            $validOperations = ['set', 'increase', 'decrease', 'close', 'open'];
            if (!in_array($data['operation_type'], $validOperations)) {
                return $this->error('无效的操作类型');
            }

            // 验证调整参数
            if (in_array($data['operation_type'], ['set', 'increase', 'decrease'])) {
                if (!isset($data['inventory_adjustment']) || $data['inventory_adjustment'] < 0) {
                    return $this->error('库存调整数量必须大于等于0');
                }
            }

            // 验证布尔值字段
            $booleanFields = ['close_sales', 'stop_sales', 'arrival_restriction', 'departure_restriction'];
            foreach ($booleanFields as $field) {
                if (isset($data[$field]) && !is_bool($data[$field])) {
                    return $this->error("{$field} 必须是布尔值");
                }
            }

            // 验证星期限制
            if (!empty($data['days_of_week'])) {
                if (!is_array($data['days_of_week'])) {
                    return $this->error('星期限制必须是数组格式');
                }
                foreach ($data['days_of_week'] as $day) {
                    if (!is_int($day) || $day < 0 || $day > 6) {
                        return $this->error('星期值必须在0-6之间（0=周日，6=周六）');
                    }
                }
            }

            // 验证日期数组
            $dateArrayFields = ['exclude_dates', 'include_dates'];
            foreach ($dateArrayFields as $field) {
                if (!empty($data[$field])) {
                    if (!is_array($data[$field])) {
                        return $this->error("{$field} 必须是数组格式");
                    }
                    foreach ($data[$field] as $date) {
                        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                            return $this->error("{$field} 中的日期格式不正确，应为 YYYY-MM-DD");
                        }
                    }
                }
            }

            $result = $this->batchEditService->createBatchEdit($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建库存批量编辑任务');
        }
    }

    /**
     * 执行批量编辑任务
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function execute(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->executeBatchEdit($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '执行库存批量编辑任务');
        }
    }

    /**
     * 取消批量编辑任务
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function cancel(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->cancelBatchEdit($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '取消库存批量编辑任务');
        }
    }

    /**
     * 获取批量编辑进度
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function progress(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->getBatchEditProgress($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取库存批量编辑进度');
        }
    }

    /**
     * 预览批量编辑影响
     *
     * @param Request $request
     * @return Response
     */
    public function preview(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'start_date', 'end_date', 'operation_type'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $result = $this->batchEditService->previewBatchEdit($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '预览库存批量编辑影响');
        }
    }

    /**
     * 批量关房
     *
     * @param Request $request
     * @return Response
     */
    public function batchClose(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'start_date', 'end_date'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $data['operation_type'] = 'close';
            $data['close_sales'] = true;
            $data['reason'] = $data['reason'] ?? '批量关房操作';

            $result = $this->batchEditService->createBatchEdit($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量关房');
        }
    }

    /**
     * 批量开房
     *
     * @param Request $request
     * @return Response
     */
    public function batchOpen(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'start_date', 'end_date'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $data['operation_type'] = 'open';
            $data['close_sales'] = false;
            $data['reason'] = $data['reason'] ?? '批量开房操作';

            $result = $this->batchEditService->createBatchEdit($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量开房');
        }
    }

    /**
     * 删除批量编辑任务
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->deleteBatchEdit($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除库存批量编辑任务');
        }
    }
}
