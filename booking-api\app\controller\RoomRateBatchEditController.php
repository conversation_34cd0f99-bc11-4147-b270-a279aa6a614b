<?php

namespace app\controller;

use app\service\RoomRateBatchEditService;
use support\Request;
use support\Response;

/**
 * 房价批量编辑控制器
 */
class RoomRateBatchEditController extends BaseController
{
    /**
     * 房价批量编辑服务
     *
     * @var RoomRateBatchEditService
     */
    private $batchEditService;

    public function __construct()
    {
        $this->batchEditService = new RoomRateBatchEditService();
    }

    /**
     * 获取批量编辑任务列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            $result = $this->batchEditService->getBatchEditList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取批量编辑任务列表');
        }
    }

    /**
     * 获取批量编辑任务详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $taskId = (int)$id;

            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->getBatchEditDetail($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取批量编辑任务详情');
        }
    }

    /**
     * 创建房价批量编辑任务
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'start_date', 'end_date', 'operation_type'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'rate_plan_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date',
                'price_adjustment' => 'numeric',
                'percentage_adjustment' => 'numeric',
                'min_price' => 'positive',
                'max_price' => 'positive'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['start_date'] >= $data['end_date']) {
                return $this->error('开始日期必须早于结束日期');
            }

            // 验证操作类型
            $validOperations = ['set', 'increase', 'decrease', 'percentage'];
            if (!in_array($data['operation_type'], $validOperations)) {
                return $this->error('无效的操作类型');
            }

            // 验证调整参数
            if (in_array($data['operation_type'], ['set', 'increase', 'decrease'])) {
                if (empty($data['price_adjustment'])) {
                    return $this->error('价格调整金额不能为空');
                }
                if ($data['price_adjustment'] <= 0) {
                    return $this->error('价格调整金额必须大于0');
                }
            } elseif ($data['operation_type'] === 'percentage') {
                if (empty($data['percentage_adjustment'])) {
                    return $this->error('百分比调整值不能为空');
                }
                if ($data['percentage_adjustment'] < -100 || $data['percentage_adjustment'] > 1000) {
                    return $this->error('百分比调整值必须在-100%到1000%之间');
                }
            }

            // 验证价格限制
            if (!empty($data['min_price']) && !empty($data['max_price'])) {
                if ($data['min_price'] >= $data['max_price']) {
                    return $this->error('最低价格必须小于最高价格');
                }
            }

            // 验证星期限制
            if (!empty($data['days_of_week'])) {
                if (!is_array($data['days_of_week'])) {
                    return $this->error('星期限制必须是数组格式');
                }
                foreach ($data['days_of_week'] as $day) {
                    if (!is_int($day) || $day < 0 || $day > 6) {
                        return $this->error('星期值必须在0-6之间（0=周日，6=周六）');
                    }
                }
            }

            // 验证日期数组
            $dateArrayFields = ['exclude_dates', 'include_dates'];
            foreach ($dateArrayFields as $field) {
                if (!empty($data[$field])) {
                    if (!is_array($data[$field])) {
                        return $this->error("{$field} 必须是数组格式");
                    }
                    foreach ($data[$field] as $date) {
                        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                            return $this->error("{$field} 中的日期格式不正确，应为 YYYY-MM-DD");
                        }
                    }
                }
            }

            // 添加创建人信息
            $data['created_by'] = $this->getCurrentUserId() ?? 1;

            $result = $this->batchEditService->createBatchEdit($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建批量编辑任务');
        }
    }

    /**
     * 执行批量编辑任务
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function execute(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->executeBatchEdit($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '执行批量编辑任务');
        }
    }

    /**
     * 取消批量编辑任务
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function cancel(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->cancelBatchEdit($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '取消批量编辑任务');
        }
    }

    /**
     * 获取批量编辑进度
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function progress(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->getBatchEditProgress($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取批量编辑进度');
        }
    }

    /**
     * 预览批量编辑影响
     *
     * @param Request $request
     * @return Response
     */
    public function preview(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'hotel_id', 'start_date', 'end_date', 'operation_type'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $result = $this->batchEditService->previewBatchEdit($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '预览批量编辑影响');
        }
    }

    /**
     * 删除批量编辑任务
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->deleteBatchEdit($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除批量编辑任务');
        }
    }

    /**
     * 获取批量编辑统计信息
     *
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $result = $this->batchEditService->getBatchEditStatistics($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取批量编辑统计信息');
        }
    }

    /**
     * 重试失败的批量编辑任务
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function retry(Request $request, $id)
    {
        try {
            $taskId = (int)$id;
            
            if ($taskId <= 0) {
                return $this->error('任务ID无效');
            }

            $result = $this->batchEditService->retryBatchEdit($taskId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '重试批量编辑任务');
        }
    }
}
