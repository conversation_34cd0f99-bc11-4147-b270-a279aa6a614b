<?php

namespace app\model;

/**
 * 订单日志模型
 * 对应数据库表：booking_logs
 */
class BookingLog extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'booking_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'booking_id',
        'action',
        'description',
        'user_id',
        'ip_address',
        'user_agent',
        'request_data',
        'response_data',
        'execution_time',
        'memory_usage',
        'error_message',
        'stack_trace',
        'created_at'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'booking_id' => 'integer',
        'user_id' => 'integer',
        'request_data' => 'array',
        'response_data' => 'array',
        'execution_time' => 'float',
        'memory_usage' => 'integer',
        'created_at' => 'datetime'
    ];

    /**
     * 操作类型常量
     */
    const ACTION_CREATED = 'created';
    const ACTION_MODIFIED = 'modified';
    const ACTION_CONFIRMED = 'confirmed';
    const ACTION_CANCELLED = 'cancelled';
    const ACTION_CHECKED_IN = 'checked_in';
    const ACTION_CHECKED_OUT = 'checked_out';
    const ACTION_PAYMENT_RECEIVED = 'payment_received';
    const ACTION_REFUND_PROCESSED = 'refund_processed';
    const ACTION_STATUS_CHANGED = 'status_changed';
    const ACTION_INVENTORY_LOCKED = 'inventory_locked';
    const ACTION_INVENTORY_RELEASED = 'inventory_released';
    const ACTION_EMAIL_SENT = 'email_sent';
    const ACTION_SMS_SENT = 'sms_sent';
    const ACTION_NOTIFICATION_SENT = 'notification_sent';

    /**
     * 获取关联的订单
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * 获取操作用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 作用域：按订单筛选
     */
    public function scopeByBooking($query, $bookingId)
    {
        return $query->where('booking_id', $bookingId);
    }

    /**
     * 作用域：按操作类型筛选
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 获取操作类型名称
     */
    public function getActionNameAttribute()
    {
        $actions = [
            self::ACTION_CREATED => '创建订单',
            self::ACTION_MODIFIED => '修改订单',
            self::ACTION_CONFIRMED => '确认订单',
            self::ACTION_CANCELLED => '取消订单',
            self::ACTION_CHECKED_IN => '办理入住',
            self::ACTION_CHECKED_OUT => '办理退房',
            self::ACTION_PAYMENT_RECEIVED => '收到付款',
            self::ACTION_REFUND_PROCESSED => '处理退款',
            self::ACTION_STATUS_CHANGED => '状态变更',
            self::ACTION_INVENTORY_LOCKED => '锁定库存',
            self::ACTION_INVENTORY_RELEASED => '释放库存',
            self::ACTION_EMAIL_SENT => '发送邮件',
            self::ACTION_SMS_SENT => '发送短信',
            self::ACTION_NOTIFICATION_SENT => '发送通知'
        ];

        return $actions[$this->action] ?? $this->action;
    }
    /**
     * 获取用户信息
     */
    public function getUserInfoAttribute()
    {
        if ($this->user) {
            return [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email
            ];
        }

        return [
            'id' => null,
            'name' => '系统',
            'email' => null
        ];
    }

    /**
     * 记录订单操作日志
     */
    public static function logAction($bookingId, $action, $description, $userId = null, $additionalData = [])
    {
        $logData = [
            'booking_id' => $bookingId,
            'action' => $action,
            'description' => $description,
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 添加额外数据
        if (!empty($additionalData['request_data'])) {
            $logData['request_data'] = $additionalData['request_data'];
        }

        if (!empty($additionalData['response_data'])) {
            $logData['response_data'] = $additionalData['response_data'];
        }

        if (!empty($additionalData['execution_time'])) {
            $logData['execution_time'] = $additionalData['execution_time'];
        }

        if (!empty($additionalData['memory_usage'])) {
            $logData['memory_usage'] = $additionalData['memory_usage'];
        }

        if (!empty($additionalData['error_message'])) {
            $logData['error_message'] = $additionalData['error_message'];
        }

        if (!empty($additionalData['stack_trace'])) {
            $logData['stack_trace'] = $additionalData['stack_trace'];
        }

        return static::create($logData);
    }

    /**
     * 获取订单操作统计
     */
    public static function getBookingActionStats($bookingId)
    {
        $logs = static::where('booking_id', $bookingId)->get();
        
        $stats = [
            'total_actions' => $logs->count(),
            'action_counts' => [],
            'user_actions' => [],
            'timeline' => []
        ];

        foreach ($logs as $log) {
            // 统计操作类型
            $action = $log->action;
            $stats['action_counts'][$action] = ($stats['action_counts'][$action] ?? 0) + 1;

            // 统计用户操作
            $userId = $log->user_id ?? 'system';
            $stats['user_actions'][$userId] = ($stats['user_actions'][$userId] ?? 0) + 1;

            // 构建时间线
            $stats['timeline'][] = [
                'action' => $log->action,
                'action_name' => $log->action_name,
                'description' => $log->description,
                'user_info' => $log->user_info,
                'created_at' => $log->formatted_created_at,
                'ip_address' => $log->ip_address
            ];
        }

        // 按时间倒序排列时间线
        usort($stats['timeline'], function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $stats;
    }

    /**
     * 清理过期日志
     */
    public static function cleanupExpiredLogs($days = 90)
    {
        $expiredDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return static::where('created_at', '<', $expiredDate)->delete();
    }

    /**
     * 获取操作频率统计
     */
    public static function getActionFrequencyStats($startDate = null, $endDate = null)
    {
        $query = static::query();

        if ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('created_at', '<=', $endDate);
        }

        $logs = $query->get();
        
        $stats = [
            'total_logs' => $logs->count(),
            'action_frequency' => [],
            'hourly_distribution' => array_fill(0, 24, 0),
            'daily_distribution' => [],
            'user_activity' => []
        ];

        foreach ($logs as $log) {
            // 操作频率统计
            $action = $log->action;
            $stats['action_frequency'][$action] = ($stats['action_frequency'][$action] ?? 0) + 1;

            // 小时分布统计
            $hour = (int)$log->created_at->format('H');
            $stats['hourly_distribution'][$hour]++;

            // 日期分布统计
            $date = $log->created_at->format('Y-m-d');
            $stats['daily_distribution'][$date] = ($stats['daily_distribution'][$date] ?? 0) + 1;

            // 用户活跃度统计
            $userId = $log->user_id ?? 'system';
            $stats['user_activity'][$userId] = ($stats['user_activity'][$userId] ?? 0) + 1;
        }

        return $stats;
    }
}
