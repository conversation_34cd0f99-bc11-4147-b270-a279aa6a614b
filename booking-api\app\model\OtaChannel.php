<?php

namespace app\model;

/**
 * OTA渠道模型
 * 对应数据库表：ota_channels
 */
class OtaChannel extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'ota_channels';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'name_en',
        'description',
        'logo_url',

        // API接口配置
        'api_endpoint',
        'api_version',
        'api_key',
        'api_secret',
        'api_config',

        // 佣金和结算
        'commission_rate',
        'commission_type',
        'settlement_method',
        'settlement_currency',
        'payment_terms',

        // 联系信息
        'contact_name',
        'contact_email',
        'contact_phone',
        'contact_address',

        // 业务配置
        'supported_currencies',
        'supported_languages',
        'booking_window',
        'cancellation_window',
        'modification_allowed',
        'instant_confirmation',

        // 限制和规则
        'min_advance_booking',
        'max_advance_booking',
        'blackout_dates',
        'rate_restrictions',

        // 状态和优先级
        'is_active',
        'priority',
        'sort_order',

        // 统计信息
        'total_bookings',
        'total_revenue',
        'last_sync_at',

        // 扩展配置字段
        'connection_type',
        'sync_settings',
        'mapping_settings',
        'rate_settings',
        'inventory_settings',
        'booking_settings',
        'notification_settings',
        'authentication_settings',
        'retry_settings',
        'timeout_settings',
        'is_test_mode',
        'last_error_at',
        'error_count',
        'success_count',
        'total_sync_count',
        'notes',
        'created_by',
        'updated_by'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        // 基础字段
        'hotel_id' => 'integer',
        'commission_rate' => 'decimal:2',
        'total_revenue' => 'decimal:2',
        'priority' => 'integer',
        'sort_order' => 'integer',
        'total_bookings' => 'integer',

        // JSON字段
        'api_config' => 'array',
        'supported_currencies' => 'array',
        'supported_languages' => 'array',
        'blackout_dates' => 'array',
        'rate_restrictions' => 'array',

        // 业务配置字段
        'booking_window' => 'integer',
        'cancellation_window' => 'integer',
        'min_advance_booking' => 'integer',
        'max_advance_booking' => 'integer',
        'modification_allowed' => 'boolean',
        'instant_confirmation' => 'boolean',
        'is_active' => 'boolean',

        // 扩展配置字段
        'sync_settings' => 'array',
        'mapping_settings' => 'array',
        'rate_settings' => 'array',
        'inventory_settings' => 'array',
        'booking_settings' => 'array',
        'notification_settings' => 'array',
        'authentication_settings' => 'array',
        'retry_settings' => 'array',
        'timeout_settings' => 'array',
        'is_test_mode' => 'boolean',
        'error_count' => 'integer',
        'success_count' => 'integer',
        'total_sync_count' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',

        // 时间字段
        'last_sync_at' => 'datetime',
        'last_error_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 渠道代码常量
     */
    const CHANNEL_CTRIP = 'ctrip';
    const CHANNEL_BOOKING = 'booking';
    const CHANNEL_EXPEDIA = 'expedia';
    const CHANNEL_AGODA = 'agoda';
    const CHANNEL_HOTELS = 'hotels';
    const CHANNEL_AIRBNB = 'airbnb';
    const CHANNEL_MEITUAN = 'meituan';
    const CHANNEL_QUNAR = 'qunar';
    const CHANNEL_FLIGGY = 'fliggy';
    const CHANNEL_TONGCHENG = 'tongcheng';

    /**
     * 连接类型常量
     */
    const CONNECTION_API = 'api';
    const CONNECTION_XML = 'xml';
    const CONNECTION_FTP = 'ftp';
    const CONNECTION_WEBHOOK = 'webhook';

    /**
     * 获取关联的酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取同步日志
     */
    public function syncLogs()
    {
        return $this->hasMany(OtaSyncLog::class, 'channel_code', 'channel_code')
            ->where('hotel_id', $this->hotel_id);
    }

    /**
     * 获取关联的房价计划
     */
    public function ratePlans()
    {
        return $this->belongsToMany(RatePlan::class, 'rate_plan_ota_channels', 'ota_channel_id', 'rate_plan_id')
            ->withPivot(['commission_rate', 'markup_rate', 'markup_amount', 'is_active', 'priority'])
            ->withTimestamps();
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：按渠道筛选
     */
    public function scopeByChannel($query, $channelCode)
    {
        return $query->where('channel_code', $channelCode);
    }

    /**
     * 作用域：激活的渠道
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：测试模式
     */
    public function scopeTestMode($query, $isTestMode = true)
    {
        return $query->where('is_test_mode', $isTestMode);
    }

    /**
     * 获取渠道名称
     */
    public function getChannelDisplayNameAttribute()
    {
        $channelNames = [
            self::CHANNEL_CTRIP => '携程',
            self::CHANNEL_BOOKING => 'Booking.com',
            self::CHANNEL_EXPEDIA => 'Expedia',
            self::CHANNEL_AGODA => 'Agoda',
            self::CHANNEL_HOTELS => 'Hotels.com',
            self::CHANNEL_AIRBNB => 'Airbnb',
            self::CHANNEL_MEITUAN => '美团',
            self::CHANNEL_QUNAR => '去哪儿',
            self::CHANNEL_FLIGGY => '飞猪',
            self::CHANNEL_TONGCHENG => '同程'
        ];

        return $channelNames[$this->channel_code] ?? $this->channel_name;
    }

    /**
     * 获取连接类型名称
     */
    public function getConnectionTypeNameAttribute()
    {
        $connectionTypes = [
            self::CONNECTION_API => 'API接口',
            self::CONNECTION_XML => 'XML接口',
            self::CONNECTION_FTP => 'FTP传输',
            self::CONNECTION_WEBHOOK => 'Webhook回调'
        ];

        return $connectionTypes[$this->connection_type] ?? $this->connection_type;
    }

    /**
     * 获取状态标签
     */
    public function getStatusLabelAttribute()
    {
        if (!$this->is_active) {
            return '未激活';
        }

        if ($this->is_test_mode) {
            return '测试模式';
        }

        return '正常运行';
    }

    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute()
    {
        if ($this->total_sync_count === 0) {
            return 0;
        }

        return round(($this->success_count / $this->total_sync_count) * 100, 2);
    }

    /**
     * 获取最后同步时间格式化
     */
    public function getLastSyncAtFormattedAttribute()
    {
        return $this->last_sync_at ? $this->last_sync_at->format('Y-m-d H:i:s') : '从未同步';
    }

    /**
     * 获取最后错误时间格式化
     */
    public function getLastErrorAtFormattedAttribute()
    {
        return $this->last_error_at ? $this->last_error_at->format('Y-m-d H:i:s') : '无错误';
    }

    /**
     * 检查是否可以同步
     */
    public function canSync()
    {
        return $this->is_active && !empty($this->api_base_url) && !empty($this->api_token);
    }

    /**
     * 更新同步统计
     */
    public function updateSyncStats($success = true)
    {
        $this->total_sync_count++;
        
        if ($success) {
            $this->success_count++;
            $this->last_sync_at = date('Y-m-d H:i:s');
        } else {
            $this->error_count++;
            $this->last_error_at = date('Y-m-d H:i:s');
        }

        $this->save();
    }

    /**
     * 重置错误计数
     */
    public function resetErrorCount()
    {
        $this->error_count = 0;
        $this->last_error_at = null;
        $this->save();
    }

    /**
     * 获取同步设置
     */
    public function getSyncSetting($key, $default = null)
    {
        $settings = $this->sync_settings ?? [];
        return $settings[$key] ?? $default;
    }

    /**
     * 设置同步设置
     */
    public function setSyncSetting($key, $value)
    {
        $settings = $this->sync_settings ?? [];
        $settings[$key] = $value;
        $this->sync_settings = $settings;
        $this->save();
    }

    /**
     * 获取映射设置
     */
    public function getMappingSetting($key, $default = null)
    {
        $settings = $this->mapping_settings ?? [];
        return $settings[$key] ?? $default;
    }

    /**
     * 设置映射设置
     */
    public function setMappingSetting($key, $value)
    {
        $settings = $this->mapping_settings ?? [];
        $settings[$key] = $value;
        $this->mapping_settings = $settings;
        $this->save();
    }

    /**
     * 测试连接
     */
    public function testConnection()
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->api_base_url . '/test');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->api_token,
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return [
                'success' => $httpCode === 200,
                'http_code' => $httpCode,
                'response' => $response,
                'message' => $httpCode === 200 ? '连接成功' : '连接失败'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'http_code' => 0,
                'response' => null,
                'message' => '连接异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取支持的同步类型
     */
    public function getSupportedSyncTypes()
    {
        $syncSettings = $this->sync_settings ?? [];
        $supportedTypes = [];

        if ($syncSettings['inventory_sync'] ?? true) {
            $supportedTypes[] = 'inventory';
        }

        if ($syncSettings['rate_sync'] ?? true) {
            $supportedTypes[] = 'rates';
        }

        if ($syncSettings['availability_sync'] ?? true) {
            $supportedTypes[] = 'availability';
        }

        if ($syncSettings['restriction_sync'] ?? true) {
            $supportedTypes[] = 'restrictions';
        }

        if ($syncSettings['booking_sync'] ?? true) {
            $supportedTypes[] = 'bookings';
        }

        return $supportedTypes;
    }

    /**
     * 获取渠道配置摘要
     */
    public function getConfigSummary()
    {
        return [
            'channel_info' => [
                'code' => $this->channel_code,
                'name' => $this->channel_display_name,
                'connection_type' => $this->connection_type_name,
                'status' => $this->status_label
            ],
            'sync_stats' => [
                'total_syncs' => $this->total_sync_count,
                'success_count' => $this->success_count,
                'error_count' => $this->error_count,
                'success_rate' => $this->success_rate . '%',
                'last_sync' => $this->last_sync_at_formatted,
                'last_error' => $this->last_error_at_formatted
            ],
            'supported_types' => $this->getSupportedSyncTypes(),
            'can_sync' => $this->canSync()
        ];
    }
}
