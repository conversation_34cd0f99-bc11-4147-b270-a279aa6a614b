<?php

namespace app\service;

use support\Db;

/**
 * 酒店政策服务类
 * 处理酒店各种政策的业务逻辑
 */
class HotelPolicyService extends BaseService
{
    /**
     * 政策类型映射
     */
    const POLICY_TYPES = [
        'check' => '入离政策',
        'dining' => '餐食政策',
        'parking' => '停车政策',
        'children' => '儿童政策',
        'pet' => '宠物政策',
        'checkin' => '入住方式政策',
        'guest' => '住客限制',
        'stay' => '在店政策',
        'deposit' => '押金政策'
    ];

    /**
     * 获取酒店所有政策
     */
    public function getHotelPolicies(int $hotelId): array
    {
        try {

            $policies = Db::table('hotel_policies')
                ->where('hotel_id', $hotelId)
                ->where('status', 'active')
                ->get();

            $result = [];
            foreach ($policies as $policy) {
                $policyArray = (array)$policy;
                $policyArray['policy_data'] = json_decode($policyArray['policy_data'], true);
                $policyArray['policy_type_name'] = self::POLICY_TYPES[$policyArray['policy_type']] ?? $policyArray['policy_type'];
                $result[$policyArray['policy_type']] = $policyArray;
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError('获取酒店政策失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 获取特定类型的政策
     */
    public function getHotelPolicy(int $hotelId, string $policyType): ?array
    {
        try {
            $policy = Db::table('hotel_policies')
                ->where('hotel_id', $hotelId)
                ->where('policy_type', $policyType)
                ->where('status', 'active')
                ->first();

            if (!$policy) {
                return null;
            }

            $policyArray = (array)$policy;
            $policyArray['policy_data'] = json_decode($policyArray['policy_data'], true);
            $policyArray['policy_type_name'] = self::POLICY_TYPES[$policyType] ?? $policyType;

            return $policyArray;
        } catch (\Exception $e) {
            $this->logError('获取酒店政策失败', [
                'hotel_id' => $hotelId,
                'policy_type' => $policyType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 更新酒店政策
     */
    public function updateHotelPolicy(int $hotelId, string $policyType, array $policyData): bool
    {
        try {
            Db::beginTransaction();

            // 检查政策是否存在
            $existingPolicy = Db::table('hotel_policies')
                ->where('hotel_id', $hotelId)
                ->where('policy_type', $policyType)
                ->first();

            $data = [
                'policy_data' => json_encode($policyData, JSON_UNESCAPED_UNICODE),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($existingPolicy) {
                // 更新现有政策
                $result = Db::table('hotel_policies')
                    ->where('hotel_id', $hotelId)
                    ->where('policy_type', $policyType)
                    ->update($data);
            } else {
                // 创建新政策
                $data['hotel_id'] = $hotelId;
                $data['policy_type'] = $policyType;
                $data['status'] = 'active';
                $data['created_at'] = date('Y-m-d H:i:s');
                
                $result = Db::table('hotel_policies')->insert($data);
            }

            if ($result) {
                // 记录操作日志
                $this->logHotelOperation(
                    $hotelId, 
                    $existingPolicy ? 'update' : 'create', 
                    'policies', 
                    '更新酒店政策: ' . (self::POLICY_TYPES[$policyType] ?? $policyType),
                    $existingPolicy ? (array)$existingPolicy : [],
                    $policyData
                );

                Db::commit();
                return true;
            } else {
                Db::rollback();
                return false;
            }
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('更新酒店政策失败', [
                'hotel_id' => $hotelId,
                'policy_type' => $policyType,
                'policy_data' => $policyData,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取入离政策模板
     */
    public function getCheckPolicyTemplate(): array
    {
        return [
            'checkInFrom' => '14:00',
            'checkInTo' => '23:00',
            'checkOutFrom' => '06:00',
            'checkOutTo' => '12:00',
            'isLimitArrival' => false,
            'checkComment' => [
                [
                    'languageCode' => 'zh-CN',
                    'content' => '请在规定时间内办理入住和退房手续'
                ]
            ]
        ];
    }

    /**
     * 获取餐食政策模板
     */
    public function getDiningPolicyTemplate(): array
    {
        return [
            'isProvideBreakfast' => false,
            'breakfastPrice' => 0,
            'breakfastTypes' => [],
            'breakfastStyleIds' => [],
            'foodCategories' => [],
            'restaurantOpenPeriod' => [
                [
                    'isOpen' => true,
                    'openTime' => '07:00',
                    'closeTime' => '10:00',
                    'weekDays' => ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
                ]
            ]
        ];
    }

    /**
     * 获取停车政策模板
     */
    public function getParkingPolicyTemplate(): array
    {
        return [
            'parkingProvided' => false,
            'chargingPointProvided' => false,
            'parkingDetails' => [],
            'chargePointDetails' => []
        ];
    }

    /**
     * 获取儿童政策模板
     */
    public function getChildrenPolicyTemplate(): array
    {
        return [
            'isAllowChildren' => true,
            'limitChildrenAge' => [
                'minChildrenAge' => 0,
                'maxChildrenAge' => 12
            ],
            'policyContent' => [
                [
                    'languageCode' => 'zh-CN',
                    'content' => '欢迎携带儿童入住'
                ]
            ],
            'isAllowSharingBed' => true,
            'policySharingBed' => [],
            'allowChildrenExtraBed' => [
                'bedType' => [
                    'NormalBedNum' => 0,
                    'infantBedNum' => 0,
                    'isOr' => false
                ],
                'extraBedDetails' => []
            ],
            'childrenBreakfast' => [
                'breakfastSpecialSet' => false,
                'rangeType' => 'Age',
                'details' => []
            ]
        ];
    }

    /**
     * 获取宠物政策模板
     */
    public function getPetPolicyTemplate(): array
    {
        return [
            'petsAllowed' => false,
            'chargeable' => false,
            'amount' => 0
        ];
    }

    /**
     * 获取押金政策模板
     */
    public function getDepositPolicyTemplate(): array
    {
        return [
            'depositRequired' => false,
            'amount' => 0,
            'frequency' => 1, // 1:固定金额，2:每间，3:每晚
            'payType' => 1, // 1:现金，2:信用卡，3:借记卡，4:第三方平台
            'refundType' => 1, // 0:不原路退还，1:原路退还
            'refundTime' => 0 // 0:当日退还，1:一周内退还，2:两周内退还
        ];
    }

    /**
     * 获取政策模板
     */
    public function getPolicyTemplate(string $policyType): array
    {
        switch ($policyType) {
            case 'check':
                return $this->getCheckPolicyTemplate();
            case 'dining':
                return $this->getDiningPolicyTemplate();
            case 'parking':
                return $this->getParkingPolicyTemplate();
            case 'children':
                return $this->getChildrenPolicyTemplate();
            case 'pet':
                return $this->getPetPolicyTemplate();
            case 'deposit':
                return $this->getDepositPolicyTemplate();
            default:
                return [];
        }
    }

    /**
     * 删除政策
     */
    public function deletePolicy(int $hotelId, string $policyType): bool
    {
        try {
            $result = Db::table('hotel_policies')
                ->where('hotel_id', $hotelId)
                ->where('policy_type', $policyType)
                ->update([
                    'status' => 'inactive',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            if ($result) {
                $this->logHotelOperation(
                    $hotelId,
                    'delete',
                    'policies',
                    '删除酒店政策: ' . (self::POLICY_TYPES[$policyType] ?? $policyType),
                    [],
                    []
                );
            }

            return $result > 0;
        } catch (\Exception $e) {
            $this->logError('删除酒店政策失败', [
                'hotel_id' => $hotelId,
                'policy_type' => $policyType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 复制政策到其他酒店
     */
    public function copyPolicyToHotel(int $sourceHotelId, int $targetHotelId, string $policyType): bool
    {
        try {
            $sourcePolicy = $this->getHotelPolicy($sourceHotelId, $policyType);
            if (!$sourcePolicy) {
                return false;
            }

            return $this->updateHotelPolicy($targetHotelId, $policyType, $sourcePolicy['policy_data']);
        } catch (\Exception $e) {
            $this->logError('复制酒店政策失败', [
                'source_hotel_id' => $sourceHotelId,
                'target_hotel_id' => $targetHotelId,
                'policy_type' => $policyType,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 记录酒店操作日志
     */
    protected function logHotelOperation(
        int $hotelId, 
        string $operationType, 
        string $operationModule, 
        string $description, 
        array $oldData = [], 
        array $newData = []
    ): void {
        try {
            Db::table('hotel_operation_logs')->insert([
                'hotel_id' => $hotelId,
                'operation_type' => $operationType,
                'operation_module' => $operationModule,
                'operation_description' => $description,
                'old_data' => !empty($oldData) ? json_encode($oldData, JSON_UNESCAPED_UNICODE) : null,
                'new_data' => !empty($newData) ? json_encode($newData, JSON_UNESCAPED_UNICODE) : null,
                'operator_id' => $this->getCurrentUserId(),
                'operator_name' => $this->getCurrentUserName(),
                'operator_ip' => $this->getClientIp(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            error_log('记录酒店操作日志失败: ' . $e->getMessage());
        }
    }

    protected function getCurrentUserId(): ?int { return 1; }
    protected function getCurrentUserName(): ?string { return 'admin'; }
    protected function getClientIp(): string { return request()->getRealIp(); }
}
