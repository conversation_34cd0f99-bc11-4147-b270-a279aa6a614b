<?php

namespace app\controller;

use app\model\HotelFacility;
use app\model\Hotel;
use app\service\HotelFacilityService;
use base\auth\Auth;
use support\Request;
use support\Response;

/**
 * 酒店设施管理控制器
 */
class HotelFacilityController extends BaseController
{
    protected $hotelFacilityService;
    public function __construct()
    {
        $this->hotelFacilityService = new HotelFacilityService();
    }

    /**
     * 获取酒店设施列表
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     * @throws \Exception
     */
//    #[Auth('booking/hotel/facility/read')]
    public function index(Request $request, $hotel_id)
    {
        $result = $this->hotelFacilityService->getHotelFacilities($hotel_id);
        return $this->success($result);

    }

    /**
     * 创建酒店设施
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    #[Auth('booking/hotel/facility/create')]
    public function store(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            // 验证酒店是否存在
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['facility_id']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'facility_id' => 'integer',
                'is_available' => 'boolean',
                'custom_charge_amount' => 'numeric',
                'custom_capacity' => 'integer',
                'booking_required' => 'boolean',
                'advance_booking_hours' => 'integer',
                'sort_order' => 'integer',
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查是否已存在相同设施
            $existing = HotelFacility::where('hotel_id', $hotelId)
                ->where('facility_id', $data['facility_id'])
                ->first();

            if ($existing) {
                return $this->error('该设施已存在');
            }

            // 设置酒店ID
            $data['hotel_id'] = $hotelId;

            // 处理JSON字段
            if (isset($data['custom_operating_hours']) && is_array($data['custom_operating_hours'])) {
                $data['custom_operating_hours'] = $data['custom_operating_hours'];
            }

            if (isset($data['images']) && is_array($data['images'])) {
                $data['images'] = $data['images'];
            }

            $facility = HotelFacility::create($data);

            // 加载关联数据
            $facility->load(['facility']);

            return $this->success($facility, '创建成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '创建酒店设施');
        }
    }

    /**
     * 获取酒店设施详情
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    #[Auth('booking/hotel/facility/read')]
    public function show(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $facilityId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($facilityId <= 0) {
                return $this->error('设施ID无效');
            }

            $facility = HotelFacility::where('hotel_id', $hotelId)
                ->with(['facility', 'hotel'])
                ->find($facilityId);

            if (!$facility) {
                return $this->error('设施不存在', 404);
            }

            return $this->success($facility);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店设施详情');
        }
    }

    /**
     * 更新酒店设施
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    #[Auth('booking/hotel/facility/update')]
    public function update(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $facilityId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($facilityId <= 0) {
                return $this->error('设施ID无效');
            }

            $facility = HotelFacility::where('hotel_id', $hotelId)->find($facilityId);
            if (!$facility) {
                return $this->error('设施不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'facility_id' => 'integer',
                'is_available' => 'boolean',
                'custom_charge_amount' => 'numeric',
                'custom_capacity' => 'integer',
                'booking_required' => 'boolean',
                'advance_booking_hours' => 'integer',
                'sort_order' => 'integer',
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 如果更新facility_id，检查是否已存在
            if (isset($data['facility_id']) && $data['facility_id'] != $facility->facility_id) {
                $existing = HotelFacility::where('hotel_id', $hotelId)
                    ->where('facility_id', $data['facility_id'])
                    ->where('id', '!=', $facilityId)
                    ->first();

                if ($existing) {
                    return $this->error('该设施已存在');
                }
            }

            // 处理JSON字段
            if (isset($data['custom_operating_hours']) && is_array($data['custom_operating_hours'])) {
                $data['custom_operating_hours'] = $data['custom_operating_hours'];
            }

            if (isset($data['images']) && is_array($data['images'])) {
                $data['images'] = $data['images'];
            }

            $facility->update($data);

            // 重新加载关联数据
            $facility->load(['facility']);

            return $this->success($facility, '更新成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店设施');
        }
    }

    /**
     * 删除酒店设施
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $id
     * @return Response
     */
    #[Auth('booking/hotel/facility/delete')]
    public function destroy(Request $request, $hotel_id, $id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $facilityId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($facilityId <= 0) {
                return $this->error('设施ID无效');
            }

            $facility = HotelFacility::where('hotel_id', $hotelId)->find($facilityId);
            if (!$facility) {
                return $this->error('设施不存在', 404);
            }

            $facility->delete();

            return $this->success(null, '删除成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '删除酒店设施');
        }
    }

    /**
     * 批量创建酒店设施
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    #[Auth('booking/hotel/facility/create')]
    public function batchStore(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            // 验证酒店是否存在
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $data = $this->getInput($request);

            if (!isset($data['facilities']) || !is_array($data['facilities'])) {
                return $this->error('设施数据格式错误');
            }

            $facilities = $data['facilities'];
            if (empty($facilities)) {
                return $this->error('设施数据不能为空');
            }

            if (count($facilities) > 50) {
                return $this->error('批量创建数量不能超过50个');
            }

            $result = HotelFacility::batchCreate($hotelId, $facilities);

            return $this->success([
                'created_count' => count($result['created']),
                'error_count' => count($result['errors']),
                'created' => $result['created'],
                'errors' => $result['errors'],
            ], '批量创建完成');

        } catch (\Exception $e) {
            return $this->handleException($e, '批量创建酒店设施');
        }
    }

    /**
     * 批量更新酒店设施
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    #[Auth('booking/hotel/facility/update')]
    public function batchUpdate(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            if (!isset($data['facilities']) || !is_array($data['facilities'])) {
                return $this->error('设施数据格式错误');
            }

            $facilities = $data['facilities'];
            if (empty($facilities)) {
                return $this->error('设施数据不能为空');
            }

            if (count($facilities) > 50) {
                return $this->error('批量更新数量不能超过50个');
            }

            $updated = [];
            $errors = [];

            foreach ($facilities as $facilityData) {
                try {
                    if (!isset($facilityData['id'])) {
                        $errors[] = '设施ID不能为空';
                        continue;
                    }

                    $facility = HotelFacility::where('hotel_id', $hotelId)
                        ->find($facilityData['id']);

                    if (!$facility) {
                        $errors[] = "设施ID {$facilityData['id']} 不存在";
                        continue;
                    }

                    // 移除ID字段，避免更新主键
                    unset($facilityData['id']);

                    $facility->update($facilityData);
                    $updated[] = $facility;

                } catch (\Exception $e) {
                    $errors[] = "更新设施ID {$facilityData['id']} 失败: " . $e->getMessage();
                }
            }

            return $this->success([
                'updated_count' => count($updated),
                'error_count' => count($errors),
                'updated' => $updated,
                'errors' => $errors,
            ], '批量更新完成');

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新酒店设施');
        }
    }

    /**
     * 批量删除酒店设施
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    #[Auth('booking/hotel/facility/delete')]
    public function batchDestroy(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            if (!isset($data['ids']) || !is_array($data['ids'])) {
                return $this->error('设施ID数据格式错误');
            }

            $ids = array_filter(array_map('intval', $data['ids']));
            if (empty($ids)) {
                return $this->error('设施ID不能为空');
            }

            if (count($ids) > 50) {
                return $this->error('批量删除数量不能超过50个');
            }

            $deleted = HotelFacility::where('hotel_id', $hotelId)
                ->whereIn('id', $ids)
                ->delete();

            return $this->success([
                'deleted_count' => $deleted,
                'requested_count' => count($ids),
            ], '批量删除完成');

        } catch (\Exception $e) {
            return $this->handleException($e, '批量删除酒店设施');
        }
    }

    /**
     * 更新设施显示顺序
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    #[Auth('booking/hotel/facility/update')]
    public function updateOrder(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            if (!isset($data['orders']) || !is_array($data['orders'])) {
                return $this->error('排序数据格式错误');
            }

            $orders = $data['orders'];
            if (empty($orders)) {
                return $this->error('排序数据不能为空');
            }

            $success = HotelFacility::updateDisplayOrder($hotelId, $orders);

            if ($success) {
                return $this->success(null, '更新排序成功');
            } else {
                return $this->error('更新排序失败');
            }

        } catch (\Exception $e) {
            return $this->handleException($e, '更新设施排序');
        }
    }

    /**
     * 获取设施分类统计
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    #[Auth('booking/hotel/facility/read')]
    public function categoryStats(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            // 通过关联的StandardFacility获取分类统计
            $stats = HotelFacility::where('hotel_facilities.hotel_id', $hotelId)
                ->join('standard_facilities', 'hotel_facilities.facility_id', '=', 'standard_facilities.id')
                ->selectRaw('standard_facilities.category_id, COUNT(*) as count, SUM(CASE WHEN hotel_facilities.is_available = 1 THEN 1 ELSE 0 END) as available_count')
                ->groupBy('standard_facilities.category_id')
                ->get()
                ->map(function ($item) {
                    return [
                        'category_id' => $item->category_id,
                        'category_name' => '分类' . $item->category_id, // 这里可以根据实际的分类表来获取名称
                        'total_count' => $item->count,
                        'available_count' => $item->available_count,
                        'unavailable_count' => $item->count - $item->available_count,
                    ];
                });

            return $this->success($stats);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取设施分类统计');
        }
    }

    /**
     * 按分类分组获取设施
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    #[Auth('booking/hotel/facility/read')]
    public function grouped(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $grouped = HotelFacility::getByHotelGrouped($hotelId);

            return $this->success($grouped);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取分组设施');
        }
    }
}
