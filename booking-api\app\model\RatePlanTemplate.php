<?php

namespace app\model;

use support\Model;

/**
 * 房价计划模板模型
 */
class RatePlanTemplate extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'rate_plan_templates';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'description',
        'category',
        'config',
        'is_active',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'config' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 作用域：启用的模板
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 应用模板到房价计划数据
     *
     * @param array $basePlanData
     * @return array
     */
    public function applyToRatePlan(array $basePlanData = [])
    {
        $templateConfig = $this->config ?? [];
        
        // 合并模板配置和基础数据，基础数据优先级更高
        return array_merge($templateConfig, $basePlanData);
    }

    /**
     * 获取分类列表
     *
     * @return array
     */
    public static function getCategories()
    {
        return [
            'standard' => '标准模板',
            'promotion' => '促销模板',
            'package' => '套餐模板',
            'corporate' => '企业模板',
            'member' => '会员模板',
        ];
    }

    /**
     * 获取分类名称
     *
     * @return string
     */
    public function getCategoryNameAttribute()
    {
        $categories = static::getCategories();
        return $categories[$this->category] ?? $this->category;
    }
}
