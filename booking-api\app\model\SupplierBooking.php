<?php

namespace app\model;

/**
 * 供应商订单模型
 * 对应数据库表：supplier_bookings
 */
class SupplierBooking extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'supplier_bookings';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'booking_id',
        'supplier_id',
        'supplier_booking_id',
        'supplier_confirmation_no',
        'supplier_hotel_id',
        'supplier_room_type_id',
        'supplier_rate_plan_id',
        'supplier_request_data',
        'supplier_response_data',
        'supplier_status',
        'supplier_amount',
        'supplier_currency',
        'commission_amount',
        'commission_rate',
        'settlement_amount',
        'settlement_status',
        'settlement_date',
        'api_request_id',
        'api_response_time',
        'error_code',
        'error_message',
        'retry_count',
        'last_retry_at'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'booking_id' => 'integer',
        'supplier_id' => 'integer',
        'supplier_request_data' => 'array',
        'supplier_response_data' => 'array',
        'supplier_amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'commission_rate' => 'decimal:4',
        'settlement_amount' => 'decimal:2',
        'settlement_date' => 'date',
        'api_response_time' => 'integer',
        'retry_count' => 'integer',
        'last_retry_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 供应商订单状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_FAILED = 'failed';
    const STATUS_TIMEOUT = 'timeout';

    /**
     * 结算状态常量
     */
    const SETTLEMENT_STATUS_PENDING = 'pending';
    const SETTLEMENT_STATUS_PROCESSING = 'processing';
    const SETTLEMENT_STATUS_COMPLETED = 'completed';
    const SETTLEMENT_STATUS_FAILED = 'failed';

    /**
     * 获取本地订单
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * 获取供应商
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * 作用域：按供应商状态筛选
     */
    public function scopeBySupplierStatus($query, $status)
    {
        return $query->where('supplier_status', $status);
    }

    /**
     * 作用域：按结算状态筛选
     */
    public function scopeBySettlementStatus($query, $status)
    {
        return $query->where('settlement_status', $status);
    }

    /**
     * 作用域：待结算的
     */
    public function scopePendingSettlement($query)
    {
        return $query->where('settlement_status', self::SETTLEMENT_STATUS_PENDING)
            ->where('supplier_status', self::STATUS_CONFIRMED);
    }

    /**
     * 作用域：需要重试的
     */
    public function scopeNeedsRetry($query, $maxRetries = 3)
    {
        return $query->where('supplier_status', self::STATUS_FAILED)
            ->where('retry_count', '<', $maxRetries);
    }

    /**
     * 获取供应商状态名称
     */
    public function getSupplierStatusNameAttribute()
    {
        $statuses = [
            self::STATUS_PENDING => '待确认',
            self::STATUS_CONFIRMED => '已确认',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_FAILED => '失败',
            self::STATUS_TIMEOUT => '超时',
        ];

        return $statuses[$this->supplier_status] ?? '未知';
    }

    /**
     * 获取结算状态名称
     */
    public function getSettlementStatusNameAttribute()
    {
        $statuses = [
            self::SETTLEMENT_STATUS_PENDING => '待结算',
            self::SETTLEMENT_STATUS_PROCESSING => '结算中',
            self::SETTLEMENT_STATUS_COMPLETED => '已结算',
            self::SETTLEMENT_STATUS_FAILED => '结算失败',
        ];

        return $statuses[$this->settlement_status] ?? '未知';
    }

    /**
     * 更新供应商状态
     */
    public function updateSupplierStatus($status, $confirmationNo = null, $responseData = null)
    {
        $this->supplier_status = $status;
        
        if ($confirmationNo) {
            $this->supplier_confirmation_no = $confirmationNo;
        }
        
        if ($responseData) {
            $this->supplier_response_data = $responseData;
        }
        
        return $this->save();
    }

    /**
     * 标记为确认
     */
    public function markConfirmed($confirmationNo, $responseData = null)
    {
        return $this->updateSupplierStatus(self::STATUS_CONFIRMED, $confirmationNo, $responseData);
    }

    /**
     * 标记为取消
     */
    public function markCancelled($responseData = null)
    {
        return $this->updateSupplierStatus(self::STATUS_CANCELLED, null, $responseData);
    }

    /**
     * 标记为失败
     */
    public function markFailed($errorCode = null, $errorMessage = null, $responseData = null)
    {
        $this->supplier_status = self::STATUS_FAILED;
        $this->error_code = $errorCode;
        $this->error_message = $errorMessage;
        
        if ($responseData) {
            $this->supplier_response_data = $responseData;
        }
        
        return $this->save();
    }

    /**
     * 标记为超时
     */
    public function markTimeout()
    {
        return $this->updateSupplierStatus(self::STATUS_TIMEOUT);
    }

    /**
     * 增加重试次数
     */
    public function incrementRetryCount()
    {
        $this->retry_count = ($this->retry_count ?? 0) + 1;
        $this->last_retry_at = now();
        return $this->save();
    }

    /**
     * 重置重试次数
     */
    public function resetRetryCount()
    {
        $this->retry_count = 0;
        $this->last_retry_at = null;
        return $this->save();
    }

    /**
     * 计算佣金
     */
    public function calculateCommission()
    {
        if ($this->commission_rate && $this->supplier_amount) {
            $this->commission_amount = $this->supplier_amount * ($this->commission_rate / 100);
            $this->settlement_amount = $this->supplier_amount - $this->commission_amount;
        }
        
        return $this;
    }

    /**
     * 更新结算状态
     */
    public function updateSettlementStatus($status, $settlementDate = null)
    {
        $this->settlement_status = $status;
        
        if ($settlementDate) {
            $this->settlement_date = $settlementDate;
        } elseif ($status === self::SETTLEMENT_STATUS_COMPLETED) {
            $this->settlement_date = now()->toDateString();
        }
        
        return $this->save();
    }

    /**
     * 标记结算完成
     */
    public function markSettlementCompleted($settlementDate = null)
    {
        return $this->updateSettlementStatus(self::SETTLEMENT_STATUS_COMPLETED, $settlementDate);
    }

    /**
     * 标记结算失败
     */
    public function markSettlementFailed()
    {
        return $this->updateSettlementStatus(self::SETTLEMENT_STATUS_FAILED);
    }

    /**
     * 检查是否可以重试
     */
    public function canRetry($maxRetries = 3)
    {
        return $this->supplier_status === self::STATUS_FAILED && 
               ($this->retry_count ?? 0) < $maxRetries;
    }

    /**
     * 检查是否可以结算
     */
    public function canSettle()
    {
        return $this->supplier_status === self::STATUS_CONFIRMED &&
               $this->settlement_status === self::SETTLEMENT_STATUS_PENDING;
    }

    /**
     * 获取API响应时间（毫秒）
     */
    public function getApiResponseTimeMs()
    {
        return $this->api_response_time;
    }

    /**
     * 设置API响应时间
     */
    public function setApiResponseTime($milliseconds)
    {
        $this->api_response_time = $milliseconds;
        return $this;
    }

    /**
     * 获取请求数据中的特定字段
     */
    public function getRequestDataValue($key, $default = null)
    {
        $data = $this->supplier_request_data ?? [];
        return $data[$key] ?? $default;
    }

    /**
     * 获取响应数据中的特定字段
     */
    public function getResponseDataValue($key, $default = null)
    {
        $data = $this->supplier_response_data ?? [];
        return $data[$key] ?? $default;
    }

    /**
     * 设置请求数据
     */
    public function setRequestData(array $data)
    {
        $this->supplier_request_data = $data;
        return $this;
    }

    /**
     * 设置响应数据
     */
    public function setResponseData(array $data)
    {
        $this->supplier_response_data = $data;
        return $this;
    }
}
