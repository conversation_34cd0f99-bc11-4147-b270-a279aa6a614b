<?php

namespace app\model;

/**
 * 房型价格模型
 * 对应数据库表：room_rates
 */
class RoomRate extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_rates';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'room_type_id',
        'rate_plan_id',
        'date',
        'base_price',
        'selling_price',
        'room_fee',
        'service_fee',
        'tax_fee',
        'city_tax',
        'discount_type',
        'discount_value',
        'currency',
        'status'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'room_type_id' => 'integer',
        'rate_plan_id' => 'integer',
        'date' => 'date',
        'base_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'room_fee' => 'decimal:2',
        'service_fee' => 'decimal:2',
        'tax_fee' => 'decimal:2',
        'city_tax' => 'decimal:2',
        'discount_value' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 折扣类型常量
     */
    const DISCOUNT_TYPE_NONE = 'none';
    const DISCOUNT_TYPE_PERCENTAGE = 'percentage';
    const DISCOUNT_TYPE_AMOUNT = 'amount';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取所属房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * 获取所属价格计划
     */
    public function ratePlan()
    {
        return $this->belongsTo(RatePlan::class);
    }

    /**
     * 作用域：按日期筛选
     */
    public function scopeByDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * 作用域：按价格范围筛选
     */
    public function scopeByPriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('selling_price', [$minPrice, $maxPrice]);
    }

    /**
     * 作用域：按货币筛选
     */
    public function scopeByCurrency($query, $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * 获取折扣类型名称
     */
    public function getDiscountTypeNameAttribute()
    {
        $types = [
            self::DISCOUNT_TYPE_NONE => '无折扣',
            self::DISCOUNT_TYPE_PERCENTAGE => '百分比折扣',
            self::DISCOUNT_TYPE_AMOUNT => '固定金额折扣',
        ];

        return $types[$this->discount_type] ?? '未知';
    }

    /**
     * 计算折扣后价格
     */
    public function calculateDiscountedPrice()
    {
        if ($this->discount_type === self::DISCOUNT_TYPE_NONE || !$this->discount_value) {
            return $this->base_price;
        }

        if ($this->discount_type === self::DISCOUNT_TYPE_PERCENTAGE) {
            $discountAmount = $this->base_price * ($this->discount_value / 100);
            return $this->base_price - $discountAmount;
        }

        if ($this->discount_type === self::DISCOUNT_TYPE_AMOUNT) {
            return max(0, $this->base_price - $this->discount_value);
        }

        return $this->base_price;
    }

    /**
     * 获取总价格（包含所有费用）
     */
    public function getTotalPriceAttribute()
    {
        return $this->selling_price + $this->service_fee + $this->tax_fee + $this->city_tax;
    }

    /**
     * 获取折扣金额
     */
    public function getDiscountAmountAttribute()
    {
        if ($this->discount_type === self::DISCOUNT_TYPE_NONE || !$this->discount_value) {
            return 0;
        }

        if ($this->discount_type === self::DISCOUNT_TYPE_PERCENTAGE) {
            return $this->base_price * ($this->discount_value / 100);
        }

        if ($this->discount_type === self::DISCOUNT_TYPE_AMOUNT) {
            return min($this->discount_value, $this->base_price);
        }

        return 0;
    }

    /**
     * 设置百分比折扣
     */
    public function setPercentageDiscount($percentage)
    {
        $this->discount_type = self::DISCOUNT_TYPE_PERCENTAGE;
        $this->discount_value = $percentage;
        $this->selling_price = $this->calculateDiscountedPrice();
        
        return $this;
    }

    /**
     * 设置固定金额折扣
     */
    public function setAmountDiscount($amount)
    {
        $this->discount_type = self::DISCOUNT_TYPE_AMOUNT;
        $this->discount_value = $amount;
        $this->selling_price = $this->calculateDiscountedPrice();
        
        return $this;
    }

    /**
     * 清除折扣
     */
    public function clearDiscount()
    {
        $this->discount_type = self::DISCOUNT_TYPE_NONE;
        $this->discount_value = 0;
        $this->selling_price = $this->base_price;
        
        return $this;
    }

    /**
     * 批量更新价格
     */
    public static function batchUpdate($updates)
    {
        foreach ($updates as $update) {
            static::updateOrCreate(
                [
                    'hotel_id' => $update['hotel_id'],
                    'room_type_id' => $update['room_type_id'],
                    'rate_plan_id' => $update['rate_plan_id'],
                    'date' => $update['date']
                ],
                $update
            );
        }
    }

    /**
     * 获取指定日期范围的平均价格
     */
    public static function getAveragePrice($hotelId, $roomTypeId, $ratePlanId, $startDate, $endDate)
    {
        return static::where('hotel_id', $hotelId)
            ->where('room_type_id', $roomTypeId)
            ->where('rate_plan_id', $ratePlanId)
            ->whereBetween('date', [$startDate, $endDate])
            ->active()
            ->avg('selling_price');
    }

    /**
     * 获取指定日期范围的最低价格
     */
    public static function getMinPrice($hotelId, $roomTypeId, $ratePlanId, $startDate, $endDate)
    {
        return static::where('hotel_id', $hotelId)
            ->where('room_type_id', $roomTypeId)
            ->where('rate_plan_id', $ratePlanId)
            ->whereBetween('date', [$startDate, $endDate])
            ->active()
            ->min('selling_price');
    }

    /**
     * 获取指定日期范围的最高价格
     */
    public static function getMaxPrice($hotelId, $roomTypeId, $ratePlanId, $startDate, $endDate)
    {
        return static::where('hotel_id', $hotelId)
            ->where('room_type_id', $roomTypeId)
            ->where('rate_plan_id', $ratePlanId)
            ->whereBetween('date', [$startDate, $endDate])
            ->active()
            ->max('selling_price');
    }
}
