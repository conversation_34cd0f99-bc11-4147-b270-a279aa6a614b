<?php

namespace app\model;

use support\Model;

/**
 * 房价计划与OTA渠道关联模型
 */
class RatePlanOtaChannel extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'rate_plan_ota_channels';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'rate_plan_id',
        'ota_channel_id',
        'hotel_id',
        
        // 渠道特定配置
        'channel_rate_plan_code',
        'commission_rate',
        'markup_rate',
        'markup_amount',
        
        // 覆盖配置
        'min_stay_override',
        'max_stay_override',
        'booking_window_override',
        'cancellation_policy_override',
        
        // 生效时间
        'effective_from',
        'effective_to',
        
        // 状态和优先级
        'is_active',
        'priority',
        
        // 同步状态
        'sync_status',
        'last_sync_at',
        'sync_error_message',
        
        // 统计数据
        'total_bookings',
        'total_revenue',
        'last_booking_at',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'rate_plan_id' => 'integer',
        'ota_channel_id' => 'integer',
        'hotel_id' => 'integer',
        'commission_rate' => 'decimal:2',
        'markup_rate' => 'decimal:2',
        'markup_amount' => 'decimal:2',
        'min_stay_override' => 'integer',
        'max_stay_override' => 'integer',
        'booking_window_override' => 'integer',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'total_bookings' => 'integer',
        'total_revenue' => 'decimal:2',
        'effective_from' => 'date',
        'effective_to' => 'date',
        'last_sync_at' => 'datetime',
        'last_booking_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联房价计划
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function ratePlan()
    {
        return $this->belongsTo(RatePlan::class);
    }

    /**
     * 关联OTA渠道
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function otaChannel()
    {
        return $this->belongsTo(OtaChannel::class);
    }

    /**
     * 关联酒店
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 作用域：激活的关联
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：有效期内的关联
     */
    public function scopeEffective($query, $date = null)
    {
        $date = $date ?: now()->toDateString();
        
        return $query->where(function ($q) use ($date) {
            $q->whereNull('effective_from')
              ->orWhere('effective_from', '<=', $date);
        })->where(function ($q) use ($date) {
            $q->whereNull('effective_to')
              ->orWhere('effective_to', '>=', $date);
        });
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：按房价计划筛选
     */
    public function scopeByRatePlan($query, $ratePlanId)
    {
        return $query->where('rate_plan_id', $ratePlanId);
    }

    /**
     * 作用域：按OTA渠道筛选
     */
    public function scopeByOtaChannel($query, $otaChannelId)
    {
        return $query->where('ota_channel_id', $otaChannelId);
    }

    /**
     * 检查关联是否有效
     *
     * @param string|null $date
     * @return bool
     */
    public function isEffective($date = null)
    {
        if (!$this->is_active) {
            return false;
        }

        $date = $date ?: now()->toDateString();

        if ($this->effective_from && $this->effective_from > $date) {
            return false;
        }

        if ($this->effective_to && $this->effective_to < $date) {
            return false;
        }

        return true;
    }

    /**
     * 获取有效的佣金比例
     *
     * @return float|null
     */
    public function getEffectiveCommissionRate()
    {
        return $this->commission_rate ?? $this->otaChannel->commission_rate ?? null;
    }

    /**
     * 获取有效的最少入住天数
     *
     * @return int|null
     */
    public function getEffectiveMinStay()
    {
        return $this->min_stay_override ?? $this->ratePlan->min_stay ?? null;
    }

    /**
     * 获取有效的最多入住天数
     *
     * @return int|null
     */
    public function getEffectiveMaxStay()
    {
        return $this->max_stay_override ?? $this->ratePlan->max_stay ?? null;
    }

    /**
     * 获取有效的预订窗口期
     *
     * @return int|null
     */
    public function getEffectiveBookingWindow()
    {
        return $this->booking_window_override ?? $this->otaChannel->booking_window ?? null;
    }

    /**
     * 获取有效的取消政策
     *
     * @return string|null
     */
    public function getEffectiveCancellationPolicy()
    {
        return $this->cancellation_policy_override ?? $this->ratePlan->cancellation_policy ?? null;
    }

    /**
     * 计算加价后的价格
     *
     * @param float $basePrice
     * @return float
     */
    public function calculateMarkupPrice($basePrice)
    {
        $price = $basePrice;

        // 应用百分比加价
        if ($this->markup_rate > 0) {
            $price += $basePrice * ($this->markup_rate / 100);
        }

        // 应用固定金额加价
        if ($this->markup_amount > 0) {
            $price += $this->markup_amount;
        }

        return round($price, 2);
    }

    /**
     * 更新统计数据
     *
     * @param int $bookings
     * @param float $revenue
     * @return void
     */
    public function updateStatistics($bookings = 1, $revenue = 0)
    {
        $this->increment('total_bookings', $bookings);
        $this->increment('total_revenue', $revenue);
        $this->update(['last_booking_at' => now()]);
    }

    /**
     * 更新同步状态
     *
     * @param string $status
     * @param string|null $errorMessage
     * @return void
     */
    public function updateSyncStatus($status, $errorMessage = null)
    {
        $this->update([
            'sync_status' => $status,
            'last_sync_at' => now(),
            'sync_error_message' => $errorMessage,
        ]);
    }
}
