<?php

namespace app\model;

use support\Model;

/**
 * 酒店集团多语言翻译模型
 */
class HotelGroupTranslation extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'hotel_group_translations';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_group_id',
        'locale',
        'name',
        'short_name',
        'description',
        'brand_slogan',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_group_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取关联的酒店集团
     */
    public function hotelGroup()
    {
        return $this->belongsTo(HotelGroup::class);
    }

    /**
     * 作用域：按语言筛选
     */
    public function scopeByLocale($query, $locale)
    {
        return $query->where('locale', $locale);
    }
}
