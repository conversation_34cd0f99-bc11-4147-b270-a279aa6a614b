<?php

namespace app\controller;

use app\model\HotelPolicyManager;
use support\Request;
use support\Response;

/**
 * 酒店政策管理控制器
 */
class HotelPolicyController
{
    /**
     * 获取酒店所有类型政策列表（按类型分组返回）
     */
    public function index(Request $request, int $id): Response
    {
        try {
            $policies = HotelPolicyManager::getHotelPolicies($id);

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $policies,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取酒店政策失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 获取指定类型的政策详情
     */
    public function show(Request $request, int $id, string $type): Response
    {
        try {
            // 验证政策类型
            if (!HotelPolicyManager::isValidPolicyType($type)) {
                return json([
                    'code' => 400,
                    'message' => '无效的政策类型: ' . $type,
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 400);
            }

            $policy = HotelPolicyManager::getHotelPolicyByType($id, $type);

            if (!$policy) {
                return json([
                    'code' => 404,
                    'message' => '政策不存在',
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 404);
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $policy,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取政策详情失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 更新指定类型的政策信息
     */
    public function update(Request $request, int $id, string $type): Response
    {
        try {
            // 验证政策类型
            if (!HotelPolicyManager::isValidPolicyType($type)) {
                return json([
                    'code' => 400,
                    'message' => '无效的政策类型: ' . $type,
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 400);
            }

            // 获取请求数据
            $data = $request->all();
            
            // 移除不应该被更新的字段
            unset($data['id'], $data['hotel_id'], $data['created_at'], $data['updated_at']);

            if (empty($data)) {
                return json([
                    'code' => 400,
                    'message' => '没有有效的更新数据',
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 400);
            }

            // 更新政策
            $updatedPolicy = HotelPolicyManager::updateHotelPolicy($id, $type, $data);

            return json([
                'code' => 200,
                'message' => '政策更新成功',
                'data' => $updatedPolicy,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\InvalidArgumentException $e) {
            return json([
                'code' => 400,
                'message' => $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 400);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新政策失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 获取政策类型列表
     */
    public function types(Request $request): Response
    {
        try {
            $locale = $request->get('locale', 'zh');
            $types = HotelPolicyManager::getPolicyTypesList($locale);

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $types,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取政策类型失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 为酒店初始化默认政策
     */
    public function initializeDefaults(Request $request, int $id): Response
    {
        try {
            $createdPolicies = HotelPolicyManager::createDefaultPolicies($id);

            return json([
                'code' => 200,
                'message' => '默认政策初始化成功',
                'data' => [
                    'hotel_id' => $id,
                    'created_policies' => $createdPolicies,
                    'created_count' => count($createdPolicies)
                ],
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '初始化默认政策失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }
}
