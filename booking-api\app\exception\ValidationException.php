<?php

namespace app\exception;

use Exception;

/**
 * 验证异常类
 * 用于处理数据验证相关的异常
 */
class ValidationException extends Exception
{
    /**
     * 验证错误
     *
     * @var array
     */
    protected $errors;

    /**
     * 构造函数
     *
     * @param array $errors 验证错误
     * @param string $message 错误消息
     * @param int $code 错误代码
     * @param Exception|null $previous 上一个异常
     */
    public function __construct(array $errors = [], string $message = '数据验证失败', int $code = 422, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errors = $errors;
    }

    /**
     * 获取验证错误
     *
     * @return array
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * 设置验证错误
     *
     * @param array $errors
     * @return $this
     */
    public function setErrors(array $errors): self
    {
        $this->errors = $errors;
        return $this;
    }

    /**
     * 添加验证错误
     *
     * @param string $field
     * @param string|array $error
     * @return $this
     */
    public function addError(string $field, $error): self
    {
        if (is_string($error)) {
            $this->errors[$field][] = $error;
        } elseif (is_array($error)) {
            $this->errors[$field] = array_merge($this->errors[$field] ?? [], $error);
        }

        return $this;
    }

    /**
     * 检查是否有指定字段的错误
     *
     * @param string $field
     * @return bool
     */
    public function hasError(string $field): bool
    {
        return isset($this->errors[$field]) && !empty($this->errors[$field]);
    }

    /**
     * 获取指定字段的错误
     *
     * @param string $field
     * @return array
     */
    public function getFieldErrors(string $field): array
    {
        return $this->errors[$field] ?? [];
    }

    /**
     * 获取第一个错误消息
     *
     * @return string|null
     */
    public function getFirstError(): ?string
    {
        foreach ($this->errors as $fieldErrors) {
            if (!empty($fieldErrors) && is_array($fieldErrors)) {
                return $fieldErrors[0];
            } elseif (is_string($fieldErrors)) {
                return $fieldErrors;
            }
        }

        return null;
    }

    /**
     * 获取所有错误消息（扁平化）
     *
     * @return array
     */
    public function getAllMessages(): array
    {
        $messages = [];

        foreach ($this->errors as $field => $fieldErrors) {
            if (is_array($fieldErrors)) {
                foreach ($fieldErrors as $error) {
                    $messages[] = $error;
                }
            } elseif (is_string($fieldErrors)) {
                $messages[] = $fieldErrors;
            }
        }

        return $messages;
    }

    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'errors' => $this->errors,
            'code' => $this->getCode()
        ];
    }

    /**
     * 转换为JSON字符串
     *
     * @return string
     */
    public function toJson(): string
    {
        return json_encode($this->toArray(), JSON_UNESCAPED_UNICODE);
    }

    /**
     * 创建验证异常实例
     *
     * @param array $errors
     * @param string $message
     * @return static
     */
    public static function withErrors(array $errors, string $message = '数据验证失败'): self
    {
        return new static($errors, $message);
    }

    /**
     * 从验证结果创建异常
     *
     * @param array $validationResult
     * @return static
     */
    public static function fromValidationResult(array $validationResult): self
    {
        $errors = $validationResult['errors'] ?? [];
        $message = $validationResult['message'] ?? '数据验证失败';

        return new static($errors, $message);
    }

    /**
     * 合并多个验证异常
     *
     * @param ValidationException ...$exceptions
     * @return static
     */
    public static function merge(ValidationException ...$exceptions): self
    {
        $mergedErrors = [];
        $messages = [];

        foreach ($exceptions as $exception) {
            $mergedErrors = array_merge_recursive($mergedErrors, $exception->getErrors());
            $messages[] = $exception->getMessage();
        }

        $message = implode('; ', array_unique($messages));

        return new static($mergedErrors, $message);
    }

    /**
     * 检查是否为空（没有错误）
     *
     * @return bool
     */
    public function isEmpty(): bool
    {
        return empty($this->errors);
    }

    /**
     * 获取错误数量
     *
     * @return int
     */
    public function count(): int
    {
        $count = 0;

        foreach ($this->errors as $fieldErrors) {
            if (is_array($fieldErrors)) {
                $count += count($fieldErrors);
            } else {
                $count++;
            }
        }

        return $count;
    }

    /**
     * 格式化错误消息
     *
     * @param string $format 格式化模板，支持 {field} 和 {message} 占位符
     * @return array
     */
    public function formatMessages(string $format = '{field}: {message}'): array
    {
        $formatted = [];

        foreach ($this->errors as $field => $fieldErrors) {
            if (is_array($fieldErrors)) {
                foreach ($fieldErrors as $error) {
                    $formatted[] = str_replace(
                        ['{field}', '{message}'],
                        [$field, $error],
                        $format
                    );
                }
            } elseif (is_string($fieldErrors)) {
                $formatted[] = str_replace(
                    ['{field}', '{message}'],
                    [$field, $fieldErrors],
                    $format
                );
            }
        }

        return $formatted;
    }

    /**
     * 转换为HTML格式
     *
     * @param string $listTag 列表标签，如 'ul' 或 'ol'
     * @param string $itemTag 列表项标签，如 'li'
     * @return string
     */
    public function toHtml(string $listTag = 'ul', string $itemTag = 'li'): string
    {
        $messages = $this->getAllMessages();

        if (empty($messages)) {
            return '';
        }

        $items = array_map(function($message) use ($itemTag) {
            return "<{$itemTag}>" . htmlspecialchars($message, ENT_QUOTES, 'UTF-8') . "</{$itemTag}>";
        }, $messages);

        return "<{$listTag}>" . implode('', $items) . "</{$listTag}>";
    }

    /**
     * 魔术方法：转换为字符串
     *
     * @return string
     */
    public function __toString(): string
    {
        return $this->getMessage() . ': ' . implode(', ', $this->getAllMessages());
    }
}
