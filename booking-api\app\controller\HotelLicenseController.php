<?php

namespace app\controller;

use app\model\Hotel;
use app\model\HotelLicense;
use support\Request;
use support\Response;

class HotelLicenseController extends BaseController
{
    /**
     * 列出酒店资质
     */
    public function index(Request $request, $id): Response
    {
        try {
            $hotel = Hotel::find($id);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $licenses = HotelLicense::where('hotel_id', $id)
                ->where('status', '!=', 'inactive')
                ->orderBy('id', 'desc')
                ->get()
                ->map(fn($l) => $l->toApiArray());

            return $this->success($licenses);
        } catch (\Exception $e) {
            return $this->error('获取资质列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取单个资质
     */
    public function show(Request $request, $id, $license_id): Response
    {
        try {
            $license = HotelLicense::where('hotel_id', $id)->where('id', $license_id)->first();
            if (!$license) {
                return $this->error('资质不存在', 404);
            }
            return $this->success($license->toApiArray());
        } catch (\Exception $e) {
            return $this->error('获取资质详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建资质
     */
    public function store(Request $request, $id): Response
    {
        try {
            $hotel = Hotel::find($id);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $data = $request->all();
            $data['hotel_id'] = (int) $id;

            $validator = validator($data, HotelLicense::rules(false));
            if ($validator->fails()) {
                return $this->error('数据验证失败', 400, $validator->errors());
            }

            // license_data 允许字符串(JSON)或数组
            if (isset($data['license_data']) && is_string($data['license_data'])) {
                $decoded = json_decode($data['license_data'], true);
                $data['license_data'] = is_array($decoded) ? $decoded : [];
            }

            // 避免重复的(hotel_id, license_type) 组合
            $exists = HotelLicense::where('hotel_id', $id)
                ->where('license_type', $data['license_type'])
                ->first();
            if ($exists) {
                return $this->error('该资质类型已存在', 409);
            }

            $license = HotelLicense::create($data);
            return $this->success($license->toApiArray(), '资质创建成功');
        } catch (\Exception $e) {
            return $this->error('创建资质失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新资质
     */
    public function update(Request $request, $id, $license_id): Response
    {
        try {
            $license = HotelLicense::where('hotel_id', $id)->where('id', $license_id)->first();
            if (!$license) {
                return $this->error('资质不存在', 404);
            }

            $data = $request->all();
            $validator = validator($data, HotelLicense::rules(true));
            if ($validator->fails()) {
                return $this->error('数据验证失败', 400, $validator->errors());
            }

            if (isset($data['license_data']) && is_string($data['license_data'])) {
                $decoded = json_decode($data['license_data'], true);
                $data['license_data'] = is_array($decoded) ? $decoded : [];
            }

            // 如果更新了license_type，需要保持(hotel_id, license_type)唯一
            if (isset($data['license_type'])) {
                $dup = HotelLicense::where('hotel_id', $id)
                    ->where('license_type', $data['license_type'])
                    ->where('id', '!=', $license_id)
                    ->first();
                if ($dup) {
                    return $this->error('该资质类型已存在', 409);
                }
            }

            $license->update($data);
            return $this->success($license->fresh()->toApiArray(), '资质更新成功');
        } catch (\Exception $e) {
            return $this->error('更新资质失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除资质
     */
    public function destroy(Request $request, $id, $license_id): Response
    {
        try {
            $license = HotelLicense::where('hotel_id', $id)->where('id', $license_id)->first();
            if (!$license) {
                return $this->error('资质不存在', 404);
            }

            $license->delete();
            return $this->success(null, '资质删除成功');
        } catch (\Exception $e) {
            return $this->error('删除资质失败: ' . $e->getMessage());
        }
    }
}


