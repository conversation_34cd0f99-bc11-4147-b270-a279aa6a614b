<?php

namespace app\service;

use app\model\Hotel;
use app\model\RoomType;
use Illuminate\Contracts\Database\Query\Builder;

/**
 * 酒店管理服务
 */
class HotelService extends BaseService
{
    /**
     * 获取酒店列表
     *
     * @param array $params
     * @return array
     */
    public function getHotelList(array $params = [])
    {
        try {
            $query = Hotel::with(['group', 'brand'])->select([
                'id','group_id','brand_id',
                'code','hotel_code','name',
                'description','country','province','city','district',
                'address','latitude','longitude','star_rating',
                'opening_date','total_rooms','active'
            ])
                ->active();

            // 城市筛选
            if (!empty($params['city'])) {
                $query->where('city', $params['city']);
            }

            // 星级筛选
            if (!empty($params['star_rating'])) {
                $query->where('star_rating', $params['star_rating']);
            }

            // 推荐筛选
            if (isset($params['is_featured'])) {
                $query->where('is_featured', (bool)$params['is_featured']);
            }

            // 关键词搜索
            if (!empty($params['keyword'])) {
                $keyword = $params['keyword'];
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('name_en', 'like', "%{$keyword}%")
                      ->orWhere('address', 'like', "%{$keyword}%");
                });
            }

            // 地理位置筛选
            if (!empty($params['latitude']) && !empty($params['longitude'])) {
                $radius = $params['radius'] ?? 10; // 默认10公里
                $query->nearby($params['latitude'], $params['longitude'], $radius);
            }

            // 排序
            $sortBy = $params['sort_by'] ?? 'created_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            $query->orderBy($sortBy, $sortOrder);

            // 分页
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $result = $this->paginate($query, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('获取酒店列表失败', ['error' => $e->getMessage(), 'params' => $params]);
            return $this->error('获取酒店列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店详情
     *
     * @param int $hotelId
     * @return array
     */
    public function getHotelDetail(int $hotelId)
    {
        try {
            $hotel = Hotel::with(['group', 'brand'])
                ->find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            return $this->success($hotel);

        } catch (\Exception $e) {
            $this->logError('获取酒店详情失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('获取酒店详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建酒店
     *
     * @param array $data
     * @return array
     */
    public function createHotel(array $data)
    {
        try {
            $this->validateRequired($data, ['name', 'city', 'address']);

            return $this->transaction(function () use ($data) {
                // 生成酒店编码
                if (empty($data['code'])) {
                    $data['code'] = $this->generateHotelCode($data['name']);
                }

                // 检查编码是否重复
                if (Hotel::where('code', $data['code'])->exists()) {
                    throw new \Exception('酒店编码已存在');
                }

                $hotel = Hotel::create($this->filterEmpty($data));

                $this->logInfo('创建酒店成功', ['hotel_id' => $hotel->id, 'hotel_name' => $hotel->name]);

                return $this->success($hotel, '创建酒店成功');
            });

        } catch (\Exception $e) {
            $this->logError('创建酒店失败', ['error' => $e->getMessage(), 'data' => $data]);
            return $this->error('创建酒店失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店信息
     *
     * @param int $hotelId
     * @param array $data
     * @return array
     */
    public function updateHotel(int $hotelId, array $data)
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            return $this->transaction(function () use ($hotel, $data) {
                // 如果更新编码，检查是否重复
                if (!empty($data['code']) && $data['code'] !== $hotel->code) {
                    if (Hotel::where('code', $data['code'])->where('id', '!=', $hotel->id)->exists()) {
                        throw new \Exception('酒店编码已存在');
                    }
                }

                $hotel->update($this->filterEmpty($data));

                $this->logInfo('更新酒店成功', ['hotel_id' => $hotel->id, 'hotel_name' => $hotel->name]);

                return $this->success($hotel, '更新酒店成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新酒店失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage(), 'data' => $data]);
            return $this->error('更新酒店失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除酒店
     *
     * @param int $hotelId
     * @return array
     */
    public function deleteHotel(int $hotelId)
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            // 检查是否有关联的订单
            if ($hotel->bookings()->exists()) {
                return $this->error('酒店存在关联订单，无法删除');
            }

            return $this->transaction(function () use ($hotel) {
                $hotelName = $hotel->name;
                $hotel->delete();

                $this->logInfo('删除酒店成功', ['hotel_id' => $hotel->id, 'hotel_name' => $hotelName]);

                return $this->success(null, '删除酒店成功');
            });

        } catch (\Exception $e) {
            $this->logError('删除酒店失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('删除酒店失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取房型列表
     *
     * @param int $hotelId
     * @param array $params
     * @return array
     */
    public function getRoomTypeList(int $hotelId, array $params = [])
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $query = $hotel->roomTypes()->active();

            // 床型筛选
            if (!empty($params['bed_type'])) {
                $query->where('bed_type', $params['bed_type']);
            }

            // 最大入住人数筛选
            if (!empty($params['max_occupancy'])) {
                $query->where('max_occupancy', '>=', $params['max_occupancy']);
            }

            // 价格范围筛选
            if (!empty($params['min_price']) || !empty($params['max_price'])) {
                $minPrice = $params['min_price'] ?? 0;
                $maxPrice = $params['max_price'] ?? 999999;
                $query->whereBetween('base_price', [$minPrice, $maxPrice]);
            }

            // 排序
            $query->orderBy('created_at');

            $roomTypes = $query->get();

            return $this->success($roomTypes);

        } catch (\Exception $e) {
            $this->logError('获取房型列表失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('获取房型列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建房型
     *
     * @param int $hotelId
     * @param array $data
     * @return array
     */
    public function createRoomType(int $hotelId, array $data)
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $this->validateRequired($data, ['name', 'code']);

            return $this->transaction(function () use ($hotel, $data) {
                // 检查房型编码在同一酒店内是否重复
                if ($hotel->roomTypes()->where('code', $data['code'])->exists()) {
                    throw new \Exception('房型编码在该酒店内已存在');
                }

                $data['hotel_id'] = $hotel->id;
                $roomType = RoomType::create($this->filterEmpty($data));

                $this->logInfo('创建房型成功', [
                    'hotel_id' => $hotel->id,
                    'room_type_id' => $roomType->id,
                    'room_type_name' => $roomType->name
                ]);

                return $this->success($roomType, '创建房型成功');
            });

        } catch (\Exception $e) {
            $this->logError('创建房型失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage(), 'data' => $data]);
            return $this->error('创建房型失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成酒店编码
     *
     * @param string $hotelName
     * @return string
     */
    private function generateHotelCode(string $hotelName)
    {
        // 简单的编码生成逻辑，实际可以更复杂
        $prefix = 'H';
        $timestamp = date('ymd');
        $random = mt_rand(100, 999);

        return $prefix . $timestamp . $random;
    }

    /**
     * 获取酒店设施信息
     *
     * @param int $hotelId
     * @return array
     */
    public function getHotelFacilities(int $hotelId)
    {
        try {
            $hotel = Hotel::find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $facilities = [
                'general' => $hotel->facilities ?? [],
                'business' => $hotel->business_facilities ?? [],
                'recreation' => $hotel->recreation_facilities ?? [],
                'dining' => $hotel->dining_facilities ?? [],
                'meeting' => $hotel->meeting_facilities ?? [],
                'spa_wellness' => $hotel->spa_wellness_facilities ?? [],
                'family' => $hotel->family_facilities ?? [],
                'pet' => $hotel->pet_facilities ?? []
            ];

            return $this->success($facilities);

        } catch (\Exception $e) {
            $this->logError('获取酒店设施信息失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('获取酒店设施信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店设施信息
     *
     * @param int $hotelId
     * @param array $data
     * @return array
     */
    public function updateHotelFacilities(int $hotelId, array $data)
    {

        try {
            $hotel = Hotel::find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            return $this->transaction(function () use ($hotel, $data) {
                // 更新各类设施信息
                $facilityFields = [
                    'general_facilities' => 'facilities',
                    'business_facilities' => 'business_facilities',
                    'recreation_facilities' => 'recreation_facilities',
                    'dining_facilities' => 'dining_facilities',
                    'meeting_facilities' => 'meeting_facilities',
                    'spa_wellness_facilities' => 'spa_wellness_facilities',
                    'family_facilities' => 'family_facilities',
                    'pet_facilities' => 'pet_facilities'
                ];

                foreach ($facilityFields as $inputField => $dbField) {
                    if (isset($data[$inputField])) {
                        $hotel->$dbField = $data[$inputField];
                    }
                }

                $hotel->save();

                $this->logInfo('更新酒店设施信息成功', [
                    'hotel_id' => $hotelId,
                    'hotel_name' => $hotel->name
                ]);

                return $this->success($hotel, '更新酒店设施信息成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新酒店设施信息失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage(), 'data' => $data]);
            return $this->error('更新酒店设施信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店政策信息
     *
     * @param int $hotelId
     * @return array
     */
    public function getHotelPolicies(int $hotelId)
    {
        try {
            $hotel = Hotel::find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $policies = [
                'booking' => $hotel->booking_policies ?? [],
                'guest' => $hotel->guest_policies ?? [],
                'cancellation' => $hotel->cancellation_policy,
                'child' => $hotel->child_policy,
                'pet' => $hotel->pet_policy,
                'safety' => $hotel->safety_policies ?? [],
                'environmental' => $hotel->environmental_policies ?? []
            ];

            return $this->success($policies);

        } catch (\Exception $e) {
            $this->logError('获取酒店政策信息失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('获取酒店政策信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店政策信息
     *
     * @param int $hotelId
     * @param array $data
     * @return array
     */
    public function updateHotelPolicies(int $hotelId, array $data)
    {
        try {
            $hotel = Hotel::find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            return $this->transaction(function () use ($hotel, $data) {
                // 更新各类政策信息
                $policyFields = [
                    'booking_policies',
                    'guest_policies',
                    'cancellation_policy',
                    'child_policy',
                    'pet_policy',
                    'safety_policies',
                    'environmental_policies'
                ];

                foreach ($policyFields as $field) {
                    if (isset($data[$field])) {
                        $hotel->$field = $data[$field];
                    }
                }

                $hotel->save();

                $this->logInfo('更新酒店政策信息成功', [
                    'hotel_id' => $hotelId,
                    'hotel_name' => $hotel->name
                ]);

                return $this->success($hotel, '更新酒店政策信息成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新酒店政策信息失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage(), 'data' => $data]);
            return $this->error('更新酒店政策信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店营业时间
     *
     * @param int $hotelId
     * @return array
     */
    public function getHotelBusinessHours(int $hotelId)
    {
        try {
            $hotel = Hotel::find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $businessHours = $hotel->business_hours ?? [
                'monday' => ['open' => '00:00', 'close' => '23:59', 'is_open' => true],
                'tuesday' => ['open' => '00:00', 'close' => '23:59', 'is_open' => true],
                'wednesday' => ['open' => '00:00', 'close' => '23:59', 'is_open' => true],
                'thursday' => ['open' => '00:00', 'close' => '23:59', 'is_open' => true],
                'friday' => ['open' => '00:00', 'close' => '23:59', 'is_open' => true],
                'saturday' => ['open' => '00:00', 'close' => '23:59', 'is_open' => true],
                'sunday' => ['open' => '00:00', 'close' => '23:59', 'is_open' => true]
            ];

            // 添加当前营业状态
            $result = [
                'business_hours' => $businessHours,
                'current_status' => $hotel->isCurrentlyOpen(),
                'timezone' => $hotel->timezone ?? 'Asia/Shanghai'
            ];

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('获取酒店营业时间失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('获取酒店营业时间失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店营业时间
     *
     * @param int $hotelId
     * @param array $data
     * @return array
     */
    public function updateHotelBusinessHours(int $hotelId, array $data)
    {
        try {
            $hotel = Hotel::find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            return $this->transaction(function () use ($hotel, $data) {
                if (isset($data['business_hours'])) {
                    $hotel->business_hours = $data['business_hours'];
                }

                if (isset($data['timezone'])) {
                    $hotel->timezone = $data['timezone'];
                }

                $hotel->save();

                $this->logInfo('更新酒店营业时间成功', [
                    'hotel_id' => $hotelId,
                    'hotel_name' => $hotel->name
                ]);

                return $this->success($hotel, '更新酒店营业时间成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新酒店营业时间失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage(), 'data' => $data]);
            return $this->error('更新酒店营业时间失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店完整信息（对标携程EBooking）
     *
     * @param int $hotelId
     * @return array
     */
    public function getHotelFullInfo(int $hotelId)
    {
        try {
            $hotel = Hotel::with(['group', 'brand', 'activeRoomTypes'])
                ->find($hotelId);

            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $fullInfo = $hotel->getFullHotelInfo();

            return $this->success($fullInfo);

        } catch (\Exception $e) {
            $this->logError('获取酒店完整信息失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('获取酒店完整信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新酒店信息
     *
     * @param array $updates
     * @return array
     */
    public function batchUpdateHotels(array $updates)
    {
        try {
            $results = [];
            $errors = [];

            return $this->transaction(function () use ($updates, &$results, &$errors) {
                foreach ($updates as $update) {
                    if (!isset($update['hotel_id']) || !isset($update['data'])) {
                        $errors[] = '缺少必需的hotel_id或data字段';
                        continue;
                    }

                    $hotelId = $update['hotel_id'];
                    $data = $update['data'];

                    $hotel = Hotel::find($hotelId);
                    if (!$hotel) {
                        $errors[] = "酒店ID {$hotelId} 不存在";
                        continue;
                    }

                    try {
                        $hotel->update($this->filterEmpty($data));
                        $results[] = [
                            'hotel_id' => $hotelId,
                            'status' => 'success',
                            'message' => '更新成功'
                        ];
                    } catch (\Exception $e) {
                        $errors[] = "酒店ID {$hotelId} 更新失败: " . $e->getMessage();
                        $results[] = [
                            'hotel_id' => $hotelId,
                            'status' => 'error',
                            'message' => $e->getMessage()
                        ];
                    }
                }

                $this->logInfo('批量更新酒店信息完成', [
                    'total' => count($updates),
                    'success' => count(array_filter($results, function($r) { return $r['status'] === 'success'; })),
                    'errors' => count($errors)
                ]);

                return $this->success([
                    'results' => $results,
                    'errors' => $errors,
                    'summary' => [
                        'total' => count($updates),
                        'success' => count(array_filter($results, function($r) { return $r['status'] === 'success'; })),
                        'failed' => count($errors)
                    ]
                ], '批量更新完成');
            });

        } catch (\Exception $e) {
            $this->logError('批量更新酒店信息失败', ['error' => $e->getMessage(), 'updates' => $updates]);
            return $this->error('批量更新酒店信息失败: ' . $e->getMessage());
        }
    }
}
