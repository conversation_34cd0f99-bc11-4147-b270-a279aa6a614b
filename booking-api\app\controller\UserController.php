<?php

namespace app\controller;

use app\model\User;
use app\model\Role;
use app\model\UserRole;
use support\Request;
use support\Response;
use support\Db;

/**
 * 用户管理控制器
 */
class UserController extends BaseController
{
    /**
     * 获取用户列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $page = (int)$request->get('page', 1);
            $perPage = (int)$request->get('per_page', 10);
            $username = $request->get('username');
            $email = $request->get('email');
            $userType = $request->get('user_type');
            $status = $request->get('status');

            $query = User::with(['roles']);

            // 搜索条件
            if ($username) {
                $query->where('username', 'like', "%{$username}%");
            }
            if ($email) {
                $query->where('email', 'like', "%{$email}%");
            }
            if ($userType) {
                $query->where('user_type', $userType);
            }
            if ($status) {
                $query->where('status', $status);
            }

            // 分页查询
            $total = $query->count();
            $users = $query->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            $items = $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'real_name' => $user->real_name,
                    'user_type' => $user->user_type,
                    'user_type_name' => $user->user_type_name,
                    'status' => $user->status,
                    'last_login_at' => $user->last_login_at,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                    'roles' => $user->roles->map(function ($role) {
                        return [
                            'id' => $role->id,
                            'name' => $role->name,
                            'code' => $role->code,
                        ];
                    }),
                ];
            });

            return $this->success([
                'items' => $items,
                'pagination' => [
                    'total' => $total,
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'last_page' => ceil($total / $perPage),
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取用户列表');
        }
    }

    /**
     * 获取用户详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $userId = (int)$id;
            
            if ($userId <= 0) {
                return $this->error('用户ID无效');
            }

            $user = User::with(['roles'])->find($userId);
            
            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            return $this->success([
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'phone' => $user->phone,
                'real_name' => $user->real_name,
                'user_type' => $user->user_type,
                'user_type_name' => $user->user_type_name,
                'status' => $user->status,
                'last_login_at' => $user->last_login_at,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
                'roles' => $user->roles->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'code' => $role->code,
                        'description' => $role->description,
                    ];
                }),
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取用户详情');
        }
    }

    /**
     * 创建用户
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['username', 'email', 'password']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'email' => 'email',
                'phone' => 'phone'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查用户名是否已存在
            if (User::where('username', $data['username'])->exists()) {
                return $this->error('用户名已存在');
            }

            // 检查邮箱是否已存在
            if (User::where('email', $data['email'])->exists()) {
                return $this->error('邮箱已存在');
            }

            // 检查手机号是否已存在
            if (!empty($data['phone']) && User::where('phone', $data['phone'])->exists()) {
                return $this->error('手机号已存在');
            }

            // 开始事务
            Db::beginTransaction();

            try {
                // 创建用户
                $userData = [
                    'username' => $data['username'],
                    'email' => $data['email'],
                    'phone' => $data['phone'] ?? null,
                    'real_name' => $data['real_name'] ?? null,
                    'user_type' => $data['user_type'] ?? User::TYPE_CUSTOMER,
                    'status' => $data['status'] ?? User::STATUS_ACTIVE,
                ];

                $user = User::create($userData);
                $user->setPassword($data['password']);
                $user->save();

                // 分配角色
                if (!empty($data['role_ids']) && is_array($data['role_ids'])) {
                    foreach ($data['role_ids'] as $roleId) {
                        UserRole::create([
                            'user_id' => $user->id,
                            'role_id' => $roleId,
                        ]);
                    }
                }

                Db::commit();

                return $this->success([
                    'id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'user_type' => $user->user_type,
                    'status' => $user->status,
                ], '用户创建成功');

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->handleException($e, '创建用户');
        }
    }

    /**
     * 更新用户
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $userId = (int)$id;
            
            if ($userId <= 0) {
                return $this->error('用户ID无效');
            }

            $user = User::find($userId);
            
            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'email' => 'email',
                'phone' => 'phone'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查用户名是否已存在（排除当前用户）
            if (!empty($data['username']) && 
                User::where('username', $data['username'])->where('id', '!=', $userId)->exists()) {
                return $this->error('用户名已存在');
            }

            // 检查邮箱是否已存在（排除当前用户）
            if (!empty($data['email']) && 
                User::where('email', $data['email'])->where('id', '!=', $userId)->exists()) {
                return $this->error('邮箱已存在');
            }

            // 检查手机号是否已存在（排除当前用户）
            if (!empty($data['phone']) && 
                User::where('phone', $data['phone'])->where('id', '!=', $userId)->exists()) {
                return $this->error('手机号已存在');
            }

            // 更新用户信息
            $updateData = [];
            $allowedFields = ['username', 'email', 'phone', 'real_name', 'user_type', 'status'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if ($updateData) {
                $user->update($updateData);
            }

            // 更新密码
            if (!empty($data['password'])) {
                $user->setPassword($data['password']);
                $user->save();
            }

            return $this->success([
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'user_type' => $user->user_type,
                'status' => $user->status,
            ], '用户更新成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新用户');
        }
    }

    /**
     * 删除用户
     *
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $userId = (int)$id;
            
            if ($userId <= 0) {
                return $this->error('用户ID无效');
            }

            $user = User::find($userId);
            
            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            // 不能删除自己
            if ($request->user && $request->user->id == $userId) {
                return $this->error('不能删除自己');
            }

            // 开始事务
            Db::beginTransaction();

            try {
                // 删除用户角色关联
                UserRole::where('user_id', $userId)->delete();
                
                // 删除用户
                $user->delete();

                Db::commit();

                return $this->success(null, '用户删除成功');

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->handleException($e, '删除用户');
        }
    }

    /**
     * 分配用户角色
     *
     * @param Request $request
     * @return Response
     */
    public function assignRoles(Request $request, $id)
    {
        try {
            $userId = (int)$id;

            if ($userId <= 0) {
                return $this->error('用户ID无效');
            }

            $user = User::find($userId);

            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['role_ids']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['role_ids'])) {
                return $this->error('角色ID必须是数组');
            }

            // 验证角色是否存在
            $validRoleIds = Role::whereIn('id', $data['role_ids'])->pluck('id')->toArray();
            $invalidRoleIds = array_diff($data['role_ids'], $validRoleIds);

            if (!empty($invalidRoleIds)) {
                return $this->error('角色ID无效: ' . implode(', ', $invalidRoleIds));
            }

            // 开始事务
            Db::beginTransaction();

            try {
                // 删除现有角色关联
                UserRole::where('user_id', $userId)->delete();

                // 添加新的角色关联
                foreach ($data['role_ids'] as $roleId) {
                    UserRole::create([
                        'user_id' => $userId,
                        'role_id' => $roleId,
                    ]);
                }

                Db::commit();

                return $this->success(null, '角色分配成功');

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->handleException($e, '分配用户角色');
        }
    }

    /**
     * 获取用户角色
     *
     * @param Request $request
     * @return Response
     */
    public function getRoles(Request $request, $id)
    {
        try {
            $userId = (int)$id;

            if ($userId <= 0) {
                return $this->error('用户ID无效');
            }

            $user = User::with(['roles'])->find($userId);

            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            $roles = $user->roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'code' => $role->code,
                    'description' => $role->description,
                    'status' => $role->status,
                ];
            });

            return $this->success($roles);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取用户角色');
        }
    }

    /**
     * 批量更新用户状态
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdateStatus(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['ids', 'status']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['ids'])) {
                return $this->error('用户ID必须是数组');
            }

            if (!in_array($data['status'], [User::STATUS_ACTIVE, User::STATUS_INACTIVE])) {
                return $this->error('状态值无效');
            }

            // 不能操作自己
            if ($request->user && in_array($request->user->id, $data['ids'])) {
                return $this->error('不能操作自己的状态');
            }

            $count = User::whereIn('id', $data['ids'])->update(['status' => $data['status']]);

            return $this->success([
                'updated_count' => $count
            ], '批量更新成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新用户状态');
        }
    }

    /**
     * 重置用户密码
     *
     * @param Request $request
     * @return Response
     */
    public function resetPassword(Request $request, $id)
    {
        try {
            $userId = (int)$id;

            if ($userId <= 0) {
                return $this->error('用户ID无效');
            }

            $user = User::find($userId);

            if (!$user) {
                return $this->error('用户不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['new_password']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证新密码长度
            if (strlen($data['new_password']) < 6) {
                return $this->error('新密码长度不能少于6位');
            }

            // 更新密码
            $user->setPassword($data['new_password']);
            $user->save();

            return $this->success(null, '密码重置成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '重置用户密码');
        }
    }

    /**
     * 获取用户统计信息
     *
     * @param Request $request
     * @return Response
     */
    public function getStats(Request $request)
    {
        try {
            $totalUsers = User::count();
            $activeUsers = User::where('status', User::STATUS_ACTIVE)->count();
            $inactiveUsers = User::where('status', User::STATUS_INACTIVE)->count();

            $userTypeStats = User::selectRaw('user_type, count(*) as count')
                ->groupBy('user_type')
                ->get()
                ->pluck('count', 'user_type')
                ->toArray();

            // 最近30天新增用户
            $recentUsers = User::where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-30 days')))
                ->count();

            return $this->success([
                'total_users' => $totalUsers,
                'active_users' => $activeUsers,
                'inactive_users' => $inactiveUsers,
                'user_type_stats' => $userTypeStats,
                'recent_users' => $recentUsers,
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取用户统计信息');
        }
    }
}
