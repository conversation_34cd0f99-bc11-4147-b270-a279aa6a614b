<?php

namespace app\model;

use support\Model;

/**
 * 床型类型使用统计模型
 */
class BedTypeStatistics extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'bed_type_statistics';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'bed_type_id',
        'usage_count',
        'room_type_count',
        'hotel_count',
        'last_used_at',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'bed_type_id' => 'integer',
        'usage_count' => 'integer',
        'room_type_count' => 'integer',
        'hotel_count' => 'integer',
        'last_used_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取关联的床型类型
     */
    public function bedType()
    {
        return $this->belongsTo(BedType::class);
    }

    /**
     * 作用域：按使用次数排序
     */
    public function scopeOrderByUsage($query, $direction = 'desc')
    {
        return $query->orderBy('usage_count', $direction);
    }

    /**
     * 作用域：热门床型（使用次数大于指定值）
     */
    public function scopePopular($query, $minUsage = 10)
    {
        return $query->where('usage_count', '>=', $minUsage);
    }
}
