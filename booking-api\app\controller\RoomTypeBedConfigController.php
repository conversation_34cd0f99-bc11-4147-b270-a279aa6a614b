<?php

namespace app\controller;

use app\service\RoomTypeBedConfigService;
use support\Request;
use support\Response;

/**
 * 房型床位配置管理控制器
 */
class RoomTypeBedConfigController extends BaseController
{
    /**
     * 房型床位配置服务
     *
     * @var RoomTypeBedConfigService
     */
    protected $bedConfigService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->bedConfigService = new RoomTypeBedConfigService();
    }

    /**
     * 获取房型床位配置
     *
     * @param Request $request
     * @param int $roomTypeId
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $result = $this->bedConfigService->getBedConfig($roomTypeId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型床位配置');
        }
    }

    /**
     * 创建或更新房型床位配置
     *
     * @param Request $request
     * @param int $roomTypeId
     * @return Response
     */
    public function store(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['bed_type']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'bed_type' => 'integer|min:1',
                'bed_count' => 'integer|min:1|max:5',
                'bed_size' => 'string|max:100',
                'extra_beds' => 'array',
                'bedding_material' => 'string|in:cotton,linen,silk,bamboo,microfiber',
                'pillow_type' => 'string|in:down,memory_foam,latex,fiber,buckwheat',
                'pillow_count' => 'integer|min:1|max:10',
                'duvet_type' => 'string|in:down,cotton,silk,wool,synthetic',
                'blanket_provided' => 'boolean',
                'bedding_change_frequency' => 'string|in:daily,on_request,every_2_days,weekly',
                'bed_notes' => 'string|max:500',
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证额外床位数据
            if (!empty($data['extra_beds'])) {
                foreach ($data['extra_beds'] as $index => $extraBed) {
                    $extraBedErrors = $this->validateFormat($extraBed, [
                        'bed_type' => 'integer|min:1',
                        'count' => 'integer|min:1|max:3',
                        'size' => 'string|max:100',
                        'fee' => 'numeric|min:0',
                    ]);
                    if ($extraBedErrors) {
                        return $this->error("额外床位[{$index}]数据格式错误", 400, $extraBedErrors);
                    }
                }
            }

            $result = $this->bedConfigService->saveBedConfig($roomTypeId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '保存房型床位配置');
        }
    }

    /**
     * 更新房型床位配置
     *
     * @param Request $request
     * @param int $roomTypeId
     * @return Response
     */
    public function update(Request $request, $id)
    {
        return $this->store($request, $id);
    }

    /**
     * 删除房型床位配置
     *
     * @param Request $request
     * @param int $roomTypeId
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $result = $this->bedConfigService->deleteBedConfig($roomTypeId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除房型床位配置');
        }
    }

    /**
     * 获取房型床位配置模板
     *
     * @param Request $request
     * @return Response
     */
    public function templates(Request $request)
    {
        try {
            $result = $this->bedConfigService->getBedConfigTemplates();
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取床位配置模板');
        }
    }

    /**
     * 验证床位配置
     *
     * @param Request $request
    * @param int $roomTypeId
     * @return Response
     */
    public function validateA(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $data = $this->getInput($request);
            $result = $this->bedConfigService->validateBedConfig($roomTypeId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '验证床位配置');
        }
    }
}
