<?php

namespace app\model;

use support\Model;

/**
 * 床型类型多语言翻译模型
 */
class BedTypeTranslation extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'bed_type_translations';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'bed_type_id',
        'locale',
        'name',
        'description',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'bed_type_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取关联的床型类型
     */
    public function bedType()
    {
        return $this->belongsTo(BedType::class);
    }

    /**
     * 作用域：按语言筛选
     */
    public function scopeByLocale($query, $locale)
    {
        return $query->where('locale', $locale);
    }

    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public static function getSupportedLocales()
    {
        return [
            'zh-CN' => '简体中文',
            'zh-TW' => '繁体中文',
            'en-US' => 'English',
            'ja-JP' => '日本語',
            'ko-KR' => '한국어',
            'th-TH' => 'ไทย',
            'vi-VN' => 'Tiếng Việt',
        ];
    }
}
