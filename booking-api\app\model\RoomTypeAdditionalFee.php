<?php

namespace app\model;

/**
 * 房型额外费用模型
 * 对应数据库表：room_type_additional_fees
 */
class RoomTypeAdditionalFee extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_type_additional_fees';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'room_type_id',
        'fee_name',
        'fee_description',
        'fee_type',
        'amount',
        'charge_type',
        'conditions',
        'is_taxable',
        'is_refundable',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'conditions' => 'array',
        'is_taxable' => 'boolean',
        'is_refundable' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * 关联房型
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type_id');
    }

    /**
     * 获取强制费用
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMandatory($query)
    {
        return $query->where('fee_type', 'mandatory');
    }

    /**
     * 获取可选费用
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOptional($query)
    {
        return $query->where('fee_type', 'optional');
    }

    /**
     * 获取条件性费用
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeConditional($query)
    {
        return $query->where('fee_type', 'conditional');
    }

    /**
     * 获取征税费用
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTaxable($query)
    {
        return $query->where('is_taxable', true);
    }

    /**
     * 获取可退费用
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRefundable($query)
    {
        return $query->where('is_refundable', true);
    }

    /**
     * 按排序排序
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取费用类型名称
     *
     * @return string
     */
    public function getFeeTypeNameAttribute()
    {
        $types = [
            'mandatory' => '强制费用',
            'optional' => '可选费用',
            'conditional' => '条件性费用',
        ];

        return $types[$this->fee_type] ?? $this->fee_type;
    }

    /**
     * 获取收费类型名称
     *
     * @return string
     */
    public function getChargeTypeNameAttribute()
    {
        $types = [
            'per_night' => '每晚',
            'per_stay' => '每次',
            'per_person' => '每人',
            'percentage' => '百分比',
        ];

        return $types[$this->charge_type] ?? $this->charge_type;
    }

    /**
     * 获取费用完整信息
     *
     * @return array
     */
    public function getFeeInfoAttribute()
    {
        return [
            'id' => $this->id,
            'fee_name' => $this->fee_name,
            'fee_description' => $this->fee_description,
            'fee_type' => $this->fee_type,
            'fee_type_name' => $this->fee_type_name,
            'amount' => $this->amount,
            'charge_type' => $this->charge_type,
            'charge_type_name' => $this->charge_type_name,
            'conditions' => $this->conditions,
            'is_taxable' => $this->is_taxable,
            'is_refundable' => $this->is_refundable,
        ];
    }

    /**
     * 验证费用
     *
     * @param array $data
     * @return array
     */
    public static function validateFee($data)
    {
        $rules = [
            'room_type_id' => 'required|integer|exists:room_types,id',
            'fee_name' => 'required|string|max:100',
            'fee_description' => 'nullable|string',
            'fee_type' => 'required|in:mandatory,optional,conditional',
            'amount' => 'required|numeric|min:0',
            'charge_type' => 'required|in:per_night,per_stay,per_person,percentage',
            'conditions' => 'nullable|array',
            'is_taxable' => 'boolean',
            'is_refundable' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];

        $messages = [
            'room_type_id.required' => '房型ID不能为空',
            'room_type_id.exists' => '房型不存在',
            'fee_name.required' => '费用名称不能为空',
            'fee_name.max' => '费用名称不能超过100个字符',
            'fee_type.required' => '费用类型不能为空',
            'fee_type.in' => '费用类型无效',
            'amount.required' => '金额不能为空',
            'amount.min' => '金额不能为负数',
            'charge_type.required' => '收费类型不能为空',
            'charge_type.in' => '收费类型无效',
            'conditions.array' => '收费条件必须是数组格式',
            'sort_order.min' => '排序不能为负数',
        ];

        return [
            'rules' => $rules,
            'messages' => $messages,
        ];
    }

    /**
     * 创建费用
     *
     * @param array $data
     * @return static
     */
    public static function createFee($data)
    {
        $validation = self::validateFee($data);
        
        // 这里应该使用验证器，暂时简化处理
        $fee = new self($data);
        $fee->save();
        
        return $fee;
    }

    /**
     * 更新费用
     *
     * @param array $data
     * @return bool
     */
    public function updateFee($data)
    {
        $validation = self::validateFee($data);
        
        // 这里应该使用验证器，暂时简化处理
        return $this->update($data);
    }

    /**
     * 获取房型的所有费用
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的强制费用
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getMandatoryFeesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('fee_type', 'mandatory')
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的可选费用
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getOptionalFeesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('fee_type', 'optional')
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的条件性费用
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getConditionalFeesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('fee_type', 'conditional')
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的征税费用
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getTaxableFeesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('is_taxable', true)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的可退费用
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRefundableFeesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('is_refundable', true)
            ->ordered()
            ->get();
    }

    /**
     * 计算费用金额
     *
     * @param int $nights 入住晚数
     * @param int $persons 人数
     * @param float $basePrice 基础价格
     * @return float
     */
    public function calculateAmount($nights = 1, $persons = 1, $basePrice = 0)
    {
        switch ($this->charge_type) {
            case 'per_night':
                return $this->amount * $nights;
            case 'per_stay':
                return $this->amount;
            case 'per_person':
                return $this->amount * $persons;
            case 'percentage':
                return $basePrice * ($this->amount / 100);
            default:
                return $this->amount;
        }
    }
}
