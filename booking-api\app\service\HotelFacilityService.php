<?php

namespace app\service;

use app\model\HotelFacility;
use support\Db;

/**
 * 酒店设施服务类
 * 处理酒店设施设备的业务逻辑
 */
class HotelFacilityService extends BaseService
{
    /**
     * 获取酒店设施列表
     */
    public function getHotelFacilities(int $hotelId): array
    {
        try {
            $facilities = HotelFacility::with(['facility'])
                ->where('hotel_id', $hotelId)
                ->where('is_available', 1)
                ->orderBy('sort_order', 'asc')
                ->orderBy('id', 'asc')
                ->get();

            return $facilities->toArray();
        } catch (\Exception $e) {
            $this->logError('获取酒店设施失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 更新酒店设施
     */
    public function updateHotelFacilities(int $hotelId, array $facilities): bool
    {
        try {
            Db::beginTransaction();

            // 先删除现有设施
            Db::table('hotel_facilities')
                ->where('hotel_id', $hotelId)
                ->delete();

            // 插入新设施
            foreach ($facilities as $facility) {
                $insertData = [
                    'hotel_id' => $hotelId,
                    'facility_name' => $facility['facility_name'],
                    'facility_id' => $facility['facility_id'] ?? null,
                    'chargeable' => $facility['chargeable'] ?? false,
                    'charge_unit' => $facility['charge_unit'] ?? null,
                    'charge_amount' => $facility['charge_amount'] ?? 0,
                    'location_info' => $facility['location_info'] ?? null,
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // 处理JSON字段
                $jsonFields = ['age_limit_info', 'open_periods', 'reservation_info', 'restaurant_infos'];
                foreach ($jsonFields as $field) {
                    if (isset($facility[$field])) {
                        $insertData[$field] = json_encode($facility[$field], JSON_UNESCAPED_UNICODE);
                    }
                }

                Db::table('hotel_facilities')->insert($insertData);
            }

            // 记录操作日志
            $this->logHotelOperation($hotelId, 'update', 'facilities', '更新酒店设施', [], $facilities);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('更新酒店设施失败', [
                'hotel_id' => $hotelId, 
                'facilities' => $facilities, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 添加单个设施
     */
    public function addFacility(int $hotelId, array $facilityData): int
    {
        try {
            $insertData = [
                'hotel_id' => $hotelId,
                'facility_name' => $facilityData['facility_name'],
                'facility_id' => $facilityData['facility_id'] ?? null,
                'chargeable' => $facilityData['chargeable'] ?? false,
                'charge_unit' => $facilityData['charge_unit'] ?? null,
                'charge_amount' => $facilityData['charge_amount'] ?? 0,
                'location_info' => $facilityData['location_info'] ?? null,
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 处理JSON字段
            $jsonFields = ['age_limit_info', 'open_periods', 'reservation_info', 'restaurant_infos'];
            foreach ($jsonFields as $field) {
                if (isset($facilityData[$field])) {
                    $insertData[$field] = json_encode($facilityData[$field], JSON_UNESCAPED_UNICODE);
                }
            }

            $facilityId = Db::table('hotel_facilities')->insertGetId($insertData);

            // 记录操作日志
            $this->logHotelOperation($hotelId, 'create', 'facilities', '添加酒店设施', [], $facilityData);

            return $facilityId;
        } catch (\Exception $e) {
            $this->logError('添加酒店设施失败', [
                'hotel_id' => $hotelId, 
                'facility_data' => $facilityData, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 更新单个设施
     */
    public function updateFacility(int $facilityId, array $facilityData): bool
    {
        try {
            $updateData = [
                'facility_name' => $facilityData['facility_name'],
                'facility_id' => $facilityData['facility_id'] ?? null,
                'chargeable' => $facilityData['chargeable'] ?? false,
                'charge_unit' => $facilityData['charge_unit'] ?? null,
                'charge_amount' => $facilityData['charge_amount'] ?? 0,
                'location_info' => $facilityData['location_info'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 处理JSON字段
            $jsonFields = ['age_limit_info', 'open_periods', 'reservation_info', 'restaurant_infos'];
            foreach ($jsonFields as $field) {
                if (isset($facilityData[$field])) {
                    $updateData[$field] = json_encode($facilityData[$field], JSON_UNESCAPED_UNICODE);
                }
            }

            $result = Db::table('hotel_facilities')
                ->where('id', $facilityId)
                ->update($updateData);

            if ($result) {
                // 获取酒店ID用于日志记录
                $hotelId = Db::table('hotel_facilities')
                    ->where('id', $facilityId)
                    ->value('hotel_id');

                $this->logHotelOperation($hotelId, 'update', 'facilities', '更新酒店设施', [], $facilityData);
            }

            return $result > 0;
        } catch (\Exception $e) {
            $this->logError('更新酒店设施失败', [
                'facility_id' => $facilityId, 
                'facility_data' => $facilityData, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 删除设施
     */
    public function deleteFacility(int $facilityId): bool
    {
        try {
            // 获取设施信息用于日志记录
            $facility = Db::table('hotel_facilities')
                ->where('id', $facilityId)
                ->first();

            if (!$facility) {
                return false;
            }

            $result = Db::table('hotel_facilities')
                ->where('id', $facilityId)
                ->update([
                    'status' => 'inactive',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            if ($result) {
                $this->logHotelOperation(
                    $facility->hotel_id, 
                    'delete', 
                    'facilities', 
                    '删除酒店设施', 
                    (array)$facility, 
                    []
                );
            }

            return $result > 0;
        } catch (\Exception $e) {
            $this->logError('删除酒店设施失败', [
                'facility_id' => $facilityId, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取设施分类列表
     */
    public function getFacilityCategories(): array
    {
        // 这里可以返回预定义的设施分类
        // 实际项目中可能从配置文件或数据库获取
        return [
            [
                'id' => 1,
                'name' => '基础设施',
                'facilities' => [
                    ['id' => 101, 'name' => 'WiFi'],
                    ['id' => 102, 'name' => '停车场'],
                    ['id' => 103, 'name' => '电梯'],
                    ['id' => 104, 'name' => '空调'],
                ]
            ],
            [
                'id' => 2,
                'name' => '餐饮设施',
                'facilities' => [
                    ['id' => 201, 'name' => '餐厅'],
                    ['id' => 202, 'name' => '酒吧'],
                    ['id' => 203, 'name' => '咖啡厅'],
                    ['id' => 204, 'name' => '客房送餐'],
                ]
            ],
            [
                'id' => 3,
                'name' => '娱乐设施',
                'facilities' => [
                    ['id' => 301, 'name' => '健身房'],
                    ['id' => 302, 'name' => '游泳池'],
                    ['id' => 303, 'name' => 'SPA'],
                    ['id' => 304, 'name' => 'KTV'],
                ]
            ],
            [
                'id' => 4,
                'name' => '商务设施',
                'facilities' => [
                    ['id' => 401, 'name' => '会议室'],
                    ['id' => 402, 'name' => '商务中心'],
                    ['id' => 403, 'name' => '传真/复印'],
                    ['id' => 404, 'name' => '打印服务'],
                ]
            ],
            [
                'id' => 5,
                'name' => '其他服务',
                'facilities' => [
                    ['id' => 501, 'name' => '洗衣服务'],
                    ['id' => 502, 'name' => '行李寄存'],
                    ['id' => 503, 'name' => '叫车服务'],
                    ['id' => 504, 'name' => '旅游咨询'],
                ]
            ]
        ];
    }

    /**
     * 批量更新设施状态
     */
    public function batchUpdateFacilityStatus(array $facilityIds, string $status): bool
    {
        try {
            $result = Db::table('hotel_facilities')
                ->whereIn('id', $facilityIds)
                ->update([
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            return $result > 0;
        } catch (\Exception $e) {
            $this->logError('批量更新设施状态失败', [
                'facility_ids' => $facilityIds, 
                'status' => $status, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 记录酒店操作日志
     */
    protected function logHotelOperation(
        int $hotelId, 
        string $operationType, 
        string $operationModule, 
        string $description, 
        array $oldData = [], 
        array $newData = []
    ): void {
        try {
            Db::table('hotel_operation_logs')->insert([
                'hotel_id' => $hotelId,
                'operation_type' => $operationType,
                'operation_module' => $operationModule,
                'operation_description' => $description,
                'old_data' => !empty($oldData) ? json_encode($oldData, JSON_UNESCAPED_UNICODE) : null,
                'new_data' => !empty($newData) ? json_encode($newData, JSON_UNESCAPED_UNICODE) : null,
                'operator_id' => $this->getCurrentUserId(),
                'operator_name' => $this->getCurrentUserName(),
                'operator_ip' => $this->getClientIp(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            error_log('记录酒店操作日志失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户ID
     */
    protected function getCurrentUserId(): ?int
    {
        return 1; // 临时实现
    }

    /**
     * 获取当前用户名
     */
    protected function getCurrentUserName(): ?string
    {
        return 'admin'; // 临时实现
    }

    /**
     * 获取客户端IP
     */
    protected function getClientIp(): string
    {
        $request = request();
        return $request->getRealIp();
    }
}
