<?php

namespace app\controller;

use support\Request;

class IndexController
{
    public function index(Request $request)
    {
        return json([
            'name' => '酒店预订系统API',
            'version' => '1.0.0',
            'description' => '提供酒店预订相关的RESTful API服务',
            'environment' => config('app.env', 'production'),
            'endpoints' => [
                'api_docs' => '/api/v1/docs/swagger',
                'health_check' => '/api/v1/health',
                'api_info' => '/api/v1/info'
            ],
            'timestamp' => time()
        ]);
    }

    public function view(Request $request)
    {
        return view('index/view', ['name' => 'webman']);
    }

    public function json(Request $request)
    {
        return json(['code' => 0, 'msg' => 'ok']);
    }

}
