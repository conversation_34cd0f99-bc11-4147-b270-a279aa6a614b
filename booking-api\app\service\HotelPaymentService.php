<?php

namespace app\service;

use support\Db;

/**
 * 酒店支付方式服务类
 */
class HotelPaymentService extends BaseService
{
    public function getHotelPayments(int $hotelId): array
    {
        try {

            $payments = Db::table('hotel_payment_methods')
                ->where('hotel_id', $hotelId)
                ->where('status', 'active')
                ->orderBy('sort_order')
                ->get();

            return $payments->map(function($payment) {
                return (array)$payment;
            })->toArray();
        } catch (\Exception $e) {
            $this->logError('获取酒店支付方式失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function updateHotelPayments(int $hotelId, array $payments): bool
    {
        try {
            Db::beginTransaction();

            // 先删除现有支付方式
            Db::table('hotel_payment_methods')->where('hotel_id', $hotelId)->delete();

            // 插入新支付方式
            foreach ($payments as $payment) {
                $insertData = [
                    'hotel_id' => $hotelId,
                    'payment_id' => $payment['payment_id'],
                    'payment_name' => $payment['payment_name'],
                    'bookable' => $payment['bookable'] ?? true,
                    'cvc_required' => $payment['cvc_required'] ?? false,
                    'payable' => $payment['payable'] ?? true,
                    'sort_order' => $payment['sort_order'] ?? 0,
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                Db::table('hotel_payment_methods')->insert($insertData);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('更新酒店支付方式失败', [
                'hotel_id' => $hotelId, 
                'payments' => $payments, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
