<?php

namespace app\service;

use support\Db;

/**
 * 酒店图片服务类
 */
class HotelImageService extends BaseService
{
    public function getHotelImages(int $hotelId): array
    {
        try {


            $images = Db::table('hotel_images')
                ->where('hotel_id', $hotelId)
                ->where('status', 'active')
                ->orderBy('sort_order')
                ->get();

            return $images->map(function($image) {
                return (array)$image;
            })->toArray();
        } catch (\Exception $e) {
            $this->logError('获取酒店图片失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function updateHotelImages(int $hotelId, array $images): bool
    {
        try {
            Db::beginTransaction();

            // 先删除现有图片
            Db::table('hotel_images')->where('hotel_id', $hotelId)->delete();

            // 插入新图片
            foreach ($images as $image) {
                $insertData = [
                    'hotel_id' => $hotelId,
                    'image_url' => $image['image_url'],
                    'image_category_id' => $image['image_category_id'] ?? null,
                    'image_category' => $image['image_category'] ?? null,
                    'image_title' => $image['image_title'] ?? null,
                    'image_description' => $image['image_description'] ?? null,
                    'sort_order' => $image['sort_order'] ?? 0,
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                Db::table('hotel_images')->insert($insertData);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('更新酒店图片失败', [
                'hotel_id' => $hotelId, 
                'images' => $images, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
