<?php

namespace app\model;

/**
 * 酒店模型
 * 对应数据库表：hotels
 */
class Hotel extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'hotels';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'group_id',
        'brand_id',
        'code',
        'hotel_code',
        'name',
        'name_en',
        'description',
        'country',
        'province',
        'city',
        'district',
        'address',
        'postal_code',
        'latitude',
        'longitude',
        'star_rating',
        'opening_date',
        'renovation_date',
        'total_rooms',
        'total_floors',
        'phone',
        'fax',
        'email',
        'website',
        'check_in_time',
        'check_out_time',
        'cancellation_policy',
        'child_policy',
        'pet_policy',
        'facilities',
        'services',
        'images',
        'videos',
        'virtual_tour_url',
        'status',
        'is_featured',
        'is_verified',
        'verified_at',
        'verified_by',
        'view_count',
        'booking_count',
        'review_count',
        'average_rating',
        // 基础信息扩展
        'business_license',
        'tax_number',
        'legal_representative',
        'contact_person',
        'contact_phone',
        'contact_email',
        'emergency_phone',
        'business_hours',
        'timezone',
        'currency',
        'languages',
        'payment_methods',
        // 多语言支持字段
        'language_code',
        'active',
        'hotel_names',
        'hotel_briefs',
        'hotel_descriptions',
        'important_informations',
        // 集团品牌信息
        'when_built',
        'last_renovation',
        'hotel_category',
        // 位置和联系信息
        'positions',
        'addresses',
        'phones',
        'emails',
        // 扩展信息
        'total_room_quantity',
        'star_licence',
        'hotel_building_area',
        'receive_foreign_guest',
        // 政策信息
        'booking_policies',
        'guest_policies',
        'safety_policies',
        'environmental_policies',
        // 设施服务
        'accessibility_features',
        'business_facilities',
        'recreation_facilities',
        'dining_facilities',
        'meeting_facilities',
        'spa_wellness_facilities',
        'family_facilities',
        'pet_facilities',
        'special_services',
        'concierge_services',
        'transportation_services',
        'laundry_services',
        'room_services',
        // 认证奖项
        'awards_certifications',
        'quality_certifications',
        'environmental_certifications',
        'safety_certifications',
        // 位置信息
        'nearby_attractions',
        'transportation_info',
        'parking_info',
        'wifi_info',
        'created_by',
        'updated_by'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'group_id' => 'integer',
        'brand_id' => 'integer',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'star_rating' => 'integer',
        'opening_date' => 'date',
        'renovation_date' => 'date',
        'total_rooms' => 'integer',
        'total_floors' => 'integer',
        'check_in_time' => 'datetime:H:i:s',
        'check_out_time' => 'datetime:H:i:s',
        'facilities' => 'array',
        'services' => 'array',
        'images' => 'array',
        'videos' => 'array',
        'is_featured' => 'boolean',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'verified_by' => 'integer',
        'view_count' => 'integer',
        'booking_count' => 'integer',
        'review_count' => 'integer',
        'average_rating' => 'decimal:2',
        'active' => 'boolean',
        'total_room_quantity' => 'integer',
        'star_licence' => 'boolean',
        'hotel_building_area' => 'integer',
        'receive_foreign_guest' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        // JSON字段转换
        'business_hours' => 'array',
        'languages' => 'array',
        'payment_methods' => 'array',
        'hotel_names' => 'array',
        'hotel_briefs' => 'array',
        'hotel_descriptions' => 'array',
        'important_informations' => 'array',
        'hotel_category' => 'array',
        'positions' => 'array',
        'addresses' => 'array',
        'phones' => 'array',
        'emails' => 'array',
        'booking_policies' => 'array',
        'guest_policies' => 'array',
        'safety_policies' => 'array',
        'environmental_policies' => 'array',
        'accessibility_features' => 'array',
        'business_facilities' => 'array',
        'recreation_facilities' => 'array',
        'dining_facilities' => 'array',
        'meeting_facilities' => 'array',
        'spa_wellness_facilities' => 'array',
        'family_facilities' => 'array',
        'pet_facilities' => 'array',
        'special_services' => 'array',
        'concierge_services' => 'array',
        'transportation_services' => 'array',
        'laundry_services' => 'array',
        'room_services' => 'array',
        'awards_certifications' => 'array',
        'quality_certifications' => 'array',
        'environmental_certifications' => 'array',
        'safety_certifications' => 'array',
        'nearby_attractions' => 'array',
        'transportation_info' => 'array',
        'parking_info' => 'array',
        'wifi_info' => 'array',
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_MAINTENANCE = 'maintenance';

    /**
     * 获取所属集团
     */
    public function group()
    {
        return $this->belongsTo(HotelGroup::class, 'group_id')->select([
                "id",
                "name",
                "code",
                "name_en",
                "description",
                "website",
                "address",
                "contact_person",
                "contact_phone",
                "contact_email",
                "established_year",
                "logo_url",
                "status",]
        );
    }

    /**
     * 获取所属品牌
     */
    public function brand()
    {
        return $this->belongsTo(HotelBrand::class, 'brand_id')->select([
                "id",
                "name",
                "code",
                "name_en",
                "short_name",
                "description",
                "description_en",
                "logo_url",
                "status",]
        );
    }

    /**
     * 获取房型
     */
    public function roomTypes()
    {
        return $this->hasMany(RoomType::class);
    }

    /**
     * 获取房间
     * 注意：rooms表暂未创建，此方法已注释
     */
    // public function rooms()
    // {
    //     return $this->hasMany(Room::class);
    // }

    /**
     * 获取库存
     */
    public function inventory()
    {
        return $this->hasMany(RoomInventory::class);
    }

    /**
     * 获取价格计划
     */
    public function ratePlans()
    {
        return $this->hasMany(RatePlan::class);
    }

    /**
     * 获取订单
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * 获取活跃的房型
     */
    public function activeRoomTypes()
    {
        return $this->roomTypes()->active();
    }

    /**
     * 获取可用的房间
     * 注意：rooms表暂未创建，此方法已注释
     */
    // public function availableRooms()
    // {
    //     return $this->rooms()->where('status', 'available');
    // }

    /**
     * 作用域：按城市筛选
     */
    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    /**
     * 作用域：按星级筛选
     */
    public function scopeByStarRating($query, $starRating)
    {
        return $query->where('star_rating', $starRating);
    }

    /**
     * 作用域：推荐酒店
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * 作用域：按地理位置筛选
     */
    public function scopeNearby($query, $latitude, $longitude, $radius = 10)
    {
        return $query->whereRaw(
            "ST_Distance_Sphere(POINT(longitude, latitude), POINT(?, ?)) <= ?",
            [$longitude, $latitude, $radius * 1000]
        );
    }

    /**
     * 获取完整地址
     */
    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->country,
            $this->province,
            $this->city,
            $this->district,
            $this->address
        ]);
        
        return implode(' ', $parts);
    }

    /**
     * 获取星级显示
     */
    public function getStarDisplayAttribute()
    {
        return $this->star_rating ? str_repeat('★', $this->star_rating) : '未评级';
    }

    /**
     * 获取主图片
     */
    public function getMainImageAttribute()
    {
        $images = $this->images ?? [];
        return !empty($images) ? $images[0] : null;
    }

    /**
     * 检查是否有指定设施
     */
    public function hasFacility($facility)
    {
        return in_array($facility, $this->facilities ?? []);
    }

    /**
     * 检查是否有指定服务
     */
    public function hasService($service)
    {
        return in_array($service, $this->services ?? []);
    }

    /**
     * 获取距离（需要传入目标坐标）
     */
    public function getDistanceTo($latitude, $longitude)
    {
        if (!$this->latitude || !$this->longitude) {
            return null;
        }

        $earthRadius = 6371; // 地球半径（公里）

        $latDelta = deg2rad($latitude - $this->latitude);
        $lonDelta = deg2rad($longitude - $this->longitude);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($this->latitude)) * cos(deg2rad($latitude)) *
             sin($lonDelta / 2) * sin($lonDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * 获取营业时间信息
     */
    public function getBusinessHoursForDay($day = null)
    {
        $day = $day ?: date('w'); // 0=周日, 1=周一, ..., 6=周六
        $businessHours = $this->business_hours ?? [];

        $dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        $dayName = $dayNames[$day] ?? 'monday';

        return $businessHours[$dayName] ?? ['open' => '00:00', 'close' => '23:59', 'is_open' => true];
    }

    /**
     * 检查当前是否营业
     */
    public function isCurrentlyOpen()
    {
        $now = new \DateTime();
        $currentTime = $now->format('H:i');
        $currentDay = $now->format('w');

        $todayHours = $this->getBusinessHoursForDay($currentDay);

        if (!$todayHours['is_open']) {
            return false;
        }

        return $currentTime >= $todayHours['open'] && $currentTime <= $todayHours['close'];
    }

    /**
     * 获取支持的语言列表
     */
    public function getSupportedLanguages()
    {
        return $this->languages ?? ['zh-CN'];
    }

    /**
     * 检查是否支持指定语言
     */
    public function supportsLanguage($language)
    {
        return in_array($language, $this->getSupportedLanguages());
    }

    /**
     * 获取支付方式列表
     */
    public function getPaymentMethods()
    {
        return $this->payment_methods ?? ['cash', 'credit_card'];
    }

    /**
     * 检查是否支持指定支付方式
     */
    public function supportsPaymentMethod($method)
    {
        return in_array($method, $this->getPaymentMethods());
    }

    /**
     * 获取无障碍设施
     */
    public function getAccessibilityFeatures()
    {
        return $this->accessibility_features ?? [];
    }

    /**
     * 检查是否有指定无障碍设施
     */
    public function hasAccessibilityFeature($feature)
    {
        return in_array($feature, $this->getAccessibilityFeatures());
    }

    /**
     * 获取停车信息
     */
    public function getParkingInfo()
    {
        return $this->parking_info ?? [
            'available' => false,
            'type' => 'none',
            'capacity' => 0,
            'fee' => 0,
            'currency' => 'CNY'
        ];
    }

    /**
     * 获取WiFi信息
     */
    public function getWifiInfo()
    {
        return $this->wifi_info ?? [
            'available' => true,
            'free' => true,
            'coverage' => 'all_areas',
            'speed' => 'standard'
        ];
    }

    /**
     * 获取联系信息
     */
    public function getContactInfo()
    {
        return [
            'phone' => $this->phone,
            'fax' => $this->fax,
            'email' => $this->email,
            'website' => $this->website,
            'contact_person' => $this->contact_person,
            'contact_phone' => $this->contact_phone,
            'contact_email' => $this->contact_email,
            'emergency_phone' => $this->emergency_phone
        ];
    }

    /**
     * 获取位置亮点
     */
    public function getLocationHighlights()
    {
        return $this->location_highlights ?? [];
    }

    /**
     * 获取附近景点
     */
    public function getNearbyAttractions()
    {
        return $this->nearby_attractions ?? [];
    }

    /**
     * 获取交通信息
     */
    public function getTransportationInfo()
    {
        return $this->transportation_info ?? [];
    }

    /**
     * 获取可持续发展信息
     */
    public function getSustainabilityInfo()
    {
        return $this->sustainability_info ?? [];
    }

    /**
     * 获取健康安全措施
     */
    public function getHealthSafetyMeasures()
    {
        return $this->health_safety_measures ?? [];
    }

    /**
     * 获取完整的酒店信息（用于API输出）
     */
    public function getFullHotelInfo()
    {
        return [
            'basic_info' => [
                'id' => $this->id,
                'code' => $this->code,
                'name' => $this->name,
                'name_en' => $this->name_en,
                'description' => $this->description,
                'star_rating' => $this->star_rating,
                'status' => $this->status,
                'is_featured' => $this->is_featured
            ],
            'location' => [
                'country' => $this->country,
                'province' => $this->province,
                'city' => $this->city,
                'district' => $this->district,
                'address' => $this->address,
                'postal_code' => $this->postal_code,
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
                'full_address' => $this->getFullAddressAttribute()
            ],
            'contact' => $this->getContactInfo(),
            'facilities' => [
                'general' => $this->facilities ?? [],
                'business' => $this->business_facilities ?? [],
                'recreation' => $this->recreation_facilities ?? [],
                'dining' => $this->dining_facilities ?? [],
                'meeting' => $this->meeting_facilities ?? [],
                'spa_wellness' => $this->spa_wellness_facilities ?? [],
                'family' => $this->family_facilities ?? [],
                'pet' => $this->pet_facilities ?? []
            ],
            'services' => [
                'general' => $this->services ?? [],
                'special' => $this->special_services ?? [],
                'concierge' => $this->concierge_services ?? []
            ],
            'policies' => [
                'booking' => $this->booking_policies ?? [],
                'guest' => $this->guest_policies ?? [],
                'cancellation' => $this->cancellation_policy,
                'child' => $this->child_policy,
                'pet' => $this->pet_policy,
                'safety' => $this->safety_policies ?? [],
                'environmental' => $this->environmental_policies ?? []
            ],
            'accessibility' => $this->getAccessibilityFeatures(),
            'parking' => $this->getParkingInfo(),
            'wifi' => $this->getWifiInfo(),
            'business_hours' => $this->business_hours ?? [],
            'payment_methods' => $this->getPaymentMethods(),
            'languages' => $this->getSupportedLanguages(),
            'images' => $this->images ?? [],
            'location_info' => [
                'highlights' => $this->getLocationHighlights(),
                'nearby_attractions' => $this->getNearbyAttractions(),
                'transportation' => $this->getTransportationInfo()
            ],
            'sustainability' => $this->getSustainabilityInfo(),
            'health_safety' => $this->getHealthSafetyMeasures()
        ];
    }
}
