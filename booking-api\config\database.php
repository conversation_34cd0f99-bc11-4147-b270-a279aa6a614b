<?php
return  [
    'default' => env('DATABASE.DRIVER', 'mysql'),
    'connections' => [
        'mysql' => [
            'driver'      => 'mysql',
            'host'        => env('DATABASE.HOSTNAME', '127.0.0.1'),
            'port'        => env('DATABASE.HOSTPORT', '3306'),
            'database'    => env('DATABASE.DATABASE', 'your_database'),
            'username'    => env('DATABASE.USERNAME', 'your_username'),
            'password'    => env('DATABASE.PASSWORD', 'your_password'),
            'charset'     => 'utf8mb4',
            'collation'   => 'utf8mb4_general_ci',
            'prefix'      => env('DATABASE.PREFIX', ''),
            'strict'      => true,
            'engine'      => null,
            'options'   => [
                PDO::ATTR_EMULATE_PREPARES => false, // Must be false for Swoole and Swow drivers.
            ],
            'pool' => [
                'max_connections' => 5,
                'min_connections' => 1,
                'wait_timeout' => 3,
                'idle_timeout' => 60,
                'heartbeat_interval' => 50,
            ],
        ],
    ],
];