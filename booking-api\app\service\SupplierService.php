<?php

namespace app\service;

use app\model\Supplier;
use app\model\SupplierHotelMapping;
use app\model\SupplierBooking;

/**
 * 供应商管理服务
 */
class SupplierService extends BaseService
{
    /**
     * 获取供应商列表
     *
     * @param array $params
     * @return array
     */
    public function getSupplierList(array $params = [])
    {
        try {
            $query = Supplier::query();

            // 类型筛选
            if (!empty($params['type'])) {
                $query->where('type', $params['type']);
            }

            // 状态筛选
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 同步状态筛选
            if (isset($params['sync_enabled'])) {
                $query->where('sync_enabled', (bool)$params['sync_enabled']);
            }

            // 认证状态筛选
            if (isset($params['is_verified'])) {
                $query->where('is_verified', (bool)$params['is_verified']);
            }

            // 关键词搜索
            if (!empty($params['keyword'])) {
                $keyword = $params['keyword'];
                $query->where(function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('code', 'like', "%{$keyword}%")
                      ->orWhere('contact_person', 'like', "%{$keyword}%");
                });
            }

            // 排序
            $sortBy = $params['sort_by'] ?? 'quality_score';
            $sortOrder = $params['sort_order'] ?? 'desc';

            // 验证排序字段是否存在
            $allowedSortFields = [
                'id', 'name', 'code', 'supplier_type', 'quality_score',
                'hotel_count', 'booking_count', 'total_revenue',
                'cooperation_start_date', 'created_at', 'updated_at'
            ];

            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                // 默认排序
                $query->orderBy('quality_score', 'desc')->orderBy('id', 'desc');
            }

            // 分页
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $result = $this->paginate($query, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('获取供应商列表失败', ['error' => $e->getMessage(), 'params' => $params]);
            return $this->error('获取供应商列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取供应商详情
     *
     * @param int $supplierId
     * @return array
     */
    public function getSupplierDetail(int $supplierId)
    {
        try {
            $supplier = Supplier::with(['hotelMappings.hotel'])
                ->find($supplierId);

            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            return $this->success($supplier);

        } catch (\Exception $e) {
            $this->logError('获取供应商详情失败', ['supplier_id' => $supplierId, 'error' => $e->getMessage()]);
            return $this->error('获取供应商详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建供应商
     *
     * @param array $data
     * @return array
     */
    public function createSupplier(array $data)
    {
        try {
            $this->validateRequired($data, ['code', 'name', 'type']);

            return $this->transaction(function () use ($data) {
                // 检查编码是否重复
                if (Supplier::where('code', $data['code'])->exists()) {
                    throw new \Exception('供应商编码已存在');
                }

                // 验证供应商类型
                $validTypes = [
                    Supplier::TYPE_HOTEL_DIRECT,
                    Supplier::TYPE_OTA,
                    Supplier::TYPE_WHOLESALER,
                    Supplier::TYPE_BEDBANK
                ];

                if (!in_array($data['type'], $validTypes)) {
                    throw new \Exception('无效的供应商类型');
                }

                // 设置默认值
                $data['sync_enabled'] = $data['sync_enabled'] ?? true;
                $data['settlement_period'] = $data['settlement_period'] ?? 30;
                $data['currency'] = $data['currency'] ?? 'CNY';
                $data['status'] = $data['status'] ?? 'active';
                $data['is_verified'] = $data['is_verified'] ?? false;
                $data['hotel_count'] = $data['hotel_count'] ?? 0;
                $data['room_count'] = $data['room_count'] ?? 0;
                $data['booking_count'] = $data['booking_count'] ?? 0;
                $data['total_revenue'] = $data['total_revenue'] ?? 0.00;

                $supplier = Supplier::create($this->filterEmpty($data));

                $this->logInfo('创建供应商成功', [
                    'supplier_id' => $supplier->id,
                    'supplier_name' => $supplier->name
                ]);

                return $this->success($supplier, '创建供应商成功');
            });

        } catch (\Exception $e) {
            $this->logError('创建供应商失败', ['error' => $e->getMessage(), 'data' => $data]);
            return $this->error('创建供应商失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新供应商信息
     *
     * @param int $supplierId
     * @param array $data
     * @return array
     */
    public function updateSupplier(int $supplierId, array $data)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            return $this->transaction(function () use ($supplier, $data) {
                // 如果更新编码，检查是否重复
                if (!empty($data['code']) && $data['code'] !== $supplier->code) {
                    if (Supplier::where('code', $data['code'])->where('id', '!=', $supplier->id)->exists()) {
                        throw new \Exception('供应商编码已存在');
                    }
                }

                // 验证供应商类型
                if (!empty($data['type'])) {
                    $validTypes = [
                        Supplier::TYPE_HOTEL_DIRECT,
                        Supplier::TYPE_OTA,
                        Supplier::TYPE_WHOLESALER,
                        Supplier::TYPE_BEDBANK
                    ];

                    if (!in_array($data['type'], $validTypes)) {
                        throw new \Exception('无效的供应商类型');
                    }
                }

                $supplier->update($this->filterEmpty($data));

                $this->logInfo('更新供应商成功', [
                    'supplier_id' => $supplier->id,
                    'supplier_name' => $supplier->name
                ]);

                return $this->success($supplier, '更新供应商成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新供应商失败', [
                'supplier_id' => $supplierId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return $this->error('更新供应商失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除供应商
     *
     * @param int $supplierId
     * @return array
     */
    public function deleteSupplier(int $supplierId)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            // 检查是否有关联的订单
            if ($supplier->supplierBookings()->exists()) {
                return $this->error('供应商存在关联订单，无法删除');
            }

            return $this->transaction(function () use ($supplier) {
                $supplierName = $supplier->name;

                // 删除酒店映射
                $supplier->hotelMappings()->delete();

                // 删除供应商
                $supplier->delete();

                $this->logInfo('删除供应商成功', [
                    'supplier_id' => $supplier->id,
                    'supplier_name' => $supplierName
                ]);

                return $this->success(null, '删除供应商成功');
            });

        } catch (\Exception $e) {
            $this->logError('删除供应商失败', ['supplier_id' => $supplierId, 'error' => $e->getMessage()]);
            return $this->error('删除供应商失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试供应商API连接
     *
     * @param int $supplierId
     * @return array
     */
    public function testSupplierConnection(int $supplierId)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            $result = $supplier->testApiConnection();

            $this->logInfo('测试供应商连接', [
                'supplier_id' => $supplierId,
                'result' => $result
            ]);

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('测试供应商连接失败', [
                'supplier_id' => $supplierId,
                'error' => $e->getMessage()
            ]);
            return $this->error('测试供应商连接失败: ' . $e->getMessage());
        }
    }

    /**
     * 启用/禁用供应商
     *
     * @param int $supplierId
     * @param bool $isActive
     * @return array
     */
    public function toggleSupplierStatus(int $supplierId, bool $isActive)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            if ($isActive) {
                $supplier->enable();
                $message = '启用供应商成功';
            } else {
                $supplier->disable();
                $message = '禁用供应商成功';
            }

            $this->logInfo($message, [
                'supplier_id' => $supplierId,
                'is_active' => $isActive
            ]);

            return $this->success($supplier, $message);

        } catch (\Exception $e) {
            $this->logError('切换供应商状态失败', [
                'supplier_id' => $supplierId,
                'is_active' => $isActive,
                'error' => $e->getMessage()
            ]);
            return $this->error('切换供应商状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取供应商酒店映射列表
     *
     * @param int $supplierId
     * @param array $params
     * @return array
     */
    public function getSupplierHotelMappings(int $supplierId, array $params = [])
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            $query = $supplier->hotelMappings()->with(['hotel']);

            // 状态筛选
            if (isset($params['is_active'])) {
                $query->where('is_active', (bool)$params['is_active']);
            }

            // 同步状态筛选
            if (!empty($params['sync_status'])) {
                $query->where('sync_status', $params['sync_status']);
            }

            // 酒店筛选
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            // 排序
            $query->orderBy('created_at', 'desc');

            // 分页
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $result = $this->paginate($query, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('获取供应商酒店映射失败', [
                'supplier_id' => $supplierId,
                'error' => $e->getMessage(),
                'params' => $params
            ]);
            return $this->error('获取供应商酒店映射失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证供应商
     */
    public function verifySupplier($supplierId, $verificationData)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            return $this->transaction(function () use ($supplier, $verificationData) {
                $supplier->update([
                    'is_verified' => true,
                    'verification_date' => date('Y-m-d'),
                    'verification_notes' => $verificationData['notes'] ?? '',
                    'status' => Supplier::STATUS_ACTIVE_STR,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 重新计算风险等级
                if (method_exists($supplier, 'calculateRiskLevel')) {
                    $supplier->calculateRiskLevel();
                }

                return $this->success($supplier->fresh(), '供应商验证成功');
            });

        } catch (\Exception $e) {
            $this->logError('验证供应商失败', [
                'supplier_id' => $supplierId,
                'verification_data' => $verificationData,
                'error' => $e->getMessage()
            ]);

            return $this->error('验证供应商失败：' . $e->getMessage());
        }
    }

    /**
     * 暂停供应商
     */
    public function suspendSupplier($supplierId, $reason)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            return $this->transaction(function () use ($supplier, $reason) {
                $supplier->update([
                    'status' => Supplier::STATUS_SUSPENDED,
                    'is_active' => false,
                    'notes' => ($supplier->notes ?? '') . "\n暂停原因：{$reason}",
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                return $this->success($supplier->fresh(), '供应商已暂停');
            });

        } catch (\Exception $e) {
            $this->logError('暂停供应商失败', [
                'supplier_id' => $supplierId,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);

            return $this->error('暂停供应商失败：' . $e->getMessage());
        }
    }

    /**
     * 恢复供应商
     */
    public function resumeSupplier($supplierId)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            return $this->transaction(function () use ($supplier) {
                $supplier->update([
                    'status' => Supplier::STATUS_ACTIVE_STR,
                    'is_active' => true,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 重新计算风险等级
                if (method_exists($supplier, 'calculateRiskLevel')) {
                    $supplier->calculateRiskLevel();
                }

                return $this->success($supplier->fresh(), '供应商已恢复');
            });

        } catch (\Exception $e) {
            $this->logError('恢复供应商失败', [
                'supplier_id' => $supplierId,
                'error' => $e->getMessage()
            ]);

            return $this->error('恢复供应商失败：' . $e->getMessage());
        }
    }

    /**
     * 获取供应商统计
     */
    public function getSupplierStats($supplierId = null)
    {
        try {
            $query = Supplier::query();

            if ($supplierId) {
                $query->where('id', $supplierId);
            }

            $suppliers = $query->get();

            $stats = [
                'total_suppliers' => $suppliers->count(),
                'active_suppliers' => $suppliers->where('is_active', true)->count(),
                'verified_suppliers' => $suppliers->where('is_verified', true)->count(),
                'pending_suppliers' => $suppliers->where('status', Supplier::STATUS_PENDING)->count(),
                'suspended_suppliers' => $suppliers->where('status', Supplier::STATUS_SUSPENDED)->count(),
                'type_distribution' => [],
                'risk_distribution' => [],
                'city_distribution' => [],
                'avg_scores' => [
                    'quality' => round($suppliers->avg('quality_score') ?? 0, 2),
                    'service' => round($suppliers->avg('service_score') ?? 0, 2),
                    'cooperation' => round($suppliers->avg('cooperation_score') ?? 0, 2)
                ],
                'total_bookings' => $suppliers->sum('total_bookings') ?? 0,
                'total_revenue' => $suppliers->sum('total_revenue') ?? 0
            ];

            // 按类型分布
            $typeStats = $suppliers->groupBy('type');
            foreach ($typeStats as $type => $typeSuppliers) {
                $stats['type_distribution'][$type] = $typeSuppliers->count();
            }

            // 按风险等级分布
            $riskStats = $suppliers->groupBy('risk_level');
            foreach ($riskStats as $risk => $riskSuppliers) {
                if ($risk) {
                    $stats['risk_distribution'][$risk] = $riskSuppliers->count();
                }
            }

            // 按城市分布
            $cityStats = $suppliers->groupBy('city');
            foreach ($cityStats as $city => $citySuppliers) {
                if ($city) {
                    $stats['city_distribution'][$city] = $citySuppliers->count();
                }
            }

            return $this->success($stats);

        } catch (\Exception $e) {
            $this->logError('获取供应商统计失败', [
                'supplier_id' => $supplierId,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取供应商统计失败：' . $e->getMessage());
        }
    }

    /**
     * 更新供应商评分
     */
    public function updateSupplierScore($supplierId, $scoreData)
    {
        try {
            $supplier = Supplier::find($supplierId);
            if (!$supplier) {
                return $this->error('供应商不存在', 404);
            }

            return $this->transaction(function () use ($supplier, $scoreData) {
                $updateData = [];

                if (isset($scoreData['quality_score'])) {
                    $updateData['quality_score'] = max(0, min(5, $scoreData['quality_score']));
                }

                if (isset($scoreData['service_score'])) {
                    $updateData['service_score'] = max(0, min(5, $scoreData['service_score']));
                }

                if (isset($scoreData['cooperation_score'])) {
                    $updateData['cooperation_score'] = max(0, min(5, $scoreData['cooperation_score']));
                }

                if (!empty($updateData)) {
                    $updateData['updated_at'] = date('Y-m-d H:i:s');
                    $supplier->update($updateData);

                    // 重新计算风险等级
                    if (method_exists($supplier, 'calculateRiskLevel')) {
                        $supplier->calculateRiskLevel();
                    }
                }

                return $this->success($supplier->fresh(), '供应商评分更新成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新供应商评分失败', [
                'supplier_id' => $supplierId,
                'score_data' => $scoreData,
                'error' => $e->getMessage()
            ]);

            return $this->error('更新供应商评分失败：' . $e->getMessage());
        }
    }
}
