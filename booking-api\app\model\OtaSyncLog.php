<?php

namespace app\model;

/**
 * OTA同步日志模型
 * 对应数据库表：ota_sync_logs
 */
class OtaSyncLog extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'ota_sync_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'channel_code',
        'sync_type',
        'status',
        'message',
        'sync_data',
        'request_data',
        'response_data',
        'error_details',
        'records_total',
        'records_success',
        'records_failed',
        'execution_time',
        'memory_usage',
        'started_at',
        'completed_at',
        'created_by',
        'created_at',
        'updated_at'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'sync_data' => 'array',
        'request_data' => 'array',
        'response_data' => 'array',
        'error_details' => 'array',
        'records_total' => 'integer',
        'records_success' => 'integer',
        'records_failed' => 'integer',
        'execution_time' => 'float',
        'memory_usage' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 同步状态常量
     */
    const STATUS_STARTED = 'started';
    const STATUS_RUNNING = 'running';
    const STATUS_COMPLETED = 'completed';
    const STATUS_PARTIAL = 'partial';
    const STATUS_FAILED = 'failed';
    const STATUS_ERROR = 'error';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * 同步类型常量
     */
    const TYPE_HOTEL_INFO = 'hotel_info';
    const TYPE_ROOM_TYPES = 'room_types';
    const TYPE_RATE_PLANS = 'rate_plans';
    const TYPE_INVENTORY = 'inventory';
    const TYPE_RATES = 'rates';
    const TYPE_AVAILABILITY = 'availability';
    const TYPE_RESTRICTIONS = 'restrictions';
    const TYPE_BOOKINGS = 'bookings';

    /**
     * 获取关联的酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取关联的OTA渠道
     */
    public function otaChannel()
    {
        return $this->belongsTo(OtaChannel::class, 'channel_code', 'channel_code')
            ->where('hotel_id', $this->hotel_id);
    }

    /**
     * 获取创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：按渠道筛选
     */
    public function scopeByChannel($query, $channelCode)
    {
        return $query->where('channel_code', $channelCode);
    }

    /**
     * 作用域：按同步类型筛选
     */
    public function scopeBySyncType($query, $syncType)
    {
        return $query->where('sync_type', $syncType);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('started_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：最近的日志
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('started_at', '>=', now()->subDays($days));
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute()
    {
        $statusNames = [
            self::STATUS_STARTED => '已开始',
            self::STATUS_RUNNING => '运行中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_PARTIAL => '部分成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_ERROR => '错误',
            self::STATUS_CANCELLED => '已取消'
        ];

        return $statusNames[$this->status] ?? $this->status;
    }

    /**
     * 获取同步类型名称
     */
    public function getSyncTypeNameAttribute()
    {
        $typeNames = [
            self::TYPE_HOTEL_INFO => '酒店信息',
            self::TYPE_ROOM_TYPES => '房型信息',
            self::TYPE_RATE_PLANS => '价格计划',
            self::TYPE_INVENTORY => '库存同步',
            self::TYPE_RATES => '价格同步',
            self::TYPE_AVAILABILITY => '房态同步',
            self::TYPE_RESTRICTIONS => '限制同步',
            self::TYPE_BOOKINGS => '订单同步'
        ];

        return $typeNames[$this->sync_type] ?? $this->sync_type;
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $statusColors = [
            self::STATUS_STARTED => 'blue',
            self::STATUS_RUNNING => 'orange',
            self::STATUS_COMPLETED => 'green',
            self::STATUS_PARTIAL => 'yellow',
            self::STATUS_FAILED => 'red',
            self::STATUS_ERROR => 'red',
            self::STATUS_CANCELLED => 'gray'
        ];

        return $statusColors[$this->status] ?? 'default';
    }

    /**
     * 获取执行时长
     */
    public function getDurationAttribute()
    {
        if (!$this->started_at) {
            return null;
        }

        $endTime = $this->completed_at ?: now();
        return $this->started_at->diffInSeconds($endTime);
    }

    /**
     * 获取格式化的执行时长
     */
    public function getDurationFormattedAttribute()
    {
        $duration = $this->duration;
        
        if ($duration === null) {
            return '未知';
        }

        if ($duration < 60) {
            return $duration . '秒';
        } elseif ($duration < 3600) {
            return floor($duration / 60) . '分' . ($duration % 60) . '秒';
        } else {
            $hours = floor($duration / 3600);
            $minutes = floor(($duration % 3600) / 60);
            $seconds = $duration % 60;
            return $hours . '时' . $minutes . '分' . $seconds . '秒';
        }
    }

    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute()
    {
        if ($this->records_total === 0) {
            return 0;
        }

        return round(($this->records_success / $this->records_total) * 100, 2);
    }

    /**
     * 获取格式化的内存使用量
     */
    public function getMemoryUsageFormattedAttribute()
    {
        if (!$this->memory_usage) {
            return '未知';
        }

        $bytes = $this->memory_usage;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * 检查是否成功
     */
    public function isSuccess()
    {
        return in_array($this->status, [self::STATUS_COMPLETED, self::STATUS_PARTIAL]);
    }

    /**
     * 检查是否失败
     */
    public function isFailed()
    {
        return in_array($this->status, [self::STATUS_FAILED, self::STATUS_ERROR]);
    }

    /**
     * 检查是否正在运行
     */
    public function isRunning()
    {
        return in_array($this->status, [self::STATUS_STARTED, self::STATUS_RUNNING]);
    }

    /**
     * 获取同步摘要
     */
    public function getSyncSummary()
    {
        return [
            'id' => $this->id,
            'hotel_id' => $this->hotel_id,
            'channel_code' => $this->channel_code,
            'sync_type' => $this->sync_type,
            'sync_type_name' => $this->sync_type_name,
            'status' => $this->status,
            'status_name' => $this->status_name,
            'status_color' => $this->status_color,
            'message' => $this->message,
            'records' => [
                'total' => $this->records_total,
                'success' => $this->records_success,
                'failed' => $this->records_failed,
                'success_rate' => $this->success_rate . '%'
            ],
            'performance' => [
                'execution_time' => $this->execution_time,
                'duration' => $this->duration_formatted,
                'memory_usage' => $this->memory_usage_formatted
            ],
            'timestamps' => [
                'started_at' => $this->started_at ? $this->started_at->format('Y-m-d H:i:s') : null,
                'completed_at' => $this->completed_at ? $this->completed_at->format('Y-m-d H:i:s') : null,
                'created_at' => $this->created_at->format('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 获取同步统计
     */
    public static function getSyncStats($hotelId = null, $channelCode = null, $days = 30)
    {
        $query = static::query();

        if ($hotelId) {
            $query->where('hotel_id', $hotelId);
        }

        if ($channelCode) {
            $query->where('channel_code', $channelCode);
        }

        $query->where('started_at', '>=', now()->subDays($days));

        $logs = $query->get();

        $stats = [
            'total_syncs' => $logs->count(),
            'success_syncs' => $logs->where('status', self::STATUS_COMPLETED)->count(),
            'partial_syncs' => $logs->where('status', self::STATUS_PARTIAL)->count(),
            'failed_syncs' => $logs->whereIn('status', [self::STATUS_FAILED, self::STATUS_ERROR])->count(),
            'running_syncs' => $logs->whereIn('status', [self::STATUS_STARTED, self::STATUS_RUNNING])->count(),
            'success_rate' => 0,
            'avg_execution_time' => 0,
            'total_records_processed' => $logs->sum('records_total'),
            'total_records_success' => $logs->sum('records_success'),
            'total_records_failed' => $logs->sum('records_failed'),
            'sync_types' => [],
            'channels' => [],
            'daily_stats' => []
        ];

        if ($stats['total_syncs'] > 0) {
            $stats['success_rate'] = round((($stats['success_syncs'] + $stats['partial_syncs']) / $stats['total_syncs']) * 100, 2);
            $stats['avg_execution_time'] = round($logs->avg('execution_time'), 2);
        }

        // 按同步类型统计
        $syncTypeStats = $logs->groupBy('sync_type');
        foreach ($syncTypeStats as $syncType => $typeLogs) {
            $stats['sync_types'][$syncType] = [
                'total' => $typeLogs->count(),
                'success' => $typeLogs->where('status', self::STATUS_COMPLETED)->count(),
                'failed' => $typeLogs->whereIn('status', [self::STATUS_FAILED, self::STATUS_ERROR])->count()
            ];
        }

        // 按渠道统计
        $channelStats = $logs->groupBy('channel_code');
        foreach ($channelStats as $channel => $channelLogs) {
            $stats['channels'][$channel] = [
                'total' => $channelLogs->count(),
                'success' => $channelLogs->where('status', self::STATUS_COMPLETED)->count(),
                'failed' => $channelLogs->whereIn('status', [self::STATUS_FAILED, self::STATUS_ERROR])->count()
            ];
        }

        // 按日期统计
        $dailyStats = $logs->groupBy(function($log) {
            return $log->started_at->format('Y-m-d');
        });

        foreach ($dailyStats as $date => $dayLogs) {
            $stats['daily_stats'][$date] = [
                'total' => $dayLogs->count(),
                'success' => $dayLogs->where('status', self::STATUS_COMPLETED)->count(),
                'failed' => $dayLogs->whereIn('status', [self::STATUS_FAILED, self::STATUS_ERROR])->count()
            ];
        }

        return $stats;
    }

    /**
     * 清理过期日志
     */
    public static function cleanupExpiredLogs($days = 90)
    {
        $expiredDate = now()->subDays($days);
        
        return static::where('started_at', '<', $expiredDate)->delete();
    }
}
