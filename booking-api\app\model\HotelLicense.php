<?php

namespace app\model;

use support\Model;

/**
 * 酒店资质模型
 */
class HotelLicense extends Model
{
    /**
     * 表名
     */
    protected $table = 'hotel_licenses';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'hotel_id',
        'license_type',
        'license_data',
        'license_number',
        'issue_date',
        'expiration_date',
        'issuing_authority',
        'status',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'license_data' => 'array',
        'issue_date' => 'date',
        'expiration_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(bool $isUpdate = false): array
    {
        $base = [
            'hotel_id' => $isUpdate ? 'integer|min:1' : 'required|integer|min:1',
            'license_type' => $isUpdate ? 'string|in:business,tax,fire,health,tourism' : 'required|string|in:business,tax,fire,health,tourism',
            'license_data' => $isUpdate ? 'array' : 'required|array',
            'license_number' => 'nullable|string|max:100',
            'issue_date' => 'nullable|date',
            'expiration_date' => 'nullable|date',
            'issuing_authority' => 'nullable|string|max:200',
            'status' => 'in:active,expired,revoked,inactive',
        ];

        return $base;
    }

    /**
     * 转换为API数组
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'hotel_id' => $this->hotel_id,
            'license_type' => $this->license_type,
            'license_data' => $this->license_data,
            'license_number' => $this->license_number,
            'issue_date' => $this->issue_date?->format('Y-m-d'),
            'expiration_date' => $this->expiration_date?->format('Y-m-d'),
            'issuing_authority' => $this->issuing_authority,
            'status' => $this->status,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}


