<?php

namespace base\AuditLog\traits;


trait Lang
{
    /**
     * 语言包
     *
     * @var array
     */
    protected array $lang = [];

    public function getLang()
    {
        return $this->lang ?? [];
    }

    public function setLang(array $lang): static
    {
        $this->lang = $lang;
        return $this;
    }

    public function mergeLang(array $lang): static
    {
        $this->lang = array_replace_recursive($this->lang, $lang);
        return $this;
    }

}