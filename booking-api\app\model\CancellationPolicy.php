<?php

namespace app\model;

use support\Model;

/**
 * 取消政策模型
 */
class CancellationPolicy extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'cancellation_policies';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'deadline_hours',
        'fee_type',
        'fee_amount',
        'is_active',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'deadline_hours' => 'integer',
        'fee_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 作用域：启用的取消政策
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 作用域：按代码查找
     */
    public function scopeByCode($query, $code)
    {
        return $query->where('code', $code);
    }

    /**
     * 获取所有启用的取消政策（用于下拉选择）
     *
     * @return array
     */
    public static function getActiveOptions()
    {
        return static::active()->ordered()->get(['code', 'name', 'description'])->toArray();
    }

    /**
     * 获取费用类型名称
     *
     * @return string
     */
    public function getFeeTypeNameAttribute()
    {
        $types = [
            'none' => '无费用',
            'fixed' => '固定金额',
            'percentage' => '百分比',
            'nights' => '按夜数',
        ];

        return $types[$this->fee_type] ?? $this->fee_type;
    }

    /**
     * 计算取消费用
     *
     * @param float $totalAmount 总金额
     * @param int $nights 入住夜数
     * @param string $currency 币种
     * @return array
     */
    public function calculateCancellationFee($totalAmount, $nights = 1, $currency = 'CNY')
    {
        $fee = 0;
        $feeText = '';

        switch ($this->fee_type) {
            case 'none':
                $fee = 0;
                $feeText = '免费取消';
                break;
            case 'fixed':
                $fee = $this->fee_amount;
                $feeText = $this->fee_amount . ' ' . $currency;
                break;
            case 'percentage':
                $fee = $totalAmount * ($this->fee_amount / 100);
                $feeText = $this->fee_amount . '%';
                break;
            case 'nights':
                $fee = ($totalAmount / $nights) * $this->fee_amount;
                $feeText = $this->fee_amount . ' 晚房费';
                break;
        }

        return [
            'fee' => round($fee, 2),
            'fee_text' => $feeText,
            'currency' => $currency,
        ];
    }

    /**
     * 检查是否在免费取消期内
     *
     * @param \DateTime|string $checkInDate 入住日期
     * @param \DateTime|string $currentDate 当前日期
     * @return bool
     */
    public function isInFreeCancellationPeriod($checkInDate, $currentDate = null)
    {
        if ($this->deadline_hours <= 0) {
            return false; // 不可取消
        }

        $currentDate = $currentDate ?: now();
        $checkInDate = is_string($checkInDate) ? new \DateTime($checkInDate) : $checkInDate;
        $currentDate = is_string($currentDate) ? new \DateTime($currentDate) : $currentDate;

        $deadlineDate = clone $checkInDate;
        $deadlineDate->sub(new \DateInterval('PT' . $this->deadline_hours . 'H'));

        return $currentDate <= $deadlineDate;
    }
}
