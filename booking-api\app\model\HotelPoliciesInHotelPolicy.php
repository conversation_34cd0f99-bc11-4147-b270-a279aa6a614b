<?php

namespace app\model;

use support\Model;

/**
 * 酒店在店政策模型
 */
class HotelPoliciesInHotelPolicy extends Model
{
    /**
     * 表名
     */
    protected $table = 'hotel_policies_in_hotel_policies';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'hotel_id',
        'housekeeping_service',
        'housekeeping_frequency',
        'towel_change_policy',
        'linen_change_policy',
        'do_not_disturb_respected',
        'maintenance_hours',
        'facility_access_hours',
        'common_area_rules',
        'wifi_policy',
        'laundry_service',
        'concierge_service',
        'lost_and_found_policy',
        'in_hotel_notes',
        'status',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'housekeeping_service' => 'boolean',
        'do_not_disturb_respected' => 'boolean',
        'facility_access_hours' => 'array',
        'common_area_rules' => 'array',
        'laundry_service' => 'boolean',
        'concierge_service' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(): array
    {
        return [
            'hotel_id' => 'required|integer|min:1',
            'housekeeping_service' => 'boolean',
            'housekeeping_frequency' => 'nullable|string|in:daily,every_other_day,weekly,on_request',
            'towel_change_policy' => 'nullable|string|max:100',
            'linen_change_policy' => 'nullable|string|max:100',
            'do_not_disturb_respected' => 'boolean',
            'maintenance_hours' => 'nullable|string|max:100',
            'wifi_policy' => 'nullable|string|max:200',
            'laundry_service' => 'boolean',
            'concierge_service' => 'boolean',
            'lost_and_found_policy' => 'nullable|string|max:200',
            'in_hotel_notes' => 'nullable|string',
            'status' => 'in:active,inactive',
        ];
    }

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id', 'id');
    }

    /**
     * 作用域：启用的政策
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 获取政策类型
     */
    public function getPolicyType(): string
    {
        return 'in_hotel';
    }

    /**
     * 获取政策显示名称
     */
    public function getPolicyDisplayName(): string
    {
        return '在店政策';
    }

    /**
     * 获取政策英文名称
     */
    public function getPolicyEnglishName(): string
    {
        return 'In-hotel Policy';
    }

    /**
     * 获取清洁频率显示文本
     */
    public function getHousekeepingFrequencyText(): string
    {
        $frequencies = [
            'daily' => '每日',
            'every_other_day' => '隔日',
            'weekly' => '每周',
            'on_request' => '按需',
        ];
        return $frequencies[$this->housekeeping_frequency] ?? $this->housekeeping_frequency ?? '每日';
    }

    /**
     * 获取公共区域规则显示文本
     */
    public function getCommonAreaRulesText(): string
    {
        if (!$this->common_area_rules || !is_array($this->common_area_rules)) {
            return '-';
        }
        
        $ruleMap = [
            'no_smoking' => '禁止吸烟',
            'no_food' => '禁止饮食',
            'quiet_zone' => '安静区域',
            'dress_code' => '着装要求',
            'no_pets' => '禁止宠物',
            'no_alcohol' => '禁止饮酒',
        ];
        
        $rules = array_map(function($rule) use ($ruleMap) {
            return $ruleMap[$rule] ?? $rule;
        }, $this->common_area_rules);
        
        return implode('、', $rules);
    }

    /**
     * 获取设施开放时间显示文本
     */
    public function getFacilityAccessHoursText(): string
    {
        if (!$this->facility_access_hours || !is_array($this->facility_access_hours)) {
            return '-';
        }
        
        $hours = [];
        foreach ($this->facility_access_hours as $facility => $time) {
            $hours[] = $facility . ': ' . $time;
        }
        
        return implode('；', $hours);
    }

    /**
     * 创建默认政策
     */
    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId,
            'housekeeping_service' => true,
            'housekeeping_frequency' => 'daily',
            'do_not_disturb_respected' => true,
            'laundry_service' => false,
            'concierge_service' => false,
            'status' => 'active',
        ]);
    }
}
