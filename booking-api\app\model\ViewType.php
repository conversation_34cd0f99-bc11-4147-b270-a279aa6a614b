<?php

namespace app\model;

use support\Model;

/**
 * 景观类型模型
 */
class ViewType extends Model
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'view_types';

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array
     */
    protected $fillable = [
        'code',
        'name_cn',
        'name_en',
        'status',
        'sort_order',
    ];

    /**
     * 属性类型转换
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // 状态常量
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取状态列表
     * @return array
     */
    public static function getStatusList()
    {
        return [
            self::STATUS_ACTIVE => '启用',
            self::STATUS_INACTIVE => '禁用',
        ];
    }

    /**
     * 获取状态文本
     * @param string $status
     * @return string
     */
    public static function getStatusText($status)
    {
        $statusList = self::getStatusList();
        return $statusList[$status] ?? '未知';
    }

    /**
     * 获取启用的景观类型列表
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getActiveList()
    {
        return self::where('status', self::STATUS_ACTIVE)
            ->orderBy('sort_order', 'asc')
            ->orderBy('id', 'asc')
            ->get();
    }

    /**
     * 获取景观类型选项（用于下拉选择）
     * @return array
     */
    public static function getOptions()
    {
        return self::getActiveList()->map(function ($item) {
            return [
                'value' => $item->code,
                'label' => $item->name_cn,
                'label_en' => $item->name_en,
            ];
        })->toArray();
    }

    /**
     * 检查是否被房型使用
     * @return bool
     */
    public function isUsedByRoomTypes()
    {
        // 检查是否有房型在使用这个景观类型
        return \app\model\RoomType::where('view_type', $this->code)->exists();
    }

    /**
     * 获取使用此景观类型的房型数量
     * @return int
     */
    public function getUsageCount()
    {
        return \app\model\RoomType::where('view_type', $this->code)->count();
    }

    /**
     * 作用域：按状态筛选
     * @param $query
     * @param string $status
     * @return mixed
     */
    public function scopeByStatus($query, $status)
    {
        if ($status) {
            return $query->where('status', $status);
        }
        return $query;
    }

    /**
     * 作用域：按关键词搜索
     * @param $query
     * @param string $keyword
     * @return mixed
     */
    public function scopeByKeyword($query, $keyword)
    {
        if ($keyword) {
            return $query->where(function ($q) use ($keyword) {
                $q->where('name_cn', 'like', "%{$keyword}%")
                  ->orWhere('name_en', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%");
            });
        }
        return $query;
    }

    /**
     * 获取下一个排序值
     * @return int
     */
    public static function getNextSortOrder()
    {
        $maxSort = self::max('sort_order');
        return $maxSort ? $maxSort + 1 : 1;
    }
}
