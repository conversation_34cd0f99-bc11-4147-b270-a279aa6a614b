<?php

declare(strict_types=1);

use support\Container;
use support\Response;
use base\base\DS;
use base\helper\Conversion;
use base\helper\Date;
use base\helper\Output;
use base\helper\Snowflake;
use base\auth\UserInfo;

if ( ! function_exists('get_locale')) {
    function get_locale(): string
    {
        return request()->header('Accept-Language', 'zh-CN');
    }
}

/**
 * 获取房型名称
 * @param array $roomType 房型数据
 * @param string $currentLocale 当前语言
 * @return string
 */
if ( ! function_exists('get_room_type_name')) {
    function get_room_type_name($roomType, $currentLocale = 'zh-CN'): string
    {
        return $currentLocale === 'zh-CN' ? $roomType['name'] : $roomType['name_en'];
    }
}

/**
 * 获取房价计划名称
 * @param array $roomType 房型数据
 * @param array $plan 房价计划数据
 * @param string $currentLocale 当前语言
 * @return string
 */
if ( ! function_exists('get_plan_name')) {
    function get_plan_name($roomType, $plan, $currentLocale = 'zh-CN'): string
    {
        $roomTypeName = get_room_type_name($roomType, $currentLocale);
        $nameStr = $plan['name'] ? '<' . $plan['name'] . '>' : '';
        $currencyStr = $plan['currency'] && $plan['currency'] !== 'CNY'
            ? '-' . currencyName($plan['currency'])
            : '';
        return $roomTypeName . $nameStr . '(' . mealName($plan['mealType'], $plan['mealCount']) . ')' . $currencyStr . '(' . $plan['code'] . ')';
    }
}

if ( ! function_exists("userinfo")) {
    /**
     * 用户基础数据
     *
     * @return UserInfo
     */
    function userinfo(): UserInfo
    {
        return Container::get(UserInfo::class);
    }
}

/**
 * 雪花算法
 */
if ( ! function_exists('snowflake')) {
    function snowflake(): int
    {
        return (new Snowflake(1))->generateId();
    }
}

if ( ! function_exists('get_week')) {
    function get_week(int $time): string
    {
        return ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][date("w", $time)];
    }
}

/**
 * 简化时间显示
 *
 * return [
 *      ['2024-01-05' , '2024-01-06'],
 *      ['2024-02-01' , '2024-02-02'],
 * ]
 *
 */
if ( ! function_exists('date_abbreviation')) {
    function date_abbreviation(array $dates): array
    {
        if (empty($dates)) {
            return [];
        }
        array_multisort($dates);
        $new_dates = [];
        $step      = 0;
        foreach ($dates as $date) {
            if ( ! isset($new_dates[$step][0])) {
                $new_dates[$step][0] = $date;
            } elseif (date('Y-m-d', strtotime($date . ' - 1 days ')) !== $new_dates[$step][1]) {
                ++$step;
                $new_dates[$step][0] = $date;
            }
            $new_dates[$step][1] = $date;
        }

        return $new_dates;
    }
}


if ( ! function_exists('env')) {
    function env($key, $default = '')
    {
        if ($value = getenv($key)) {
            return $value;
        }

        return $default;
    }
}

if ( ! function_exists("ds")) {
    /**
     * 数据集实例化快捷方式
     *
     * @param ?array $data
     *
     * @return DS
     *
     * @uses lixuecong <<EMAIL>>
     */
    function ds(?array $data = []): DS
    {
        return new DS($data);
    }
}




if ( ! function_exists("success")) {
    /**
     * 正常输出
     *
     * @param array|null $data
     * @param string $msg
     * @param int $code
     *
     * @return Response
     * @author: lixuecong <<EMAIL>>
     *
     * @copyright  2022/2/22 16:15
     */
    function success(?array $data = [], string $msg = 'success', int $code = 200)
    {
        return Output::success($data, $msg, $code);
    }
}

if ( ! function_exists("error")) {
    /**
     * 异常输出
     *
     * @param int $code
     * @param string $msg
     * @param array|null $data
     *
     * @return Response
     *
     * @author: lixuecong <<EMAIL>>
     *
     * @copyright  2022/2/22 16:15
     */
    function error(int $code = 404, string $msg = 'error', ?array $data = []): Response
    {
        return Output::error($code, $msg, $data);
    }
}
if ( ! function_exists("formatDateTime")) {
    /**
     * 日期格式标准输出
     *
     * @param int|string $datetime 输入日期
     * @param string $format 输出格式
     * @param string $is_null 当为空时的输出
     *
     * @return string
     *
     * @uses lixuecong <<EMAIL>>
     *
     * @trigger_error
     */
    function formatDateTime($datetime, string $format = 'Y-m-d H:i:s', string $is_null = ''): string
    {
        return Date::formatDateTime($datetime, $format, $is_null);
    }
}


if ( ! function_exists("unixTimestamp")) {
    /**
     * 时间字符串转UNIX时间戳
     *
     * @param $str
     *
     * @return int
     *
     * @uses lixuecong <<EMAIL>>
     */
    function unixTimestamp($str): int
    {
        return Date::unixTimestamp($str);
    }
}

if ( ! function_exists("json2Arr")) {
    /**
     * JSON转数组
     *
     * @param string $json
     * @param int $options
     *
     * @return array
     *
     * @throws JsonException
     * @uses lixuecong <<EMAIL>>
     */
    function json2Arr(string $json, $options = JSON_UNESCAPED_UNICODE): array
    {
        return Conversion::json2Arr($json, $options);
    }
}


if ( ! function_exists("arr2Json")) {
    /**
     * 数组转JSON
     *
     * @param array $array
     * @param int $options
     *
     * @return mixed
     * @throws JsonException
     */
    function arr2Json(array $array, int $options = JSON_UNESCAPED_UNICODE)
    {
        return Conversion::arr2Json($array, $options);
    }
}

if ( ! function_exists("str2Arr")) {
    /**
     * 字符串转数组
     *
     * @param string $text 待转内容
     * @param string $spear 分隔符
     * @param null|array $allow 限定规则
     *
     * @return array
     *
     * @uses lixuecong <<EMAIL>>
     *
     */
    function str2Arr(string $text, string $spear = ',', ?array $allow = null): array
    {
        return Conversion::str2Arr($text, $spear, $allow);
    }
}

if ( ! function_exists("arr2Int")) {
    /**
     * 字符串转数组
     *
     * @param array $arr 数组
     *
     * @return array
     *
     * @uses lixuecong <<EMAIL>>
     */
    function arr2Int(array $arr): array
    {
        foreach ($arr as &$value) {
            $value = (int)$value;
        }

        return $arr;
    }
}


if ( ! function_exists("arr2Str")) {
    /**
     * 数组转字符串
     *
     * @param array $data 待转数组
     * @param string $spear 分隔字符
     * @param null|array $allow 限定规则
     *
     * @return string
     *
     * @uses lixuecong <<EMAIL>>
     */
    function arr2Str(array $data, string $spear = ',', ?array $allow = null): string
    {
        return Conversion::arr2Str($data, $spear, $allow);
    }
}


if ( ! function_exists('get_date_format_range')) {
    /**
     * 获取指定日期段内每一天的日期
     *
     * @param string $start_time 开始日期  20180612
     * @param string $end_time 结束日期  20180620
     * @param string $format 日期格式
     *
     * @return Array
     */

    function get_date_format_range(string $start_time, string $end_time, string $format = 'Ymd'): array
    {
        $start_timestamp = strtotime($start_time);
        $end_timestamp   = strtotime($end_time);
        $days            = ($end_timestamp - $start_timestamp) / 86400 + 1;
        $date            = [];
        for ($i = 0; $i < $days; $i++) {
            $date[] = date($format, $start_timestamp + (86400 * $i));
        }

        return $date;
    }
}


if ( ! function_exists('get_date_rate')) {
    function get_date_rate($startDate, $endDate): array
    {
//    if (date('Y-m-d', strtotime($startDate)) !== $startDate || date('Y-m-d', strtotime($endDate)) !== $endDate) {
//        return '日期格式不正确';
//    }
        $temp_date   = date('Y-m-d', strtotime($startDate));
        $endDate     = date('Y-m-d', strtotime($endDate));
        $return_data = [];
        $i           = 0;

        while (strtotime($temp_date) < strtotime($endDate)) {
            $temp         = [];
            $month        = strtotime('first day of +' . $i . ' month', strtotime($startDate));
            $temp['name'] = date('m', $month);
            if ($i === 0) {
                $temp['startDate'] = date('Y-m-d', strtotime($startDate));
            } else {
                $temp['startDate'] = date('Y-m-01', $month);
            }
            if (date('Y-m', strtotime($temp['startDate'])) === date('Y-m', strtotime($endDate))) {
                $temp['endDate'] = date('Y-m-d', strtotime($endDate));
            } else {
                $temp['endDate'] = date('Y-m-t', $month);
            }

            $temp_date     = $temp['endDate'];
            $return_data[] = $temp;
            $i++;
        }


        return $return_data;
    }
}
if ( ! function_exists('get_user_os')) {
    function get_user_os($user_agent): string
    {
        if (false !== stripos($user_agent, "win")) {
            $OS = 'Windows';
        } elseif (false !== stripos($user_agent, "mac")) {
            $OS = 'MAC';
        } elseif (false !== stripos($user_agent, "linux")) {
            $OS = 'Linux';
        } elseif (false !== stripos($user_agent, "unix")) {
            $OS = 'Unix';
        } elseif (false !== stripos($user_agent, "bsd")) {
            $OS = 'BSD';
        } else {
            $OS = 'Other';
        }

        return $OS;
    }
}

if ( ! function_exists('get_user_browser')) {
    function get_user_browser($user_agent): string
    {
        if (false !== stripos($user_agent, "MSIE")) {
            $br = 'MSIE';
        } elseif (false !== stripos($user_agent, "Firefox")) {
            $br = 'Firefox';
        } elseif (false !== stripos($user_agent, "Chrome")) {
            $br = 'Chrome';
        } elseif (false !== stripos($user_agent, "Safari")) {
            $br = 'Safari';
        } elseif (false !== stripos($user_agent, "Opera")) {
            $br = 'Opera';
        } else {
            $br = 'Other';
        }

        return $br;
    }
}

/**
 * 验证日期格式是否正确
 * @param string $date 日期字符串，格式为YYYY-MM-DD
 * @param string $format 日期格式
 * @return bool
 */
function validateDate($date, $format = 'Y-m-d')
{
    $d = \DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}
/**
 * 生成订单号
 * @return string
 */
function getTradeNo($head = 'BO')
{
    list($msec, $sec) = explode(' ', microtime());
    $a = mt_rand(10, 99999)  . substr($msec, 2, 5);
    $num = time() + $a + substr(str_shuffle(str_repeat('123456789', 5)), 0, 10);

    return $head.date('md').$num;
}




// 获取房型名称
function getRoomTypeName($roomType, $currentLocale = 'zh-CN') {
    foreach ($roomType['lang'] as $langItem) {
        if ($langItem['languageCode'] === $currentLocale) {
            return $langItem['content'];
        }
    }
    return $roomType['roomTypeCode'];
}

// 获取房价计划名称
function getRatePlanName($roomType, $ratePlan, $currentLocale = 'zh-CN') {
    // 获取房型名称
    $roomTypeName = getRoomTypeName($roomType, $currentLocale);

    // 处理名称部分，如果name为空则不展示
    $nameStr = $ratePlan['name'] ? '<' . $ratePlan['name'] . '>' : '';

    // 获取货币名称，人民币(CNY)默认不展示
    $currencyStr = $ratePlan['currency'] && $ratePlan['currency'] !== 'CNY'
        ? '-' . currencyName($ratePlan['currency'])
        : '';

    // 房型名字 + 是否有早餐 + 货币 + ratePlanCode
    return $roomTypeName . $nameStr . '(' . mealName($ratePlan['mealType'], $ratePlan['mealCount']) . ')' . $currencyStr . '(' . $ratePlan['ratePlanId'] . ')';
}

// 获取餐饮名称
function mealName($mealType, $mealCount) {
// 定义餐饮类型选项
    $mealTypeOptions = [
        ['value' => '1', 'label' => '晚餐'],
        ['value' => '2', 'label' => '午餐'],
        ['value' => '3', 'label' => '午+晚餐'],
        ['value' => '4', 'label' => '早餐'],
        ['value' => '5', 'label' => '早+晚餐'],
        ['value' => '6', 'label' => '早+午餐'],
        ['value' => '7', 'label' => '早+午+晚餐'],
        ['value' => '8', 'label' => '半餐', 'description' => '可选餐。早餐、午餐、晚餐、三选二，详询酒店前台'],
        ['value' => '9', 'label' => '全餐', 'description' => '早午晚餐，并且额外供应小食、饮料'],
        ['value' => '10', 'label' => '午/晚二选一'],
        ['value' => '11', 'label' => '早+ 午/晚二选一'],
    ];
    foreach ($mealTypeOptions as $mealTypeInfo) {
        if ($mealTypeInfo['value'] === strval($mealType)) {
            $typeName = $mealTypeInfo['label'];
            break;
        }
    }

    if (!isset($typeName)) {
        $typeName = '无早';
    }

    // 如果有数量，则拼接数量信息
    if ($mealCount !== null && $mealCount > 0) {
        return $typeName . ' x ' . $mealCount;
    }

    return $typeName;
}

// 获取货币名称
function currencyName($currencyCode) {
// 定义货币选项
    $currencyOptions = [
        ['value' => 'USD', 'label' => '美元'],
        ['value' => 'JPY', 'label' => '日元'],
        ['value' => 'KRW', 'label' => '韩元'],
        ['value' => 'HKD', 'label' => '港币'],
        ['value' => 'TWD', 'label' => '台币'],
        ['value' => 'EUR', 'label' => '欧元'],
        ['value' => 'GBP', 'label' => '英镑'],
        ['value' => 'CNY', 'label' => '人民币'],
        ['value' => 'RUB', 'label' => '卢布'],
        ['value' => 'MYR', 'label' => '马来西亚林吉特'],
        ['value' => 'IDR', 'label' => '印尼卢比'],
        ['value' => 'THB', 'label' => '泰铢'],
        ['value' => 'VND', 'label' => '越南盾'],
        ['value' => 'PHP', 'label' => '菲律宾比索'],
        ['value' => 'CHF', 'label' => '瑞士法郎'],
        ['value' => 'PLN', 'label' => '波兰兹罗提'],
        ['value' => 'TRY', 'label' => '土耳其里拉'],
        ['value' => 'BRL', 'label' => '巴西雷亚尔'],
        ['value' => 'ARS', 'label' => '阿根廷比索'],
    ];
    foreach ($currencyOptions as $currencyInfo) {
        if ($currencyInfo['value'] === $currencyCode) {
            return $currencyInfo['label'];
        }
    }
    return $currencyCode; // 如果未找到，返回货币代码本身
}


// 定义价格规则应用函数

function apply_price_rules(string $price, array $rules, $scale = 2)
{
    $result = '0';
    foreach ($rules as $rule) {
        $value = (string) $rule['value'];
        $symbol = (int) $rule['symbol'];
        switch ($symbol) {
            case 1:
            {
                // 加价 - 加上价格（以厘为单位，所以乘以100）
                $addValue = bcmul($value, '100', $scale);
                $result = bcadd($result, bcadd($price, $addValue, $scale), $scale);
                break;
            }
            case 2:
            {
                // 减价 - 减去价格的百分比
                $percentage = bcdiv($value, '100', $scale);
                $subtractValue = bcmul($price, $percentage, $scale);
                $result = bcadd($result, bcsub($price, $subtractValue, $scale), $scale);
                break;
            }
            case 3:
            {
                // 乘以 - 直接乘以系数
                $result = bcadd($result, bcmul($price, $value, $scale), $scale);
                break;
            }
            case 4:
            {
                // 除以 - 除以系数，避免除以0
                if (bccomp($value, '0', $scale) === 0) {
                    return $price;
                }
                $result = bcadd($result, bcdiv($price, $value, $scale), $scale);
                break;
            }
            default:
            {
                $result = bcadd($result, $price, $scale);
            }
        }
    }
    return $result;
}
