<?php

namespace app\model;

/**
 * 房型预订限制模型
 * 对应数据库表：room_type_booking_restrictions
 */
class RoomTypeBookingRestriction extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_type_booking_restrictions';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'room_type_id',
        'restriction_type',
        'restriction_name',
        'restriction_description',
        'restriction_rules',
        'is_active',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'restriction_rules' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * 关联房型
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type_id');
    }

    /**
     * 获取最少入住限制
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMinimumStay($query)
    {
        return $query->where('restriction_type', 'minimum_stay');
    }

    /**
     * 获取最多入住限制
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMaximumStay($query)
    {
        return $query->where('restriction_type', 'maximum_stay');
    }

    /**
     * 获取提前预订限制
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAdvanceBooking($query)
    {
        return $query->where('restriction_type', 'advance_booking');
    }

    /**
     * 获取最后时刻限制
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLastMinute($query)
    {
        return $query->where('restriction_type', 'last_minute');
    }

    /**
     * 获取启用限制
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 按排序排序
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取限制类型名称
     *
     * @return string
     */
    public function getRestrictionTypeNameAttribute()
    {
        $types = [
            'minimum_stay' => '最少入住',
            'maximum_stay' => '最多入住',
            'advance_booking' => '提前预订',
            'last_minute' => '最后时刻',
        ];

        return $types[$this->restriction_type] ?? $this->restriction_type;
    }

    /**
     * 获取限制完整信息
     *
     * @return array
     */
    public function getRestrictionInfoAttribute()
    {
        return [
            'id' => $this->id,
            'restriction_type' => $this->restriction_type,
            'restriction_type_name' => $this->restriction_type_name,
            'restriction_name' => $this->restriction_name,
            'restriction_description' => $this->restriction_description,
            'restriction_rules' => $this->restriction_rules,
            'is_active' => $this->is_active,
        ];
    }

    /**
     * 验证限制
     *
     * @param array $data
     * @return array
     */
    public static function validateRestriction($data)
    {
        $rules = [
            'room_type_id' => 'required|integer|exists:room_types,id',
            'restriction_type' => 'required|in:minimum_stay,maximum_stay,advance_booking,last_minute',
            'restriction_name' => 'required|string|max:100',
            'restriction_description' => 'nullable|string',
            'restriction_rules' => 'nullable|array',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];

        $messages = [
            'room_type_id.required' => '房型ID不能为空',
            'room_type_id.exists' => '房型不存在',
            'restriction_type.required' => '限制类型不能为空',
            'restriction_type.in' => '限制类型无效',
            'restriction_name.required' => '限制名称不能为空',
            'restriction_name.max' => '限制名称不能超过100个字符',
            'restriction_rules.array' => '限制规则必须是数组格式',
            'sort_order.min' => '排序不能为负数',
        ];

        return [
            'rules' => $rules,
            'messages' => $messages,
        ];
    }

    /**
     * 创建限制
     *
     * @param array $data
     * @return static
     */
    public static function createRestriction($data)
    {
        $validation = self::validateRestriction($data);
        
        // 这里应该使用验证器，暂时简化处理
        $restriction = new self($data);
        $restriction->save();
        
        return $restriction;
    }

    /**
     * 更新限制
     *
     * @param array $data
     * @return bool
     */
    public function updateRestriction($data)
    {
        $validation = self::validateRestriction($data);
        
        // 这里应该使用验证器，暂时简化处理
        return $this->update($data);
    }

    /**
     * 获取房型的所有限制
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->active()
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的指定类型限制
     *
     * @param int $roomTypeId
     * @param string $restrictionType
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomTypeAndType($roomTypeId, $restrictionType)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('restriction_type', $restrictionType)
            ->active()
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的最少入住限制
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getMinimumStayRestrictionsByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'minimum_stay');
    }

    /**
     * 获取房型的最多入住限制
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getMaximumStayRestrictionsByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'maximum_stay');
    }

    /**
     * 获取房型的提前预订限制
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAdvanceBookingRestrictionsByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'advance_booking');
    }

    /**
     * 获取房型的最后时刻限制
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getLastMinuteRestrictionsByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'last_minute');
    }

    /**
     * 检查是否满足限制条件
     *
     * @param array $bookingData 预订数据
     * @return bool
     */
    public function checkRestriction($bookingData)
    {
        if (!$this->is_active || !$this->restriction_rules) {
            return true;
        }

        $rules = $this->restriction_rules;
        
        switch ($this->restriction_type) {
            case 'minimum_stay':
                return $this->checkMinimumStay($bookingData, $rules);
            case 'maximum_stay':
                return $this->checkMaximumStay($bookingData, $rules);
            case 'advance_booking':
                return $this->checkAdvanceBooking($bookingData, $rules);
            case 'last_minute':
                return $this->checkLastMinute($bookingData, $rules);
            default:
                return true;
        }
    }

    /**
     * 检查最少入住限制
     *
     * @param array $bookingData
     * @param array $rules
     * @return bool
     */
    private function checkMinimumStay($bookingData, $rules)
    {
        $nights = $bookingData['nights'] ?? 1;
        $minNights = $rules['min_nights'] ?? 1;
        
        return $nights >= $minNights;
    }

    /**
     * 检查最多入住限制
     *
     * @param array $bookingData
     * @param array $rules
     * @return bool
     */
    private function checkMaximumStay($bookingData, $rules)
    {
        $nights = $bookingData['nights'] ?? 1;
        $maxNights = $rules['max_nights'] ?? 30;
        
        return $nights <= $maxNights;
    }

    /**
     * 检查提前预订限制
     *
     * @param array $bookingData
     * @param array $rules
     * @return bool
     */
    private function checkAdvanceBooking($bookingData, $rules)
    {
        $checkInDate = $bookingData['check_in_date'] ?? null;
        $advanceDays = $rules['advance_days'] ?? 0;
        
        if (!$checkInDate) {
            return true;
        }
        
        $checkInDateTime = new \DateTime($checkInDate);
        $now = new \DateTime();
        $diffDays = $checkInDateTime->diff($now)->days;
        
        return $diffDays >= $advanceDays;
    }

    /**
     * 检查最后时刻限制
     *
     * @param array $bookingData
     * @param array $rules
     * @return bool
     */
    private function checkLastMinute($bookingData, $rules)
    {
        $checkInDate = $bookingData['check_in_date'] ?? null;
        $lastMinuteDays = $rules['last_minute_days'] ?? 0;
        
        if (!$checkInDate) {
            return true;
        }
        
        $checkInDateTime = new \DateTime($checkInDate);
        $now = new \DateTime();
        $diffDays = $checkInDateTime->diff($now)->days;
        
        return $diffDays <= $lastMinuteDays;
    }
}
