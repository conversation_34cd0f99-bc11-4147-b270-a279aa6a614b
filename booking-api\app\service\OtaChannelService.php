<?php

namespace app\service;

use app\model\OtaChannel;
use app\model\RatePlan;

/**
 * OTA渠道服务
 */
class OtaChannelService extends BaseService
{
    /**
     * 获取OTA渠道列表
     *
     * @param array $params
     * @return array
     */
    public function getOtaChannels(array $params = [])
    {
        try {
            $query = OtaChannel::query();

            // 搜索条件
            if (!empty($params['keyword'])) {
                $query->search($params['keyword']);
            }

            // 状态筛选
            if (isset($params['is_active'])) {
                $query->where('is_active', (bool)$params['is_active']);
            }

            // 佣金比例范围筛选
            if (!empty($params['min_commission_rate'])) {
                $query->where('commission_rate', '>=', $params['min_commission_rate']);
            }
            if (!empty($params['max_commission_rate'])) {
                $query->where('commission_rate', '<=', $params['max_commission_rate']);
            }

            // 结算方式筛选
            if (!empty($params['settlement_method'])) {
                $query->where('settlement_method', $params['settlement_method']);
            }

            // 排序
            $sortField = $params['sort_field'] ?? 'priority';
            $sortOrder = $params['sort_order'] ?? 'desc';

            $query->orderBy($sortField, $sortOrder);

            // 分页
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;
            
            $result = $query->paginate($perPage, ['*'], 'page', $page);

            return [
                'items' => $result->items(),
                'pagination' => [
                    'total' => $result->total(),
                    'current_page' => $result->currentPage(),
                    'per_page' => $result->perPage(),
                    'last_page' => $result->lastPage(),
                ]
            ];

        } catch (\Exception $e) {
            $this->logError('获取OTA渠道列表失败', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 获取OTA渠道详情
     *
     * @param int $id
     * @return array
     */
    public function getOtaChannelDetail(int $id)
    {
        try {
            $otaChannel = OtaChannel::find($id);
            if (!$otaChannel) {
                return $this->error('OTA渠道不存在', 404);
            }

            // 加载关联的房价计划
            $otaChannel->load(['ratePlans' => function ($query) {
                $query->with(['hotel', 'roomType']);
            }]);

            return $this->success($otaChannel);

        } catch (\Exception $e) {
            $this->logError('获取OTA渠道详情失败', ['id' => $id, 'error' => $e->getMessage()]);
            return $this->error('获取OTA渠道详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建OTA渠道
     *
     * @param array $data
     * @return array
     */
    public function createOtaChannel(array $data)
    {
        try {
            // 调试信息
            error_log('OTA Channel data: ' . json_encode($data));
            $this->validateRequired($data, ['name', 'code', 'commission_rate']);

            return $this->transaction(function () use ($data) {
                // 检查代码是否重复
                if (OtaChannel::where('code', $data['code'])->exists()) {
                    throw new \Exception('渠道代码已存在');
                }

                // 设置默认值
                $data = array_merge([
                    'commission_type' => 'percentage',
                    'settlement_method' => 'monthly',
                    'settlement_currency' => 'CNY',
                    'is_active' => true,
                    'priority' => 0,
                    'sort_order' => 0,
                    'booking_window' => 365,
                    'cancellation_window' => 24,
                    'modification_allowed' => true,
                    'instant_confirmation' => true,
                    'min_advance_booking' => 0,
                    'total_bookings' => 0,
                    'total_revenue' => 0.00,
                ], $data);

                $otaChannel = OtaChannel::create($this->filterEmpty($data));

                $this->logInfo('创建OTA渠道成功', [
                    'ota_channel_id' => $otaChannel->id,
                    'name' => $otaChannel->name,
                    'code' => $otaChannel->code
                ]);

                return $this->success($otaChannel, '创建OTA渠道成功');
            });

        } catch (\Exception $e) {
            $this->logError('创建OTA渠道失败', ['data' => $data, 'error' => $e->getMessage()]);
            return $this->error('创建OTA渠道失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新OTA渠道
     *
     * @param int $id
     * @param array $data
     * @return array
     */
    public function updateOtaChannel(int $id, array $data)
    {
        try {
            $otaChannel = OtaChannel::find($id);
            if (!$otaChannel) {
                return $this->error('OTA渠道不存在', 404);
            }

            return $this->transaction(function () use ($otaChannel, $data) {
                // 检查代码是否重复（排除自己）
                if (!empty($data['code']) && $data['code'] !== $otaChannel->code) {
                    if (OtaChannel::where('code', $data['code'])->where('id', '!=', $otaChannel->id)->exists()) {
                        throw new \Exception('渠道代码已存在');
                    }
                }

                $otaChannel->update($this->filterEmpty($data));

                $this->logInfo('更新OTA渠道成功', [
                    'ota_channel_id' => $otaChannel->id,
                    'name' => $otaChannel->name,
                    'changes' => array_keys($data)
                ]);

                return $this->success($otaChannel, '更新OTA渠道成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新OTA渠道失败', ['id' => $id, 'data' => $data, 'error' => $e->getMessage()]);
            return $this->error('更新OTA渠道失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除OTA渠道
     *
     * @param int $id
     * @return array
     */
    public function deleteOtaChannel(int $id)
    {
        try {
            $otaChannel = OtaChannel::find($id);
            if (!$otaChannel) {
                return $this->error('OTA渠道不存在', 404);
            }

            return $this->transaction(function () use ($otaChannel) {
                // 检查是否有关联的房价计划
                $ratePlanCount = $otaChannel->ratePlans()->count();
                if ($ratePlanCount > 0) {
                    throw new \Exception("该渠道关联了 {$ratePlanCount} 个房价计划，无法删除");
                }

                $channelName = $otaChannel->name;
                $otaChannel->delete();

                $this->logInfo('删除OTA渠道成功', [
                    'ota_channel_id' => $id,
                    'name' => $channelName
                ]);

                return $this->success(null, '删除OTA渠道成功');
            });

        } catch (\Exception $e) {
            $this->logError('删除OTA渠道失败', ['id' => $id, 'error' => $e->getMessage()]);
            return $this->error('删除OTA渠道失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作OTA渠道
     *
     * @param array $data
     * @return array
     */
    public function batchOtaChannels(array $data)
    {
        try {
            $this->validateRequired($data, ['action', 'ids']);

            $action = $data['action'];
            $ids = $data['ids'];

            if (empty($ids) || !is_array($ids)) {
                throw new \Exception('请选择要操作的渠道');
            }

            return $this->transaction(function () use ($action, $ids) {
                $otaChannels = OtaChannel::whereIn('id', $ids)->get();
                
                if ($otaChannels->isEmpty()) {
                    throw new \Exception('未找到要操作的渠道');
                }

                $results = [];
                foreach ($otaChannels as $otaChannel) {
                    switch ($action) {
                        case 'activate':
                            $otaChannel->update(['is_active' => true]);
                            $results[] = "渠道 {$otaChannel->name} 已启用";
                            break;
                        case 'deactivate':
                            $otaChannel->update(['is_active' => false]);
                            $results[] = "渠道 {$otaChannel->name} 已禁用";
                            break;
                        case 'delete':
                            // 检查关联
                            if ($otaChannel->ratePlans()->count() > 0) {
                                $results[] = "渠道 {$otaChannel->name} 有关联数据，跳过删除";
                            } else {
                                $otaChannel->delete();
                                $results[] = "渠道 {$otaChannel->name} 已删除";
                            }
                            break;
                        default:
                            throw new \Exception('不支持的操作类型');
                    }
                }

                $this->logInfo('批量操作OTA渠道成功', [
                    'action' => $action,
                    'ids' => $ids,
                    'results' => $results
                ]);

                return $this->success(['results' => $results], '批量操作完成');
            });

        } catch (\Exception $e) {
            $this->logError('批量操作OTA渠道失败', ['data' => $data, 'error' => $e->getMessage()]);
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 切换OTA渠道状态
     *
     * @param int $id
     * @return array
     */
    public function toggleOtaChannelStatus(int $id)
    {
        try {
            $otaChannel = OtaChannel::find($id);
            if (!$otaChannel) {
                return $this->error('OTA渠道不存在', 404);
            }

            $newStatus = !$otaChannel->is_active;
            $otaChannel->update(['is_active' => $newStatus]);

            $statusText = $newStatus ? '启用' : '禁用';
            
            $this->logInfo('切换OTA渠道状态成功', [
                'ota_channel_id' => $id,
                'name' => $otaChannel->name,
                'new_status' => $newStatus
            ]);

            return $this->success($otaChannel, "渠道已{$statusText}");

        } catch (\Exception $e) {
            $this->logError('切换OTA渠道状态失败', ['id' => $id, 'error' => $e->getMessage()]);
            return $this->error('状态切换失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取OTA渠道选项
     *
     * @return array
     */
    public function getOtaChannelOptions()
    {
        try {
            $otaChannels = OtaChannel::active()
                ->orderBy('priority', 'desc')
                ->select(['id', 'name', 'code', 'commission_rate', 'settlement_method'])
                ->get();

            return $otaChannels->map(function ($channel) {
                return [
                    'value' => $channel->id,
                    'label' => $channel->name,
                    'code' => $channel->code,
                    'commission_rate' => $channel->commission_rate,
                    'settlement_method' => $channel->settlement_method,
                ];
            })->toArray();

        } catch (\Exception $e) {
            $this->logError('获取OTA渠道选项失败', ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
