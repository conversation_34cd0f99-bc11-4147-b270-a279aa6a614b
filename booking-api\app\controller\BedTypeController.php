<?php

namespace app\controller;

use app\service\BedTypeService;
use support\Request;
use support\Response;

/**
 * 床型类型管理控制器
 */
class BedTypeController extends BaseController
{
    /**
     * 床型类型服务
     *
     * @var BedTypeService
     */
    protected $bedTypeService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->bedTypeService = new BedTypeService();
    }

    /**
     * 获取床型类型列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $result = $this->bedTypeService->getBedTypeList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取床型类型列表');
        }
    }

    /**
     * 获取床型类型详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $id = (int)$id;
            
            if ($id <= 0) {
                return $this->error('床型类型ID无效');
            }

            $locale = $request->get('locale', 'zh-CN');
            $result = $this->bedTypeService->getBedTypeDetail($id, $locale);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取床型类型详情');
        }
    }

    /**
     * 创建床型类型
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['code', 'name', 'category']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'code' => 'string|max:50',
                'name' => 'string|max:100',
                'category' => 'string|in:standard,sofa,tatami,other',
                'size' => 'string|in:single,double,queen,king,twin',
                'width' => 'integer|min:0',
                'length' => 'integer|min:0',
                'max_occupancy' => 'integer|min:1|max:10',
                'is_standard' => 'boolean',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->bedTypeService->createBedType($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建床型类型');
        }
    }

    /**
     * 更新床型类型
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $id = (int)$id;
            
            if ($id <= 0) {
                return $this->error('床型类型ID无效');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'code' => 'string|max:50',
                'name' => 'string|max:100',
                'category' => 'string|in:standard,sofa,tatami,other',
                'size' => 'string|in:single,double,queen,king,twin',
                'width' => 'integer|min:0',
                'length' => 'integer|min:0',
                'max_occupancy' => 'integer|min:1|max:10',
                'is_standard' => 'boolean',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->bedTypeService->updateBedType($id, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新床型类型');
        }
    }

    /**
     * 删除床型类型
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $id = (int)$id;
            
            if ($id <= 0) {
                return $this->error('床型类型ID无效');
            }

            $result = $this->bedTypeService->deleteBedType($id);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除床型类型');
        }
    }

    /**
     * 获取床型类型选项（用于下拉选择）
     *
     * @param Request $request
     * @return Response
     */
    public function options(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $result = $this->bedTypeService->getBedTypeOptions($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取床型类型选项');
        }
    }

    /**
     * 批量更新床型类型状态
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdateStatus(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['ids', 'is_active']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['ids']) || empty($data['ids'])) {
                return $this->error('ids必须是非空数组');
            }

            $result = $this->bedTypeService->batchUpdateStatus($data['ids'], $data['is_active']);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新状态');
        }
    }

    /**
     * 获取床型类型统计信息
     *
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request)
    {
        try {
            $result = $this->bedTypeService->getBedTypeStatistics();
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取统计信息');
        }
    }

    /**
     * 获取床型分类列表
     *
     * @param Request $request
     * @return Response
     */
    public function categories(Request $request)
    {
        try {
            $categories = \app\model\BedType::getCategories();
            
            $result = [];
            foreach ($categories as $code => $name) {
                $result[] = [
                    'value' => $code,
                    'label' => $name,
                ];
            }

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取床型分类');
        }
    }

    /**
     * 获取床型尺寸列表
     *
     * @param Request $request
     * @return Response
     */
    public function sizes(Request $request)
    {
        try {
            $sizes = \app\model\BedType::getSizes();
            
            $result = [];
            foreach ($sizes as $code => $name) {
                $result[] = [
                    'value' => $code,
                    'label' => $name,
                ];
            }

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取床型尺寸');
        }
    }

    /**
     * 获取支持的语言列表
     *
     * @param Request $request
     * @return Response
     */
    public function locales(Request $request)
    {
        try {
            $locales = \app\model\BedTypeTranslation::getSupportedLocales();
            
            $result = [];
            foreach ($locales as $code => $name) {
                $result[] = [
                    'value' => $code,
                    'label' => $name,
                ];
            }

            return $this->success($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取支持语言');
        }
    }
}
