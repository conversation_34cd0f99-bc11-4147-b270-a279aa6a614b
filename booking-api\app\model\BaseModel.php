<?php

namespace app\model;

use support\Model;

/**
 * 基础模型类
 * 提供通用的模型功能和约定
 */
abstract class BaseModel extends Model
{
    /**
     * 指示模型是否应该被时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 时间戳字段名
     *
     * @var array
     */
    protected $dates = ['created_at', 'updated_at'];

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [];

    /**
     * 不可批量赋值的属性
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取格式化的创建时间
     *
     * @return string
     */
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : null;
    }

    /**
     * 获取格式化的更新时间
     *
     * @return string
     */
    public function getFormattedUpdatedAtAttribute()
    {
        return $this->updated_at ? $this->updated_at->format('Y-m-d H:i:s') : null;
    }

    /**
     * 作用域：活跃状态
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：非活跃状态
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }
}
