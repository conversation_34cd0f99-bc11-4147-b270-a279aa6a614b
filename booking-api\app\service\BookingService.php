<?php

namespace app\service;

use app\model\Hotel;
use app\model\RoomType;
use app\model\RatePlan;
use app\model\Booking;
use app\model\RoomInventory;
use app\model\RoomRate;
use app\model\InventoryLog;

/**
 * 订单管理服务
 */
class BookingService extends BaseService
{
    /**
     * 获取订单列表
     *
     * @param array $params
     * @return array
     */
    public function getBookingList(array $params = [])
    {
        try {
            $query = Booking::with(['hotel', 'roomType', 'ratePlan', 'user']);

            // 酒店筛选
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            // 状态筛选
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 支付状态筛选
            if (!empty($params['payment_status'])) {
                $query->where('payment_status', $params['payment_status']);
            }

            // 入住日期筛选
            if (!empty($params['check_in_date'])) {
                $query->where('check_in_date', $params['check_in_date']);
            }

            // 入住日期范围筛选
            if (!empty($params['check_in_start']) && !empty($params['check_in_end'])) {
                $query->whereBetween('check_in_date', [$params['check_in_start'], $params['check_in_end']]);
            }

            // 客人信息筛选
            if (!empty($params['guest_name'])) {
                $query->where('guest_name', 'like', "%{$params['guest_name']}%");
            }

            if (!empty($params['guest_phone'])) {
                $query->where('guest_phone', 'like', "%{$params['guest_phone']}%");
            }

            // 订单号筛选
            if (!empty($params['booking_no'])) {
                $query->where('booking_no', 'like', "%{$params['booking_no']}%");
            }

            // 渠道筛选
            if (!empty($params['source_channel'])) {
                $query->where('source_channel', $params['source_channel']);
            }

            // 排序
            $sortBy = $params['sort_by'] ?? 'created_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            $query->orderBy($sortBy, $sortOrder);

            // 分页
            $page = $params['page'] ?? 1;
            $limit = $params['limit'] ?? 20;

            $result = $this->paginate($query, $page, $limit);

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('获取订单列表失败', ['error' => $e->getMessage(), 'params' => $params]);
            return $this->error('获取订单列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取订单详情
     *
     * @param int $bookingId
     * @return array
     */
    public function getBookingDetail(int $bookingId)
    {
        try {
            $booking = Booking::with(['hotel', 'roomType', 'ratePlan', 'user', 'logs'])
                ->find($bookingId);

            if (!$booking) {
                return $this->error('订单不存在', 404);
            }

            return $this->success($booking);

        } catch (\Exception $e) {
            $this->logError('获取订单详情失败', ['booking_id' => $bookingId, 'error' => $e->getMessage()]);
            return $this->error('获取订单详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建订单
     *
     * @param array $data
     * @return array
     */
    public function createBooking(array $data)
    {
        try {
            $this->validateRequired($data, [
                'hotel_id', 'room_type_id', 'rate_plan_id',
                'check_in_date', 'check_out_date',
                'rooms', 'adults', 'guest_name', 'guest_phone'
            ]);

            $this->validateDateRange($data['check_in_date'], $data['check_out_date']);

            // 计算入住天数
            $checkInDate = new \DateTime($data['check_in_date']);
            $checkOutDate = new \DateTime($data['check_out_date']);
            $nights = $checkInDate->diff($checkOutDate)->days;

            if ($nights <= 0) {
                throw new \InvalidArgumentException('入住天数必须大于0');
            }

            return $this->transaction(function () use ($data, $nights) {
                // 验证酒店、房型、价格计划是否存在
                $hotel = Hotel::find($data['hotel_id']);
                if (!$hotel) {
                    throw new \Exception('酒店不存在');
                }

                $roomType = RoomType::where('hotel_id', $data['hotel_id'])
                    ->find($data['room_type_id']);
                if (!$roomType) {
                    throw new \Exception('房型不存在');
                }

                $ratePlan = RatePlan::where('hotel_id', $data['hotel_id'])
                    ->find($data['rate_plan_id']);
                if (!$ratePlan) {
                    throw new \Exception('价格计划不存在');
                }

                // 检查库存
                $inventoryService = new InventoryService();
                $inventoryResult = $inventoryService->queryInventory([
                    'hotel_id' => $data['hotel_id'],
                    'room_type_id' => $data['room_type_id'],
                    'start_date' => $data['check_in_date'],
                    'end_date' => date('Y-m-d', strtotime($data['check_out_date'] . ' -1 day')),
                    'available_only' => true
                ]);

                if ($inventoryResult['code'] !== 0) {
                    throw new \Exception('查询库存失败');
                }

                $inventory = $inventoryResult['data'];
                $rooms = (int)$data['rooms'];

                foreach ($inventory as $item) {
                    if ($item['available_rooms'] < $rooms) {
                        throw new \Exception("日期 {$item['date']} 库存不足");
                    }
                }

                // 计算价格
                $totalRoomPrice = 0;
                $rateService = new RateService();
                $ratesResult = $rateService->queryRates([
                    'hotel_id' => $data['hotel_id'],
                    'room_type_id' => $data['room_type_id'],
                    'rate_plan_id' => $data['rate_plan_id'],
                    'start_date' => $data['check_in_date'],
                    'end_date' => date('Y-m-d', strtotime($data['check_out_date'] . ' -1 day'))
                ]);

                if ($ratesResult['code'] !== 0) {
                    throw new \Exception('查询价格失败');
                }

                $rates = $ratesResult['data'];
                foreach ($rates as $rate) {
                    $totalRoomPrice += $rate['selling_price'] * $rooms;
                }

                // 创建订单
                $bookingData = array_merge($data, [
                    'booking_no' => Booking::generateBookingNo(),
                    'nights' => $nights,
                    'room_price' => $this->formatAmount($totalRoomPrice),
                    'service_fee' => $this->formatAmount($data['service_fee'] ?? 0),
                    'tax_fee' => $this->formatAmount($data['tax_fee'] ?? 0),
                    'discount_amount' => $this->formatAmount($data['discount_amount'] ?? 0),
                    'currency' => $data['currency'] ?? 'CNY',
                    'source_channel' => $data['source_channel'] ?? 'api',
                    'status' => Booking::STATUS_PENDING,
                    'payment_status' => Booking::PAYMENT_STATUS_UNPAID,
                    'paid_amount' => 0
                ]);

                $booking = Booking::create($bookingData);
                $booking->updateTotalAmount();

                // 扣减库存
                $this->deductInventory($booking);

                $this->logInfo('创建订单成功', [
                    'booking_id' => $booking->id,
                    'booking_no' => $booking->booking_no,
                    'hotel_id' => $booking->hotel_id,
                    'total_amount' => $booking->total_amount
                ]);

                return $this->success($booking, '创建订单成功');
            });

        } catch (\Exception $e) {
            $this->logError('创建订单失败', ['error' => $e->getMessage(), 'data' => $data]);
            return $this->error('创建订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认订单
     *
     * @param int $bookingId
     * @param array $data
     * @return array
     */
    public function confirmBooking(int $bookingId, array $data = [])
    {
        try {
            $booking = Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在', 404);
            }

            if ($booking->status !== Booking::STATUS_PENDING) {
                return $this->error('订单状态不允许确认');
            }

            return $this->transaction(function () use ($booking, $data) {
                $confirmationNo = $data['confirmation_no'] ?? null;
                $confirmedBy = $this->getCurrentUser()->id ?? null;

                $booking->confirm($confirmationNo, $confirmedBy);

                $this->logInfo('确认订单成功', [
                    'booking_id' => $booking->id,
                    'booking_no' => $booking->booking_no,
                    'confirmation_no' => $booking->confirmation_no
                ]);

                return $this->success($booking, '确认订单成功');
            });

        } catch (\Exception $e) {
            $this->logError('确认订单失败', ['booking_id' => $bookingId, 'error' => $e->getMessage()]);
            return $this->error('确认订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 取消订单
     *
     * @param int $bookingId
     * @param array $data
     * @return array
     */
    public function cancelBooking(int $bookingId, array $data = [])
    {
        try {
            $booking = Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在', 404);
            }

            if (!$booking->canCancel()) {
                return $this->error('订单当前状态不允许取消');
            }

            return $this->transaction(function () use ($booking, $data) {
                $reason = $data['reason'] ?? '客人主动取消';
                $cancelledBy = $this->getCurrentUser()->id ?? null;

                $booking->cancel($reason, $cancelledBy);

                // 释放库存
                $this->releaseInventory($booking);

                $this->logInfo('取消订单成功', [
                    'booking_id' => $booking->id,
                    'booking_no' => $booking->booking_no,
                    'reason' => $reason
                ]);

                return $this->success($booking, '取消订单成功');
            });

        } catch (\Exception $e) {
            $this->logError('取消订单失败', ['booking_id' => $bookingId, 'error' => $e->getMessage()]);
            return $this->error('取消订单失败: ' . $e->getMessage());
        }
    }

    /**
     * 办理入住
     *
     * @param int $bookingId
     * @param array $data
     * @return array
     */
    public function checkInBooking(int $bookingId, array $data = [])
    {
        try {
            $booking = Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在', 404);
            }

            if (!$booking->canCheckIn()) {
                return $this->error('订单当前状态不允许入住');
            }

            return $this->transaction(function () use ($booking, $data) {
                $roomNumbers = $data['room_numbers'] ?? null;
                $booking->checkIn($roomNumbers);

                $this->logInfo('办理入住成功', [
                    'booking_id' => $booking->id,
                    'booking_no' => $booking->booking_no,
                    'room_numbers' => $roomNumbers
                ]);

                return $this->success($booking, '办理入住成功');
            });

        } catch (\Exception $e) {
            $this->logError('办理入住失败', ['booking_id' => $bookingId, 'error' => $e->getMessage()]);
            return $this->error('办理入住失败: ' . $e->getMessage());
        }
    }

    /**
     * 办理退房
     *
     * @param int $bookingId
     * @param array $data
     * @return array
     */
    public function checkOutBooking(int $bookingId, array $data = [])
    {
        try {
            $booking = Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在', 404);
            }

            if (!$booking->canCheckOut()) {
                return $this->error('订单当前状态不允许退房');
            }

            return $this->transaction(function () use ($booking, $data) {
                $booking->checkOut();

                $this->logInfo('办理退房成功', [
                    'booking_id' => $booking->id,
                    'booking_no' => $booking->booking_no
                ]);

                return $this->success($booking, '办理退房成功');
            });

        } catch (\Exception $e) {
            $this->logError('办理退房失败', ['booking_id' => $bookingId, 'error' => $e->getMessage()]);
            return $this->error('办理退房失败: ' . $e->getMessage());
        }
    }

    /**
     * 扣减库存
     *
     * @param Booking $booking
     */
    private function deductInventory(Booking $booking)
    {
        $startDate = $booking->check_in_date->toDateString();
        $endDate = $booking->check_out_date->subDay()->toDateString();
        
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $inventory = RoomInventory::where('hotel_id', $booking->hotel_id)
                ->where('room_type_id', $booking->room_type_id)
                ->where('date', $currentDate)
                ->first();

            if ($inventory) {
                $beforeAvailable = $inventory->available_rooms;
                $inventory->bookRooms($booking->rooms);

                // 记录库存变更日志
                InventoryLog::logBookingChange(
                    $booking->hotel_id,
                    $booking->room_type_id,
                    $currentDate,
                    $beforeAvailable,
                    $inventory->available_rooms,
                    $booking->id
                );
            }

            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
        }
    }

    /**
     * 释放库存
     *
     * @param Booking $booking
     */
    private function releaseInventory(Booking $booking)
    {
        $startDate = $booking->check_in_date->toDateString();
        $endDate = $booking->check_out_date->subDay()->toDateString();
        
        $currentDate = $startDate;
        while ($currentDate <= $endDate) {
            $inventory = RoomInventory::where('hotel_id', $booking->hotel_id)
                ->where('room_type_id', $booking->room_type_id)
                ->where('date', $currentDate)
                ->first();

            if ($inventory) {
                $beforeAvailable = $inventory->available_rooms;
                $inventory->cancelBooking($booking->rooms);

                // 记录库存变更日志
                InventoryLog::logCancellationChange(
                    $booking->hotel_id,
                    $booking->room_type_id,
                    $currentDate,
                    $beforeAvailable,
                    $inventory->available_rooms,
                    $booking->id
                );
            }

            $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
        }
    }
}
