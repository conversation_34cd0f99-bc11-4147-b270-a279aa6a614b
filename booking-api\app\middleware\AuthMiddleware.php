<?php

namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Firebase\JWT\BeforeValidException;
use app\model\User;
use base\auth\JwtService;

/**
 * SSO统一认证中间件
 * 兼容IAM系统的JWT token验证
 */
class AuthMiddleware implements MiddlewareInterface
{
    /**
     * JWT密钥配置 - 从环境变量获取，与IAM系统保持一致
     */
    private static function getJwtSecret(): string
    {
        return config('app.jwt_secret', 'your-jwt-secret-key-change-in-production');
    }

    /**
     * JWT算法
     */
    private const JWT_ALGORITHM = 'HS256';

    /**
     * 处理请求
     *
     * @param Request $request
     * @param callable $handler
     * @return Response
     */
    public function process(Request $request, callable $handler): Response
    {
        // 开发环境下暂时跳过认证，生产环境需要启用
        if (config('app.debug', false)) {
            // 在开发环境下创建一个模拟用户
            $mockUser = new \stdClass();
            $mockUser->id = 1;
            $mockUser->username = 'admin';
            $mockUser->user_type = 'admin';
            $mockUser->status = 'active';
            $request->user = $mockUser;
            return $handler($request);
        }

        // 获取Authorization头
        $authorization = $request->header('Authorization');

        if (!$authorization) {
            return $this->unauthorizedResponse('缺少Authorization头');
        }

        // 检查Bearer token格式
        if (!preg_match('/^Bearer\s+(.+)$/', $authorization, $matches)) {
            return $this->unauthorizedResponse('Authorization头格式错误');
        }

        $token = $matches[1];

        try {
            // 使用IAM系统兼容的token验证
            $userInfo = $this->verifyIamToken($token);

            // 将标准化的用户信息添加到请求中
            $request->user = $this->createUserObject($userInfo);
            $request->userInfo = $userInfo;

        } catch (ExpiredException $e) {
            return $this->unauthorizedResponse('Token已过期');
        } catch (SignatureInvalidException $e) {
            return $this->unauthorizedResponse('Token签名无效');
        } catch (BeforeValidException $e) {
            return $this->unauthorizedResponse('Token尚未生效');
        } catch (\Exception $e) {
            return $this->unauthorizedResponse('Token验证失败: ' . $e->getMessage());
        }

        return $handler($request);
    }

    /**
     * 验证IAM系统的JWT token
     *
     * @param string $token
     * @return array
     * @throws \Exception
     */
    private function verifyIamToken(string $token): array
    {
        try {
            // 使用统一的JwtService验证token
            $payload = JwtService::verifyAccessToken($token);

            // 标准化用户信息字段，兼容IAM系统的字段名
            return [
                'userId' => $payload['userId'] ?? $payload['user_id'] ?? null,
                'username' => $payload['username'] ?? null,
                'realName' => $payload['realName'] ?? $payload['real_name'] ?? $payload['username'],
                'tenantId' => $payload['tenantId'] ?? $payload['tenant_id'] ?? 'default',
                'tenantName' => $payload['tenantName'] ?? $payload['tenant_name'] ?? '默认租户',
                'email' => $payload['email'] ?? null,
                'roles' => $payload['roles'] ?? ['user'],
                'jti' => $payload['jti'] ?? null,
                'iss' => $payload['iss'] ?? null,
                'exp' => $payload['exp'] ?? null,
            ];

        } catch (\Exception $e) {
            throw new \Exception('Token验证失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建标准化的用户对象
     *
     * @param array $userInfo
     * @return \stdClass
     */
    private function createUserObject(array $userInfo): \stdClass
    {
        $user = new \stdClass();
        $user->id = $userInfo['userId'];
        $user->user_id = $userInfo['userId'];
        $user->username = $userInfo['username'];
        $user->real_name = $userInfo['realName'];
        $user->email = $userInfo['email'];
        $user->tenant_id = $userInfo['tenantId'];
        $user->tenant_name = $userInfo['tenantName'];
        $user->roles = $userInfo['roles'];
        $user->status = 'active'; // IAM验证通过的用户默认为活跃状态
        $user->user_type = in_array('admin', $userInfo['roles']) ? 'admin' : 'user';

        return $user;
    }

    /**
     * 验证token（兼容IAM系统）
     *
     * @param string $token
     * @return array|null
     */
    public static function validateToken(string $token): ?array
    {
        try {
            $decoded = JWT::decode($token, new Key(self::getJwtSecret(), self::JWT_ALGORITHM));
            return (array)$decoded;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 从token中获取用户ID（兼容IAM系统）
     *
     * @param string $token
     * @return string|null
     */
    public static function getUserIdFromToken(string $token): ?string
    {
        $data = self::validateToken($token);
        return $data['userId'] ?? $data['user_id'] ?? null;
    }

    /**
     * 返回未授权响应
     *
     * @param string $message
     * @return Response
     */
    private function unauthorizedResponse(string $message): Response
    {
        return json([
            'code' => 401,
            'message' => $message,
            'timestamp' => time()
        ], 401);
    }

    /**
     * 获取JWT配置（兼容IAM系统）
     *
     * @return array
     */
    public static function getJwtConfig(): array
    {
        return [
            'secret' => self::getJwtSecret(),
            'algorithm' => self::JWT_ALGORITHM,
            'expire' => 3600 // 1小时，与IAM系统保持一致
        ];
    }

    /**
     * 检查token是否即将过期
     *
     * @param string $token
     * @param int $threshold 阈值（秒）
     * @return bool
     */
    public static function isTokenExpiringSoon(string $token, int $threshold = 3600): bool
    {
        $data = self::validateToken($token);
        if (!$data) {
            return true;
        }

        $exp = $data['exp'] ?? 0;
        return ($exp - time()) <= $threshold;
    }

    /**
     * 获取token剩余有效时间
     *
     * @param string $token
     * @return int 剩余秒数，-1表示无效token
     */
    public static function getTokenRemainingTime(string $token): int
    {
        $data = self::validateToken($token);
        if (!$data) {
            return -1;
        }

        $exp = $data['exp'] ?? 0;
        return max(0, $exp - time());
    }

    /**
     * 撤销token（添加到黑名单）
     * 注意：这需要配合Redis或数据库实现token黑名单
     *
     * @param string $token
     * @return bool
     */
    public static function revokeToken(string $token): bool
    {
        // 这里应该将token添加到黑名单
        // 可以使用Redis存储，key为token的jti或者token本身的hash
        // 暂时返回true，实际项目中需要实现
        return true;
    }

    /**
     * 检查token是否在黑名单中
     *
     * @param string $token
     * @return bool
     */
    public static function isTokenRevoked(string $token): bool
    {
        // 这里应该检查token是否在黑名单中
        // 暂时返回false，实际项目中需要实现
        return false;
    }
}
