<?php

namespace app\model;

use support\Model;

/**
 * 酒店设施模型
 */
class HotelFacility extends Model
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'hotel_facilities';

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'facility_id', // 关联标准设施ID
        'is_available',
        'custom_charge_amount',
        'custom_operating_hours',
        'custom_capacity',
        'location',
        'booking_required',
        'advance_booking_hours',
        'special_notes',
        'images',
        'sort_order',
    ];

    /**
     * 属性类型转换
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'facility_id' => 'integer',
        'is_available' => 'boolean',
        'custom_charge_amount' => 'decimal:2',
        'custom_operating_hours' => 'array',
        'custom_capacity' => 'integer',
        'booking_required' => 'boolean',
        'advance_booking_hours' => 'integer',
        'images' => 'array',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 设施分类定义
     */
    const CATEGORIES = [
        'basic' => '基础设施',
        'dining' => '餐饮设施',
        'entertainment' => '娱乐设施',
        'business' => '商务设施',
        'sports' => '运动设施',
        'health' => '健康美容',
        'fitness' => '健身设施',
        'recreation' => '休闲设施',
        'children' => '儿童设施',
        'transport' => '交通服务',
        'service' => '服务设施',
        'safety' => '安全设施',
        'accessibility' => '无障碍设施',
        'shopping' => '购物设施',
        'policy' => '政策规定',
        'other' => '其他设施',
    ];

    /**
     * 收费类型定义
     */
    const FEE_TYPES = [
        'one_time' => '一次性',
        'per_day' => '每天',
        'per_hour' => '每小时',
        'per_use' => '每次使用',
    ];

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id');
    }

    /**
     * 关联设施模板
     */
    public function facility()
    {
        return $this->belongsTo(FacilityTemplate::class, 'facility_id');
    }

    /**
     * 关联设施模板（兼容旧版本）
     */
    public function template()
    {
        return $this->belongsTo(FacilityTemplate::class, 'facility_id');
    }

    /**
     * 获取分类名称
     */
    public function getCategoryNameAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? $this->category;
    }

    /**
     * 获取收费类型名称
     */
    public function getFeeTypeNameAttribute(): string
    {
        return self::FEE_TYPES[$this->fee_type] ?? $this->fee_type;
    }

    /**
     * 获取显示名称（从关联的标准设施获取）
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->facility ? $this->facility->name : '未知设施';
    }

    /**
     * 作用域：可用的设施
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * 作用域：按分类筛选（通过关联的标准设施）
     */
    public function scopeByCategory($query, $categoryId)
    {
        if ($categoryId) {
            return $query->whereHas('facility', function ($q) use ($categoryId) {
                $q->where('category_id', $categoryId);
            });
        }
        return $query;
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取酒店的所有设施按分类分组
     */
    public static function getByHotelGrouped(int $hotelId): array
    {
        $facilities = self::byHotel($hotelId)
            ->with(['facility'])
            ->where('is_available', true)
            ->orderBy('sort_order', 'asc')
            ->orderBy('id', 'asc')
            ->get();

        $grouped = [];
        foreach ($facilities as $facility) {
            // 使用关联的标准设施的分类ID
            $categoryId = $facility->facility ? $facility->facility->category_id : 'unknown';
            $categoryName = '分类' . $categoryId; // 这里可以根据实际的分类表来获取名称

            if (!isset($grouped[$categoryId])) {
                $grouped[$categoryId] = [
                    'category_id' => $categoryId,
                    'category_name' => $categoryName,
                    'facilities' => [],
                ];
            }
            $grouped[$categoryId]['facilities'][] = $facility;
        }

        return array_values($grouped);
    }

    /**
     * 批量创建设施
     */
    public static function batchCreate(int $hotelId, array $facilities): array
    {
        $created = [];
        $errors = [];

        foreach ($facilities as $facilityData) {
            try {
                $facilityData['hotel_id'] = $hotelId;
                $facility = self::create($facilityData);
                $created[] = $facility;
            } catch (\Exception $e) {
                $facilityName = $facilityData['facility_id'] ?? '未知设施';
                $errors[] = "创建设施失败: 设施ID {$facilityName} - {$e->getMessage()}";
            }
        }

        return [
            'created' => $created,
            'errors' => $errors,
        ];
    }

    /**
     * 从模板同步设施
     */
    public static function syncFromTemplate(int $hotelId, int $templateId, array $options = []): bool
    {
        try {
            $template = FacilityTemplate::find($templateId);
            if (!$template) {
                return false;
            }

            // 检查是否已存在
            $existing = self::where('hotel_id', $hotelId)
                ->where('facility_id', $templateId)
                ->first();

            if ($existing) {
                return false; // 已存在，不重复添加
            }

            $facilityData = [
                'hotel_id' => $hotelId,
                'facility_id' => $templateId,
                'is_available' => $options['is_available'] ?? true,
                'custom_charge_amount' => $options['custom_charge_amount'] ?? null,
                'location' => $options['location'] ?? null,
                'sort_order' => $options['sort_order'] ?? 999,
            ];

            self::create($facilityData);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 更新显示顺序
     */
    public static function updateDisplayOrder(int $hotelId, array $orders): bool
    {
        try {
            foreach ($orders as $order) {
                self::where('hotel_id', $hotelId)
                    ->where('id', $order['id'])
                    ->update(['sort_order' => $order['order']]);
            }
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
