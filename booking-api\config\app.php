<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use support\Request;

return [
    'debug'             => env('APP_DEBUG', false),
    'error_reporting'   => E_ALL,
    'default_timezone'  => 'Asia/Shanghai',
    'request_class'     => Request::class,
    'public_path'       => base_path() . DIRECTORY_SEPARATOR . 'public',
    'runtime_path'      => base_path(false) . DIRECTORY_SEPARATOR . 'runtime',
    'controller_suffix' => '',
    'controller_reuse'  => false,
    'url'               => env('APP_URL', 'http://localhost:8787'),

    // JWT配置 - 与IAM系统保持一致
    'jwt_secret'        => env('JWT_SECRET', 'your-jwt-secret-key-change-in-production'),
    'jwt_algorithm'     => env('JWT_ALGORITHM', 'HS256'),
    'jwt_expire'        => env('JWT_EXPIRE', 3600), // 1小时

    // SSO配置
    'sso_base_url'      => env('SSO_BASE_URL', 'http://localhost:5667'),
    'sso_client_id'     => env('SSO_CLIENT_ID', 'booking'),
    'sso_client_secret' => env('SSO_CLIENT_SECRET', ''),

    // IAM系统配置
    'iam_admin_url'     => env('IAM_ADMIN_URL', 'http://localhost:5173'),
];
