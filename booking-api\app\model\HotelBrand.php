<?php

namespace app\model;

/**
 * 酒店品牌模型
 * 对应数据库表：hotel_brands
 */
class HotelBrand extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'hotel_brands';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'group_id',
        'parent_id',
        'code',
        'name',
        'name_en',
        'short_name',
        'description',
        'description_en',

        // 品牌定位
        'brand_positioning',
        'target_market',
        'price_range',
        'star_rating',

        // 品牌特色
        'brand_features',
        'service_standards',
        'amenities',

        // 视觉识别
        'logo_url',
        'brand_color_primary',
        'brand_color_secondary',
        'brand_font',
        'brand_slogan',
        'brand_story',

        // 联系信息
        'contact_person',
        'contact_phone',
        'contact_email',
        'website',

        // 地理信息
        'origin_country',
        'operating_regions',

        // 业务信息
        'established_year',
        'business_model',
        'hotel_count',
        'room_count',

        // 市场表现
        'market_share',
        'growth_rate',
        'customer_satisfaction',
        'brand_value',
        'currency',

        // 认证和奖项
        'certifications',
        'awards',
        'quality_standards',

        // 状态信息
        'status',
        'is_public',
        'is_verified',
        'is_featured',
        'verified_at',
        'verified_by',

        // 排序和权重
        'sort_order',
        'level',
        'path',

        'created_by',
        'updated_by',

        // 兼容旧字段
        'logo',
        'star_level',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'group_id' => 'integer',
        'parent_id' => 'integer',
        'star_rating' => 'decimal:1',
        'brand_features' => 'array',
        'service_standards' => 'array',
        'amenities' => 'array',
        'operating_regions' => 'array',
        'established_year' => 'integer',
        'hotel_count' => 'integer',
        'room_count' => 'integer',
        'market_share' => 'decimal:2',
        'growth_rate' => 'decimal:2',
        'customer_satisfaction' => 'decimal:2',
        'brand_value' => 'decimal:2',
        'certifications' => 'array',
        'awards' => 'array',
        'quality_standards' => 'array',
        'is_public' => 'boolean',
        'is_verified' => 'boolean',
        'is_featured' => 'boolean',
        'verified_at' => 'datetime',
        'verified_by' => 'integer',
        'sort_order' => 'integer',
        'level' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',

        // 兼容旧字段
        'star_level' => 'integer',
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 获取所属集团
     */
    public function group()
    {
        return $this->belongsTo(HotelGroup::class, 'group_id');
    }

    /**
     * 获取品牌下的酒店
     */
    public function hotels()
    {
        return $this->hasMany(Hotel::class, 'brand_id');
    }

    /**
     * 获取活跃的酒店
     */
    public function activeHotels()
    {
        return $this->hotels()->active();
    }

    /**
     * 获取父品牌
     */
    public function parent()
    {
        return $this->belongsTo(HotelBrand::class, 'parent_id');
    }

    /**
     * 获取子品牌
     */
    public function children()
    {
        return $this->hasMany(HotelBrand::class, 'parent_id');
    }

    /**
     * 获取多语言翻译
     */
    public function translations()
    {
        return $this->hasMany(HotelBrandTranslation::class);
    }

    /**
     * 获取统计信息
     */
    public function statistics()
    {
        return $this->hasOne(HotelBrandStatistics::class);
    }

    /**
     * 作用域：按星级筛选
     */
    public function scopeByStarLevel($query, $starLevel)
    {
        return $query->where('star_level', $starLevel);
    }

    /**
     * 获取酒店数量
     */
    public function getHotelCountAttribute()
    {
        return $this->hotels()->count();
    }

    /**
     * 获取星级显示
     */
    public function getStarDisplayAttribute()
    {
        return $this->star_level ? str_repeat('★', $this->star_level) : '未评级';
    }

    /**
     * 作用域：活跃的品牌
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：公开显示的品牌
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 作用域：已认证的品牌
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * 作用域：推荐品牌
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * 作用域：按品牌定位筛选
     */
    public function scopeByPositioning($query, $positioning)
    {
        return $query->where('brand_positioning', $positioning);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取所有启用的品牌（用于下拉选择）
     *
     * @param string $locale
     * @param int|null $groupId
     * @return array
     */
    public static function getActiveOptions($locale = 'zh-CN', $groupId = null)
    {
        $query = static::active()->public()->ordered();

        if ($groupId) {
            $query->where('group_id', $groupId);
        }

        $brands = $query->get();

        return $brands->map(function ($brand) use ($locale) {
            return [
                'value' => $brand->id,
                'label' => $brand->getLocalizedName($locale),
                'code' => $brand->code,
                'short_name' => $brand->short_name,
                'group_id' => $brand->group_id,
                'brand_positioning' => $brand->brand_positioning,
                'star_rating' => $brand->star_rating ?? $brand->star_level,
                'logo_url' => $brand->logo_url ?? $brand->logo,
                'is_verified' => $brand->is_verified,
                'is_featured' => $brand->is_featured,
            ];
        })->toArray();
    }

    /**
     * 获取指定语言的名称
     *
     * @param string $locale
     * @return string
     */
    public function getLocalizedName($locale = 'zh-CN')
    {
        if ($locale === 'zh-CN') {
            return $this->name;
        }

        if ($locale === 'en-US' && $this->name_en) {
            return $this->name_en;
        }

        return $this->name;
    }

    /**
     * 获取品牌定位列表
     *
     * @return array
     */
    public static function getBrandPositionings()
    {
        return [
            'luxury' => '奢华',
            'upscale' => '高档',
            'midscale' => '中档',
            'economy' => '经济',
            'budget' => '预算',
        ];
    }

    /**
     * 获取目标市场列表
     *
     * @return array
     */
    public static function getTargetMarkets()
    {
        return [
            'business' => '商务',
            'leisure' => '休闲',
            'family' => '家庭',
            'boutique' => '精品',
        ];
    }

    /**
     * 获取价格区间列表
     *
     * @return array
     */
    public static function getPriceRanges()
    {
        return [
            'high' => '高价',
            'medium' => '中价',
            'low' => '低价',
        ];
    }
}
