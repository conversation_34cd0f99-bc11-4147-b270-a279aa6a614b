<?php

namespace app\controller;

use support\Request;
use support\Response;
use app\model\HotelImage;
use app\model\ImageCategory;

/**
 * 图片上传控制器
 */
class ImageUploadController   extends BaseController
{
    /**
     * 预上传图片（仅上传文件，不保存到数据库）
     */
    public function preUpload(Request $request): Response
    {
        try {
            // 获取上传的文件
            $file = $request->file('image');
            if (!$file || !$file->isValid()) {
                return $this->error('请选择要上传的图片文件', 400);
            }

            // 验证文件类型
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            $mimeType = $file->getUploadMimeType();
            if (!in_array($mimeType, $allowedTypes)) {
                return $this->error('只支持 JPEG、PNG、GIF、WebP 格式的图片', 400);
            }

            // 获取文件信息（在移动文件前）
            $originalName = $file->getUploadName();
            $fileSize = $file->getSize();
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);

            // 验证文件大小 (最大10MB)
            $maxSize = 10 * 1024 * 1024; // 10MB
            if ($fileSize > $maxSize) {
                return $this->error('图片文件大小不能超过10MB', 400);
            }

            // 创建临时上传目录
            $uploadDir = 'uploads/temp/' . date('Y/m/d');
            $publicPath = config('app.public_path', base_path() . '/public');
            $fullUploadDir = $publicPath . '/' . $uploadDir;
            if (!is_dir($fullUploadDir)) {
                mkdir($fullUploadDir, 0755, true);
            }

            // 生成文件名
            $fileName = uniqid() . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . '/' . $fileName;
            $fullFilePath = $fullUploadDir . '/' . $fileName;

            // 移动文件
            if ($file->move($fullFilePath) === false) {
                return $this->error('文件上传失败', 500);
            }

            // 获取图片尺寸（移动文件后）
            $imageInfo = getimagesize($fullFilePath);
            $width = $imageInfo[0] ?? 0;
            $height = $imageInfo[1] ?? 0;

            // 获取实际文件大小（如果之前获取失败）
            if (!$fileSize) {
                $fileSize = filesize($fullFilePath);
            }

            // 生成缩略图
            // $this->generateThumbnail($fullFilePath, $extension);

            // 生成完整URL
            $baseUrl = config('app.url', 'http://localhost:8890');
            $imageUrl = rtrim($baseUrl, '/') . '/' . ltrim($filePath, '/');
            $thumbnailUrl = rtrim($baseUrl, '/') . '/' . ltrim(str_replace('.' . $extension, '_thumb.' . $extension, $filePath), '/');

            return $this->success([
                'temp_id' => uniqid(), // 临时ID，用于后续绑定
                'image_url' => $imageUrl,
                'thumbnail_url' => $thumbnailUrl,
                'file_name' => $originalName,
                'file_size' => $fileSize,
                'file_type' => $mimeType,
                'width' => $width,
                'height' => $height,
                'temp_path' => $filePath, // 临时路径，用于后续移动文件
            ]);
        } catch (\Exception $e) {
            return $this->error('预上传失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 确认上传并绑定业务数据
     */
    public function confirmUpload(Request $request): Response
    {
        try {
            $hotelId = (int)$request->post('hotel_id');
            $categoryId = (int)$request->post('category_id');
            $parentCategoryId = (int)$request->post('parent_category_id', 0);
            $title = $request->post('title', '');
            $description = $request->post('description', '');
            $tempPath = $request->post('temp_path');
            $tempId = $request->post('temp_id');

            // 验证必填参数
            if (!$hotelId || !$categoryId || !$tempPath) {
                return $this->error('参数不完整', 400);
            }

            // 验证分类是否存在
            if (!ImageCategory::exists($categoryId)) {
                return $this->error('图片分类不存在', 400);
            }

            // 验证临时文件是否存在
            $publicPath = config('app.public_path', base_path() . '/public');
            $tempFilePath = $publicPath . '/' . ltrim($tempPath, '/');
            if (!file_exists($tempFilePath)) {
                return $this->error('临时文件不存在', 400);
            }

            // 创建正式上传目录
            $uploadDir = 'uploads/hotels/' . $hotelId . '/images/' . date('Y/m');
            $fullUploadDir = $publicPath . '/' . $uploadDir;
            if (!is_dir($fullUploadDir)) {
                mkdir($fullUploadDir, 0755, true);
            }

            // 移动文件到正式目录
            $fileName = basename($tempPath);
            $finalPath = $uploadDir . '/' . $fileName;
            $finalFilePath = $fullUploadDir . '/' . $fileName;

            if (!rename($tempFilePath, $finalFilePath)) {
                return $this->error('文件移动失败', 500);
            }

            // 移动缩略图
            $tempThumbnailPath = str_replace('.', '_thumb.', $tempFilePath);
            $finalThumbnailPath = str_replace('.', '_thumb.', $finalFilePath);
            if (file_exists($tempThumbnailPath)) {
                rename($tempThumbnailPath, $finalThumbnailPath);
            }

            // 获取图片信息
            $imageInfo = getimagesize($finalFilePath);
            $width = $imageInfo[0] ?? 0;
            $height = $imageInfo[1] ?? 0;
            $fileSize = filesize($finalFilePath);

            // 获取显示顺序
            $displayOrder = HotelImage::where('hotel_id', $hotelId)->max('sort_order') + 1;

            // 保存到数据库
            $imageData = [
                'hotel_id' => $hotelId,
                'image_url' => $finalPath,
                'image_title' => $title,
                'image_description' => $description,
                'image_category_id' => $categoryId,
                'sort_order' => $displayOrder,
                'status' => 'active',
            ];

            $image = HotelImage::create($imageData);

            // 如果是第一张图片，原本设置主图，现已移除

            return $this->success($image);
        } catch (\Exception $e) {
            return $this->error('确认上传失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 上传图片（原有方法，保持兼容性）
     */
    public function upload(Request $request): Response
    {
        try {
            $hotelId = (int)$request->post('hotel_id');
            $categoryId = (int)$request->post('category_id');
            $parentCategoryId = (int)$request->post('parent_category_id', 0);
            $title = $request->post('title', '');
            $description = $request->post('description', '');

            // 验证必填参数
            if (!$hotelId || !$categoryId) {
                return $this->error('酒店ID和分类ID不能为空', 400);
            }

            // 验证分类是否存在
            if (!ImageCategory::exists($categoryId)) {
                return $this->error('图片分类不存在', 400);
            }

            // 获取上传的文件
            $file = $request->file('image');
            if (!$file || !$file->isValid()) {
                return $this->error('请选择要上传的图片文件', 400);
            }

            // 验证文件类型
            $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            $mimeType = $file->getUploadMimeType();
            if (!in_array($mimeType, $allowedTypes)) {
                return $this->error('只支持 JPEG、PNG、GIF、WebP 格式的图片', 400);
            }

            // 验证文件大小 (最大10MB)
            $maxSize = 10 * 1024 * 1024; // 10MB
            if ($file->getSize() > $maxSize) {
                return $this->error('图片文件大小不能超过10MB', 400);
            }

            // 创建上传目录
            $uploadDir = 'uploads/hotels/' . $hotelId . '/images/' . date('Y/m');
            $publicPath = config('app.public_path', base_path() . '/public');
            $fullUploadDir = $publicPath . '/' . $uploadDir;
            if (!is_dir($fullUploadDir)) {
                mkdir($fullUploadDir, 0755, true);
            }

            // 生成文件名
            $originalName = $file->getUploadName();
            $extension = pathinfo($originalName, PATHINFO_EXTENSION);
            $fileName = uniqid() . '_' . time() . '.' . $extension;
            $filePath = $uploadDir . '/' . $fileName;
            $fullFilePath = $fullUploadDir . '/' . $fileName;

            // 移动文件
            if ($file->move($fullFilePath) === false) {
                return $this->error('文件上传失败', 500);
            }

            // 获取图片尺寸
            $imageInfo = getimagesize($fullFilePath);
            $width = $imageInfo[0] ?? 0;
            $height = $imageInfo[1] ?? 0;

            // 生成缩略图
            // $this->generateThumbnail($fullFilePath, $extension);

            // 获取显示顺序
            $displayOrder = HotelImage::where('hotel_id', $hotelId)->max('sort_order') + 1;

            // 保存到数据库
            $imageData = [
                'hotel_id' => $hotelId,
                'image_url' => $filePath,
                'image_title' => $title,
                'image_description' => $description,
                'image_category_id' => $categoryId,
                'sort_order' => $displayOrder,
                'status' => 'active',
            ];

            $image = HotelImage::create($imageData);

            // 如果是第一张图片，原本设置主图，现已移除

            return $this->success($image, '图片上传成功');
        } catch (\Exception $e) {
            return $this->error('图片上传失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量上传图片
     */
    public function batchUpload(Request $request): Response
    {
        try {
            $hotelId = (int)$request->post('hotel_id');
            $categoryId = (int)$request->post('category_id');
            $parentCategoryId = (int)$request->post('parent_category_id', 0);

            // 验证必填参数
            if (!$hotelId || !$categoryId) {
                return $this->error('酒店ID和分类ID不能为空', 400);
            }

            // 获取上传的文件
            $files = $request->file('images');
            if (!$files || !is_array($files)) {
                return $this->error('请选择要上传的图片文件', 400);
            }

            $uploaded = [];
            $errors = [];

            foreach ($files as $index => $file) {
                try {
                    if (!$file || !$file->isValid()) {
                        $errors[] = "文件 {$index}: 文件无效";
                        continue;
                    }

                    // 模拟单个文件上传
                    $singleRequest = new Request();
                    $singleRequest->post = [
                        'hotel_id' => $hotelId,
                        'category_id' => $categoryId,
                        'parent_category_id' => $parentCategoryId,
                        'title' => '',
                        'description' => '',
                    ];
                    $singleRequest->files = ['image' => $file];

                    $result = $this->upload($singleRequest);
                    $resultData = json_decode($result->getContent(), true);

                    if ($resultData['code'] === 0) {
                        $uploaded[] = $resultData['data'];
                    } else {
                        $errors[] = "文件 {$index}: " . $resultData['message'];
                    }
                } catch (\Exception $e) {
                    $errors[] = "文件 {$index}: " . $e->getMessage();
                }
            }

            return $this->success([
                'uploaded' => $uploaded,
                'uploaded_count' => count($uploaded),
                'error_count' => count($errors),
                'errors' => $errors,
            ], '批量上传完成');
        } catch (\Exception $e) {
            return $this->error('批量上传失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除图片
     */
    public function delete(Request $request, $id): Response
    {
        try {
            $image = HotelImage::find($id);

            if (!$image) {
                return $this->error('图片不存在', 404);
            }

            // 删除文件
            $image->deleteFile();

            // 删除数据库记录
            $image->delete();

            return $this->success(null, '图片删除成功');
        } catch (\Exception $e) {
            return $this->error('删除图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除图片
     */
    public function batchDelete(Request $request): Response
    {
        try {
            $imageIds = $request->post('image_ids', []);

            if (empty($imageIds) || !is_array($imageIds)) {
                return $this->error('请选择要删除的图片', 400);
            }

            $result = HotelImage::batchDelete($imageIds);

            return $this->success(
                $result,
                "成功删除 {$result['deleted']} 张图片"
            );
        } catch (\Exception $e) {
            return $this->error('批量删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成缩略图
     */
    private function generateThumbnail(string $filePath, string $extension): bool
    {
        try {
            $thumbnailPath = str_replace('.' . $extension, '_thumb.' . $extension, $filePath);
            $thumbnailWidth = 300;
            $thumbnailHeight = 200;

            // 根据文件类型创建图像资源
            switch (strtolower($extension)) {
                case 'jpg':
                case 'jpeg':
                    $source = imagecreatefromjpeg($filePath);
                    break;
                case 'png':
                    $source = imagecreatefrompng($filePath);
                    break;
                case 'gif':
                    $source = imagecreatefromgif($filePath);
                    break;
                case 'webp':
                    $source = imagecreatefromwebp($filePath);
                    break;
                default:
                    return false;
            }

            if (!$source) {
                return false;
            }

            // 获取原图尺寸
            $originalWidth = imagesx($source);
            $originalHeight = imagesy($source);

            // 计算缩略图尺寸，保持比例
            $ratio = min($thumbnailWidth / $originalWidth, $thumbnailHeight / $originalHeight);
            $newWidth = (int)($originalWidth * $ratio);
            $newHeight = (int)($originalHeight * $ratio);

            // 创建缩略图
            $thumbnail = imagecreatetruecolor($newWidth, $newHeight);
            
            // 保持透明度
            if (in_array($extension, ['png', 'gif'])) {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefill($thumbnail, 0, 0, $transparent);
            }

            // 缩放图像
            imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

            // 保存缩略图
            switch (strtolower($extension)) {
                case 'jpg':
                case 'jpeg':
                    imagejpeg($thumbnail, $thumbnailPath, 85);
                    break;
                case 'png':
                    imagepng($thumbnail, $thumbnailPath, 8);
                    break;
                case 'gif':
                    imagegif($thumbnail, $thumbnailPath);
                    break;
                case 'webp':
                    imagewebp($thumbnail, $thumbnailPath, 85);
                    break;
            }

            // 释放资源
            imagedestroy($source);
            imagedestroy($thumbnail);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
