<?php

namespace app\service;

use support\Db;

/**
 * 酒店资质服务类
 */
class HotelLicenseService extends BaseService
{
    public function getHotelLicenses(int $hotelId): array
    {
        try {
            $licenses = Db::table('hotel_licenses')
                ->where('hotel_id', $hotelId)
                ->where('status', '!=', 'inactive')
                ->get();

            return $licenses->map(function($license) {
                $licenseArray = (array)$license;
                $licenseArray['license_data'] = json_decode($licenseArray['license_data'], true);
                return $licenseArray;
            })->toArray();
        } catch (\Exception $e) {
            $this->logError('获取酒店资质失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function updateHotelLicense(int $hotelId, string $licenseType, array $licenseData): bool
    {
        try {
            Db::beginTransaction();

            $data = [
                'license_data' => json_encode($licenseData, JSON_UNESCAPED_UNICODE),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $existingLicense = Db::table('hotel_licenses')
                ->where('hotel_id', $hotelId)
                ->where('license_type', $licenseType)
                ->first();

            if ($existingLicense) {
                $result = Db::table('hotel_licenses')
                    ->where('hotel_id', $hotelId)
                    ->where('license_type', $licenseType)
                    ->update($data);
            } else {
                $data['hotel_id'] = $hotelId;
                $data['license_type'] = $licenseType;
                $data['status'] = 'active';
                $data['created_at'] = date('Y-m-d H:i:s');
                
                $result = Db::table('hotel_licenses')->insert($data);
            }

            Db::commit();
            return $result > 0;
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('更新酒店资质失败', [
                'hotel_id' => $hotelId,
                'license_type' => $licenseType,
                'license_data' => $licenseData,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
