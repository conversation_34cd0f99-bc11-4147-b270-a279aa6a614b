<?php

namespace base\middleware;

use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class AcceptLanguage implements MiddlewareInterface
{
    public array $lang = [
        ['value' => 'en-US', 'label' => '英语'],
        ['value' => 'ja-JP', 'label' => '日语'],
        ['value' => 'ko-KR', 'label' => '韩语'],
        ['value' => 'zh-CN', 'label' => '简体中文'],
        ['value' => 'zh-HK', 'label' => '繁体中文'],
        ['value' => 'fr-FR', 'label' => '法语'],
        ['value' => 'de-DE', 'label' => '德语'],
        ['value' => 'es-ES', 'label' => '西班牙语'],
        ['value' => 'ru-RU', 'label' => '俄语'],
        ['value' => 'ms-MY', 'label' => '马来语'],
        ['value' => 'id-ID', 'label' => '印尼语'],
        ['value' => 'th-TH', 'label' => '泰语'],
        ['value' => 'vi-VN', 'label' => '越南语'],
        ['value' => 'tl-PH', 'label' => '菲律宾语'],
        ['value' => 'it-IT', 'label' => '意大利语'],
        ['value' => 'pl-PL', 'label' => '波兰语'],
        ['value' => 'tr-TR', 'label' => '土耳其语'],
        ['value' => 'pt-BR', 'label' => '葡萄牙语'],
        ['value' => 'el-GR', 'label' => '希腊语'],
        ['value' => 'nl-NL', 'label' => '荷兰语'],
    ];

    public function process(Request $request, callable $handler): Response
    {
        $acceptLanguage = $request->header('accept-language', '');
        $languages = [];

        $lang = $this->lang;

        $supportedValues = array_column($lang, 'value'); // 提取所有支持的value

        if (!empty($acceptLanguage)) {
            preg_match_all(
                '/([a-z]{1,8}(?:-[a-z]{1,8})*)(?:;\s*q=([0-9.]+))?/i',
                $acceptLanguage,
                $matches,
                PREG_SET_ORDER
            );

            foreach ($matches as $match) {
                $rawCode = $match[1];
                $weight = isset($match[2]) ? (float)$match[2] : 1.0;

                // 格式转换逻辑
                $codeParts = explode('-', $rawCode);
                $formattedCode = count($codeParts) > 1
                    ? strtolower($codeParts[0]) . '-' . strtoupper($codeParts[1])
                    : strtolower($rawCode);

                $languages[] = [
                    'code' => $formattedCode,
                    'raw_code' => strtolower($formattedCode),
                    'weight' => $weight
                ];
            }

            usort($languages, fn($a, $b) => $b['weight'] <=> $a['weight']);
        }

        // 匹配逻辑
        $preferred = 'zh-CN'; // 默认值
        foreach ($languages as $lang) {
            if (in_array($lang['code'], $supportedValues)) {
                $preferred = $lang['code'];
                break;
            }
        }
        $request->acceptLanguage = $preferred;

        return $handler($request);
    }
}