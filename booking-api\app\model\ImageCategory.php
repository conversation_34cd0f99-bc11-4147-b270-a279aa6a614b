<?php

namespace app\model;

use support\Model;

/**
 * 图片分类模型
 */
class ImageCategory extends Model
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'image_categories';

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array
     */
    protected $fillable = [
        'category_id',
        'parent_id',
        'category_name',
        'category_group',
        'description',
        'icon',
        'sort_order',
        'is_active',
    ];

    /**
     * 属性类型转换
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'category_id' => 'integer',
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联父分类
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'category_id');
    }

    /**
     * 关联子分类
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'category_id');
    }

    /**
     * 关联酒店图片
     */
    public function hotelImages()
    {
        return $this->hasMany(HotelImage::class, 'category_id', 'category_id');
    }

    /**
     * 作用域：启用的分类
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：一级分类
     */
    public function scopeParent($query)
    {
        return $query->where('parent_id', 0);
    }

    /**
     * 作用域：子分类
     */
    public function scopeChildren($query, $parentId = null)
    {
        if ($parentId !== null) {
            return $query->where('parent_id', $parentId);
        }
        return $query->where('parent_id', '>', 0);
    }

    /**
     * 作用域：按分组筛选
     */
    public function scopeByGroup($query, $group)
    {
        if ($group) {
            return $query->where('category_group', $group);
        }
        return $query;
    }

    /**
     * 作用域：排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('category_id', 'asc');
    }

    /**
     * 获取树形结构的分类数据
     */
    public static function getTree(): array
    {
        $categories = self::active()->ordered()->get();
        
        $tree = [];
        $categoryMap = [];

        // 先建立映射
        foreach ($categories as $category) {
            $categoryMap[$category->category_id] = $category->toArray();
            $categoryMap[$category->category_id]['children'] = [];
        }

        // 构建树形结构
        foreach ($categories as $category) {
            if ($category->parent_id == 0) {
                // 一级分类
                $tree[] = &$categoryMap[$category->category_id];
            } else {
                // 二级分类
                if (isset($categoryMap[$category->parent_id])) {
                    $categoryMap[$category->parent_id]['children'][] = &$categoryMap[$category->category_id];
                }
            }
        }

        return $tree;
    }

    /**
     * 获取扁平化的分类列表
     */
    public static function getFlat(): array
    {
        return self::active()->ordered()->get()->toArray();
    }

    /**
     * 获取分组统计
     */
    public static function getGroupStats(): array
    {
        $stats = self::selectRaw('category_group, COUNT(*) as count')
            ->where('is_active', true)
            ->groupBy('category_group')
            ->pluck('count', 'category_group')
            ->toArray();

        $groups = [
            '外观' => '外观',
            '餐饮' => '餐饮',
            '休闲' => '休闲',
            '商务' => '商务',
            '家庭亲子' => '家庭亲子',
            '公共区域' => '公共区域',
            '周边' => '周边',
            '其他' => '其他',
        ];

        $result = [];
        foreach ($groups as $key => $name) {
            $result[] = [
                'group' => $key,
                'name' => $name,
                'count' => $stats[$key] ?? 0,
            ];
        }

        return $result;
    }

    /**
     * 根据分类ID获取完整路径
     */
    public static function getCategoryPath(int $categoryId): array
    {
        $category = self::where('category_id', $categoryId)->first();
        if (!$category) {
            return [];
        }

        $path = [$category];

        if ($category->parent_id > 0) {
            $parent = self::where('category_id', $category->parent_id)->first();
            if ($parent) {
                array_unshift($path, $parent);
            }
        }

        return $path;
    }

    /**
     * 检查分类是否存在
     */
    public static function exists(int $categoryId): bool
    {
        return self::where('category_id', $categoryId)->where('is_active', true)->exists();
    }

    /**
     * 获取子分类列表
     */
    public static function getChildrenByCategoryId(int $parentId): array
    {
        return self::where('parent_id', $parentId)
            ->active()
            ->ordered()
            ->get()
            ->toArray();
    }

    /**
     * 批量导入分类数据
     */
    public static function batchImport(array $data): array
    {
        $imported = 0;
        $skipped = 0;
        $errors = [];

        foreach ($data as $item) {
            try {
                // 检查是否已存在
                $existing = self::where('category_id', $item['category_id'])->first();
                if ($existing) {
                    $skipped++;
                    continue;
                }

                // 创建新记录
                self::create($item);
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "导入失败: {$item['category_name']} - {$e->getMessage()}";
            }
        }

        return [
            'imported' => $imported,
            'skipped' => $skipped,
            'errors' => $errors,
        ];
    }

    /**
     * 获取分类的使用统计
     */
    public function getUsageStats(): array
    {
        $imageCount = HotelImage::where('category_id', $this->category_id)->count();
        $hotelCount = HotelImage::where('category_id', $this->category_id)
            ->distinct('hotel_id')
            ->count('hotel_id');

        return [
            'image_count' => $imageCount,
            'hotel_count' => $hotelCount,
        ];
    }
}
