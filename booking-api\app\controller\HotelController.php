<?php

namespace app\controller;

use app\service\HotelService;
use base\auth\Auth;
use support\Request;
use support\Response;

/**
 * 酒店管理控制器
 */
class HotelController extends BaseController
{
    /**
     * 酒店服务
     *
     * @var HotelService
     */
    private $hotelService;

    public function __construct()
    {
        $this->hotelService = new HotelService();
    }

    /**
     * 获取酒店列表
     *
     * @param Request $request
     * @return Response
     */
    #[Auth('booking/hotel/read')]
    public function index(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            $result = $this->hotelService->getHotelList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店列表');
        }
    }

    /**
     * 获取酒店详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $result = $this->hotelService->getHotelDetail($hotelId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店详情');
        }
    }

    /**
     * 创建酒店
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['name', 'city', 'address']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'email' => 'email',
                'contact_email' => 'email',
                'phone' => 'phone',
                'contact_phone' => 'phone',
                'emergency_phone' => 'phone',
                'star_rating' => 'integer',
                'total_rooms' => 'integer',
                'total_floors' => 'integer',
                'latitude' => 'numeric',
                'longitude' => 'numeric'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证星级范围
            if (!empty($data['star_rating']) && ($data['star_rating'] < 1 || $data['star_rating'] > 5)) {
                return $this->error('星级必须在1-5之间');
            }

            // 验证坐标范围
            if (!empty($data['latitude']) && ($data['latitude'] < -90 || $data['latitude'] > 90)) {
                return $this->error('纬度必须在-90到90之间');
            }
            if (!empty($data['longitude']) && ($data['longitude'] < -180 || $data['longitude'] > 180)) {
                return $this->error('经度必须在-180到180之间');
            }

            // 验证JSON字段格式
            $jsonFields = [
                'facilities', 'services', 'images', 'videos', 'business_hours', 'languages',
                'payment_methods', 'hotel_names', 'hotel_briefs', 'hotel_descriptions',
                'important_informations', 'hotel_category', 'positions', 'addresses',
                'phones', 'emails', 'booking_policies', 'guest_policies', 'safety_policies',
                'environmental_policies', 'accessibility_features', 'business_facilities',
                'recreation_facilities', 'dining_facilities', 'meeting_facilities',
                'spa_wellness_facilities', 'family_facilities', 'pet_facilities',
                'special_services', 'concierge_services', 'transportation_services',
                'laundry_services', 'room_services', 'awards_certifications',
                'quality_certifications', 'environmental_certifications', 'safety_certifications',
                'nearby_attractions', 'transportation_info', 'parking_info', 'wifi_info'
            ];

            foreach ($jsonFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->hotelService->createHotel($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建酒店');
        }
    }

    /**
     * 更新酒店信息
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'email' => 'email',
                'contact_email' => 'email',
                'phone' => 'phone',
                'contact_phone' => 'phone',
                'emergency_phone' => 'phone',
                'star_rating' => 'integer',
                'total_rooms' => 'integer',
                'total_floors' => 'integer',
                'latitude' => 'numeric',
                'longitude' => 'numeric'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证星级范围
            if (!empty($data['star_rating']) && ($data['star_rating'] < 1 || $data['star_rating'] > 5)) {
                return $this->error('星级必须在1-5之间');
            }

            // 验证坐标范围
            if (!empty($data['latitude']) && ($data['latitude'] < -90 || $data['latitude'] > 90)) {
                return $this->error('纬度必须在-90到90之间');
            }
            if (!empty($data['longitude']) && ($data['longitude'] < -180 || $data['longitude'] > 180)) {
                return $this->error('经度必须在-180到180之间');
            }


            $result = $this->hotelService->updateHotel($hotelId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店信息');
        }
    }

    /**
     * 删除酒店
     *
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $result = $this->hotelService->deleteHotel($hotelId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除酒店');
        }
    }

    /**
     * 获取酒店房型列表
     *
     * @param Request $request
     * @return Response
     */
    public function roomTypes(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);
            $result = $this->hotelService->getRoomTypeList($hotelId, $params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型列表');
        }
    }

    /**
     * 创建房型
     *
     * @param Request $request
     * @return Response
     */
    public function createRoomType(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['name', 'code']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'area' => 'positive',
                'max_occupancy' => 'integer',
                'max_adults' => 'integer',
                'max_children' => 'integer',
                'bed_count' => 'integer',
                'total_rooms' => 'integer',
                'base_price' => 'positive',
                'sort_order' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->hotelService->createRoomType($hotelId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建房型');
        }
    }

    /**
     * 搜索酒店
     *
     * @param Request $request
     * @return Response
     */
    public function search(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            // 添加搜索特定的参数处理
            if (!empty($params['check_in_date']) && !empty($params['check_out_date'])) {
                // 验证日期格式
                $dateErrors = $this->validateFormat($params, [
                    'check_in_date' => 'date',
                    'check_out_date' => 'date'
                ]);
                if ($dateErrors) {
                    return $this->error('日期格式错误', 400, $dateErrors);
                }

                // 验证日期范围
                if ($params['check_in_date'] >= $params['check_out_date']) {
                    return $this->error('入住日期必须早于离店日期');
                }
            }

            $result = $this->hotelService->getHotelList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '搜索酒店');
        }
    }

    /**
     * 获取推荐酒店
     *
     * @param Request $request
     * @return Response
     */
    public function featured(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request),
                ['is_featured' => true]
            );

            $result = $this->hotelService->getHotelList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取推荐酒店');
        }
    }

    /**
     * 获取附近酒店
     *
     * @param Request $request
     * @return Response
     */
    public function nearby(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            // 验证必需参数
            $errors = $this->validateRequired($params, ['latitude', 'longitude']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证坐标格式
            $latitude = (float)$params['latitude'];
            $longitude = (float)$params['longitude'];

            if ($latitude < -90 || $latitude > 90) {
                return $this->error('纬度必须在-90到90之间');
            }

            if ($longitude < -180 || $longitude > 180) {
                return $this->error('经度必须在-180到180之间');
            }

            $params['latitude'] = $latitude;
            $params['longitude'] = $longitude;

            $result = $this->hotelService->getHotelList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取附近酒店');
        }
    }

    /**
     * 获取酒店设施信息
     *
     * @param Request $request
     * @return Response
     */
    public function facilities(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $result = $this->hotelService->getHotelFacilities($hotelId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店设施信息');
        }
    }

    /**
     * 更新酒店设施信息
     *
     * @param Request $request
     * @return Response
     */
    public function updateFacilities(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证设施数据格式
            $facilityFields = [
                'general_facilities', 'business_facilities', 'recreation_facilities',
                'dining_facilities', 'meeting_facilities', 'spa_wellness_facilities',
                'family_facilities', 'pet_facilities'
            ];

            foreach ($facilityFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->hotelService->updateHotelFacilities($hotelId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店设施信息');
        }
    }

    /**
     * 获取酒店政策信息
     *
     * @param Request $request
     * @return Response
     */
    public function policies(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $result = $this->hotelService->getHotelPolicies($hotelId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店政策信息');
        }
    }

    /**
     * 更新酒店政策信息
     *
     * @param Request $request
     * @return Response
     */
    public function updatePolicies(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证政策数据格式
            $policyFields = [
                'booking_policies', 'guest_policies', 'safety_policies',
                'environmental_policies'
            ];

            foreach ($policyFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->hotelService->updateHotelPolicies($hotelId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店政策信息');
        }
    }

    /**
     * 获取酒店营业时间
     *
     * @param Request $request
     * @return Response
     */
    public function businessHours(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $result = $this->hotelService->getHotelBusinessHours($hotelId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取酒店营业时间');
        }
    }

    /**
     * 更新酒店营业时间
     *
     * @param Request $request
     * @return Response
     */
    public function updateBusinessHours(Request $request, $id)
    {
        try {
            $hotelId = (int)$id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证营业时间格式
            if (!empty($data['business_hours'])) {
                if (!is_array($data['business_hours'])) {
                    return $this->error('营业时间必须是数组格式');
                }

                $requiredDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                foreach ($requiredDays as $day) {
                    if (!isset($data['business_hours'][$day])) {
                        return $this->error("缺少 {$day} 的营业时间设置");
                    }

                    $dayHours = $data['business_hours'][$day];
                    if (!isset($dayHours['open']) || !isset($dayHours['close']) || !isset($dayHours['is_open'])) {
                        return $this->error("{$day} 的营业时间格式不正确");
                    }

                    // 验证时间格式
                    if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $dayHours['open']) ||
                        !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $dayHours['close'])) {
                        return $this->error("{$day} 的时间格式不正确，应为 HH:MM");
                    }
                }
            }

            $result = $this->hotelService->updateHotelBusinessHours($hotelId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店营业时间');
        }
    }
}
