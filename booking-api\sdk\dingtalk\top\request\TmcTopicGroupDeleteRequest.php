<?php
/**
 * TOP API: taobao.tmc.topic.group.delete request
 * 
 * <AUTHOR> create
 * @since 1.0, 2025.07.02
 */
class TmcTopicGroupDeleteRequest
{
	/** 
	 * 消息分组Id，一般不用填写，如果分组已经被删除，则根据问题排查工具返回的ID删除路由关系
	 **/
	private $groupId;
	
	/** 
	 * 消息分组名
	 **/
	private $groupName;
	
	/** 
	 * 消息topic名称，多个以逗号(,)分割
	 **/
	private $topics;
	
	private $apiParas = array();
	
	public function setGroupId($groupId)
	{
		$this->groupId = $groupId;
		$this->apiParas["group_id"] = $groupId;
	}

	public function getGroupId()
	{
		return $this->groupId;
	}

	public function setGroupName($groupName)
	{
		$this->groupName = $groupName;
		$this->apiParas["group_name"] = $groupName;
	}

	public function getGroupName()
	{
		return $this->groupName;
	}

	public function setTopics($topics)
	{
		$this->topics = $topics;
		$this->apiParas["topics"] = $topics;
	}

	public function getTopics()
	{
		return $this->topics;
	}

	public function getApiMethodName()
	{
		return "taobao.tmc.topic.group.delete";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
		RequestCheckUtil::checkNotNull($this->groupName,"groupName");
		RequestCheckUtil::checkNotNull($this->topics,"topics");
		RequestCheckUtil::checkMaxListSize($this->topics,20,"topics");
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
