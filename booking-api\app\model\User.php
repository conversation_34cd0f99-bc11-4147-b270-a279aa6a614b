<?php

namespace app\model;

use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 用户模型
 * 对应数据库表：users
 */
class User extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'email',
        'phone',
        'password_hash',
        'real_name',
        'avatar',
        'gender',
        'birthday',
        'id_card',
        'user_type',
        'status',
        'last_login_at',
        'email_verified_at',
        'phone_verified_at'
    ];

    /**
     * 应该隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'password_hash',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'birthday' => 'date',
        'last_login_at' => 'datetime',
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 用户类型常量
     */
    const TYPE_ADMIN = 'admin';
    const TYPE_HOTEL_MANAGER = 'hotel_manager';
    const TYPE_STAFF = 'staff';
    const TYPE_CUSTOMER = 'customer';

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_BANNED = 'banned';

    /**
     * 性别常量
     */
    const GENDER_MALE = 'male';
    const GENDER_FEMALE = 'female';
    const GENDER_UNKNOWN = 'unknown';

    /**
     * 获取用户角色
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_roles');
    }

    /**
     * 获取用户创建的订单
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * 获取用户确认的订单
     */
    public function confirmedBookings()
    {
        return $this->hasMany(Booking::class, 'confirmed_by');
    }

    /**
     * 获取用户取消的订单
     */
    public function cancelledBookings()
    {
        return $this->hasMany(Booking::class, 'cancelled_by');
    }

    /**
     * 获取库存变更日志
     */
    public function inventoryLogs()
    {
        return $this->hasMany(InventoryLog::class, 'operator_id');
    }

    /**
     * 作用域：管理员
     */
    public function scopeAdmins($query)
    {
        return $query->where('user_type', self::TYPE_ADMIN);
    }

    /**
     * 作用域：酒店管理员
     */
    public function scopeHotelManagers($query)
    {
        return $query->where('user_type', self::TYPE_HOTEL_MANAGER);
    }

    /**
     * 作用域：员工
     */
    public function scopeStaff($query)
    {
        return $query->where('user_type', self::TYPE_STAFF);
    }

    /**
     * 作用域：客户
     */
    public function scopeCustomers($query)
    {
        return $query->where('user_type', self::TYPE_CUSTOMER);
    }

    /**
     * 作用域：已验证邮箱
     */
    public function scopeEmailVerified($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    /**
     * 作用域：已验证手机
     */
    public function scopePhoneVerified($query)
    {
        return $query->whereNotNull('phone_verified_at');
    }

    /**
     * 获取用户类型名称
     */
    public function getUserTypeNameAttribute()
    {
        $types = [
            self::TYPE_ADMIN => '管理员',
            self::TYPE_HOTEL_MANAGER => '酒店管理员',
            self::TYPE_STAFF => '员工',
            self::TYPE_CUSTOMER => '客户',
        ];

        return $types[$this->user_type] ?? '未知';
    }

    /**
     * 获取性别名称
     */
    public function getGenderNameAttribute()
    {
        $genders = [
            self::GENDER_MALE => '男',
            self::GENDER_FEMALE => '女',
            self::GENDER_UNKNOWN => '未知',
        ];

        return $genders[$this->gender] ?? '未知';
    }

    /**
     * 检查是否有指定角色
     */
    public function hasRole($roleName)
    {
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * 检查是否有指定权限
     */
    public function hasPermission($permission)
    {
        return $this->roles()->whereJsonContains('permissions', $permission)->exists();
    }

    /**
     * 验证密码
     */
    public function verifyPassword($password)
    {
        return password_verify($password, $this->password_hash);
    }

    /**
     * 设置密码
     */
    public function setPassword($password)
    {
        $this->password_hash = password_hash($password, PASSWORD_DEFAULT);
        return $this;
    }

    /**
     * 更新最后登录时间
     */
    public function updateLastLogin()
    {
        $this->last_login_at = date('Y-m-d H:i:s');
        $this->save();
        return $this;
    }
}
