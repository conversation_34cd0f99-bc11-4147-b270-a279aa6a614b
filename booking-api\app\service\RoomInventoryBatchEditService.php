<?php

namespace app\service;

use app\model\RoomInventoryBatchEdit;
use app\model\RoomInventory;
use app\model\Hotel;
use app\model\RoomType;
use Carbon\Carbon;

/**
 * 库存批量编辑服务
 */
class RoomInventoryBatchEditService extends BaseService
{
    /**
     * 获取批量编辑任务列表
     *
     * @param array $params
     * @return array
     */
    public function getBatchEditList(array $params = [])
    {
        try {
            $query = RoomInventoryBatchEdit::with(['hotel', 'roomType', 'creator']);

            // 筛选条件
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            if (!empty($params['room_type_id'])) {
                $query->where('room_type_id', $params['room_type_id']);
            }

            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            if (!empty($params['operation_type'])) {
                $query->where('operation_type', $params['operation_type']);
            }

            if (!empty($params['created_by'])) {
                $query->where('created_by', $params['created_by']);
            }

            if (!empty($params['start_date'])) {
                $query->where('created_at', '>=', $params['start_date']);
            }

            if (!empty($params['end_date'])) {
                $query->where('created_at', '<=', $params['end_date']);
            }

            // 排序
            $sortField = $params['sort_field'] ?? 'created_at';
            $sortOrder = $params['sort_order'] ?? 'desc';
            $query->orderBy($sortField, $sortOrder);

            // 分页
            $page = $params['page'] ?? 1;
            $pageSize = $params['page_size'] ?? 20;
            $result = $query->paginate($pageSize, ['*'], 'page', $page);

            return $this->success([
                'list' => $result->items(),
                'total' => $result->total(),
                'page' => $result->currentPage(),
                'page_size' => $result->perPage(),
                'total_pages' => $result->lastPage()
            ]);

        } catch (\Exception $e) {
            return $this->error('获取库存批量编辑任务列表失败：' . $e->getMessage());
        }
    }

    /**
     * 获取批量编辑任务详情
     *
     * @param int $taskId
     * @return array
     */
    public function getBatchEditDetail(int $taskId)
    {
        try {
            $task = RoomInventoryBatchEdit::with(['hotel', 'roomType', 'creator'])
                ->find($taskId);

            if (!$task) {
                return $this->error('库存批量编辑任务不存在');
            }

            return $this->success($task);

        } catch (\Exception $e) {
            return $this->error('获取库存批量编辑任务详情失败：' . $e->getMessage());
        }
    }

    /**
     * 创建批量编辑任务
     *
     * @param array $data
     * @return array
     */
    public function createBatchEdit(array $data)
    {
        try {
            // 验证酒店是否存在
            $hotel = Hotel::find($data['hotel_id']);
            if (!$hotel) {
                return $this->error('酒店不存在');
            }

            // 验证房型（如果指定）
            if (!empty($data['room_type_id'])) {
                $roomType = RoomType::where('hotel_id', $data['hotel_id'])
                    ->where('id', $data['room_type_id'])
                    ->first();
                if (!$roomType) {
                    return $this->error('房型不存在或不属于该酒店');
                }
            }

            // 计算影响的日期数量
            $affectedDates = $this->calculateAffectedDates($data);
            $data['total_count'] = count($affectedDates);

            // 创建批量编辑任务
            $task = RoomInventoryBatchEdit::create($data);

            return $this->success($task, '库存批量编辑任务创建成功');

        } catch (\Exception $e) {
            return $this->error('创建库存批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 执行批量编辑任务
     *
     * @param int $taskId
     * @return array
     */
    public function executeBatchEdit(int $taskId)
    {
        try {
            $task = RoomInventoryBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('库存批量编辑任务不存在');
            }

            if ($task->status !== RoomInventoryBatchEdit::STATUS_PENDING) {
                return $this->error('任务状态不允许执行');
            }

            // 标记为处理中
            $task->markAsProcessing();

            // 获取影响的日期
            $affectedDates = $task->getAffectedDates();
            $processedCount = 0;
            $successCount = 0;
            $errorMessages = [];

            foreach ($affectedDates as $date) {
                try {
                    $this->processDateInventory($task, $date);
                    $successCount++;
                } catch (\Exception $e) {
                    $errorMessages[] = "日期 {$date}: " . $e->getMessage();
                }
                
                $processedCount++;
                
                // 更新进度
                $task->updateProgress($processedCount, count($affectedDates));
            }

            // 标记完成状态
            if ($successCount === count($affectedDates)) {
                $task->markAsCompleted();
            } else {
                $task->markAsFailed(implode('; ', $errorMessages));
            }

            return $this->success([
                'task_id' => $taskId,
                'processed_count' => $processedCount,
                'success_count' => $successCount,
                'error_count' => count($errorMessages),
                'status' => $task->status
            ], '库存批量编辑任务执行完成');

        } catch (\Exception $e) {
            // 标记任务失败
            if (isset($task)) {
                $task->markAsFailed($e->getMessage());
            }
            return $this->error('执行库存批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 取消批量编辑任务
     *
     * @param int $taskId
     * @return array
     */
    public function cancelBatchEdit(int $taskId)
    {
        try {
            $task = RoomInventoryBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('库存批量编辑任务不存在');
            }

            if (!in_array($task->status, [RoomInventoryBatchEdit::STATUS_PENDING, RoomInventoryBatchEdit::STATUS_PROCESSING])) {
                return $this->error('任务状态不允许取消');
            }

            $task->markAsFailed('用户取消');

            return $this->success(null, '库存批量编辑任务已取消');

        } catch (\Exception $e) {
            return $this->error('取消库存批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 获取批量编辑进度
     *
     * @param int $taskId
     * @return array
     */
    public function getBatchEditProgress(int $taskId)
    {
        try {
            $task = RoomInventoryBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('库存批量编辑任务不存在');
            }

            return $this->success([
                'task_id' => $taskId,
                'status' => $task->status,
                'status_name' => $task->status_name,
                'processed_count' => $task->processed_count,
                'total_count' => $task->total_count,
                'progress_percentage' => $task->progress_percentage,
                'error_message' => $task->error_message,
                'created_at' => $task->created_at,
                'processed_at' => $task->processed_at
            ]);

        } catch (\Exception $e) {
            return $this->error('获取库存批量编辑进度失败：' . $e->getMessage());
        }
    }

    /**
     * 预览批量编辑影响
     *
     * @param array $data
     * @return array
     */
    public function previewBatchEdit(array $data)
    {
        try {
            // 计算影响的日期
            $affectedDates = $this->calculateAffectedDates($data);
            
            // 获取当前库存数据
            $query = RoomInventory::where('hotel_id', $data['hotel_id'])
                ->whereIn('date', $affectedDates);

            if (!empty($data['room_type_id'])) {
                $query->where('room_type_id', $data['room_type_id']);
            }

            $currentInventory = $query->get();

            // 计算预览数据
            $preview = [];
            foreach ($currentInventory as $inventory) {
                $originalQuantity = $inventory->total_rooms;
                $newQuantity = $this->calculateNewInventory($originalQuantity, $data);
                
                $preview[] = [
                    'date' => $inventory->date,
                    'room_type_id' => $inventory->room_type_id,
                    'original_quantity' => $originalQuantity,
                    'new_quantity' => $newQuantity,
                    'change_amount' => $newQuantity - $originalQuantity,
                    'close_sales' => $data['close_sales'] ?? false,
                    'stop_sales' => $data['stop_sales'] ?? false
                ];
            }

            return $this->success([
                'affected_dates_count' => count($affectedDates),
                'affected_inventory_count' => count($preview),
                'preview_data' => $preview,
                'summary' => [
                    'total_dates' => count($affectedDates),
                    'total_inventory' => count($preview),
                    'operation_type' => $data['operation_type'],
                    'adjustment_value' => $data['inventory_adjustment'] ?? 0
                ]
            ]);

        } catch (\Exception $e) {
            return $this->error('预览库存批量编辑影响失败：' . $e->getMessage());
        }
    }

    /**
     * 删除批量编辑任务
     *
     * @param int $taskId
     * @return array
     */
    public function deleteBatchEdit(int $taskId)
    {
        try {
            $task = RoomInventoryBatchEdit::find($taskId);
            if (!$task) {
                return $this->error('库存批量编辑任务不存在');
            }

            if ($task->status === RoomInventoryBatchEdit::STATUS_PROCESSING) {
                return $this->error('正在处理中的任务不能删除');
            }

            $task->delete();

            return $this->success(null, '库存批量编辑任务删除成功');

        } catch (\Exception $e) {
            return $this->error('删除库存批量编辑任务失败：' . $e->getMessage());
        }
    }

    /**
     * 计算影响的日期
     *
     * @param array $data
     * @return array
     */
    private function calculateAffectedDates(array $data)
    {
        $dates = [];
        $current = Carbon::parse($data['start_date']);
        $end = Carbon::parse($data['end_date']);

        while ($current <= $end) {
            // 检查是否在包含日期中（如果设置了）
            if (!empty($data['include_dates']) && !in_array($current->toDateString(), $data['include_dates'])) {
                $current = $current->addDay();
                continue;
            }

            // 检查是否在排除日期中
            if (!empty($data['exclude_dates']) && in_array($current->toDateString(), $data['exclude_dates'])) {
                $current = $current->addDay();
                continue;
            }

            // 检查星期限制
            if (!empty($data['days_of_week']) && !in_array($current->dayOfWeek, $data['days_of_week'])) {
                $current = $current->addDay();
                continue;
            }

            $dates[] = $current->toDateString();
            $current = $current->addDay();
        }

        return $dates;
    }

    /**
     * 处理指定日期的库存
     *
     * @param RoomInventoryBatchEdit $task
     * @param string $date
     * @return void
     */
    private function processDateInventory(RoomInventoryBatchEdit $task, string $date)
    {
        $query = RoomInventory::where('hotel_id', $task->hotel_id)
            ->where('date', $date);

        if ($task->room_type_id) {
            $query->where('room_type_id', $task->room_type_id);
        }

        $inventories = $query->get();

        foreach ($inventories as $inventory) {
            $updateData = [];

            // 库存调整
            if (isset($task->inventory_adjustment)) {
                $newQuantity = $task->calculateAdjustedInventory($inventory->total_rooms);
                $updateData['total_rooms'] = $newQuantity;
                $updateData['available_rooms'] = min($newQuantity, $inventory->available_rooms);
            }

            // 销售控制
            if (isset($task->close_sales)) {
                $updateData['close_sales'] = $task->close_sales;
            }

            if (isset($task->stop_sales)) {
                $updateData['stop_sales'] = $task->stop_sales;
            }

            // 入住离店限制
            if (isset($task->arrival_restriction)) {
                $updateData['arrival_restriction'] = $task->arrival_restriction;
            }

            if (isset($task->departure_restriction)) {
                $updateData['departure_restriction'] = $task->departure_restriction;
            }

            // 最小最大入住天数
            if (isset($task->min_stay_adjustment)) {
                $updateData['min_stay'] = $task->min_stay_adjustment;
            }

            if (isset($task->max_stay_adjustment)) {
                $updateData['max_stay'] = $task->max_stay_adjustment;
            }

            if (!empty($updateData)) {
                $inventory->update($updateData);
            }
        }
    }

    /**
     * 计算新库存
     *
     * @param int $originalQuantity
     * @param array $data
     * @return int
     */
    private function calculateNewInventory(int $originalQuantity, array $data)
    {
        switch ($data['operation_type']) {
            case 'set':
                return $data['inventory_adjustment'] ?? $originalQuantity;
            case 'increase':
                return $originalQuantity + ($data['inventory_adjustment'] ?? 0);
            case 'decrease':
                return max(0, $originalQuantity - ($data['inventory_adjustment'] ?? 0));
            case 'close':
                return 0;
            case 'open':
                return $data['inventory_adjustment'] ?? $originalQuantity;
            default:
                return $originalQuantity;
        }
    }
}
