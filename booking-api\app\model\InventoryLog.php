<?php

namespace app\model;

/**
 * 库存变更日志模型
 * 对应数据库表：inventory_logs
 */
class InventoryLog extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'inventory_logs';

    /**
     * 指示模型是否应该被时间戳
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'room_type_id',
        'date',
        'change_type',
        'change_reason',
        'before_available',
        'after_available',
        'change_amount',
        'operator_id',
        'operator_name',
        'source_system',
        'reference_id'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'room_type_id' => 'integer',
        'date' => 'date',
        'before_available' => 'integer',
        'after_available' => 'integer',
        'change_amount' => 'integer',
        'operator_id' => 'integer',
        'reference_id' => 'integer',
        'created_at' => 'datetime',
    ];

    /**
     * 变更类型常量
     */
    const CHANGE_TYPE_MANUAL = 'manual';
    const CHANGE_TYPE_BOOKING = 'booking';
    const CHANGE_TYPE_CANCELLATION = 'cancellation';
    const CHANGE_TYPE_SYSTEM = 'system';

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取所属房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * 获取操作人
     */
    public function operator()
    {
        return $this->belongsTo(User::class, 'operator_id');
    }

    /**
     * 作用域：按变更类型筛选
     */
    public function scopeByChangeType($query, $changeType)
    {
        return $query->where('change_type', $changeType);
    }

    /**
     * 作用域：按日期筛选
     */
    public function scopeByDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * 作用域：按操作人筛选
     */
    public function scopeByOperator($query, $operatorId)
    {
        return $query->where('operator_id', $operatorId);
    }

    /**
     * 获取变更类型名称
     */
    public function getChangeTypeNameAttribute()
    {
        $types = [
            self::CHANGE_TYPE_MANUAL => '手动调整',
            self::CHANGE_TYPE_BOOKING => '预订扣减',
            self::CHANGE_TYPE_CANCELLATION => '取消释放',
            self::CHANGE_TYPE_SYSTEM => '系统调整',
        ];

        return $types[$this->change_type] ?? '未知';
    }

    /**
     * 记录库存变更
     */
    public static function logChange($data)
    {
        return static::create([
            'hotel_id' => $data['hotel_id'],
            'room_type_id' => $data['room_type_id'],
            'date' => $data['date'],
            'change_type' => $data['change_type'],
            'change_reason' => $data['change_reason'] ?? null,
            'before_available' => $data['before_available'],
            'after_available' => $data['after_available'],
            'change_amount' => $data['after_available'] - $data['before_available'],
            'operator_id' => $data['operator_id'] ?? null,
            'operator_name' => $data['operator_name'] ?? null,
            'source_system' => $data['source_system'] ?? 'api',
            'reference_id' => $data['reference_id'] ?? null,
        ]);
    }

    /**
     * 记录预订变更
     */
    public static function logBookingChange($hotelId, $roomTypeId, $date, $beforeAvailable, $afterAvailable, $bookingId, $operatorId = null)
    {
        return static::logChange([
            'hotel_id' => $hotelId,
            'room_type_id' => $roomTypeId,
            'date' => $date,
            'change_type' => self::CHANGE_TYPE_BOOKING,
            'change_reason' => '预订确认',
            'before_available' => $beforeAvailable,
            'after_available' => $afterAvailable,
            'operator_id' => $operatorId,
            'reference_id' => $bookingId,
        ]);
    }

    /**
     * 记录取消变更
     */
    public static function logCancellationChange($hotelId, $roomTypeId, $date, $beforeAvailable, $afterAvailable, $bookingId, $operatorId = null)
    {
        return static::logChange([
            'hotel_id' => $hotelId,
            'room_type_id' => $roomTypeId,
            'date' => $date,
            'change_type' => self::CHANGE_TYPE_CANCELLATION,
            'change_reason' => '订单取消',
            'before_available' => $beforeAvailable,
            'after_available' => $afterAvailable,
            'operator_id' => $operatorId,
            'reference_id' => $bookingId,
        ]);
    }

    /**
     * 记录手动调整
     */
    public static function logManualChange($hotelId, $roomTypeId, $date, $beforeAvailable, $afterAvailable, $reason, $operatorId)
    {
        return static::logChange([
            'hotel_id' => $hotelId,
            'room_type_id' => $roomTypeId,
            'date' => $date,
            'change_type' => self::CHANGE_TYPE_MANUAL,
            'change_reason' => $reason,
            'before_available' => $beforeAvailable,
            'after_available' => $afterAvailable,
            'operator_id' => $operatorId,
        ]);
    }
}
