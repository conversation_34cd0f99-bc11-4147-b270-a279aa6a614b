-- Migration: create hotel_licenses table if not exists
CREATE TABLE IF NOT EXISTS `hotel_licenses` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hotel_id` bigint(20) UNSIGNED NOT NULL COMMENT '酒店ID',
  `license_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资质类型：business/tax/fire/health/tourism',
  `license_data` json NOT NULL COMMENT '资质数据（JSON格式）',
  `license_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证件号码',
  `issue_date` date DEFAULT NULL COMMENT '发证日期',
  `expiration_date` date DEFAULT NULL COMMENT '过期日期',
  `issuing_authority` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发证机关',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active/expired/revoked/inactive',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_hotel_license` (`hotel_id`,`license_type`) USING BTREE,
  KEY `idx_hotel_id` (`hotel_id`) USING BTREE,
  KEY `idx_license_type` (`license_type`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_expiration_date` (`expiration_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='酒店资质证照表';
