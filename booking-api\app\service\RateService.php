<?php

namespace app\service;

use app\model\Hotel;
use app\model\RoomType;
use app\model\RatePlan;
use app\model\RoomRate;
use app\model\OtaChannel;
use app\model\RatePlanOtaChannel;

/**
 * 价格管理服务
 */
class RateService extends BaseService
{
    /**
     * 查询价格
     *
     * @param array $params
     * @return array
     */
    public function queryRates(array $params)
    {
        try {
            $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            $this->validateDateRange($params['start_date'], $params['end_date']);

            $query = RoomRate::with(['hotel', 'roomType', 'ratePlan'])
                ->where('hotel_id', $params['hotel_id'])
                ->whereBetween('date', [$params['start_date'], $params['end_date']])
                ->active();

            // 房型筛选
            if (!empty($params['room_type_id'])) {
                $query->where('room_type_id', $params['room_type_id']);
            }

            // 价格计划筛选
            if (!empty($params['rate_plan_id'])) {
                $query->where('rate_plan_id', $params['rate_plan_id']);
            }

            // 价格范围筛选
            if (!empty($params['min_price']) || !empty($params['max_price'])) {
                $minPrice = $params['min_price'] ?? 0;
                $maxPrice = $params['max_price'] ?? 999999;
                $query->whereBetween('selling_price', [$minPrice, $maxPrice]);
            }

            // 货币筛选
            if (!empty($params['currency'])) {
                $query->where('currency', $params['currency']);
            }

            $rates = $query->orderBy('date')->orderBy('room_type_id')->get();

            return $this->success($rates);

        } catch (\Exception $e) {
            $this->logError('查询价格失败', ['error' => $e->getMessage(), 'params' => $params]);
            return $this->error('查询价格失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新价格
     *
     * @param array $data
     * @return array
     */
    public function updateRate(array $data)
    {
        try {
            $this->validateRequired($data, ['hotel_id', 'room_type_id', 'rate_plan_id', 'date', 'selling_price']);

            if (!$this->validateDate($data['date'])) {
                throw new \InvalidArgumentException('日期格式错误');
            }

            return $this->transaction(function () use ($data) {
                $rate = RoomRate::where('hotel_id', $data['hotel_id'])
                    ->where('room_type_id', $data['room_type_id'])
                    ->where('rate_plan_id', $data['rate_plan_id'])
                    ->where('date', $data['date'])
                    ->first();

                if ($rate) {
                    $rate->update($this->filterEmpty($data));
                } else {
                    $rate = RoomRate::create($data);
                }

                $this->logInfo('更新价格成功', [
                    'hotel_id' => $data['hotel_id'],
                    'room_type_id' => $data['room_type_id'],
                    'rate_plan_id' => $data['rate_plan_id'],
                    'date' => $data['date'],
                    'selling_price' => $rate->selling_price
                ]);

                return $this->success($rate, '更新价格成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新价格失败', ['error' => $e->getMessage(), 'data' => $data]);
            return $this->error('更新价格失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新价格
     *
     * @param array $updates
     * @return array
     */
    public function batchUpdateRates(array $updates)
    {
        try {
            if (empty($updates['updates']) || !is_array($updates['updates'])) {
                throw new \InvalidArgumentException('updates参数必须是数组');
            }

            return $this->transaction(function () use ($updates) {
                $results = [];

                foreach ($updates['updates'] as $data) {
                    $this->validateRequired($data, ['hotel_id', 'room_type_id', 'rate_plan_id', 'date', 'selling_price']);

                    $rate = RoomRate::where('hotel_id', $data['hotel_id'])
                        ->where('room_type_id', $data['room_type_id'])
                        ->where('rate_plan_id', $data['rate_plan_id'])
                        ->where('date', $data['date'])
                        ->first();

                    if ($rate) {
                        $rate->update($this->filterEmpty($data));
                    } else {
                        $rate = RoomRate::create($data);
                    }

                    $results[] = $rate;
                }

                $this->logInfo('批量更新价格成功', ['count' => count($results)]);

                return $this->success($results, '批量更新价格成功');
            });

        } catch (\Exception $e) {
            $this->logError('批量更新价格失败', ['error' => $e->getMessage(), 'updates' => $updates]);
            return $this->error('批量更新价格失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取价格计划列表
     *
     * @param int $hotelId
     * @param array $params
     * @return array
     */
    public function getRatePlans(int $hotelId, array $params = [])
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $query = $hotel->ratePlans()->active();

            // 计划类型筛选
            if (!empty($params['plan_type'])) {
                $query->where('plan_type', $params['plan_type']);
            }

            // 有效期筛选 - 暂时注释，因为表中没有 valid_from/valid_to 字段
            // if (!empty($params['valid_date'])) {
            //     $query->valid($params['valid_date']);
            // }

            // 可退款筛选 - 暂时注释，因为表中没有 is_refundable 字段
            // if (isset($params['is_refundable'])) {
            //     $query->where('is_refundable', (bool)$params['is_refundable']);
            // }

            $ratePlans = $query->orderBy('priority', 'desc')->orderBy('created_at', 'desc')->get();

            return $this->success($ratePlans);

        } catch (\Exception $e) {
            $this->logError('获取价格计划失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('获取价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 按房型分组获取价格计划列表
     *
     * @param int $hotelId
     * @param array $params
     * @return array
     */
    public function getRatePlansByRoomType(int $hotelId, array $params = [])
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            // 获取酒店的房型列表
            $roomTypesQuery = $hotel->roomTypes()->active();

            // 房型筛选
            if (!empty($params['room_type_id'])) {
                $roomTypesQuery->where('id', $params['room_type_id']);
            }

            $roomTypes = $roomTypesQuery->orderBy('sort_order', 'asc')->orderBy('created_at', 'asc')->get();

            $result = [];
            foreach ($roomTypes as $roomType) {
                // 获取该房型的价格计划
                $ratePlansQuery = $roomType->ratePlans()->active();

                // 计划类型筛选
                if (!empty($params['plan_type'])) {
                    $ratePlansQuery->where('plan_type', $params['plan_type']);
                }

                // 关键词搜索
                if (!empty($params['keyword'])) {
                    $ratePlansQuery->where(function ($query) use ($params) {
                        $query->where('name', 'like', '%' . $params['keyword'] . '%')
                              ->orWhere('code', 'like', '%' . $params['keyword'] . '%');
                    });
                }

                // 早餐筛选
                if (isset($params['breakfast_included'])) {
                    $ratePlansQuery->where('breakfast_included', (bool)$params['breakfast_included']);
                }

                // 状态筛选
                if (!empty($params['status'])) {
                    switch ($params['status']) {
                        case 'active':
                            $ratePlansQuery->where('is_active', true);
                            break;
                        case 'inactive':
                            $ratePlansQuery->where('is_active', false);
                            break;
                        case 'published':
                            $ratePlansQuery->where('is_published', true);
                            break;
                        case 'unpublished':
                            $ratePlansQuery->where('is_published', false);
                            break;
                    }
                }

                $ratePlans = $ratePlansQuery->orderBy('priority', 'desc')->orderBy('created_at', 'desc')->get();

                // 只有当房型有价格计划时才添加到结果中
                if ($ratePlans->count() > 0) {
                    // 为每个价格计划生成显示名称
                    $ratePlans->each(function ($ratePlan) use ($roomType) {
                        $ratePlan->display_name = $this->get_plan_name($roomType, $ratePlan);
                        $ratePlan->room_type_name = $roomType->name;
                        $ratePlan->hotel_name = $roomType->hotel->name ?? '';
                    });

                    $result[] = [
                        'room_type' => [
                            'id' => $roomType->id,
                            'name' => $roomType->name,
                            'code' => $roomType->code,
                            'description' => $roomType->description,
                            'max_occupancy' => $roomType->max_occupancy,
                            'bed_type' => $roomType->bed_type,
                            'area' => $roomType->area,
                        ],
                        'rate_plans' => $ratePlans,
                        'rate_plans_count' => $ratePlans->count(),
                    ];
                }
            }

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('按房型获取价格计划失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            return $this->error('按房型获取价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建价格计划
     *
     * @param int $hotelId
     * @param array $data
     * @return array
     */
    public function createRatePlan(int $hotelId, array $data)
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $this->validateRequired($data, ['code', 'name', 'plan_type']);

            return $this->transaction(function () use ($hotel, $data) {
                // 检查价格计划编码在同一酒店内是否重复
                if ($hotel->ratePlans()->where('code', $data['code'])->exists()) {
                    throw new \Exception('价格计划编码在该酒店内已存在');
                }

                // 提取OTA渠道相关数据
                $otaChannelIds = $data['ota_channel_ids'] ?? [];
                $channelConfigs = $data['channel_configs'] ?? [];

                // 移除OTA渠道数据，避免在创建RatePlan时出错
                unset($data['ota_channel_ids'], $data['channel_configs']);

                $data['hotel_id'] = $hotel->id;
                $ratePlan = RatePlan::create($this->filterEmpty($data));

                // 处理OTA渠道绑定
                if (!empty($otaChannelIds)) {
                    $this->bindOtaChannels($ratePlan, $otaChannelIds, $channelConfigs);
                }

                $this->logInfo('创建价格计划成功', [
                    'hotel_id' => $hotel->id,
                    'rate_plan_id' => $ratePlan->id,
                    'rate_plan_name' => $ratePlan->name,
                    'ota_channels_count' => count($otaChannelIds)
                ]);

                // 加载关联数据并返回
                $ratePlan->load([
                    'ratePlanOtaChannels' => function ($query) {
                        $query->with(['otaChannel' => function ($subQuery) {
                            $subQuery->select(['id', 'name', 'code', 'logo_url', 'commission_rate', 'is_active']);
                        }]);
                    }
                ]);

                // 构建响应数据
                $responseData = $ratePlan->toArray();

                // 添加OTA渠道绑定摘要信息
                if ($ratePlan->ratePlanOtaChannels && $ratePlan->ratePlanOtaChannels->count() > 0) {
                    $responseData['ota_channels_summary'] = [
                        'total_channels' => count($otaChannelIds),
                        'active_channels' => $ratePlan->ratePlanOtaChannels->where('is_active', true)->count(),
                        'channels' => $ratePlan->ratePlanOtaChannels->map(function ($binding) {
                            return [
                                'channel_id' => $binding->ota_channel_id,
                                'channel_name' => $binding->otaChannel->name ?? 'Unknown',
                                'channel_code' => $binding->otaChannel->code ?? 'Unknown',
                                'channel_logo_url' => $binding->otaChannel->logo_url ?? null,
                                'commission_rate' => $binding->commission_rate,
                                'markup_rate' => $binding->markup_rate,
                                'markup_amount' => $binding->markup_amount,
                                'is_active' => $binding->is_active,
                                'effective_from' => $binding->effective_from,
                                'effective_to' => $binding->effective_to,
                                'sync_status' => $binding->sync_status ?? 'pending',
                                'created_at' => $binding->created_at,
                                'updated_at' => $binding->updated_at
                            ];
                        })->toArray()
                    ];
                } else {
                    $responseData['ota_channels_summary'] = [
                        'total_channels' => 0,
                        'active_channels' => 0,
                        'channels' => []
                    ];
                }

                return $this->success($responseData, '创建价格计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('创建价格计划失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage(), 'data' => $data]);
            return $this->error('创建价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新价格计划
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param array $data
     * @return array
     */
    public function updateRatePlan(int $hotelId, int $ratePlanId, array $data)
    {
        try {
            $ratePlan = RatePlan::where('hotel_id', $hotelId)->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            return $this->transaction(function () use ($ratePlan, $data) {
                // 如果更新编码，检查是否重复
                if (!empty($data['code']) && $data['code'] !== $ratePlan->code) {
                    if (RatePlan::where('hotel_id', $ratePlan->hotel_id)
                        ->where('code', $data['code'])
                        ->where('id', '!=', $ratePlan->id)
                        ->exists()) {
                        throw new \Exception('价格计划编码已存在');
                    }
                }

                $ratePlan->update($this->filterEmpty($data));

                $this->logInfo('更新价格计划成功', [
                    'hotel_id' => $ratePlan->hotel_id,
                    'rate_plan_id' => $ratePlan->id,
                    'rate_plan_name' => $ratePlan->name
                ]);

                return $this->success($ratePlan, '更新价格计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新价格计划失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return $this->error('更新价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除价格计划
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @return array
     */
    public function deleteRatePlan(int $hotelId, int $ratePlanId)
    {
        try {
            $ratePlan = RatePlan::where('hotel_id', $hotelId)->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            // 检查是否有关联的价格数据
            if ($ratePlan->roomRates()->exists()) {
                return $this->error('价格计划存在关联价格数据，无法删除');
            }

            // 检查是否有关联的订单
            if ($ratePlan->bookings()->exists()) {
                return $this->error('价格计划存在关联订单，无法删除');
            }

            return $this->transaction(function () use ($ratePlan) {
                $ratePlanName = $ratePlan->name;
                $ratePlan->delete();

                $this->logInfo('删除价格计划成功', [
                    'hotel_id' => $ratePlan->hotel_id,
                    'rate_plan_id' => $ratePlan->id,
                    'rate_plan_name' => $ratePlanName
                ]);

                return $this->success(null, '删除价格计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('删除价格计划失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage()
            ]);
            return $this->error('删除价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作价格计划
     *
     * @param string $action
     * @param array $ids
     * @return array
     */
    public function batchActionRatePlans(string $action, array $ids)
    {
        try {
            if (empty($ids)) {
                throw new \InvalidArgumentException('ID列表不能为空');
            }

            return $this->transaction(function () use ($action, $ids) {
                $results = [];
                $errors = [];

                foreach ($ids as $id) {
                    try {
                        switch ($action) {
                            case 'publish':
                                $result = $this->publishRatePlan(0, $id, false);
                                break;
                            case 'unpublish':
                                $result = $this->unpublishRatePlan(0, $id, false);
                                break;
                            case 'activate':
                                $result = $this->activateRatePlan(0, $id, false);
                                break;
                            case 'deactivate':
                                $result = $this->deactivateRatePlan(0, $id, false);
                                break;
                            case 'delete':
                                $result = $this->deleteRatePlan(0, $id);
                                break;
                            default:
                                throw new \InvalidArgumentException('无效的操作类型');
                        }

                        if ($result['success']) {
                            $results[] = $id;
                        } else {
                            $errors[] = ['id' => $id, 'error' => $result['message']];
                        }
                    } catch (\Exception $e) {
                        $errors[] = ['id' => $id, 'error' => $e->getMessage()];
                    }
                }

                $this->logInfo('批量操作价格计划完成', [
                    'action' => $action,
                    'success_count' => count($results),
                    'error_count' => count($errors)
                ]);

                return $this->success([
                    'success_ids' => $results,
                    'errors' => $errors,
                    'success_count' => count($results),
                    'error_count' => count($errors)
                ], '批量操作完成');
            });

        } catch (\Exception $e) {
            $this->logError('批量操作价格计划失败', [
                'action' => $action,
                'ids' => $ids,
                'error' => $e->getMessage()
            ]);
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 发布价格计划
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param bool $validateHotel
     * @return array
     */
    public function publishRatePlan(int $hotelId, int $ratePlanId, bool $validateHotel = true)
    {
        try {
            $query = RatePlan::query();
            if ($validateHotel && $hotelId > 0) {
                $query->where('hotel_id', $hotelId);
            }

            $ratePlan = $query->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            if ($ratePlan->is_published) {
                return $this->error('价格计划已经发布');
            }

            return $this->transaction(function () use ($ratePlan) {
                $ratePlan->update([
                    'is_published' => true,
                    'published_at' => date('Y-m-d H:i:s')
                ]);

                $this->logInfo('发布价格计划成功', [
                    'hotel_id' => $ratePlan->hotel_id,
                    'rate_plan_id' => $ratePlan->id,
                    'rate_plan_name' => $ratePlan->name
                ]);

                return $this->success($ratePlan, '发布价格计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('发布价格计划失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage()
            ]);
            return $this->error('发布价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 取消发布价格计划
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param bool $validateHotel
     * @return array
     */
    public function unpublishRatePlan(int $hotelId, int $ratePlanId, bool $validateHotel = true)
    {
        try {
            $query = RatePlan::query();
            if ($validateHotel && $hotelId > 0) {
                $query->where('hotel_id', $hotelId);
            }

            $ratePlan = $query->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            if (!$ratePlan->is_published) {
                return $this->error('价格计划未发布');
            }

            return $this->transaction(function () use ($ratePlan) {
                $ratePlan->update([
                    'is_published' => false,
                    'unpublished_at' => date('Y-m-d H:i:s')
                ]);

                $this->logInfo('取消发布价格计划成功', [
                    'hotel_id' => $ratePlan->hotel_id,
                    'rate_plan_id' => $ratePlan->id,
                    'rate_plan_name' => $ratePlan->name
                ]);

                return $this->success($ratePlan, '取消发布价格计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('取消发布价格计划失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage()
            ]);
            return $this->error('取消发布价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 激活价格计划
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param bool $validateHotel
     * @return array
     */
    public function activateRatePlan(int $hotelId, int $ratePlanId, bool $validateHotel = true)
    {
        try {
            $query = RatePlan::query();
            if ($validateHotel && $hotelId > 0) {
                $query->where('hotel_id', $hotelId);
            }

            $ratePlan = $query->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            if ($ratePlan->is_active) {
                return $this->error('价格计划已经激活');
            }

            return $this->transaction(function () use ($ratePlan) {
                $ratePlan->update([
                    'is_active' => true,
                    'activated_at' => date('Y-m-d H:i:s')
                ]);

                $this->logInfo('激活价格计划成功', [
                    'hotel_id' => $ratePlan->hotel_id,
                    'rate_plan_id' => $ratePlan->id,
                    'rate_plan_name' => $ratePlan->name
                ]);

                return $this->success($ratePlan, '激活价格计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('激活价格计划失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage()
            ]);
            return $this->error('激活价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 停用价格计划
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param bool $validateHotel
     * @return array
     */
    public function deactivateRatePlan(int $hotelId, int $ratePlanId, bool $validateHotel = true)
    {
        try {
            $query = RatePlan::query();
            if ($validateHotel && $hotelId > 0) {
                $query->where('hotel_id', $hotelId);
            }

            $ratePlan = $query->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            if (!$ratePlan->is_active) {
                return $this->error('价格计划已经停用');
            }

            return $this->transaction(function () use ($ratePlan) {
                $ratePlan->update([
                    'is_active' => false,
                    'deactivated_at' => date('Y-m-d H:i:s')
                ]);

                $this->logInfo('停用价格计划成功', [
                    'hotel_id' => $ratePlan->hotel_id,
                    'rate_plan_id' => $ratePlan->id,
                    'rate_plan_name' => $ratePlan->name
                ]);

                return $this->success($ratePlan, '停用价格计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('停用价格计划失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage()
            ]);
            return $this->error('停用价格计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证价格计划数据
     *
     * @param array $data
     * @return array
     */
    public function validateRatePlanData(array $data)
    {
        try {
            $errors = [];

            // 验证必需字段
            $requiredFields = ['name', 'code', 'hotel_id', 'room_type_id', 'base_price'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    $errors[$field] = "字段 {$field} 是必需的";
                }
            }

            // 验证价格
            if (isset($data['base_price']) && $data['base_price'] <= 0) {
                $errors['base_price'] = '基础价格必须大于0';
            }

            // 验证入住天数限制
            if (isset($data['min_stay']) && isset($data['max_stay'])) {
                if ($data['min_stay'] > $data['max_stay']) {
                    $errors['stay_limit'] = '最少入住天数不能大于最多入住天数';
                }
            }

            // 验证提前预订天数限制
            if (isset($data['min_advance_booking']) && isset($data['max_advance_booking'])) {
                if ($data['min_advance_booking'] > $data['max_advance_booking']) {
                    $errors['advance_booking'] = '最少提前预订天数不能大于最多提前预订天数';
                }
            }

            // 验证客人限制
            if (isset($data['max_adults']) && isset($data['max_occupancy'])) {
                if ($data['max_adults'] > $data['max_occupancy']) {
                    $errors['occupancy'] = '最大成人数不能大于最大入住人数';
                }
            }

            // 验证代码唯一性
            if (!empty($data['code']) && !empty($data['hotel_id'])) {
                $exists = RatePlan::where('hotel_id', $data['hotel_id'])
                    ->where('code', $data['code']);

                if (!empty($data['id'])) {
                    $exists->where('id', '!=', $data['id']);
                }

                if ($exists->exists()) {
                    $errors['code'] = '价格计划代码在该酒店内已存在';
                }
            }

            $isValid = empty($errors);

            return $this->success([
                'valid' => $isValid,
                'errors' => $errors,
                'message' => $isValid ? '数据验证通过' : '数据验证失败'
            ]);

        } catch (\Exception $e) {
            $this->logError('验证价格计划数据失败', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return $this->error('验证失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取价格计划模板
     *
     * @return array
     */
    public function getRatePlanTemplates()
    {
        try {
            // 模拟模板数据，实际应该从数据库或配置文件获取
            $templates = [
                [
                    'id' => 1,
                    'name' => '标准房价模板',
                    'description' => '适用于大部分标准房型的基础价格计划',
                    'config' => [
                        'plan_type' => 'standard',
                        'min_stay' => 1,
                        'max_stay' => 7,
                        'min_advance_booking' => 0,
                        'max_advance_booking' => 365,
                        'breakfast_included' => false,
                        'cancellation_policy' => 'flexible',
                        'cancellation_deadline' => 24,
                        'modification_allowed' => true,
                        'modification_deadline' => 24,
                        'prepayment_required' => false,
                        'max_occupancy' => 2,
                        'max_adults' => 2,
                        'max_children' => 0,
                        'is_active' => true,
                        'is_published' => false,
                        'priority' => 100
                    ]
                ],
                [
                    'id' => 2,
                    'name' => '早鸟优惠模板',
                    'description' => '提前预订享受优惠价格的促销计划',
                    'config' => [
                        'plan_type' => 'promotion',
                        'min_stay' => 2,
                        'max_stay' => 14,
                        'min_advance_booking' => 7,
                        'max_advance_booking' => 90,
                        'breakfast_included' => true,
                        'breakfast_count' => 2,
                        'cancellation_policy' => 'moderate',
                        'cancellation_deadline' => 48,
                        'modification_allowed' => false,
                        'prepayment_required' => true,
                        'prepayment_type' => 'full',
                        'max_occupancy' => 2,
                        'max_adults' => 2,
                        'max_children' => 0,
                        'is_active' => true,
                        'is_published' => false,
                        'priority' => 200
                    ]
                ],
                [
                    'id' => 3,
                    'name' => '商务套餐模板',
                    'description' => '包含会议室和商务服务的套餐计划',
                    'config' => [
                        'plan_type' => 'package',
                        'min_stay' => 1,
                        'max_stay' => 30,
                        'min_advance_booking' => 0,
                        'max_advance_booking' => 365,
                        'breakfast_included' => true,
                        'breakfast_count' => 2,
                        'cancellation_policy' => 'flexible',
                        'cancellation_deadline' => 24,
                        'modification_allowed' => true,
                        'modification_deadline' => 24,
                        'prepayment_required' => false,
                        'max_occupancy' => 3,
                        'max_adults' => 2,
                        'max_children' => 1,
                        'channel_restrictions' => ['online', 'phone', 'corporate'],
                        'is_active' => true,
                        'is_published' => false,
                        'priority' => 150
                    ]
                ]
            ];

            return $this->success($templates);

        } catch (\Exception $e) {
            $this->logError('获取价格计划模板失败', ['error' => $e->getMessage()]);
            return $this->error('获取价格计划模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取早餐类型列表
     *
     * @return array
     */
    public function getBreakfastTypes()
    {
        try {
            // 模拟早餐类型数据
            $breakfastTypes = [
                ['code' => 'continental', 'name' => '欧陆式早餐'],
                ['code' => 'american', 'name' => '美式早餐'],
                ['code' => 'chinese', 'name' => '中式早餐'],
                ['code' => 'buffet', 'name' => '自助早餐'],
                ['code' => 'light', 'name' => '简易早餐'],
            ];

            return $this->success($breakfastTypes);

        } catch (\Exception $e) {
            $this->logError('获取早餐类型失败', ['error' => $e->getMessage()]);
            return $this->error('获取早餐类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取取消政策列表
     *
     * @return array
     */
    public function getCancellationPolicies()
    {
        try {
            // 模拟取消政策数据
            $policies = [
                [
                    'code' => 'flexible',
                    'name' => '灵活取消',
                    'description' => '入住前24小时免费取消',
                    'deadline_hours' => 24,
                    'fee_type' => 'none',
                    'fee_amount' => 0
                ],
                [
                    'code' => 'moderate',
                    'name' => '适中取消',
                    'description' => '入住前48小时免费取消，之后收取一晚房费',
                    'deadline_hours' => 48,
                    'fee_type' => 'nights',
                    'fee_amount' => 1
                ],
                [
                    'code' => 'strict',
                    'name' => '严格取消',
                    'description' => '入住前7天免费取消，之后收取50%房费',
                    'deadline_hours' => 168,
                    'fee_type' => 'percentage',
                    'fee_amount' => 50
                ],
                [
                    'code' => 'non_refundable',
                    'name' => '不可退款',
                    'description' => '预订后不可取消，不可退款',
                    'deadline_hours' => 0,
                    'fee_type' => 'percentage',
                    'fee_amount' => 100
                ]
            ];

            return $this->success($policies);

        } catch (\Exception $e) {
            $this->logError('获取取消政策失败', ['error' => $e->getMessage()]);
            return $this->error('获取取消政策失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取价格计划操作日志
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @return array
     */
    public function getRatePlanLogs(int $hotelId, int $ratePlanId)
    {
        try {
            $ratePlan = RatePlan::where('hotel_id', $hotelId)->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            // 模拟操作日志数据，实际应该从日志表获取
            $logs = [
                [
                    'id' => 1,
                    'action' => 'create',
                    'action_text' => '创建房价计划',
                    'user_id' => 1,
                    'user_name' => '管理员',
                    'details' => '创建了新的房价计划',
                    'created_at' => $ratePlan->created_at
                ],
                [
                    'id' => 2,
                    'action' => 'update',
                    'action_text' => '更新房价计划',
                    'user_id' => 1,
                    'user_name' => '管理员',
                    'details' => '更新了房价计划信息',
                    'created_at' => $ratePlan->updated_at
                ]
            ];

            if ($ratePlan->is_published) {
                $logs[] = [
                    'id' => 3,
                    'action' => 'publish',
                    'action_text' => '发布房价计划',
                    'user_id' => 1,
                    'user_name' => '管理员',
                    'details' => '发布了房价计划',
                    'created_at' => $ratePlan->published_at ?? $ratePlan->updated_at
                ];
            }

            return $this->success($logs);

        } catch (\Exception $e) {
            $this->logError('获取价格计划操作日志失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取操作日志失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取价格计划统计数据
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param array $params
     * @return array
     */
    public function getRatePlanStatistics(int $hotelId, int $ratePlanId, array $params = [])
    {
        try {
            $ratePlan = RatePlan::where('hotel_id', $hotelId)->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            // 模拟统计数据，实际应该从订单和价格表计算
            $statistics = [
                'total_bookings' => rand(100, 1000),
                'total_revenue' => rand(10000, 100000),
                'monthly_bookings' => rand(10, 100),
                'monthly_revenue' => rand(1000, 10000),
                'average_price' => rand(200, 800),
                'occupancy_rate' => rand(60, 95),
                'cancellation_rate' => rand(5, 20),
                'revenue_trend' => [
                    ['date' => date('Y-m-d', strtotime('-6 days')), 'revenue' => rand(500, 2000)],
                    ['date' => date('Y-m-d', strtotime('-5 days')), 'revenue' => rand(500, 2000)],
                    ['date' => date('Y-m-d', strtotime('-4 days')), 'revenue' => rand(500, 2000)],
                    ['date' => date('Y-m-d', strtotime('-3 days')), 'revenue' => rand(500, 2000)],
                    ['date' => date('Y-m-d', strtotime('-2 days')), 'revenue' => rand(500, 2000)],
                    ['date' => date('Y-m-d', strtotime('-1 days')), 'revenue' => rand(500, 2000)],
                    ['date' => date('Y-m-d'), 'revenue' => rand(500, 2000)]
                ]
            ];

            return $this->success($statistics);

        } catch (\Exception $e) {
            $this->logError('获取价格计划统计数据失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取统计数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出价格计划数据
     *
     * @param array $params
     * @return array
     */
    public function exportRatePlans(array $params = [])
    {
        try {
            // 这里应该实现实际的导出逻辑
            // 可以使用 PhpSpreadsheet 或其他库来生成 Excel 文件

            $this->logInfo('导出价格计划数据', ['params' => $params]);

            return $this->success([
                'download_url' => '/api/downloads/rate-plans-export-' . time() . '.xlsx',
                'filename' => 'rate-plans-export-' . date('Y-m-d-H-i-s') . '.xlsx'
            ], '导出任务已创建');

        } catch (\Exception $e) {
            $this->logError('导出价格计划数据失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入价格计划数据
     *
     * @param mixed $file
     * @return array
     */
    public function importRatePlans($file)
    {
        try {
            // 这里应该实现实际的导入逻辑
            // 可以使用 PhpSpreadsheet 或其他库来解析 Excel 文件

            $this->logInfo('导入价格计划数据', ['filename' => $file->getClientOriginalName()]);

            // 模拟导入结果
            $result = [
                'total_rows' => rand(10, 100),
                'success_count' => rand(8, 95),
                'error_count' => rand(0, 5),
                'errors' => []
            ];

            return $this->success($result, '导入完成');

        } catch (\Exception $e) {
            $this->logError('导入价格计划数据失败', [
                'error' => $e->getMessage()
            ]);
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 复制房价计划
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param array $copyData
     * @return array
     */
    public function copyRatePlan(int $hotelId, int $ratePlanId, array $copyData)
    {
        try {
            $originalPlan = RatePlan::where('hotel_id', $hotelId)->find($ratePlanId);
            if (!$originalPlan) {
                return $this->error('原房价计划不存在', 404);
            }

            return $this->transaction(function () use ($originalPlan, $copyData) {
                // 准备复制数据
                $newPlanData = $originalPlan->toArray();

                // 移除不需要复制的字段
                unset($newPlanData['id'], $newPlanData['created_at'], $newPlanData['updated_at']);
                unset($newPlanData['published_at'], $newPlanData['unpublished_at']);
                unset($newPlanData['activated_at'], $newPlanData['deactivated_at']);

                // 应用新的数据
                $newPlanData = array_merge($newPlanData, $copyData);

                // 设置默认状态
                $newPlanData['is_published'] = false;
                $newPlanData['is_active'] = true;

                // 创建新的房价计划
                $newPlan = RatePlan::create($newPlanData);

                // 记录操作日志
                RatePlanLog::logCreate($newPlan, [
                    'user_id' => $this->getCurrentUserId(),
                    'user_name' => $this->getCurrentUserName()
                ]);

                $this->logInfo('复制房价计划成功', [
                    'original_plan_id' => $originalPlan->id,
                    'new_plan_id' => $newPlan->id,
                    'hotel_id' => $originalPlan->hotel_id
                ]);

                return $this->success($newPlan, '复制房价计划成功');
            });

        } catch (\Exception $e) {
            $this->logError('复制房价计划失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'copy_data' => $copyData,
                'error' => $e->getMessage()
            ]);
            return $this->error('复制房价计划失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户ID
     *
     * @return int|null
     */
    private function getCurrentUserId()
    {
        // 这里应该从认证系统获取当前用户ID
        return 1; // 临时返回固定值
    }

    /**
     * 获取当前用户名
     *
     * @return string
     */
    private function getCurrentUserName()
    {
        // 这里应该从认证系统获取当前用户名
        return '管理员'; // 临时返回固定值
    }

    /**
     * 绑定OTA渠道到价格计划
     *
     * @param RatePlan $ratePlan
     * @param array $otaChannelIds
     * @param array $channelConfigs
     * @return void
     * @throws \Exception
     */
    private function bindOtaChannels(RatePlan $ratePlan, array $otaChannelIds, array $channelConfigs = [])
    {
        try {
            // 验证OTA渠道是否存在且有效
            $validChannels = OtaChannel::whereIn('id', $otaChannelIds)
                ->where('is_active', true)
                ->pluck('id')
                ->toArray();

            if (count($validChannels) !== count($otaChannelIds)) {
                $invalidChannels = array_diff($otaChannelIds, $validChannels);
                throw new \Exception('以下OTA渠道不存在或未激活: ' . implode(', ', $invalidChannels));
            }

            // 删除现有的绑定关系
            RatePlanOtaChannel::where('rate_plan_id', $ratePlan->id)->delete();

            // 创建新的绑定关系
            foreach ($otaChannelIds as $channelId) {
                $config = $channelConfigs[$channelId] ?? [];

                // 准备创建数据
                $createData = [
                    'rate_plan_id' => $ratePlan->id,
                    'ota_channel_id' => $channelId,
                    'hotel_id' => $ratePlan->hotel_id,

                    // 渠道特定配置
                    'channel_rate_plan_code' => $config['channel_rate_plan_code'] ?? null,
                    'commission_rate' => $config['commission_rate'] ?? null,
                    'markup_rate' => $config['markup_rate'] ?? 0,
                    'markup_amount' => $config['markup_amount'] ?? 0,

                    // 覆盖配置
                    'min_stay_override' => $config['min_stay_override'] ?? null,
                    'max_stay_override' => $config['max_stay_override'] ?? null,
                    'booking_window_override' => $config['booking_window_override'] ?? null,
                    'cancellation_policy_override' => $config['cancellation_policy_override'] ?? null,

                    // 生效时间
                    'effective_from' => $config['effective_from'] ?? null,
                    'effective_to' => $config['effective_to'] ?? null,

                    // 状态和优先级
                    'is_active' => $config['is_active'] ?? true,
                    'priority' => $config['priority'] ?? 0,

                    // 同步状态
                    'sync_status' => $config['sync_status'] ?? 'pending',
                    'last_sync_at' => $config['last_sync_at'] ?? null,
                    'sync_error_message' => $config['sync_error_message'] ?? null,

                    // 统计数据
                    'total_bookings' => $config['total_bookings'] ?? 0,
                    'total_revenue' => $config['total_revenue'] ?? 0,
                    'last_booking_at' => $config['last_booking_at'] ?? null,
                ];

                // 过滤空值并创建记录
                $filteredData = $this->filterEmpty($createData);
                RatePlanOtaChannel::create($filteredData);

                $this->logInfo('创建OTA渠道绑定记录', [
                    'rate_plan_id' => $ratePlan->id,
                    'ota_channel_id' => $channelId,
                    'config_fields' => array_keys($config),
                    'created_data' => $filteredData
                ]);
            }

            $this->logInfo('绑定OTA渠道成功', [
                'rate_plan_id' => $ratePlan->id,
                'ota_channel_ids' => $otaChannelIds,
                'channels_count' => count($otaChannelIds)
            ]);

        } catch (\Exception $e) {
            $this->logError('绑定OTA渠道失败', [
                'rate_plan_id' => $ratePlan->id,
                'ota_channel_ids' => $otaChannelIds,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 更新价格计划的OTA渠道关联
     *
     * @param int $hotelId
     * @param int $ratePlanId
     * @param array $data
     * @return array
     */
    public function updateRatePlanOtaChannels(int $hotelId, int $ratePlanId, array $data)
    {
        try {
            $ratePlan = RatePlan::where('hotel_id', $hotelId)->find($ratePlanId);
            if (!$ratePlan) {
                return $this->error('价格计划不存在', 404);
            }

            $otaChannelIds = $data['ota_channel_ids'] ?? [];
            $channelConfigs = $data['channel_configs'] ?? [];

            return $this->transaction(function () use ($ratePlan, $otaChannelIds, $channelConfigs) {
                $this->bindOtaChannels($ratePlan, $otaChannelIds, $channelConfigs);

                $this->logInfo('更新价格计划OTA渠道关联成功', [
                    'hotel_id' => $ratePlan->hotel_id,
                    'rate_plan_id' => $ratePlan->id,
                    'ota_channels_count' => count($otaChannelIds)
                ]);

                return $this->success($ratePlan->load('otaChannels'), '更新OTA渠道关联成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新价格计划OTA渠道关联失败', [
                'hotel_id' => $hotelId,
                'rate_plan_id' => $ratePlanId,
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return $this->error('更新OTA渠道关联失败: ' . $e->getMessage());
        }
    }
}
