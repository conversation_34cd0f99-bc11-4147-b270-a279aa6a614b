<?php

namespace app\exception;

use Exception;

/**
 * 业务异常类
 * 用于处理业务逻辑相关的异常
 */
class BusinessException extends Exception
{
    /**
     * 错误代码
     *
     * @var string
     */
    protected $errorCode;

    /**
     * 错误数据
     *
     * @var array
     */
    protected $errorData;

    /**
     * HTTP状态码
     *
     * @var int
     */
    protected $httpStatusCode;

    /**
     * 业务异常代码常量
     */
    const VALIDATION_ERROR = 'VALIDATION_ERROR';
    const RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND';
    const PERMISSION_DENIED = 'PERMISSION_DENIED';
    const BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION';
    const EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR';
    const DATA_CONFLICT = 'DATA_CONFLICT';
    const RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED';
    const MAINTENANCE_MODE = 'MAINTENANCE_MODE';

    // 酒店相关异常
    const HOTEL_NOT_FOUND = 'HOTEL_NOT_FOUND';
    const HOTEL_INACTIVE = 'HOTEL_INACTIVE';
    const ROOM_TYPE_NOT_FOUND = 'ROOM_TYPE_NOT_FOUND';
    const ROOM_TYPE_INACTIVE = 'ROOM_TYPE_INACTIVE';
    const RATE_PLAN_NOT_FOUND = 'RATE_PLAN_NOT_FOUND';
    const RATE_PLAN_INACTIVE = 'RATE_PLAN_INACTIVE';

    // 库存相关异常
    const INSUFFICIENT_INVENTORY = 'INSUFFICIENT_INVENTORY';
    const INVENTORY_NOT_AVAILABLE = 'INVENTORY_NOT_AVAILABLE';
    const INVENTORY_BLOCKED = 'INVENTORY_BLOCKED';
    const OVERBOOKING_LIMIT_EXCEEDED = 'OVERBOOKING_LIMIT_EXCEEDED';

    // 预订相关异常
    const BOOKING_NOT_FOUND = 'BOOKING_NOT_FOUND';
    const BOOKING_ALREADY_CONFIRMED = 'BOOKING_ALREADY_CONFIRMED';
    const BOOKING_ALREADY_CANCELLED = 'BOOKING_ALREADY_CANCELLED';
    const BOOKING_CANNOT_BE_MODIFIED = 'BOOKING_CANNOT_BE_MODIFIED';
    const BOOKING_EXPIRED = 'BOOKING_EXPIRED';
    const INVALID_BOOKING_DATES = 'INVALID_BOOKING_DATES';
    const MINIMUM_STAY_VIOLATION = 'MINIMUM_STAY_VIOLATION';
    const MAXIMUM_STAY_VIOLATION = 'MAXIMUM_STAY_VIOLATION';
    const ADVANCE_BOOKING_VIOLATION = 'ADVANCE_BOOKING_VIOLATION';

    // 支付相关异常
    const PAYMENT_FAILED = 'PAYMENT_FAILED';
    const PAYMENT_TIMEOUT = 'PAYMENT_TIMEOUT';
    const PAYMENT_CANCELLED = 'PAYMENT_CANCELLED';
    const REFUND_FAILED = 'REFUND_FAILED';
    const INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE';

    // OTA同步相关异常
    const OTA_CHANNEL_NOT_FOUND = 'OTA_CHANNEL_NOT_FOUND';
    const OTA_CHANNEL_INACTIVE = 'OTA_CHANNEL_INACTIVE';
    const OTA_API_ERROR = 'OTA_API_ERROR';
    const OTA_AUTHENTICATION_FAILED = 'OTA_AUTHENTICATION_FAILED';
    const OTA_RATE_LIMIT_EXCEEDED = 'OTA_RATE_LIMIT_EXCEEDED';
    const OTA_SYNC_IN_PROGRESS = 'OTA_SYNC_IN_PROGRESS';

    // 供应商相关异常
    const SUPPLIER_NOT_FOUND = 'SUPPLIER_NOT_FOUND';
    const SUPPLIER_INACTIVE = 'SUPPLIER_INACTIVE';
    const SUPPLIER_BLACKLISTED = 'SUPPLIER_BLACKLISTED';
    const SUPPLIER_CONTRACT_EXPIRED = 'SUPPLIER_CONTRACT_EXPIRED';

    /**
     * 构造函数
     *
     * @param string $message 错误消息
     * @param string $errorCode 错误代码
     * @param array $errorData 错误数据
     * @param int $httpStatusCode HTTP状态码
     * @param Exception|null $previous 上一个异常
     */
    public function __construct(
        string $message = '',
        string $errorCode = self::BUSINESS_RULE_VIOLATION,
        array $errorData = [],
        int $httpStatusCode = 400,
        Exception $previous = null
    ) {
        parent::__construct($message, 0, $previous);
        
        $this->errorCode = $errorCode;
        $this->errorData = $errorData;
        $this->httpStatusCode = $httpStatusCode;
    }

    /**
     * 获取错误代码
     *
     * @return string
     */
    public function getErrorCode(): string
    {
        return $this->errorCode;
    }

    /**
     * 获取错误数据
     *
     * @return array
     */
    public function getErrorData(): array
    {
        return $this->errorData;
    }

    /**
     * 获取HTTP状态码
     *
     * @return int
     */
    public function getHttpStatusCode(): int
    {
        return $this->httpStatusCode;
    }

    /**
     * 转换为数组
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'error_code' => $this->errorCode,
            'message' => $this->getMessage(),
            'data' => $this->errorData,
            'http_status_code' => $this->httpStatusCode
        ];
    }

    /**
     * 创建验证错误异常
     *
     * @param string $message
     * @param array $errors
     * @return static
     */
    public static function validationError(string $message = '数据验证失败', array $errors = []): self
    {
        return new static($message, self::VALIDATION_ERROR, ['errors' => $errors], 422);
    }

    /**
     * 创建资源未找到异常
     *
     * @param string $resource
     * @param mixed $id
     * @return static
     */
    public static function resourceNotFound(string $resource, $id = null): self
    {
        $message = $id ? "{$resource} (ID: {$id}) 不存在" : "{$resource} 不存在";
        return new static($message, self::RESOURCE_NOT_FOUND, ['resource' => $resource, 'id' => $id], 404);
    }

    /**
     * 创建权限拒绝异常
     *
     * @param string $action
     * @param string $resource
     * @return static
     */
    public static function permissionDenied(string $action = '', string $resource = ''): self
    {
        $message = $action && $resource ? "无权限执行 {$action} 操作于 {$resource}" : "权限不足";
        return new static($message, self::PERMISSION_DENIED, ['action' => $action, 'resource' => $resource], 403);
    }

    /**
     * 创建数据冲突异常
     *
     * @param string $message
     * @param array $conflictData
     * @return static
     */
    public static function dataConflict(string $message, array $conflictData = []): self
    {
        return new static($message, self::DATA_CONFLICT, $conflictData, 409);
    }

    /**
     * 创建库存不足异常
     *
     * @param int $roomTypeId
     * @param string $date
     * @param int $requested
     * @param int $available
     * @return static
     */
    public static function insufficientInventory(int $roomTypeId, string $date, int $requested, int $available): self
    {
        return new static(
            "库存不足：请求 {$requested} 间，可用 {$available} 间",
            self::INSUFFICIENT_INVENTORY,
            [
                'room_type_id' => $roomTypeId,
                'date' => $date,
                'requested' => $requested,
                'available' => $available
            ],
            400
        );
    }

    /**
     * 创建预订已确认异常
     *
     * @param int $bookingId
     * @return static
     */
    public static function bookingAlreadyConfirmed(int $bookingId): self
    {
        return new static(
            "订单已确认，无法修改",
            self::BOOKING_ALREADY_CONFIRMED,
            ['booking_id' => $bookingId],
            400
        );
    }

    /**
     * 创建预订已取消异常
     *
     * @param int $bookingId
     * @return static
     */
    public static function bookingAlreadyCancelled(int $bookingId): self
    {
        return new static(
            "订单已取消",
            self::BOOKING_ALREADY_CANCELLED,
            ['booking_id' => $bookingId],
            400
        );
    }

    /**
     * 创建无效预订日期异常
     *
     * @param string $checkIn
     * @param string $checkOut
     * @return static
     */
    public static function invalidBookingDates(string $checkIn, string $checkOut): self
    {
        return new static(
            "无效的预订日期：入住日期不能晚于或等于退房日期",
            self::INVALID_BOOKING_DATES,
            ['check_in' => $checkIn, 'check_out' => $checkOut],
            400
        );
    }

    /**
     * 创建支付失败异常
     *
     * @param string $reason
     * @param array $paymentData
     * @return static
     */
    public static function paymentFailed(string $reason, array $paymentData = []): self
    {
        return new static(
            "支付失败：{$reason}",
            self::PAYMENT_FAILED,
            $paymentData,
            400
        );
    }

    /**
     * 创建OTA API错误异常
     *
     * @param string $channel
     * @param string $error
     * @param array $context
     * @return static
     */
    public static function otaApiError(string $channel, string $error, array $context = []): self
    {
        return new static(
            "OTA API错误 ({$channel}): {$error}",
            self::OTA_API_ERROR,
            array_merge(['channel' => $channel], $context),
            500
        );
    }

    /**
     * 创建外部服务错误异常
     *
     * @param string $service
     * @param string $error
     * @param array $context
     * @return static
     */
    public static function externalServiceError(string $service, string $error, array $context = []): self
    {
        return new static(
            "外部服务错误 ({$service}): {$error}",
            self::EXTERNAL_SERVICE_ERROR,
            array_merge(['service' => $service], $context),
            503
        );
    }

    /**
     * 创建频率限制异常
     *
     * @param string $resource
     * @param int $limit
     * @param int $window
     * @return static
     */
    public static function rateLimitExceeded(string $resource, int $limit, int $window): self
    {
        return new static(
            "请求频率超限：{$resource} 在 {$window} 秒内最多允许 {$limit} 次请求",
            self::RATE_LIMIT_EXCEEDED,
            ['resource' => $resource, 'limit' => $limit, 'window' => $window],
            429
        );
    }

    /**
     * 创建维护模式异常
     *
     * @param string $message
     * @return static
     */
    public static function maintenanceMode(string $message = '系统正在维护中，请稍后再试'): self
    {
        return new static($message, self::MAINTENANCE_MODE, [], 503);
    }
}
