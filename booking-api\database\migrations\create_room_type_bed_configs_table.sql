-- 创建房型床位配置表
CREATE TABLE IF NOT EXISTS `room_type_bed_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_type_id` bigint(20) unsigned NOT NULL COMMENT '房型ID',
  `bed_type` bigint(20) unsigned DEFAULT NULL COMMENT '主床型ID',
  `bed_count` int(11) NOT NULL DEFAULT '1' COMMENT '主床数量',
  `bed_size` varchar(100) DEFAULT NULL COMMENT '床尺寸',
  `extra_beds` json DEFAULT NULL COMMENT '额外床位配置',
  `bedding_material` varchar(50) DEFAULT 'cotton' COMMENT '床单材质',
  `pillow_type` varchar(50) DEFAULT 'down' COMMENT '枕头类型',
  `pillow_count` int(11) NOT NULL DEFAULT '2' COMMENT '枕头数量',
  `duvet_type` varchar(50) DEFAULT 'down' COMMENT '被子类型',
  `blanket_provided` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否提供毯子',
  `bedding_change_frequency` varchar(50) DEFAULT 'daily' COMMENT '床品更换频率',
  `bed_notes` text COMMENT '床位说明',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_type_id` (`room_type_id`),
  KEY `idx_bed_type` (`bed_type`),
  KEY `idx_bedding_material` (`bedding_material`),
  KEY `idx_pillow_type` (`pillow_type`),
  KEY `idx_duvet_type` (`duvet_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  CONSTRAINT `fk_room_type_bed_configs_room_type_id` FOREIGN KEY (`room_type_id`) REFERENCES `room_types` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_room_type_bed_configs_bed_type` FOREIGN KEY (`bed_type`) REFERENCES `bed_types` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='房型床位配置表';

-- 插入一些示例数据（可选）
INSERT INTO `room_type_bed_configs` (`room_type_id`, `bed_type`, `bed_count`, `bed_size`, `extra_beds`, `bedding_material`, `pillow_type`, `pillow_count`, `duvet_type`, `blanket_provided`, `bedding_change_frequency`, `bed_notes`) VALUES
(1, 1, 1, '1.8m×2.0m', '[]', 'cotton', 'down', 2, 'down', 1, 'daily', '标准双人间配置'),
(2, 2, 1, '2.0m×2.0m', '[{"bed_type":3,"count":1,"size":"1.2m×2.0m","fee":100.00}]', 'cotton', 'down', 4, 'down', 1, 'daily', '家庭房配置，包含额外床位'),
(3, 4, 1, '2.2m×2.2m', '[]', 'silk', 'memory_foam', 4, 'silk', 1, 'daily', '豪华套房配置，使用高端床品');
