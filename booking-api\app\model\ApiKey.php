<?php

namespace app\model;

/**
 * API密钥模型
 * 对应数据库表：api_keys
 */
class ApiKey extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'api_keys';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'key',
        'secret',
        'user_id',
        'permissions',
        'ip_whitelist',
        'rate_limit',
        'rate_limit_window',
        'is_active',
        'expires_at',
        'last_used_at',
        'usage_count',
        'description'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'user_id' => 'integer',
        'permissions' => 'array',
        'ip_whitelist' => 'array',
        'rate_limit' => 'integer',
        'rate_limit_window' => 'integer',
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
        'last_used_at' => 'datetime',
        'usage_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 应该隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'secret',
    ];

    /**
     * 获取关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 作用域：活跃的API密钥
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：未过期的API密钥
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', ());
        });
    }

    /**
     * 检查API密钥是否过期
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 检查IP是否在白名单中
     */
    public function isIpAllowed(string $ip): bool
    {
        $whitelist = $this->ip_whitelist ?? [];
        
        // 如果白名单为空，允许所有IP
        if (empty($whitelist)) {
            return true;
        }

        // 检查精确匹配
        if (in_array($ip, $whitelist)) {
            return true;
        }

        // 检查CIDR匹配
        foreach ($whitelist as $allowedIp) {
            if ($this->ipInCidr($ip, $allowedIp)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查IP是否在CIDR范围内
     */
    private function ipInCidr(string $ip, string $cidr): bool
    {
        if (strpos($cidr, '/') === false) {
            return $ip === $cidr;
        }

        list($subnet, $mask) = explode('/', $cidr);
        
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return $this->ipv4InCidr($ip, $subnet, (int)$mask);
        } elseif (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            return $this->ipv6InCidr($ip, $subnet, (int)$mask);
        }

        return false;
    }

    /**
     * 检查IPv4是否在CIDR范围内
     */
    private function ipv4InCidr(string $ip, string $subnet, int $mask): bool
    {
        $ipLong = ip2long($ip);
        $subnetLong = ip2long($subnet);
        $maskLong = -1 << (32 - $mask);
        
        return ($ipLong & $maskLong) === ($subnetLong & $maskLong);
    }

    /**
     * 检查IPv6是否在CIDR范围内
     */
    private function ipv6InCidr(string $ip, string $subnet, int $mask): bool
    {
        $ipBin = inet_pton($ip);
        $subnetBin = inet_pton($subnet);
        
        if ($ipBin === false || $subnetBin === false) {
            return false;
        }

        $bytesToCheck = intval($mask / 8);
        $bitsToCheck = $mask % 8;

        // 检查完整字节
        for ($i = 0; $i < $bytesToCheck; $i++) {
            if ($ipBin[$i] !== $subnetBin[$i]) {
                return false;
            }
        }

        // 检查剩余位
        if ($bitsToCheck > 0) {
            $maskByte = 0xFF << (8 - $bitsToCheck);
            return (ord($ipBin[$bytesToCheck]) & $maskByte) === (ord($subnetBin[$bytesToCheck]) & $maskByte);
        }

        return true;
    }

    /**
     * 检查请求频率限制
     */
    public function checkRateLimit(): bool
    {
        if (!$this->rate_limit || !$this->rate_limit_window) {
            return true; // 没有限制
        }

        $cacheKey = "api_rate_limit:{$this->id}";
        $window = $this->rate_limit_window; // 秒
        $limit = $this->rate_limit;

        // 这里应该使用Redis实现滑动窗口限流
        // 暂时简单实现：检查最近一段时间的使用次数
        $recentUsage = $this->getRecentUsageCount($window);
        
        return $recentUsage < $limit;
    }

    /**
     * 获取最近使用次数（简单实现）
     */
    private function getRecentUsageCount(int $windowSeconds): int
    {
        // 这里应该从缓存或日志中获取实际的使用次数
        // 暂时返回0，实际项目中需要实现
        return 0;
    }

    /**
     * 记录API使用情况
     */
    public function recordUsage($request = null): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => date('Y-m-d H:i:s')]);

        // 这里可以记录更详细的使用日志
        if ($request) {
            $this->logApiUsage($request);
        }
    }

    /**
     * 记录API使用日志
     */
    private function logApiUsage($request): void
    {
        // 这里可以记录到专门的API使用日志表
        // 包括请求路径、方法、IP、用户代理等信息
    }

    /**
     * 检查是否有指定权限
     */
    public function hasPermission(string $permission): bool
    {
        $permissions = $this->permissions ?? [];
        return in_array($permission, $permissions) || in_array('*', $permissions);
    }

    /**
     * 添加权限
     */
    public function addPermission(string $permission): self
    {
        $permissions = $this->permissions ?? [];
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->permissions = $permissions;
        }
        return $this;
    }

    /**
     * 移除权限
     */
    public function removePermission(string $permission): self
    {
        $permissions = $this->permissions ?? [];
        $key = array_search($permission, $permissions);
        if ($key !== false) {
            unset($permissions[$key]);
            $this->permissions = array_values($permissions);
        }
        return $this;
    }

    /**
     * 生成API密钥
     */
    public static function generateKey(): string
    {
        return 'ak_' . bin2hex(random_bytes(16));
    }

    /**
     * 生成API密钥和密码
     */
    public static function generateKeyPair(): array
    {
        return [
            'key' => self::generateKey(),
            'secret' => 'sk_' . bin2hex(random_bytes(32))
        ];
    }

    /**
     * 启用API密钥
     */
    public function enable(): bool
    {
        $this->is_active = true;
        return $this->save();
    }

    /**
     * 禁用API密钥
     */
    public function disable(): bool
    {
        $this->is_active = false;
        return $this->save();
    }

    /**
     * 设置过期时间
     */
    public function setExpiresAt($expiresAt): self
    {
        $this->expires_at = $expiresAt;
        return $this;
    }

    /**
     * 设置IP白名单
     */
    public function setIpWhitelist(array $ips): self
    {
        $this->ip_whitelist = $ips;
        return $this;
    }

    /**
     * 设置频率限制
     */
    public function setRateLimit(int $limit, int $windowSeconds = 3600): self
    {
        $this->rate_limit = $limit;
        $this->rate_limit_window = $windowSeconds;
        return $this;
    }

    /**
     * 获取使用统计
     */
    public function getUsageStats(): array
    {
        return [
            'total_usage' => $this->usage_count,
            'last_used_at' => $this->last_used_at,
            'is_active' => $this->is_active,
            'is_expired' => $this->isExpired(),
            'expires_at' => $this->expires_at,
        ];
    }
}
