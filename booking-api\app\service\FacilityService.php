<?php

namespace app\service;

use app\model\HotelFacility;
use think\facade\Db;

class FacilityService
{
    /**
     * 获取酒店设施列表
     */
    public function getHotelFacilities(int $hotelId): array
    {
        try {
            $facilities = HotelFacility::where('hotel_id', $hotelId)
                ->order('id', 'asc')
                ->select()
                ->toArray();

            return $facilities;
        } catch (\Exception $e) {
            throw new \Exception('获取设施列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店设施
     */
    public function updateHotelFacilities(int $hotelId, array $facilities): bool
    {
        try {
            Db::startTrans();

            // 删除现有设施
            HotelFacility::where('hotel_id', $hotelId)->delete();

            // 添加新设施
            foreach ($facilities as $facility) {
                $facilityData = [
                    'hotel_id' => $hotelId,
                    'facility_name' => $facility['facility_name'] ?? '',
                    'facility_category' => $facility['facility_category'] ?? '',
                    'chargeable' => $facility['chargeable'] ?? false,
                    'charge_amount' => $facility['charge_amount'] ?? 0,
                    'charge_unit' => $facility['charge_unit'] ?? '',
                    'location_info' => $facility['location_info'] ?? '',
                    'description' => $facility['description'] ?? '',
                    'active' => $facility['active'] ?? true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];

                HotelFacility::create($facilityData);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception('更新设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加单个设施
     */
    public function addFacility(int $hotelId, array $facilityData): bool
    {
        try {
            $data = [
                'hotel_id' => $hotelId,
                'facility_name' => $facilityData['facility_name'] ?? '',
                'facility_category' => $facilityData['facility_category'] ?? '',
                'chargeable' => $facilityData['chargeable'] ?? false,
                'charge_amount' => $facilityData['charge_amount'] ?? 0,
                'charge_unit' => $facilityData['charge_unit'] ?? '',
                'location_info' => $facilityData['location_info'] ?? '',
                'description' => $facilityData['description'] ?? '',
                'active' => $facilityData['active'] ?? true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            HotelFacility::create($data);
            return true;
        } catch (\Exception $e) {
            throw new \Exception('添加设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除设施
     */
    public function deleteFacility(int $facilityId): bool
    {
        try {
            HotelFacility::destroy($facilityId);
            return true;
        } catch (\Exception $e) {
            throw new \Exception('删除设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取设施分类列表
     */
    public function getFacilityCategories(): array
    {
        return [
            ['value' => 'room_amenities', 'label' => '客房设施'],
            ['value' => 'bathroom_amenities', 'label' => '浴室设施'],
            ['value' => 'entertainment', 'label' => '娱乐设施'],
            ['value' => 'business', 'label' => '商务设施'],
            ['value' => 'dining', 'label' => '餐饮设施'],
            ['value' => 'fitness', 'label' => '健身设施'],
            ['value' => 'spa_wellness', 'label' => '水疗养生'],
            ['value' => 'transportation', 'label' => '交通服务'],
            ['value' => 'internet', 'label' => '网络服务'],
            ['value' => 'accessibility', 'label' => '无障碍设施'],
            ['value' => 'family', 'label' => '家庭设施'],
            ['value' => 'safety', 'label' => '安全设施'],
            ['value' => 'other', 'label' => '其他设施'],
        ];
    }

    /**
     * 批量导入设施模板
     */
    public function importFacilityTemplate(int $hotelId, string $templateType): bool
    {
        $templates = [
            'business' => [
                ['facility_name' => '免费WiFi', 'facility_category' => 'internet', 'chargeable' => false, 'location_info' => 'InHotel'],
                ['facility_name' => '24小时前台', 'facility_category' => 'business', 'chargeable' => false, 'location_info' => 'Lobby'],
                ['facility_name' => '商务中心', 'facility_category' => 'business', 'chargeable' => false, 'location_info' => 'InHotel'],
                ['facility_name' => '会议室', 'facility_category' => 'business', 'chargeable' => true, 'charge_amount' => 200, 'charge_unit' => 'Per Hour', 'location_info' => 'InHotel'],
                ['facility_name' => '行李寄存', 'facility_category' => 'business', 'chargeable' => false, 'location_info' => 'Lobby'],
                ['facility_name' => '叫醒服务', 'facility_category' => 'business', 'chargeable' => false, 'location_info' => 'Room'],
            ],
            'resort' => [
                ['facility_name' => '游泳池', 'facility_category' => 'entertainment', 'chargeable' => false, 'location_info' => 'Pool'],
                ['facility_name' => 'SPA中心', 'facility_category' => 'spa_wellness', 'chargeable' => true, 'charge_amount' => 300, 'charge_unit' => 'Per Time', 'location_info' => 'Spa'],
                ['facility_name' => '健身房', 'facility_category' => 'fitness', 'chargeable' => false, 'location_info' => 'Gym'],
                ['facility_name' => '儿童游乐场', 'facility_category' => 'family', 'chargeable' => false, 'location_info' => 'InHotel'],
                ['facility_name' => '网球场', 'facility_category' => 'entertainment', 'chargeable' => true, 'charge_amount' => 100, 'charge_unit' => 'Per Hour', 'location_info' => 'OutHotel'],
                ['facility_name' => '海滩服务', 'facility_category' => 'entertainment', 'chargeable' => false, 'location_info' => 'OutHotel'],
            ],
            'budget' => [
                ['facility_name' => '免费WiFi', 'facility_category' => 'internet', 'chargeable' => false, 'location_info' => 'InHotel'],
                ['facility_name' => '24小时前台', 'facility_category' => 'business', 'chargeable' => false, 'location_info' => 'Lobby'],
                ['facility_name' => '行李寄存', 'facility_category' => 'business', 'chargeable' => false, 'location_info' => 'Lobby'],
                ['facility_name' => '电梯', 'facility_category' => 'accessibility', 'chargeable' => false, 'location_info' => 'InHotel'],
                ['facility_name' => '空调', 'facility_category' => 'room_amenities', 'chargeable' => false, 'location_info' => 'Room'],
            ],
        ];

        if (!isset($templates[$templateType])) {
            throw new \Exception('模板类型不存在');
        }

        try {
            foreach ($templates[$templateType] as $facility) {
                $this->addFacility($hotelId, $facility);
            }
            return true;
        } catch (\Exception $e) {
            throw new \Exception('导入模板失败: ' . $e->getMessage());
        }
    }
}
