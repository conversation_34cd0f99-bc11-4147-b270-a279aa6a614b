<?php

namespace app\model;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 房型床位配置模型
 */
class RoomTypeBedConfig extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'room_type_bed_configs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'room_type_id',
        'bed_type',
        'bed_count',
        'bed_size',
        'extra_beds',
        'bedding_material',
        'pillow_type',
        'pillow_count',
        'duvet_type',
        'blanket_provided',
        'bedding_change_frequency',
        'bed_notes',
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'bed_type' => 'integer',
        'bed_count' => 'integer',
        'pillow_count' => 'integer',
        'blanket_provided' => 'boolean',
        'extra_beds' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * 关联房型
     *
     * @return BelongsTo
     */
    public function roomType(): BelongsTo
    {
        return $this->belongsTo(RoomType::class, 'room_type_id');
    }

    /**
     * 关联主床型
     *
     * @return BelongsTo
     */
    public function bedType(): BelongsTo
    {
        return $this->belongsTo(BedType::class, 'bed_type');
    }

    /**
     * 获取床位材质名称
     *
     * @return string
     */
    public function getBeddingMaterialNameAttribute(): string
    {
        $materials = [
            'cotton' => '纯棉',
            'linen' => '亚麻',
            'silk' => '真丝',
            'bamboo' => '竹纤维',
            'microfiber' => '超细纤维',
        ];

        return $materials[$this->bedding_material] ?? $this->bedding_material;
    }

    /**
     * 获取枕头类型名称
     *
     * @return string
     */
    public function getPillowTypeNameAttribute(): string
    {
        $types = [
            'down' => '羽绒枕',
            'memory_foam' => '记忆棉枕',
            'latex' => '乳胶枕',
            'fiber' => '纤维枕',
            'buckwheat' => '荞麦枕',
        ];

        return $types[$this->pillow_type] ?? $this->pillow_type;
    }

    /**
     * 获取被子类型名称
     *
     * @return string
     */
    public function getDuvetTypeNameAttribute(): string
    {
        $types = [
            'down' => '羽绒被',
            'cotton' => '棉被',
            'silk' => '蚕丝被',
            'wool' => '羊毛被',
            'synthetic' => '合成纤维被',
        ];

        return $types[$this->duvet_type] ?? $this->duvet_type;
    }

    /**
     * 获取床品更换频率名称
     *
     * @return string
     */
    public function getBeddingChangeFrequencyNameAttribute(): string
    {
        $frequencies = [
            'daily' => '每日更换',
            'on_request' => '按需更换',
            'every_2_days' => '每2天更换',
            'weekly' => '每周更换',
        ];

        return $frequencies[$this->bedding_change_frequency] ?? $this->bedding_change_frequency;
    }

    /**
     * 获取额外床位总费用
     *
     * @return float
     */
    public function getExtraBedsTotalFeeAttribute(): float
    {
        if (empty($this->extra_beds)) {
            return 0.0;
        }

        $totalFee = 0.0;
        foreach ($this->extra_beds as $extraBed) {
            $totalFee += ($extraBed['fee'] ?? 0.0) * ($extraBed['count'] ?? 1);
        }

        return $totalFee;
    }

    /**
     * 获取额外床位总数量
     *
     * @return int
     */
    public function getExtraBedsTotalCountAttribute(): int
    {
        if (empty($this->extra_beds)) {
            return 0;
        }

        $totalCount = 0;
        foreach ($this->extra_beds as $extraBed) {
            $totalCount += $extraBed['count'] ?? 1;
        }

        return $totalCount;
    }

    /**
     * 获取总床位数量（主床 + 额外床位）
     *
     * @return int
     */
    public function getTotalBedCountAttribute(): int
    {
        return $this->bed_count + $this->extra_beds_total_count;
    }

    /**
     * 检查是否有额外床位
     *
     * @return bool
     */
    public function hasExtraBeds(): bool
    {
        return !empty($this->extra_beds) && count($this->extra_beds) > 0;
    }

    /**
     * 获取床位配置摘要
     *
     * @return string
     */
    public function getBedConfigSummaryAttribute(): string
    {
        $summary = [];

        // 主床信息
        if ($this->bed_type && $this->bedType) {
            $summary[] = "主床：{$this->bedType->name} × {$this->bed_count}";
        }

        // 额外床位信息
        if ($this->hasExtraBeds()) {
            foreach ($this->extra_beds as $extraBed) {
                if (!empty($extraBed['bed_type'])) {
                    $bedType = BedType::find($extraBed['bed_type']);
                    if ($bedType) {
                        $count = $extraBed['count'] ?? 1;
                        $fee = $extraBed['fee'] ?? 0;
                        $summary[] = "额外：{$bedType->name} × {$count}";
                        if ($fee > 0) {
                            $summary[] = "(加床费：¥{$fee}/晚)";
                        }
                    }
                }
            }
        }

        return implode('，', $summary);
    }
}
