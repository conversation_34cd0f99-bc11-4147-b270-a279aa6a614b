<?php

namespace app\model;

/**
 * 数据同步日志模型
 * 对应数据库表：sync_logs
 */
class SyncLog extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'sync_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'sync_type',
        'sync_direction',
        'channel_id',
        'supplier_id',
        'hotel_id',
        'external_id',
        'sync_status',
        'request_data',
        'response_data',
        'error_message',
        'error_code',
        'retry_count',
        'max_retries',
        'next_retry_at',
        'started_at',
        'completed_at',
        'execution_time',
        'records_processed',
        'records_success',
        'records_failed',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'channel_id' => 'integer',
        'supplier_id' => 'integer',
        'hotel_id' => 'integer',
        'request_data' => 'array',
        'response_data' => 'array',
        'retry_count' => 'integer',
        'max_retries' => 'integer',
        'next_retry_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'execution_time' => 'decimal:3',
        'records_processed' => 'integer',
        'records_success' => 'integer',
        'records_failed' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 同步类型常量
     */
    const SYNC_TYPE_HOTEL = 'hotel';
    const SYNC_TYPE_ROOM_TYPE = 'room_type';
    const SYNC_TYPE_INVENTORY = 'inventory';
    const SYNC_TYPE_RATE = 'rate';
    const SYNC_TYPE_BOOKING = 'booking';

    /**
     * 同步方向常量
     */
    const DIRECTION_PUSH = 'push';
    const DIRECTION_PULL = 'pull';
    const DIRECTION_BIDIRECTIONAL = 'bidirectional';

    /**
     * 同步状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_PARTIAL = 'partial';

    /**
     * 获取所属渠道
     */
    public function channel()
    {
        return $this->belongsTo(OtaChannel::class, 'channel_id');
    }

    /**
     * 获取所属供应商
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id');
    }

    /**
     * 作用域：按同步类型筛选
     */
    public function scopeBySyncType($query, $syncType)
    {
        return $query->where('sync_type', $syncType);
    }

    /**
     * 作用域：按同步方向筛选
     */
    public function scopeBySyncDirection($query, $direction)
    {
        return $query->where('sync_direction', $direction);
    }

    /**
     * 作用域：按同步状态筛选
     */
    public function scopeBySyncStatus($query, $status)
    {
        return $query->where('sync_status', $status);
    }

    /**
     * 作用域：按渠道筛选
     */
    public function scopeByChannel($query, $channelId)
    {
        return $query->where('channel_id', $channelId);
    }

    /**
     * 作用域：按供应商筛选
     */
    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：成功的同步
     */
    public function scopeSuccess($query)
    {
        return $query->where('sync_status', self::STATUS_SUCCESS);
    }

    /**
     * 作用域：失败的同步
     */
    public function scopeFailed($query)
    {
        return $query->where('sync_status', self::STATUS_FAILED);
    }

    /**
     * 作用域：待重试的同步
     */
    public function scopePendingRetry($query)
    {
        return $query->where('sync_status', self::STATUS_FAILED)
                    ->where('retry_count', '<', 'max_retries')
                    ->where(function ($q) {
                        $q->whereNull('next_retry_at')
                          ->orWhere('next_retry_at', '<=', now());
                    });
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 获取同步类型名称
     */
    public function getSyncTypeNameAttribute()
    {
        $types = [
            self::SYNC_TYPE_HOTEL => '酒店信息',
            self::SYNC_TYPE_ROOM_TYPE => '房型信息',
            self::SYNC_TYPE_INVENTORY => '库存信息',
            self::SYNC_TYPE_RATE => '价格信息',
            self::SYNC_TYPE_BOOKING => '订单信息',
        ];

        return $types[$this->sync_type] ?? '未知';
    }

    /**
     * 获取同步方向名称
     */
    public function getSyncDirectionNameAttribute()
    {
        $directions = [
            self::DIRECTION_PUSH => '推送',
            self::DIRECTION_PULL => '拉取',
            self::DIRECTION_BIDIRECTIONAL => '双向',
        ];

        return $directions[$this->sync_direction] ?? '未知';
    }

    /**
     * 获取同步状态名称
     */
    public function getSyncStatusNameAttribute()
    {
        $statuses = [
            self::STATUS_PENDING => '待处理',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_PARTIAL => '部分成功',
        ];

        return $statuses[$this->sync_status] ?? '未知';
    }

    /**
     * 获取状态颜色
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_PENDING => 'blue',
            self::STATUS_PROCESSING => 'orange',
            self::STATUS_SUCCESS => 'green',
            self::STATUS_FAILED => 'red',
            self::STATUS_PARTIAL => 'yellow',
        ];

        return $colors[$this->sync_status] ?? 'default';
    }

    /**
     * 检查是否成功
     */
    public function isSuccess()
    {
        return $this->sync_status === self::STATUS_SUCCESS;
    }

    /**
     * 检查是否失败
     */
    public function isFailed()
    {
        return $this->sync_status === self::STATUS_FAILED;
    }

    /**
     * 检查是否部分成功
     */
    public function isPartial()
    {
        return $this->sync_status === self::STATUS_PARTIAL;
    }

    /**
     * 检查是否正在处理
     */
    public function isProcessing()
    {
        return $this->sync_status === self::STATUS_PROCESSING;
    }

    /**
     * 检查是否待处理
     */
    public function isPending()
    {
        return $this->sync_status === self::STATUS_PENDING;
    }

    /**
     * 检查是否可以重试
     */
    public function canRetry()
    {
        return $this->isFailed() && $this->retry_count < $this->max_retries;
    }

    /**
     * 检查是否需要重试
     */
    public function needsRetry()
    {
        return $this->canRetry() && 
               (!$this->next_retry_at || $this->next_retry_at <= now());
    }

    /**
     * 开始同步
     */
    public function start()
    {
        $this->update([
            'sync_status' => self::STATUS_PROCESSING,
            'started_at' => now(),
        ]);
    }

    /**
     * 标记为成功
     */
    public function markAsSuccess($responseData = null, $recordsProcessed = null, $recordsSuccess = null)
    {
        $data = [
            'sync_status' => self::STATUS_SUCCESS,
            'completed_at' => now(),
            'execution_time' => $this->started_at ? now()->diffInSeconds($this->started_at) : null,
        ];

        if ($responseData !== null) {
            $data['response_data'] = $responseData;
        }

        if ($recordsProcessed !== null) {
            $data['records_processed'] = $recordsProcessed;
        }

        if ($recordsSuccess !== null) {
            $data['records_success'] = $recordsSuccess;
        }

        $this->update($data);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed($errorMessage = null, $errorCode = null, $responseData = null)
    {
        $data = [
            'sync_status' => self::STATUS_FAILED,
            'completed_at' => now(),
            'execution_time' => $this->started_at ? now()->diffInSeconds($this->started_at) : null,
            'retry_count' => $this->retry_count + 1,
        ];

        if ($errorMessage !== null) {
            $data['error_message'] = $errorMessage;
        }

        if ($errorCode !== null) {
            $data['error_code'] = $errorCode;
        }

        if ($responseData !== null) {
            $data['response_data'] = $responseData;
        }

        // 设置下次重试时间
        if ($this->retry_count + 1 < $this->max_retries) {
            $retryDelay = min(300, pow(2, $this->retry_count + 1) * 60); // 指数退避，最大5分钟
            $data['next_retry_at'] = now()->addSeconds($retryDelay);
        }

        $this->update($data);
    }

    /**
     * 标记为部分成功
     */
    public function markAsPartial($responseData = null, $recordsProcessed = null, $recordsSuccess = null, $recordsFailed = null)
    {
        $data = [
            'sync_status' => self::STATUS_PARTIAL,
            'completed_at' => now(),
            'execution_time' => $this->started_at ? now()->diffInSeconds($this->started_at) : null,
        ];

        if ($responseData !== null) {
            $data['response_data'] = $responseData;
        }

        if ($recordsProcessed !== null) {
            $data['records_processed'] = $recordsProcessed;
        }

        if ($recordsSuccess !== null) {
            $data['records_success'] = $recordsSuccess;
        }

        if ($recordsFailed !== null) {
            $data['records_failed'] = $recordsFailed;
        }

        $this->update($data);
    }

    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute()
    {
        if ($this->records_processed == 0) {
            return 0;
        }

        return round(($this->records_success / $this->records_processed) * 100, 2);
    }

    /**
     * 获取失败率
     */
    public function getFailureRateAttribute()
    {
        if ($this->records_processed == 0) {
            return 0;
        }

        return round(($this->records_failed / $this->records_processed) * 100, 2);
    }

    /**
     * 获取执行时间（格式化）
     */
    public function getFormattedExecutionTimeAttribute()
    {
        if (!$this->execution_time) {
            return '-';
        }

        if ($this->execution_time < 60) {
            return round($this->execution_time, 2) . '秒';
        } elseif ($this->execution_time < 3600) {
            return round($this->execution_time / 60, 2) . '分钟';
        } else {
            return round($this->execution_time / 3600, 2) . '小时';
        }
    }
}
