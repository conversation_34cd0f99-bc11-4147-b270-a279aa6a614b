<?php

namespace app\service;

use support\Db;
use support\Cache;
use support\Log;
use Exception;

/**
 * 系统健康检查服务
 * 用于监控系统各个组件的健康状态
 */
class HealthCheckService
{
    /**
     * 健康状态常量
     */
    const STATUS_HEALTHY = 'healthy';
    const STATUS_DEGRADED = 'degraded';
    const STATUS_UNHEALTHY = 'unhealthy';

    /**
     * 执行完整的健康检查
     *
     * @return array
     */
    public static function checkAll(): array
    {
        $checks = [
            'database' => self::checkDatabase(),
            'cache' => self::checkCache(),
            'disk_space' => self::checkDiskSpace(),
            'memory' => self::checkMemory(),
            'external_services' => self::checkExternalServices(),
            'queue' => self::checkQueue(),
            'logs' => self::checkLogs()
        ];

        // 计算整体健康状态
        $overallStatus = self::calculateOverallStatus($checks);

        return [
            'status' => $overallStatus,
            'timestamp' => date('Y-m-d H:i:s'),
            'checks' => $checks,
            'summary' => self::generateSummary($checks)
        ];
    }

    /**
     * 检查数据库连接
     *
     * @return array
     */
    public static function checkDatabase(): array
    {
        $startTime = microtime(true);
        
        try {
            // 测试数据库连接
            $result = Db::select('SELECT 1 as test');
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            if (empty($result)) {
                return [
                    'status' => self::STATUS_UNHEALTHY,
                    'message' => '数据库查询返回空结果',
                    'response_time' => $responseTime
                ];
            }

            // 检查响应时间
            $status = $responseTime > 1000 ? self::STATUS_DEGRADED : self::STATUS_HEALTHY;
            $message = $responseTime > 1000 ? '数据库响应较慢' : '数据库连接正常';

            return [
                'status' => $status,
                'message' => $message,
                'response_time' => $responseTime,
                'details' => [
                    'connection_count' => self::getDatabaseConnectionCount(),
                    'slow_queries' => self::getSlowQueryCount()
                ]
            ];

        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNHEALTHY,
                'message' => '数据库连接失败: ' . $e->getMessage(),
                'response_time' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * 检查缓存服务
     *
     * @return array
     */
    public static function checkCache(): array
    {
        $startTime = microtime(true);
        
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';

            // 测试缓存写入
            Cache::set($testKey, $testValue, 60);
            
            // 测试缓存读取
            $cachedValue = Cache::get($testKey);
            
            // 清理测试数据
            Cache::delete($testKey);

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            if ($cachedValue !== $testValue) {
                return [
                    'status' => self::STATUS_UNHEALTHY,
                    'message' => '缓存读写不一致',
                    'response_time' => $responseTime
                ];
            }

            $status = $responseTime > 500 ? self::STATUS_DEGRADED : self::STATUS_HEALTHY;
            $message = $responseTime > 500 ? '缓存响应较慢' : '缓存服务正常';

            return [
                'status' => $status,
                'message' => $message,
                'response_time' => $responseTime,
                'details' => [
                    'memory_usage' => self::getCacheMemoryUsage(),
                    'hit_rate' => self::getCacheHitRate()
                ]
            ];

        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNHEALTHY,
                'message' => '缓存服务异常: ' . $e->getMessage(),
                'response_time' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }

    /**
     * 检查磁盘空间
     *
     * @return array
     */
    public static function checkDiskSpace(): array
    {
        try {
            $rootPath = '/';
            $totalBytes = disk_total_space($rootPath);
            $freeBytes = disk_free_space($rootPath);
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercent = round(($usedBytes / $totalBytes) * 100, 2);

            $status = self::STATUS_HEALTHY;
            $message = '磁盘空间充足';

            if ($usagePercent > 90) {
                $status = self::STATUS_UNHEALTHY;
                $message = '磁盘空间严重不足';
            } elseif ($usagePercent > 80) {
                $status = self::STATUS_DEGRADED;
                $message = '磁盘空间不足';
            }

            return [
                'status' => $status,
                'message' => $message,
                'details' => [
                    'total' => self::formatBytes($totalBytes),
                    'used' => self::formatBytes($usedBytes),
                    'free' => self::formatBytes($freeBytes),
                    'usage_percent' => $usagePercent
                ]
            ];

        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNHEALTHY,
                'message' => '磁盘空间检查失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 检查内存使用情况
     *
     * @return array
     */
    public static function checkMemory(): array
    {
        try {
            $memoryUsage = memory_get_usage(true);
            $memoryPeak = memory_get_peak_usage(true);
            $memoryLimit = ini_get('memory_limit');
            
            // 转换内存限制为字节
            $memoryLimitBytes = self::convertToBytes($memoryLimit);
            $usagePercent = round(($memoryUsage / $memoryLimitBytes) * 100, 2);

            $status = self::STATUS_HEALTHY;
            $message = '内存使用正常';

            if ($usagePercent > 90) {
                $status = self::STATUS_UNHEALTHY;
                $message = '内存使用过高';
            } elseif ($usagePercent > 80) {
                $status = self::STATUS_DEGRADED;
                $message = '内存使用较高';
            }

            return [
                'status' => $status,
                'message' => $message,
                'details' => [
                    'current' => self::formatBytes($memoryUsage),
                    'peak' => self::formatBytes($memoryPeak),
                    'limit' => $memoryLimit,
                    'usage_percent' => $usagePercent
                ]
            ];

        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNHEALTHY,
                'message' => '内存检查失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 检查外部服务
     *
     * @return array
     */
    public static function checkExternalServices(): array
    {
        $services = [
            'payment_gateway' => self::checkPaymentGateway(),
            'email_service' => self::checkEmailService(),
            'sms_service' => self::checkSmsService()
        ];

        $healthyCount = 0;
        $totalCount = count($services);

        foreach ($services as $service) {
            if ($service['status'] === self::STATUS_HEALTHY) {
                $healthyCount++;
            }
        }

        $status = self::STATUS_HEALTHY;
        $message = '所有外部服务正常';

        if ($healthyCount === 0) {
            $status = self::STATUS_UNHEALTHY;
            $message = '所有外部服务异常';
        } elseif ($healthyCount < $totalCount) {
            $status = self::STATUS_DEGRADED;
            $message = '部分外部服务异常';
        }

        return [
            'status' => $status,
            'message' => $message,
            'details' => $services
        ];
    }

    /**
     * 检查队列服务
     *
     * @return array
     */
    public static function checkQueue(): array
    {
        // 这里应该实现具体的队列检查逻辑
        return [
            'status' => self::STATUS_HEALTHY,
            'message' => '队列服务正常',
            'details' => [
                'pending_jobs' => 0,
                'failed_jobs' => 0,
                'processed_jobs' => 0
            ]
        ];
    }

    /**
     * 检查日志系统
     *
     * @return array
     */
    public static function checkLogs(): array
    {
        try {
            $logPath = runtime_path() . '/logs';
            
            if (!is_dir($logPath)) {
                return [
                    'status' => self::STATUS_UNHEALTHY,
                    'message' => '日志目录不存在'
                ];
            }

            if (!is_writable($logPath)) {
                return [
                    'status' => self::STATUS_UNHEALTHY,
                    'message' => '日志目录不可写'
                ];
            }

            // 测试日志写入
            Log::info('Health check test log');

            return [
                'status' => self::STATUS_HEALTHY,
                'message' => '日志系统正常',
                'details' => [
                    'log_path' => $logPath,
                    'writable' => true
                ]
            ];

        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNHEALTHY,
                'message' => '日志系统异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 计算整体健康状态
     *
     * @param array $checks
     * @return string
     */
    private static function calculateOverallStatus(array $checks): string
    {
        $unhealthyCount = 0;
        $degradedCount = 0;
        $totalCount = count($checks);

        foreach ($checks as $check) {
            switch ($check['status']) {
                case self::STATUS_UNHEALTHY:
                    $unhealthyCount++;
                    break;
                case self::STATUS_DEGRADED:
                    $degradedCount++;
                    break;
            }
        }

        if ($unhealthyCount > 0) {
            return self::STATUS_UNHEALTHY;
        }

        if ($degradedCount > 0) {
            return self::STATUS_DEGRADED;
        }

        return self::STATUS_HEALTHY;
    }

    /**
     * 生成健康检查摘要
     *
     * @param array $checks
     * @return array
     */
    private static function generateSummary(array $checks): array
    {
        $summary = [
            'total' => count($checks),
            'healthy' => 0,
            'degraded' => 0,
            'unhealthy' => 0
        ];

        foreach ($checks as $check) {
            switch ($check['status']) {
                case self::STATUS_HEALTHY:
                    $summary['healthy']++;
                    break;
                case self::STATUS_DEGRADED:
                    $summary['degraded']++;
                    break;
                case self::STATUS_UNHEALTHY:
                    $summary['unhealthy']++;
                    break;
            }
        }

        return $summary;
    }

    /**
     * 格式化字节数
     *
     * @param int $bytes
     * @return string
     */
    private static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 转换内存限制为字节
     *
     * @param string $val
     * @return int
     */
    private static function convertToBytes(string $val): int
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val) - 1]);
        $val = (int)$val;

        switch ($last) {
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }

    // 以下是辅助方法，需要根据实际情况实现

    private static function getDatabaseConnectionCount(): int
    {
        // 实现获取数据库连接数的逻辑
        return 0;
    }

    private static function getSlowQueryCount(): int
    {
        // 实现获取慢查询数量的逻辑
        return 0;
    }

    private static function getCacheMemoryUsage(): string
    {
        // 实现获取缓存内存使用情况的逻辑
        return '0 MB';
    }

    private static function getCacheHitRate(): float
    {
        // 实现获取缓存命中率的逻辑
        return 0.0;
    }

    private static function checkPaymentGateway(): array
    {
        // 实现支付网关检查逻辑
        return ['status' => self::STATUS_HEALTHY, 'message' => '支付网关正常'];
    }

    private static function checkEmailService(): array
    {
        // 实现邮件服务检查逻辑
        return ['status' => self::STATUS_HEALTHY, 'message' => '邮件服务正常'];
    }

    private static function checkSmsService(): array
    {
        // 实现短信服务检查逻辑
        return ['status' => self::STATUS_HEALTHY, 'message' => '短信服务正常'];
    }
}
