<?php

namespace app\service;

use app\model\Hotel;
use app\model\RoomType;
use app\model\RatePlan;
use app\model\RoomInventory;
use app\model\Rate;
use app\model\OtaChannel;
use app\model\OtaSyncLog;
use support\Db;
use support\Log;

/**
 * OTA数据同步服务类
 * 对标携程EBooking的OTA数据同步功能
 */
class OtaSyncService extends BaseService
{
    /**
     * 支持的OTA渠道
     */
    const SUPPORTED_CHANNELS = [
        'ctrip' => '携程',
        'booking' => 'Booking.com',
        'expedia' => 'Expedia',
        'agoda' => 'Agoda',
        'hotels' => 'Hotels.com',
        'airbnb' => 'Airbnb',
        'meituan' => '美团',
        'qunar' => '去哪儿',
        'fliggy' => '飞猪',
        'tongcheng' => '同程'
    ];

    /**
     * 同步类型
     */
    const SYNC_TYPES = [
        'inventory' => '库存同步',
        'rates' => '价格同步',
        'availability' => '房态同步',
        'restrictions' => '限制同步',
        'bookings' => '订单同步'
    ];

    /**
     * 同步酒店基础信息到OTA
     */
    public function syncHotelInfo($hotelId, $channelCode, $options = [])
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在');
            }

            $channel = OtaChannel::where('channel_code', $channelCode)
                ->where('hotel_id', $hotelId)
                ->where('is_active', true)
                ->first();

            if (!$channel) {
                return $this->error('OTA渠道配置不存在或未激活');
            }

            // 记录同步开始
            $syncLog = $this->createSyncLog($hotelId, $channelCode, 'hotel_info', 'started');

            // 构建酒店信息数据
            $hotelData = $this->buildHotelData($hotel, $channel);

            // 调用OTA API同步酒店信息
            $syncResult = $this->callOtaApi($channel, 'hotel/update', $hotelData);

            if ($syncResult['success']) {
                $this->updateSyncLog($syncLog->id, 'completed', '酒店信息同步成功', $syncResult);
                return $this->success([
                    'sync_log_id' => $syncLog->id,
                    'message' => '酒店信息同步成功',
                    'data' => $syncResult['data']
                ]);
            } else {
                $this->updateSyncLog($syncLog->id, 'failed', $syncResult['message'], $syncResult);
                return $this->error($syncResult['message']);
            }

        } catch (\Exception $e) {
            Log::error('同步酒店信息失败', [
                'hotel_id' => $hotelId,
                'channel' => $channelCode,
                'error' => $e->getMessage()
            ]);
            
            if (isset($syncLog)) {
                $this->updateSyncLog($syncLog->id, 'error', $e->getMessage());
            }
            
            return $this->error('同步酒店信息失败：' . $e->getMessage());
        }
    }

    /**
     * 同步房型信息到OTA
     */
    public function syncRoomTypes($hotelId, $channelCode, $roomTypeIds = null)
    {
        try {
            $hotel = Hotel::find($hotelId);
            if (!$hotel) {
                return $this->error('酒店不存在');
            }

            $channel = OtaChannel::where('channel_code', $channelCode)
                ->where('hotel_id', $hotelId)
                ->where('is_active', true)
                ->first();

            if (!$channel) {
                return $this->error('OTA渠道配置不存在或未激活');
            }

            // 获取要同步的房型
            $roomTypesQuery = RoomType::where('hotel_id', $hotelId)
                ->where('status', 'active');

            if ($roomTypeIds) {
                $roomTypesQuery->whereIn('id', $roomTypeIds);
            }

            $roomTypes = $roomTypesQuery->get();

            if ($roomTypes->isEmpty()) {
                return $this->error('没有找到要同步的房型');
            }

            $syncLog = $this->createSyncLog($hotelId, $channelCode, 'room_types', 'started');
            $syncResults = [];

            foreach ($roomTypes as $roomType) {
                try {
                    // 构建房型数据
                    $roomTypeData = $this->buildRoomTypeData($roomType, $channel);

                    // 调用OTA API同步房型
                    $result = $this->callOtaApi($channel, 'room-type/update', $roomTypeData);

                    $syncResults[] = [
                        'room_type_id' => $roomType->id,
                        'room_type_name' => $roomType->name,
                        'success' => $result['success'],
                        'message' => $result['message'] ?? '',
                        'data' => $result['data'] ?? null
                    ];

                } catch (\Exception $e) {
                    $syncResults[] = [
                        'room_type_id' => $roomType->id,
                        'room_type_name' => $roomType->name,
                        'success' => false,
                        'message' => $e->getMessage(),
                        'data' => null
                    ];
                }
            }

            // 统计同步结果
            $successCount = count(array_filter($syncResults, fn($r) => $r['success']));
            $totalCount = count($syncResults);
            $status = $successCount === $totalCount ? 'completed' : ($successCount > 0 ? 'partial' : 'failed');

            $this->updateSyncLog($syncLog->id, $status, "房型同步完成：成功 {$successCount}/{$totalCount}", [
                'results' => $syncResults,
                'summary' => [
                    'total' => $totalCount,
                    'success' => $successCount,
                    'failed' => $totalCount - $successCount
                ]
            ]);

            return $this->success([
                'sync_log_id' => $syncLog->id,
                'message' => "房型同步完成：成功 {$successCount}/{$totalCount}",
                'results' => $syncResults,
                'summary' => [
                    'total' => $totalCount,
                    'success' => $successCount,
                    'failed' => $totalCount - $successCount
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('同步房型信息失败', [
                'hotel_id' => $hotelId,
                'channel' => $channelCode,
                'error' => $e->getMessage()
            ]);
            
            if (isset($syncLog)) {
                $this->updateSyncLog($syncLog->id, 'error', $e->getMessage());
            }
            
            return $this->error('同步房型信息失败：' . $e->getMessage());
        }
    }

    /**
     * 同步库存和房态到OTA
     */
    public function syncInventory($hotelId, $channelCode, $startDate, $endDate, $roomTypeIds = null)
    {
        try {
            $channel = OtaChannel::where('channel_code', $channelCode)
                ->where('hotel_id', $hotelId)
                ->where('is_active', true)
                ->first();

            if (!$channel) {
                return $this->error('OTA渠道配置不存在或未激活');
            }

            $syncLog = $this->createSyncLog($hotelId, $channelCode, 'inventory', 'started');

            // 获取库存数据
            $inventoryQuery = RoomInventory::where('hotel_id', $hotelId)
                ->whereBetween('date', [$startDate, $endDate]);

            if ($roomTypeIds) {
                $inventoryQuery->whereIn('room_type_id', $roomTypeIds);
            }

            $inventories = $inventoryQuery->get();

            if ($inventories->isEmpty()) {
                $this->updateSyncLog($syncLog->id, 'completed', '没有找到要同步的库存数据');
                return $this->success(['message' => '没有找到要同步的库存数据']);
            }

            // 按房型分组处理
            $inventoryByRoomType = $inventories->groupBy('room_type_id');
            $syncResults = [];

            foreach ($inventoryByRoomType as $roomTypeId => $roomTypeInventories) {
                try {
                    // 构建库存数据
                    $inventoryData = $this->buildInventoryData($roomTypeInventories, $channel);

                    // 调用OTA API同步库存
                    $result = $this->callOtaApi($channel, 'inventory/update', $inventoryData);

                    $syncResults[] = [
                        'room_type_id' => $roomTypeId,
                        'date_range' => $startDate . ' ~ ' . $endDate,
                        'records_count' => $roomTypeInventories->count(),
                        'success' => $result['success'],
                        'message' => $result['message'] ?? '',
                        'data' => $result['data'] ?? null
                    ];

                } catch (\Exception $e) {
                    $syncResults[] = [
                        'room_type_id' => $roomTypeId,
                        'date_range' => $startDate . ' ~ ' . $endDate,
                        'records_count' => $roomTypeInventories->count(),
                        'success' => false,
                        'message' => $e->getMessage(),
                        'data' => null
                    ];
                }
            }

            // 统计同步结果
            $successCount = count(array_filter($syncResults, fn($r) => $r['success']));
            $totalCount = count($syncResults);
            $status = $successCount === $totalCount ? 'completed' : ($successCount > 0 ? 'partial' : 'failed');

            $this->updateSyncLog($syncLog->id, $status, "库存同步完成：成功 {$successCount}/{$totalCount}", [
                'results' => $syncResults,
                'summary' => [
                    'total' => $totalCount,
                    'success' => $successCount,
                    'failed' => $totalCount - $successCount
                ]
            ]);

            return $this->success([
                'sync_log_id' => $syncLog->id,
                'message' => "库存同步完成：成功 {$successCount}/{$totalCount}",
                'results' => $syncResults
            ]);

        } catch (\Exception $e) {
            Log::error('同步库存失败', [
                'hotel_id' => $hotelId,
                'channel' => $channelCode,
                'error' => $e->getMessage()
            ]);
            
            if (isset($syncLog)) {
                $this->updateSyncLog($syncLog->id, 'error', $e->getMessage());
            }
            
            return $this->error('同步库存失败：' . $e->getMessage());
        }
    }

    /**
     * 同步价格到OTA
     */
    public function syncRates($hotelId, $channelCode, $startDate, $endDate, $roomTypeIds = null, $ratePlanIds = null)
    {
        try {
            $channel = OtaChannel::where('channel_code', $channelCode)
                ->where('hotel_id', $hotelId)
                ->where('is_active', true)
                ->first();

            if (!$channel) {
                return $this->error('OTA渠道配置不存在或未激活');
            }

            $syncLog = $this->createSyncLog($hotelId, $channelCode, 'rates', 'started');

            // 获取价格数据
            $ratesQuery = Rate::where('hotel_id', $hotelId)
                ->whereBetween('date', [$startDate, $endDate]);

            if ($roomTypeIds) {
                $ratesQuery->whereIn('room_type_id', $roomTypeIds);
            }

            if ($ratePlanIds) {
                $ratesQuery->whereIn('rate_plan_id', $ratePlanIds);
            }

            $rates = $ratesQuery->get();

            if ($rates->isEmpty()) {
                $this->updateSyncLog($syncLog->id, 'completed', '没有找到要同步的价格数据');
                return $this->success(['message' => '没有找到要同步的价格数据']);
            }

            // 按房型和价格计划分组处理
            $ratesByGroup = $rates->groupBy(function($rate) {
                return $rate->room_type_id . '_' . $rate->rate_plan_id;
            });

            $syncResults = [];

            foreach ($ratesByGroup as $groupKey => $groupRates) {
                try {
                    // 构建价格数据
                    $rateData = $this->buildRateData($groupRates, $channel);

                    // 调用OTA API同步价格
                    $result = $this->callOtaApi($channel, 'rates/update', $rateData);

                    $syncResults[] = [
                        'group_key' => $groupKey,
                        'room_type_id' => $groupRates->first()->room_type_id,
                        'rate_plan_id' => $groupRates->first()->rate_plan_id,
                        'date_range' => $startDate . ' ~ ' . $endDate,
                        'records_count' => $groupRates->count(),
                        'success' => $result['success'],
                        'message' => $result['message'] ?? '',
                        'data' => $result['data'] ?? null
                    ];

                } catch (\Exception $e) {
                    $syncResults[] = [
                        'group_key' => $groupKey,
                        'room_type_id' => $groupRates->first()->room_type_id,
                        'rate_plan_id' => $groupRates->first()->rate_plan_id,
                        'date_range' => $startDate . ' ~ ' . $endDate,
                        'records_count' => $groupRates->count(),
                        'success' => false,
                        'message' => $e->getMessage(),
                        'data' => null
                    ];
                }
            }

            // 统计同步结果
            $successCount = count(array_filter($syncResults, fn($r) => $r['success']));
            $totalCount = count($syncResults);
            $status = $successCount === $totalCount ? 'completed' : ($successCount > 0 ? 'partial' : 'failed');

            $this->updateSyncLog($syncLog->id, $status, "价格同步完成：成功 {$successCount}/{$totalCount}", [
                'results' => $syncResults,
                'summary' => [
                    'total' => $totalCount,
                    'success' => $successCount,
                    'failed' => $totalCount - $successCount
                ]
            ]);

            return $this->success([
                'sync_log_id' => $syncLog->id,
                'message' => "价格同步完成：成功 {$successCount}/{$totalCount}",
                'results' => $syncResults
            ]);

        } catch (\Exception $e) {
            Log::error('同步价格失败', [
                'hotel_id' => $hotelId,
                'channel' => $channelCode,
                'error' => $e->getMessage()
            ]);
            
            if (isset($syncLog)) {
                $this->updateSyncLog($syncLog->id, 'error', $e->getMessage());
            }
            
            return $this->error('同步价格失败：' . $e->getMessage());
        }
    }

    /**
     * 创建同步日志
     */
    private function createSyncLog($hotelId, $channelCode, $syncType, $status)
    {
        return OtaSyncLog::create([
            'hotel_id' => $hotelId,
            'channel_code' => $channelCode,
            'sync_type' => $syncType,
            'status' => $status,
            'started_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 更新同步日志
     */
    private function updateSyncLog($logId, $status, $message = null, $data = null)
    {
        $updateData = [
            'status' => $status,
            'completed_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($message) {
            $updateData['message'] = $message;
        }

        if ($data) {
            $updateData['sync_data'] = json_encode($data, JSON_UNESCAPED_UNICODE);
        }

        return OtaSyncLog::where('id', $logId)->update($updateData);
    }

    /**
     * 构建酒店数据
     */
    private function buildHotelData($hotel, $channel)
    {
        // 根据不同OTA渠道构建相应的数据格式
        // 这里需要根据具体的OTA API文档来实现
        return [
            'hotel_id' => $hotel->id,
            'name' => $hotel->name,
            'description' => $hotel->description,
            'address' => $hotel->address,
            'city' => $hotel->city,
            'country' => $hotel->country,
            'phone' => $hotel->phone,
            'email' => $hotel->email,
            'star_rating' => $hotel->star_rating,
            'facilities' => $hotel->facilities ?? [],
            'policies' => $hotel->policies ?? []
        ];
    }

    /**
     * 构建房型数据
     */
    private function buildRoomTypeData($roomType, $channel)
    {
        return [
            'room_type_id' => $roomType->id,
            'name' => $roomType->name,
            'description' => $roomType->description,
            'bed_type' => $roomType->bed_type,
            'max_occupancy' => $roomType->max_occupancy,
            'area' => $roomType->area,
            'amenities' => $roomType->amenities ?? [],
            'images' => $roomType->images ?? []
        ];
    }

    /**
     * 构建库存数据
     */
    private function buildInventoryData($inventories, $channel)
    {
        $inventoryData = [];
        
        foreach ($inventories as $inventory) {
            $inventoryData[] = [
                'date' => $inventory->date,
                'room_type_id' => $inventory->room_type_id,
                'available_rooms' => $inventory->available_rooms,
                'is_closed' => $inventory->is_closed,
                'is_stop_sale' => $inventory->is_stop_sale,
                'min_stay' => $inventory->min_stay,
                'max_stay' => $inventory->max_stay,
                'close_reason' => $inventory->close_reason,
                'stop_sale_reason' => $inventory->stop_sale_reason
            ];
        }

        return [
            'room_type_id' => $inventories->first()->room_type_id,
            'inventory_data' => $inventoryData
        ];
    }

    /**
     * 构建价格数据
     */
    private function buildRateData($rates, $channel)
    {
        $rateData = [];
        
        foreach ($rates as $rate) {
            $rateData[] = [
                'date' => $rate->date,
                'room_type_id' => $rate->room_type_id,
                'rate_plan_id' => $rate->rate_plan_id,
                'base_price' => $rate->base_price,
                'service_fee' => $rate->service_fee,
                'tax_fee' => $rate->tax_fee,
                'discount_percentage' => $rate->discount_percentage,
                'currency' => $rate->currency
            ];
        }

        return [
            'room_type_id' => $rates->first()->room_type_id,
            'rate_plan_id' => $rates->first()->rate_plan_id,
            'rate_data' => $rateData
        ];
    }

    /**
     * 调用OTA API
     */
    private function callOtaApi($channel, $endpoint, $data)
    {
        // 这里需要根据具体的OTA API来实现
        // 不同的OTA有不同的API格式和认证方式
        
        try {
            $apiUrl = $channel->api_base_url . '/' . $endpoint;
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $channel->api_token
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $responseData = json_decode($response, true);
                return [
                    'success' => true,
                    'data' => $responseData,
                    'message' => '同步成功'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "API调用失败，HTTP状态码：{$httpCode}",
                    'data' => $response
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API调用异常：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}
