<?php

namespace app\controller;

use app\service\HealthCheckService;
use support\Request;
use support\Response;

/**
 * 健康检查控制器
 * 提供系统健康状态检查接口
 */
class HealthController extends BaseController
{
    /**
     * 简单健康检查
     * 用于负载均衡器等快速检查
     *
     * @param Request $request
     * @return Response
     */
    public function ping(Request $request)
    {
        return json([
            'status' => 'ok',
            'timestamp' => time(),
            'message' => 'Service is running'
        ]);
    }

    /**
     * 基础健康检查
     * 检查核心服务状态
     *
     * @param Request $request
     * @return Response
     */
    public function basic(Request $request)
    {
        try {
            $checks = [
                'database' => HealthCheckService::checkDatabase(),
                'cache' => HealthCheckService::checkCache()
            ];

            $isHealthy = true;
            foreach ($checks as $check) {
                if ($check['status'] === HealthCheckService::STATUS_UNHEALTHY) {
                    $isHealthy = false;
                    break;
                }
            }

            $statusCode = $isHealthy ? 200 : 503;

            return json([
                'status' => $isHealthy ? 'healthy' : 'unhealthy',
                'timestamp' => date('Y-m-d H:i:s'),
                'checks' => $checks
            ], $statusCode);

        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'timestamp' => date('Y-m-d H:i:s'),
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 完整健康检查
     * 检查所有系统组件
     *
     * @param Request $request
     * @return Response
     */
    public function full(Request $request)
    {
        try {
            $healthCheck = HealthCheckService::checkAll();
            
            $statusCode = 200;
            switch ($healthCheck['status']) {
                case HealthCheckService::STATUS_UNHEALTHY:
                    $statusCode = 503;
                    break;
                case HealthCheckService::STATUS_DEGRADED:
                    $statusCode = 200; // 降级状态仍返回200，但在响应中标明
                    break;
            }

            return json($healthCheck, $statusCode);

        } catch (\Exception $e) {
            return json([
                'status' => 'error',
                'timestamp' => date('Y-m-d H:i:s'),
                'message' => $e->getMessage(),
                'checks' => []
            ], 500);
        }
    }

    /**
     * 检查特定组件
     *
     * @param Request $request
     * @param string $component
     * @return Response
     */
    public function component(Request $request, $component)
    {
        try {
            $result = null;

            switch ($component) {
                case 'database':
                    $result = HealthCheckService::checkDatabase();
                    break;
                case 'cache':
                    $result = HealthCheckService::checkCache();
                    break;
                case 'disk':
                    $result = HealthCheckService::checkDiskSpace();
                    break;
                case 'memory':
                    $result = HealthCheckService::checkMemory();
                    break;
                case 'external':
                    $result = HealthCheckService::checkExternalServices();
                    break;
                case 'queue':
                    $result = HealthCheckService::checkQueue();
                    break;
                case 'logs':
                    $result = HealthCheckService::checkLogs();
                    break;
                default:
                    return $this->error('不支持的组件: ' . $component, 400);
            }

            $statusCode = $result['status'] === HealthCheckService::STATUS_UNHEALTHY ? 503 : 200;

            return json([
                'component' => $component,
                'timestamp' => date('Y-m-d H:i:s'),
                'result' => $result
            ], $statusCode);

        } catch (\Exception $e) {
            return json([
                'component' => $component,
                'timestamp' => date('Y-m-d H:i:s'),
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取系统信息
     *
     * @param Request $request
     * @return Response
     */
    public function info(Request $request)
    {
        try {
            $info = [
                'system' => [
                    'php_version' => PHP_VERSION,
                    'os' => PHP_OS,
                    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                    'memory_limit' => ini_get('memory_limit'),
                    'max_execution_time' => ini_get('max_execution_time'),
                    'upload_max_filesize' => ini_get('upload_max_filesize'),
                    'post_max_size' => ini_get('post_max_size')
                ],
                'application' => [
                    'name' => config('app.name', 'Booking System'),
                    'version' => config('app.version', '1.0.0'),
                    'environment' => config('app.env', 'production'),
                    'debug' => config('app.debug', false),
                    'timezone' => config('app.default_timezone', 'UTC')
                ],
                'database' => [
                    'driver' => config('database.default.driver', 'mysql'),
                    'host' => config('database.default.host', 'localhost'),
                    'port' => config('database.default.port', 3306),
                    'database' => config('database.default.database', '')
                ],
                'cache' => [
                    'driver' => config('cache.default', 'redis'),
                    'prefix' => config('cache.prefix', '')
                ],
                'runtime' => [
                    'uptime' => $this->getUptime(),
                    'current_memory' => memory_get_usage(true),
                    'peak_memory' => memory_get_peak_usage(true),
                    'load_average' => $this->getLoadAverage()
                ]
            ];

            return $this->success($info);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取系统信息');
        }
    }

    /**
     * 获取系统运行时间
     *
     * @return string
     */
    private function getUptime(): string
    {
        if (function_exists('sys_getloadavg') && file_exists('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptime = floatval($uptime);
            
            $days = floor($uptime / 86400);
            $hours = floor(($uptime % 86400) / 3600);
            $minutes = floor(($uptime % 3600) / 60);
            
            return sprintf('%d天 %d小时 %d分钟', $days, $hours, $minutes);
        }
        
        return 'Unknown';
    }

    /**
     * 获取系统负载
     *
     * @return array|null
     */
    private function getLoadAverage(): ?array
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => round($load[0], 2),
                '5min' => round($load[1], 2),
                '15min' => round($load[2], 2)
            ];
        }
        
        return null;
    }

    /**
     * 获取版本信息
     *
     * @param Request $request
     * @return Response
     */
    public function version(Request $request)
    {
        return json([
            'application' => [
                'name' => config('app.name', 'Booking System'),
                'version' => config('app.version', '1.0.0'),
                'build' => config('app.build', 'unknown'),
                'commit' => config('app.commit', 'unknown')
            ],
            'php' => [
                'version' => PHP_VERSION,
                'extensions' => get_loaded_extensions()
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 获取依赖状态
     *
     * @param Request $request
     * @return Response
     */
    public function dependencies(Request $request)
    {
        try {
            $dependencies = [
                'required_extensions' => [
                    'pdo' => extension_loaded('pdo'),
                    'pdo_mysql' => extension_loaded('pdo_mysql'),
                    'redis' => extension_loaded('redis'),
                    'curl' => extension_loaded('curl'),
                    'json' => extension_loaded('json'),
                    'mbstring' => extension_loaded('mbstring'),
                    'openssl' => extension_loaded('openssl')
                ],
                'optional_extensions' => [
                    'gd' => extension_loaded('gd'),
                    'imagick' => extension_loaded('imagick'),
                    'zip' => extension_loaded('zip'),
                    'xml' => extension_loaded('xml')
                ],
                'directories' => [
                    'storage_writable' => is_writable(runtime_path()),
                    'logs_writable' => is_writable(runtime_path() . '/logs'),
                    'cache_writable' => is_writable(runtime_path() . '/cache')
                ]
            ];

            // 检查是否有必需的扩展缺失
            $missingRequired = array_filter($dependencies['required_extensions'], function($loaded) {
                return !$loaded;
            });

            $status = empty($missingRequired) ? 'ok' : 'error';
            $statusCode = empty($missingRequired) ? 200 : 500;

            return json([
                'status' => $status,
                'dependencies' => $dependencies,
                'missing_required' => array_keys($missingRequired),
                'timestamp' => date('Y-m-d H:i:s')
            ], $statusCode);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取依赖状态');
        }
    }

    /**
     * 获取性能指标
     *
     * @param Request $request
     * @return Response
     */
    public function metrics(Request $request)
    {
        try {
            $metrics = [
                'memory' => [
                    'current' => memory_get_usage(true),
                    'peak' => memory_get_peak_usage(true),
                    'limit' => ini_get('memory_limit')
                ],
                'requests' => [
                    'total' => $this->getRequestCount(),
                    'per_second' => $this->getRequestsPerSecond(),
                    'average_response_time' => $this->getAverageResponseTime()
                ],
                'database' => [
                    'queries_count' => $this->getDatabaseQueryCount(),
                    'slow_queries' => $this->getSlowQueryCount(),
                    'connections' => $this->getDatabaseConnectionCount()
                ],
                'cache' => [
                    'hits' => $this->getCacheHits(),
                    'misses' => $this->getCacheMisses(),
                    'hit_rate' => $this->getCacheHitRate()
                ]
            ];

            return $this->success($metrics);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取性能指标');
        }
    }

    // 以下是辅助方法，需要根据实际情况实现

    private function getRequestCount(): int
    {
        // 实现获取请求总数的逻辑
        return 0;
    }

    private function getRequestsPerSecond(): float
    {
        // 实现获取每秒请求数的逻辑
        return 0.0;
    }

    private function getAverageResponseTime(): float
    {
        // 实现获取平均响应时间的逻辑
        return 0.0;
    }

    private function getDatabaseQueryCount(): int
    {
        // 实现获取数据库查询数的逻辑
        return 0;
    }

    private function getSlowQueryCount(): int
    {
        // 实现获取慢查询数的逻辑
        return 0;
    }

    private function getDatabaseConnectionCount(): int
    {
        // 实现获取数据库连接数的逻辑
        return 0;
    }

    private function getCacheHits(): int
    {
        // 实现获取缓存命中数的逻辑
        return 0;
    }

    private function getCacheMisses(): int
    {
        // 实现获取缓存未命中数的逻辑
        return 0;
    }

    private function getCacheHitRate(): float
    {
        // 实现获取缓存命中率的逻辑
        return 0.0;
    }
}
