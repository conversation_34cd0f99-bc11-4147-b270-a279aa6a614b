<?php

namespace app\model;

/**
 * 订单模型
 * 对应数据库表：bookings
 */
class Booking extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'bookings';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'booking_no',
        'hotel_id',
        'room_type_id',
        'rate_plan_id',
        'user_id',
        'check_in_date',
        'check_out_date',
        'nights',
        'rooms',
        'adults',
        'children',
        'guest_name',
        'guest_phone',
        'guest_email',
        'guest_id_card',
        'guest_nationality',
        'guest_details',
        'room_price',
        'service_fee',
        'tax_fee',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'currency',
        'special_requests',
        'arrival_time',
        'source_channel',
        'source_reference',
        'status',
        'payment_status',
        'confirmation_no',
        'confirmed_at',
        'confirmed_by',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason',
        'checked_in_at',
        'checked_out_at',
        'actual_room_numbers',
        'rating',
        'review',
        'reviewed_at',

        // 新增字段 - 对标携程EBooking
        'booking_type',
        'booking_source',
        'booking_channel',
        'booking_agent_id',
        'booking_company_id',
        'group_booking_id',
        'series_booking_id',
        'parent_booking_id',
        'master_booking_id',
        'linked_bookings',
        'booking_reference',
        'external_booking_id',
        'pms_booking_id',
        'channel_booking_id',
        'ota_booking_id',
        'gds_booking_id',
        'corporate_booking_id',
        'travel_agent_booking_id',
        'tour_operator_booking_id',
        'wholesaler_booking_id',
        'consolidator_booking_id',
        'bed_type_preference',
        'room_location_preference',
        'floor_preference',
        'view_preference',
        'smoking_preference',
        'accessibility_requirements',
        'dietary_requirements',
        'language_preference',
        'communication_preference',
        'marketing_consent',
        'data_processing_consent',
        'third_party_sharing_consent',
        'promotional_consent',
        'newsletter_consent',
        'sms_consent',
        'call_consent',
        'email_consent',
        'push_notification_consent',
        'loyalty_program_number',
        'membership_tier',
        'membership_benefits',
        'corporate_account_id',
        'corporate_rate_code',
        'negotiated_rate_code',
        'promotional_code',
        'discount_code',
        'voucher_code',
        'package_code',
        'campaign_code',
        'affiliate_code',
        'referral_code',
        'tracking_code',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'booking_device',
        'booking_platform',
        'booking_app_version',
        'booking_browser',
        'booking_os',
        'booking_ip_address',
        'booking_location',
        'booking_timezone',
        'booking_language',
        'booking_currency_original',
        'exchange_rate',
        'original_amount',
        'commission_amount',
        'commission_rate',
        'markup_amount',
        'markup_rate',
        'net_amount',
        'gross_amount',
        'tax_breakdown',
        'fee_breakdown',
        'discount_breakdown',
        'payment_breakdown',
        'refund_breakdown',
        'penalty_breakdown',
        'insurance_amount',
        'insurance_details',
        'cancellation_insurance',
        'travel_insurance',
        'covid_insurance',
        'medical_insurance',
        'baggage_insurance',
        'flight_delay_insurance',
        'rental_car_insurance',
        'activity_insurance',
        'equipment_insurance',
        'liability_insurance',
        'property_insurance',
        'business_insurance',
        'event_insurance',
        'weather_insurance',
        'cancellation_policy_details',
        'modification_policy_details',
        'no_show_policy_details',
        'early_departure_policy_details',
        'late_arrival_policy_details',
        'guarantee_policy_details',
        'deposit_policy_details',
        'payment_policy_details',
        'refund_policy_details',
        'penalty_policy_details',
        'upgrade_policy_details',
        'downgrade_policy_details',
        'overbooking_policy_details',
        'force_majeure_policy_details',
        'privacy_policy_acceptance',
        'terms_conditions_acceptance',
        'booking_conditions_acceptance',
        'rate_conditions_acceptance',
        'cancellation_conditions_acceptance',
        'modification_conditions_acceptance',
        'liability_waiver_acceptance',
        'damage_waiver_acceptance',
        'age_verification',
        'identity_verification',
        'credit_card_verification',
        'address_verification',
        'phone_verification',
        'email_verification',
        'document_verification',
        'background_check',
        'credit_check',
        'fraud_check',
        'risk_assessment',
        'compliance_check',
        'sanctions_check',
        'pep_check',
        'aml_check',
        'kyc_check',
        'security_deposit_amount',
        'security_deposit_status',
        'damage_deposit_amount',
        'damage_deposit_status',
        'incidental_deposit_amount',
        'incidental_deposit_status',
        'authorization_amount',
        'authorization_status',
        'pre_authorization_amount',
        'pre_authorization_status',
        'hold_amount',
        'hold_status',
        'guarantee_amount',
        'guarantee_status',
        'collateral_amount',
        'collateral_status'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'room_type_id' => 'integer',
        'rate_plan_id' => 'integer',
        'user_id' => 'integer',
        'check_in_date' => 'date',
        'check_out_date' => 'date',
        'nights' => 'integer',
        'rooms' => 'integer',
        'adults' => 'integer',
        'children' => 'integer',
        'guest_details' => 'array',
        'room_price' => 'decimal:2',
        'service_fee' => 'decimal:2',
        'tax_fee' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'arrival_time' => 'datetime:H:i:s',
        'confirmed_at' => 'datetime',
        'confirmed_by' => 'integer',
        'cancelled_at' => 'datetime',
        'cancelled_by' => 'integer',
        'checked_in_at' => 'datetime',
        'checked_out_at' => 'datetime',
        'actual_room_numbers' => 'array',
        'rating' => 'integer',
        'reviewed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',

        // 新增字段转换
        'booking_agent_id' => 'integer',
        'booking_company_id' => 'integer',
        'group_booking_id' => 'integer',
        'series_booking_id' => 'integer',
        'parent_booking_id' => 'integer',
        'master_booking_id' => 'integer',
        'linked_bookings' => 'array',
        'accessibility_requirements' => 'array',
        'dietary_requirements' => 'array',
        'marketing_consent' => 'boolean',
        'data_processing_consent' => 'boolean',
        'third_party_sharing_consent' => 'boolean',
        'promotional_consent' => 'boolean',
        'newsletter_consent' => 'boolean',
        'sms_consent' => 'boolean',
        'call_consent' => 'boolean',
        'email_consent' => 'boolean',
        'push_notification_consent' => 'boolean',
        'membership_benefits' => 'array',
        'corporate_account_id' => 'integer',
        'exchange_rate' => 'decimal:4',
        'original_amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'commission_rate' => 'decimal:4',
        'markup_amount' => 'decimal:2',
        'markup_rate' => 'decimal:4',
        'net_amount' => 'decimal:2',
        'gross_amount' => 'decimal:2',
        'tax_breakdown' => 'array',
        'fee_breakdown' => 'array',
        'discount_breakdown' => 'array',
        'payment_breakdown' => 'array',
        'refund_breakdown' => 'array',
        'penalty_breakdown' => 'array',
        'insurance_amount' => 'decimal:2',
        'insurance_details' => 'array',
        'cancellation_insurance' => 'boolean',
        'travel_insurance' => 'boolean',
        'covid_insurance' => 'boolean',
        'medical_insurance' => 'boolean',
        'baggage_insurance' => 'boolean',
        'flight_delay_insurance' => 'boolean',
        'rental_car_insurance' => 'boolean',
        'activity_insurance' => 'boolean',
        'equipment_insurance' => 'boolean',
        'liability_insurance' => 'boolean',
        'property_insurance' => 'boolean',
        'business_insurance' => 'boolean',
        'event_insurance' => 'boolean',
        'weather_insurance' => 'boolean',
        'cancellation_policy_details' => 'array',
        'modification_policy_details' => 'array',
        'no_show_policy_details' => 'array',
        'early_departure_policy_details' => 'array',
        'late_arrival_policy_details' => 'array',
        'guarantee_policy_details' => 'array',
        'deposit_policy_details' => 'array',
        'payment_policy_details' => 'array',
        'refund_policy_details' => 'array',
        'penalty_policy_details' => 'array',
        'upgrade_policy_details' => 'array',
        'downgrade_policy_details' => 'array',
        'overbooking_policy_details' => 'array',
        'force_majeure_policy_details' => 'array',
        'privacy_policy_acceptance' => 'boolean',
        'terms_conditions_acceptance' => 'boolean',
        'booking_conditions_acceptance' => 'boolean',
        'rate_conditions_acceptance' => 'boolean',
        'cancellation_conditions_acceptance' => 'boolean',
        'modification_conditions_acceptance' => 'boolean',
        'liability_waiver_acceptance' => 'boolean',
        'damage_waiver_acceptance' => 'boolean',
        'age_verification' => 'boolean',
        'identity_verification' => 'boolean',
        'credit_card_verification' => 'boolean',
        'address_verification' => 'boolean',
        'phone_verification' => 'boolean',
        'email_verification' => 'boolean',
        'document_verification' => 'boolean',
        'background_check' => 'boolean',
        'credit_check' => 'boolean',
        'fraud_check' => 'boolean',
        'risk_assessment' => 'boolean',
        'compliance_check' => 'boolean',
        'sanctions_check' => 'boolean',
        'pep_check' => 'boolean',
        'aml_check' => 'boolean',
        'kyc_check' => 'boolean',
        'security_deposit_amount' => 'decimal:2',
        'damage_deposit_amount' => 'decimal:2',
        'incidental_deposit_amount' => 'decimal:2',
        'authorization_amount' => 'decimal:2',
        'pre_authorization_amount' => 'decimal:2',
        'hold_amount' => 'decimal:2',
        'guarantee_amount' => 'decimal:2',
        'collateral_amount' => 'decimal:2'
    ];

    /**
     * 订单状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_CONFIRMED = 'confirmed';
    const STATUS_CHECKED_IN = 'checked_in';
    const STATUS_CHECKED_OUT = 'checked_out';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_NO_SHOW = 'no_show';

    /**
     * 支付状态常量
     */
    const PAYMENT_STATUS_UNPAID = 'unpaid';
    const PAYMENT_STATUS_PARTIAL_PAID = 'partial_paid';
    const PAYMENT_STATUS_PAID = 'paid';
    const PAYMENT_STATUS_REFUNDED = 'refunded';
    const PAYMENT_STATUS_PARTIAL_REFUND = 'partial_refund';

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取所属房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * 获取价格计划
     */
    public function ratePlan()
    {
        return $this->belongsTo(RatePlan::class);
    }

    /**
     * 获取预订用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取确认人
     */
    public function confirmedByUser()
    {
        return $this->belongsTo(User::class, 'confirmed_by');
    }

    /**
     * 获取取消人
     */
    public function cancelledByUser()
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    /**
     * 获取订单日志
     */
    public function logs()
    {
        return $this->hasMany(BookingLog::class);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待确认订单
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：已确认订单
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', self::STATUS_CONFIRMED);
    }

    /**
     * 作用域：已入住订单
     */
    public function scopeCheckedIn($query)
    {
        return $query->where('status', self::STATUS_CHECKED_IN);
    }

    /**
     * 作用域：已退房订单
     */
    public function scopeCheckedOut($query)
    {
        return $query->where('status', self::STATUS_CHECKED_OUT);
    }

    /**
     * 作用域：已取消订单
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', self::STATUS_CANCELLED);
    }

    /**
     * 作用域：按入住日期筛选
     */
    public function scopeByCheckInDate($query, $date)
    {
        return $query->where('check_in_date', $date);
    }

    /**
     * 作用域：按入住日期范围筛选
     */
    public function scopeByCheckInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('check_in_date', [$startDate, $endDate]);
    }

    /**
     * 作用域：按客人信息筛选
     */
    public function scopeByGuest($query, $guestName = null, $guestPhone = null)
    {
        if ($guestName) {
            $query->where('guest_name', 'like', "%{$guestName}%");
        }
        
        if ($guestPhone) {
            $query->where('guest_phone', 'like', "%{$guestPhone}%");
        }
        
        return $query;
    }

    /**
     * 获取订单状态名称
     */
    public function getStatusNameAttribute()
    {
        $statuses = [
            self::STATUS_PENDING => '待确认',
            self::STATUS_CONFIRMED => '已确认',
            self::STATUS_CHECKED_IN => '已入住',
            self::STATUS_CHECKED_OUT => '已退房',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_NO_SHOW => '未到店',
        ];

        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取支付状态名称
     */
    public function getPaymentStatusNameAttribute()
    {
        $statuses = [
            self::PAYMENT_STATUS_UNPAID => '未支付',
            self::PAYMENT_STATUS_PARTIAL_PAID => '部分支付',
            self::PAYMENT_STATUS_PAID => '已支付',
            self::PAYMENT_STATUS_REFUNDED => '已退款',
            self::PAYMENT_STATUS_PARTIAL_REFUND => '部分退款',
        ];

        return $statuses[$this->payment_status] ?? '未知';
    }

    /**
     * 获取未支付金额
     */
    public function getUnpaidAmountAttribute()
    {
        return max(0, $this->total_amount - $this->paid_amount);
    }

    /**
     * 检查是否可以取消
     */
    public function canCancel()
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]);
    }

    /**
     * 检查是否可以入住
     */
    public function canCheckIn()
    {
        return $this->status === self::STATUS_CONFIRMED;
    }

    /**
     * 检查是否可以退房
     */
    public function canCheckOut()
    {
        return $this->status === self::STATUS_CHECKED_IN;
    }

    /**
     * 确认订单
     */
    public function confirm($confirmationNo = null, $confirmedBy = null)
    {
        $this->status = self::STATUS_CONFIRMED;
        $this->confirmation_no = $confirmationNo ?: $this->generateConfirmationNo();
        $this->confirmed_at = now();
        $this->confirmed_by = $confirmedBy;
        
        return $this->save();
    }

    /**
     * 取消订单
     */
    public function cancel($reason = null, $cancelledBy = null)
    {
        if (!$this->canCancel()) {
            throw new \Exception('订单当前状态不允许取消');
        }
        
        $this->status = self::STATUS_CANCELLED;
        $this->cancelled_at = now();
        $this->cancelled_by = $cancelledBy;
        $this->cancellation_reason = $reason;
        
        return $this->save();
    }

    /**
     * 办理入住
     */
    public function checkIn($roomNumbers = null)
    {
        if (!$this->canCheckIn()) {
            throw new \Exception('订单当前状态不允许入住');
        }
        
        $this->status = self::STATUS_CHECKED_IN;
        $this->checked_in_at = date('Y-m-d H:i:s');
        
        if ($roomNumbers) {
            $this->actual_room_numbers = is_array($roomNumbers) ? $roomNumbers : [$roomNumbers];
        }
        
        return $this->save();
    }

    /**
     * 办理退房
     */
    public function checkOut()
    {
        if (!$this->canCheckOut()) {
            throw new \Exception('订单当前状态不允许退房');
        }
        
        $this->status = self::STATUS_CHECKED_OUT;
        $this->checked_out_at = date('Y-m-d H:i:s');
        
        return $this->save();
    }

    /**
     * 生成订单号
     */
    public static function generateBookingNo()
    {
        return 'BK' . date('Ymd') . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * 生成确认号
     */
    public function generateConfirmationNo()
    {
        return 'CONF' . date('Ymd') . str_pad($this->id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * 计算总金额
     */
    public function calculateTotalAmount()
    {
        return $this->room_price + $this->service_fee + $this->tax_fee - $this->discount_amount;
    }

    /**
     * 更新总金额
     */
    public function updateTotalAmount()
    {
        $this->total_amount = $this->calculateTotalAmount();
        return $this->save();
    }

    /**
     * 获取预订类型名称
     */
    public function getBookingTypeNameAttribute()
    {
        $types = [
            'individual' => '个人预订',
            'group' => '团队预订',
            'corporate' => '企业预订',
            'series' => '系列预订',
            'package' => '套餐预订',
            'allotment' => '配额预订',
            'contract' => '合同预订',
            'wholesale' => '批发预订',
            'consolidator' => '整合预订',
            'tour' => '旅游预订',
            'mice' => 'MICE预订',
            'wedding' => '婚庆预订',
            'event' => '活动预订'
        ];

        return $types[$this->booking_type] ?? '个人预订';
    }

    /**
     * 获取预订来源名称
     */
    public function getBookingSourceNameAttribute()
    {
        $sources = [
            'direct' => '直接预订',
            'website' => '官网预订',
            'mobile_app' => '手机应用',
            'phone' => '电话预订',
            'email' => '邮件预订',
            'walk_in' => '现场预订',
            'ota' => 'OTA平台',
            'gds' => 'GDS系统',
            'travel_agent' => '旅行社',
            'tour_operator' => '旅游运营商',
            'wholesaler' => '批发商',
            'consolidator' => '整合商',
            'corporate' => '企业直订',
            'group' => '团队预订',
            'mice' => 'MICE预订',
            'social_media' => '社交媒体',
            'affiliate' => '联盟营销',
            'referral' => '推荐预订'
        ];

        return $sources[$this->booking_source] ?? '直接预订';
    }

    /**
     * 获取预订渠道名称
     */
    public function getBookingChannelNameAttribute()
    {
        $channels = [
            'direct' => '直销渠道',
            'online' => '在线渠道',
            'offline' => '线下渠道',
            'mobile' => '移动渠道',
            'voice' => '语音渠道',
            'email' => '邮件渠道',
            'social' => '社交渠道',
            'partner' => '合作伙伴',
            'distribution' => '分销渠道',
            'wholesale' => '批发渠道',
            'retail' => '零售渠道'
        ];

        return $channels[$this->booking_channel] ?? '直销渠道';
    }

    /**
     * 获取客人偏好信息
     */
    public function getGuestPreferencesInfo()
    {
        return [
            'bed_type' => $this->bed_type_preference,
            'room_location' => $this->room_location_preference,
            'floor' => $this->floor_preference,
            'view' => $this->view_preference,
            'smoking' => $this->smoking_preference,
            'accessibility' => $this->accessibility_requirements ?? [],
            'dietary' => $this->dietary_requirements ?? [],
            'language' => $this->language_preference,
            'communication' => $this->communication_preference
        ];
    }

    /**
     * 获取同意信息
     */
    public function getConsentInfo()
    {
        return [
            'marketing' => $this->marketing_consent ?? false,
            'data_processing' => $this->data_processing_consent ?? false,
            'third_party_sharing' => $this->third_party_sharing_consent ?? false,
            'promotional' => $this->promotional_consent ?? false,
            'newsletter' => $this->newsletter_consent ?? false,
            'sms' => $this->sms_consent ?? false,
            'call' => $this->call_consent ?? false,
            'email' => $this->email_consent ?? false,
            'push_notification' => $this->push_notification_consent ?? false
        ];
    }

    /**
     * 获取会员信息
     */
    public function getMembershipInfo()
    {
        return [
            'program_number' => $this->loyalty_program_number,
            'tier' => $this->membership_tier,
            'benefits' => $this->membership_benefits ?? [],
            'corporate_account' => $this->corporate_account_id,
            'corporate_rate_code' => $this->corporate_rate_code,
            'negotiated_rate_code' => $this->negotiated_rate_code
        ];
    }

    /**
     * 获取促销代码信息
     */
    public function getPromotionalCodesInfo()
    {
        return [
            'promotional_code' => $this->promotional_code,
            'discount_code' => $this->discount_code,
            'voucher_code' => $this->voucher_code,
            'package_code' => $this->package_code,
            'campaign_code' => $this->campaign_code,
            'affiliate_code' => $this->affiliate_code,
            'referral_code' => $this->referral_code,
            'tracking_code' => $this->tracking_code
        ];
    }

    /**
     * 获取营销追踪信息
     */
    public function getMarketingTrackingInfo()
    {
        return [
            'utm_source' => $this->utm_source,
            'utm_medium' => $this->utm_medium,
            'utm_campaign' => $this->utm_campaign,
            'utm_term' => $this->utm_term,
            'utm_content' => $this->utm_content,
            'device' => $this->booking_device,
            'platform' => $this->booking_platform,
            'app_version' => $this->booking_app_version,
            'browser' => $this->booking_browser,
            'os' => $this->booking_os,
            'ip_address' => $this->booking_ip_address,
            'location' => $this->booking_location,
            'timezone' => $this->booking_timezone,
            'language' => $this->booking_language
        ];
    }

    /**
     * 获取财务信息
     */
    public function getFinancialInfo()
    {
        return [
            'currency_original' => $this->booking_currency_original,
            'exchange_rate' => $this->exchange_rate,
            'original_amount' => $this->original_amount,
            'commission' => [
                'amount' => $this->commission_amount,
                'rate' => $this->commission_rate
            ],
            'markup' => [
                'amount' => $this->markup_amount,
                'rate' => $this->markup_rate
            ],
            'net_amount' => $this->net_amount,
            'gross_amount' => $this->gross_amount,
            'breakdowns' => [
                'tax' => $this->tax_breakdown ?? [],
                'fee' => $this->fee_breakdown ?? [],
                'discount' => $this->discount_breakdown ?? [],
                'payment' => $this->payment_breakdown ?? [],
                'refund' => $this->refund_breakdown ?? [],
                'penalty' => $this->penalty_breakdown ?? []
            ]
        ];
    }

    /**
     * 获取保险信息
     */
    public function getInsuranceInfo()
    {
        return [
            'total_amount' => $this->insurance_amount,
            'details' => $this->insurance_details ?? [],
            'types' => [
                'cancellation' => $this->cancellation_insurance ?? false,
                'travel' => $this->travel_insurance ?? false,
                'covid' => $this->covid_insurance ?? false,
                'medical' => $this->medical_insurance ?? false,
                'baggage' => $this->baggage_insurance ?? false,
                'flight_delay' => $this->flight_delay_insurance ?? false,
                'rental_car' => $this->rental_car_insurance ?? false,
                'activity' => $this->activity_insurance ?? false,
                'equipment' => $this->equipment_insurance ?? false,
                'liability' => $this->liability_insurance ?? false,
                'property' => $this->property_insurance ?? false,
                'business' => $this->business_insurance ?? false,
                'event' => $this->event_insurance ?? false,
                'weather' => $this->weather_insurance ?? false
            ]
        ];
    }

    /**
     * 获取政策详情信息
     */
    public function getPolicyDetailsInfo()
    {
        return [
            'cancellation' => $this->cancellation_policy_details ?? [],
            'modification' => $this->modification_policy_details ?? [],
            'no_show' => $this->no_show_policy_details ?? [],
            'early_departure' => $this->early_departure_policy_details ?? [],
            'late_arrival' => $this->late_arrival_policy_details ?? [],
            'guarantee' => $this->guarantee_policy_details ?? [],
            'deposit' => $this->deposit_policy_details ?? [],
            'payment' => $this->payment_policy_details ?? [],
            'refund' => $this->refund_policy_details ?? [],
            'penalty' => $this->penalty_policy_details ?? [],
            'upgrade' => $this->upgrade_policy_details ?? [],
            'downgrade' => $this->downgrade_policy_details ?? [],
            'overbooking' => $this->overbooking_policy_details ?? [],
            'force_majeure' => $this->force_majeure_policy_details ?? []
        ];
    }

    /**
     * 获取验证状态信息
     */
    public function getVerificationStatusInfo()
    {
        return [
            'age' => $this->age_verification ?? false,
            'identity' => $this->identity_verification ?? false,
            'credit_card' => $this->credit_card_verification ?? false,
            'address' => $this->address_verification ?? false,
            'phone' => $this->phone_verification ?? false,
            'email' => $this->email_verification ?? false,
            'document' => $this->document_verification ?? false,
            'background' => $this->background_check ?? false,
            'credit' => $this->credit_check ?? false,
            'fraud' => $this->fraud_check ?? false,
            'risk' => $this->risk_assessment ?? false,
            'compliance' => $this->compliance_check ?? false,
            'sanctions' => $this->sanctions_check ?? false,
            'pep' => $this->pep_check ?? false,
            'aml' => $this->aml_check ?? false,
            'kyc' => $this->kyc_check ?? false
        ];
    }

    /**
     * 获取押金信息
     */
    public function getDepositInfo()
    {
        return [
            'security_deposit' => [
                'amount' => $this->security_deposit_amount,
                'status' => $this->security_deposit_status
            ],
            'damage_deposit' => [
                'amount' => $this->damage_deposit_amount,
                'status' => $this->damage_deposit_status
            ],
            'incidental_deposit' => [
                'amount' => $this->incidental_deposit_amount,
                'status' => $this->incidental_deposit_status
            ],
            'authorization' => [
                'amount' => $this->authorization_amount,
                'status' => $this->authorization_status
            ],
            'pre_authorization' => [
                'amount' => $this->pre_authorization_amount,
                'status' => $this->pre_authorization_status
            ],
            'hold' => [
                'amount' => $this->hold_amount,
                'status' => $this->hold_status
            ],
            'guarantee' => [
                'amount' => $this->guarantee_amount,
                'status' => $this->guarantee_status
            ],
            'collateral' => [
                'amount' => $this->collateral_amount,
                'status' => $this->collateral_status
            ]
        ];
    }

    /**
     * 检查是否为团队预订
     */
    public function isGroupBooking()
    {
        return $this->booking_type === 'group' || !empty($this->group_booking_id);
    }

    /**
     * 检查是否为企业预订
     */
    public function isCorporateBooking()
    {
        return $this->booking_type === 'corporate' || !empty($this->corporate_account_id);
    }

    /**
     * 检查是否为系列预订
     */
    public function isSeriesBooking()
    {
        return $this->booking_type === 'series' || !empty($this->series_booking_id);
    }

    /**
     * 检查是否需要验证
     */
    public function requiresVerification()
    {
        $verificationStatus = $this->getVerificationStatusInfo();
        return !$verificationStatus['identity'] || !$verificationStatus['credit_card'];
    }

    /**
     * 检查是否通过所有验证
     */
    public function isFullyVerified()
    {
        $verificationStatus = $this->getVerificationStatusInfo();
        $requiredVerifications = ['identity', 'credit_card', 'email', 'phone'];

        foreach ($requiredVerifications as $verification) {
            if (!$verificationStatus[$verification]) {
                return false;
            }
        }

        return true;
    }

    /**
     * 计算详细总金额（包含所有费用）
     */
    public function calculateDetailedTotalAmount()
    {
        $baseAmount = $this->room_price;
        $serviceAmount = $this->service_fee;
        $taxAmount = $this->tax_fee;
        $discountAmount = $this->discount_amount;
        $insuranceAmount = $this->insurance_amount ?? 0;

        // 计算其他费用
        $feeBreakdown = $this->fee_breakdown ?? [];
        $additionalFees = 0;
        foreach ($feeBreakdown as $fee) {
            $additionalFees += $fee['amount'] ?? 0;
        }

        return $baseAmount + $serviceAmount + $taxAmount + $insuranceAmount + $additionalFees - $discountAmount;
    }
}
