<?php

namespace base\AuditLog;

use base\base\DS;
use base\exception\BaseException;
use think\facade\Db;
use base\AuditLog\traits\Context;
use base\AuditLog\traits\Data;
use base\AuditLog\traits\Field;
use base\AuditLog\traits\Lang;
use base\AuditLog\traits\Message;
use base\AuditLog\traits\Module;
use base\AuditLog\traits\Parse;
use base\AuditLog\traits\Scene;
use base\AuditLog\traits\Struct;

abstract class AirLog
{
    use Field;
    use Scene;
    use Data;
    use Message;
    use Module;
    use Context;
    use Parse;
    use Struct;
    use Lang;

    /**
     * 当前操作时间戳
     *
     * @var int
     */
    protected int $time;

    /**
     * @var mixed
     */
    private mixed $config;

    protected string $tableName = '';

    private function __construct()
    {
        $this->init();
        $this->time = time();
        if (!$this->get('user')) {
            $this->set('user', userinfo());
        }
        $this->getModule();
    }

    public function setUser(array $userinfo): self
    {
        $this->set('user', new DS($userinfo));
        return $this;
    }

    /**
     * @param array $config 配置数据
     *
     * @return self
     */
    public function config(array $config = []): self
    {
        $this->config = ds($config);

        return $this;
    }

    public function init(): void
    {
        $this->config();
    }

    /**
     *
     * @return self
     */
    public static function make(): self
    {
        return new static();
    }

    public function fetch(): array
    {
        // 获取场景配置
        if ($this->currentScene) {
            $this->getScene($this->currentScene);
        }

        // 加载配置
        if ($this->config->isEmpty()) {
            $this->config = ds(config('audit_log'));
        }

        // 初始化数据
        $this->initData();

        // 返回格式化后的数据
        return [
            'tableName' => $this->tableName,
            'module' => $this->getModule(),
            'traceId' => $this->getTraceId(),
            'message' => $this->getMessage(),
            'old' => $this->context['old'] ?? [],
            'new' => $this->context['new'] ?? [],
            'diff' => $this->diffData,
            'desc' => $this->diffInfo,
            'lang' => $this->lang,
            'config' => $this->config->toArray(),
            'uid' => $this->get('user.userId', 0),
            'username' => $this->get('user.username', ''),
            'createTime' => $this->time,
        ];
    }

    public function save(): array
    {
        return [
            'tableName' => $this->tableName,
            'dbId' => $this->saveDB($this->fetch())
        ];
    }

    /**
     * @throws BaseException
     */
    private function saveDB(array $data): int
    {
        try {
            return Db::name($data['tableName'])
                ->insertGetId([
                    'module' => $data['module'],
                    'trace_id' => $data['traceId'],
                    'message' => $data['message'],
                    'old' => arr2Json($data['old']),
                    'new' => arr2Json($data['new']),
                    'diff' => arr2Json($data['diff']),
                    'desc' => arr2Json($data['desc']),
                    'lang' => arr2Json($data['lang']),
                    'config' => arr2Json($data['config']),
                    'uid' => $data['uid'],
                    'username' => $data['username'],
                    'create_time' => $data['createTime'],
                ]);
        } catch (\Exception $e) {
            throw new BaseException($e->getMessage());
        }
    }
}
