<?php

namespace app\model;

/**
 * 酒店政策管理器
 */
class HotelPolicyManager
{
    /**
     * 政策类型映射
     */
    const POLICY_TYPES = [
        'check_in_out' => [
            'model' => HotelPoliciesCheckInOutPolicy::class,
            'name' => '入离政策',
            'name_en' => 'Check-in/Check-out Policy',
        ],
        'meal' => [
            'model' => HotelPoliciesMealPolicy::class,
            'name' => '餐食政策',
            'name_en' => 'Meal Policy',
        ],
        'parking' => [
            'model' => HotelPoliciesParkingPolicy::class,
            'name' => '停车场政策',
            'name_en' => 'Parking Policy',
        ],
        'children' => [
            'model' => HotelPoliciesChildrenPolicy::class,
            'name' => '儿童政策',
            'name_en' => 'Children Policy',
        ],
        'pet' => [
            'model' => HotelPoliciesPetPolicy::class,
            'name' => '宠物政策',
            'name_en' => 'Pet Policy',
        ],
        'check_in_method' => [
            'model' => HotelPoliciesCheckInMethodPolicy::class,
            'name' => '入住方式政策',
            'name_en' => 'Check-in Method Policy',
        ],
        'guest_restriction' => [
            'model' => HotelPoliciesGuestRestrictionPolicy::class,
            'name' => '住客限制',
            'name_en' => 'Guest Restriction Policy',
        ],
        'in_hotel' => [
            'model' => HotelPoliciesInHotelPolicy::class,
            'name' => '在店政策',
            'name_en' => 'In-hotel Policy',
        ],
        'deposit' => [
            'model' => HotelPoliciesDepositPolicy::class,
            'name' => '押金政策',
            'name_en' => 'Deposit Policy',
        ],
    ];

    /**
     * 获取所有政策类型
     */
    public static function getAllPolicyTypes(): array
    {
        return array_keys(self::POLICY_TYPES);
    }

    /**
     * 获取政策类型信息
     */
    public static function getPolicyTypeInfo(string $type): ?array
    {
        return self::POLICY_TYPES[$type] ?? null;
    }

    /**
     * 获取政策模型类
     */
    public static function getPolicyModel(string $type): ?string
    {
        return self::POLICY_TYPES[$type]['model'] ?? null;
    }

    /**
     * 验证政策类型
     */
    public static function isValidPolicyType(string $type): bool
    {
        return isset(self::POLICY_TYPES[$type]);
    }

    /**
     * 获取酒店的所有政策
     */
    public static function getHotelPolicies(int $hotelId): array
    {
        $policies = [];
        
        foreach (self::POLICY_TYPES as $type => $config) {
            $modelClass = $config['model'];
            $policy = $modelClass::where('hotel_id', $hotelId)
                ->where('status', 'active')
                ->first();
            
            $policies[$type] = [
                'type' => $type,
                'name' => $config['name'],
                'name_en' => $config['name_en'],
                'data' => $policy ? $policy->toArray() : null,
                'exists' => $policy !== null,
            ];
        }
        
        return $policies;
    }

    /**
     * 获取指定类型的政策
     */
    public static function getHotelPolicyByType(int $hotelId, string $type): ?array
    {
        if (!self::isValidPolicyType($type)) {
            return null;
        }
        
        $config = self::POLICY_TYPES[$type];
        $modelClass = $config['model'];
        $policy = $modelClass::where('hotel_id', $hotelId)
            ->where('status', 'active')
            ->first();
        
        return [
            'type' => $type,
            'name' => $config['name'],
            'name_en' => $config['name_en'],
            'data' => $policy ? $policy->toArray() : null,
            'exists' => $policy !== null,
        ];
    }

    /**
     * 更新指定类型的政策
     */
    public static function updateHotelPolicy(int $hotelId, string $type, array $data): ?array
    {
        if (!self::isValidPolicyType($type)) {
            throw new \InvalidArgumentException("Invalid policy type: {$type}");
        }
        
        $config = self::POLICY_TYPES[$type];
        $modelClass = $config['model'];
        
        // 查找现有政策
        $policy = $modelClass::where('hotel_id', $hotelId)->first();
        
        if ($policy) {
            // 更新现有政策
            $policy->update($data);
        } else {
            // 创建新政策
            $data['hotel_id'] = $hotelId;
            $data['status'] = $data['status'] ?? 'active';
            $policy = $modelClass::create($data);
        }
        
        return [
            'type' => $type,
            'name' => $config['name'],
            'name_en' => $config['name_en'],
            'data' => $policy->fresh()->toArray(),
            'exists' => true,
        ];
    }

    /**
     * 为酒店创建默认政策
     */
    public static function createDefaultPolicies(int $hotelId): array
    {
        $createdPolicies = [];
        
        foreach (self::POLICY_TYPES as $type => $config) {
            $modelClass = $config['model'];
            
            // 检查是否已存在
            $existing = $modelClass::where('hotel_id', $hotelId)->first();
            if (!$existing) {
                // 创建默认政策
                if (method_exists($modelClass, 'createDefault')) {
                    $policy = $modelClass::createDefault($hotelId);
                    $createdPolicies[$type] = $policy->toArray();
                }
            }
        }
        
        return $createdPolicies;
    }

    /**
     * 获取政策类型的本地化名称
     */
    public static function getPolicyTypeName(string $type, string $locale = 'zh'): string
    {
        $config = self::POLICY_TYPES[$type] ?? null;
        if (!$config) {
            return $type;
        }
        
        return $locale === 'en' ? $config['name_en'] : $config['name'];
    }

    /**
     * 获取所有政策类型的本地化列表
     */
    public static function getPolicyTypesList(string $locale = 'zh'): array
    {
        $list = [];
        foreach (self::POLICY_TYPES as $type => $config) {
            $list[$type] = $locale === 'en' ? $config['name_en'] : $config['name'];
        }
        return $list;
    }
}
