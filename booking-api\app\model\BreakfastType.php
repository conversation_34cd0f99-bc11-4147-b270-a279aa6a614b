<?php

namespace app\model;

use support\Model;

/**
 * 早餐类型模型
 */
class BreakfastType extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'breakfast_types';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'is_active',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 作用域：启用的早餐类型
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 作用域：按代码查找
     */
    public function scopeByCode($query, $code)
    {
        return $query->where('code', $code);
    }

    /**
     * 获取所有启用的早餐类型（用于下拉选择）
     *
     * @return array
     */
    public static function getActiveOptions()
    {
        return static::active()->ordered()->get(['code', 'name'])->toArray();
    }
}
