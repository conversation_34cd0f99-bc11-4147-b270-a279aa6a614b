<?php

namespace app\model;

use support\Model;

/**
 * 酒店品牌多语言翻译模型
 */
class HotelBrandTranslation extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'hotel_brand_translations';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_brand_id',
        'locale',
        'name',
        'short_name',
        'description',
        'brand_slogan',
        'brand_story',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_brand_id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取关联的酒店品牌
     */
    public function hotelBrand()
    {
        return $this->belongsTo(HotelBrand::class);
    }

    /**
     * 作用域：按语言筛选
     */
    public function scopeByLocale($query, $locale)
    {
        return $query->where('locale', $locale);
    }
}
