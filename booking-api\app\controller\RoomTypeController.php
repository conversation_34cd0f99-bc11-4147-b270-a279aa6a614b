<?php

namespace app\controller;

use app\service\HotelService;
use app\model\RoomType;
use support\Request;
use support\Response;

/**
 * 房型管理控制器
 */
class RoomTypeController extends BaseController
{
    /**
     * 酒店服务
     *
     * @var HotelService
     */
    private $hotelService;

    public function __construct()
    {
        $this->hotelService = new HotelService();
    }

    /**
     * 获取房型列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = $this->getInput($request);

            $query = RoomType::with(['hotel']);

            // 按酒店筛选
            if (!empty($params['hotel_id'])) {
                $query->where('hotel_id', $params['hotel_id']);
            }

            // 按状态筛选
            if (!empty($params['status'])) {
                $query->where('status', $params['status']);
            }

            // 移除无效字段筛选（表中无 bed_type 字段），仅保留有效筛选项

            // 搜索
            if (!empty($params['search'])) {
                $search = $params['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // 分页
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;

            $result = $query->paginate($perPage, ['*'], 'page', $page);

            return $this->success([
                'items' => $result->items(),
                'pagination' => [
                    'current' => $result->currentPage(),
                    'per_page' => $result->perPage(),
                    'total' => $result->total(),
                    'last_page' => $result->lastPage(),
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型列表');
        }
    }

    /**
     * 创建房型
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'name', 'code']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式（仅保留表存在字段）
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'area' => 'numeric',
                'max_occupancy' => 'integer',
                'max_adults' => 'integer',
                'max_children' => 'integer',
                'bed_count' => 'integer',
                'total_rooms' => 'integer',
                'base_price' => 'numeric'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 仅校验存在于表中的少量可选枚举（其余前端暂不传入）
            $enumValidations = [
                'smoking_policy' => ['non_smoking', 'smoking', 'smoking_allowed', 'designated_area'],
                'status' => ['active', 'inactive', 'maintenance']
            ];
            foreach ($enumValidations as $field => $validValues) {
                if (!empty($data[$field]) && !in_array($data[$field], $validValues)) {
                    return $this->error("字段 {$field} 的值无效");
                }
            }

            // 允许写入的字段（严格按 room_types 表）
            $allowed = [
                'hotel_id','code','name','name_en','description','area','max_occupancy','max_adults','max_children',
                'bed_count','floor_range','total_rooms','room_category','smoking_policy','bathroom_type','window_type',
                'balcony_type','decoration_style','room_orientation','room_dimensions','turndown_service','base_price',
                'extra_bed_price','child_price','status','is_featured','sort_order'
            ];

            // 从创建页面映射（occupancy_xxx 已由前端转为 max_*，这里仅透传）
            $roomTypeData = array_intersect_key($data, array_flip($allowed));
            $roomTypeData['code'] = uniqid();
            // 如果提供 bed_json，则转换为 bed_types 数组
            $bedTypesData = [];
            if (!empty($data['bed_json']) && is_array($data['bed_json'])) {
                $flat = [];
                $primaryId = null;
                foreach ($data['bed_json'] as $idx => $programme) {
                    if (!is_array($programme)) continue;
                    foreach ($programme as $i => $item) {
                        if (!isset($item['bedTypeId'])) continue;
                        $btId = (int)$item['bedTypeId'];
                        $count = (int)($item['num'] ?? 1);
                        if ($primaryId === null) { $primaryId = $btId; }
                        if (!isset($flat[$btId])) { $flat[$btId] = 0; }
                        $flat[$btId] += max(1, $count);
                    }
                }
                $sort = 0;
                foreach ($flat as $btId => $count) {
                    $bedTypesData[] = [
                        'bed_type_id' => $btId,
                        'bed_count' => $count,
                        'is_primary' => $btId === $primaryId,
                        'sort_order' => $sort++,
                    ];
                }
                // 汇总 bed_count 字段
                $roomTypeData['bed_count'] = array_sum(array_column($bedTypesData, 'bed_count'));
            } elseif (!empty($data['bed_types']) && is_array($data['bed_types'])) {
                $bedTypesData = $data['bed_types'];
                if (!isset($roomTypeData['bed_count'])) {
                    $roomTypeData['bed_count'] = array_sum(array_map(function($x){return (int)($x['bed_count'] ?? 1);}, $bedTypesData));
                }
            }

            // 创建房型
            $roomType = RoomType::create($roomTypeData);

            // 处理床型关联
            if (!empty($bedTypesData)) {
                $this->syncBedTypes($roomType, $bedTypesData);
            }

            // 重新加载房型数据，包含关联信息
            $roomType->load(['bedTypes', 'primaryBedType']);

            return $this->success($roomType, '创建房型成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '创建房型');
        }
    }

    /**
     * 获取房型详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;

            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::with(['hotel', 'bedTypes', 'primaryBedType'])->find($roomTypeId);

            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            // 格式化床型配置数据
            $bedTypesFormatted = $roomType->bedTypes->map(function ($bedType) {
                return [
                    'id' => 'bed_' . $bedType->id . '_' . $bedType->pivot->id,
                    'bed_type_id' => $bedType->id,
                    'bed_count' => $bedType->pivot->bed_count,
                    'is_primary' => (bool)$bedType->pivot->is_primary,
                    'sort_order' => $bedType->pivot->sort_order,
                    'bed_type' => [
                        'id' => $bedType->id,
                        'code' => $bedType->code,
                        'name' => $bedType->name,
                        'name_en' => $bedType->name_en,
                        'category' => $bedType->category,
                        'size' => $bedType->size,
                        'max_occupancy' => $bedType->max_occupancy,
                        'icon' => $bedType->icon,
                    ]
                ];
            });

            // 添加格式化的床型配置到响应数据
            $roomTypeData = $roomType->toArray();
            $roomTypeData['bed_types'] = $bedTypesFormatted;

            return $this->success($roomTypeData);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型详情');
        }
    }

    /**
     * 更新房型信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;

            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证数据格式（仅保留表存在字段）
            $formatErrors = $this->validateFormat($data, [
                'area' => 'numeric',
                'max_occupancy' => 'integer',
                'max_adults' => 'integer',
                'max_children' => 'integer',
                'bed_count' => 'integer',
                'total_rooms' => 'integer',
                'base_price' => 'numeric',
                'sort_order' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 如果更新编码，检查是否重复
            if (!empty($data['code']) && $data['code'] !== $roomType->code) {
                $exists = RoomType::where('hotel_id', $roomType->hotel_id)
                    ->where('code', $data['code'])
                    ->where('id', '!=', $roomType->id)
                    ->exists();
                
                if ($exists) {
                    return $this->error('房型编码在该酒店内已存在');
                }
            }

            // 处理床型配置
            $bedTypesData = $data['bed_types'] ?? null;
            unset($data['bed_types']); // 从主数据中移除，单独处理

            // 更新房型基本信息
            $roomType->update(array_filter($data, function($value) {
                return $value !== null && $value !== '';
            }));

            // 处理床型关联
            if ($bedTypesData !== null) {
                $this->syncBedTypes($roomType, $bedTypesData);
            }

            // 重新加载房型数据，包含关联信息
            $roomType->load(['bedTypes', 'primaryBedType']);

            return $this->success($roomType, '更新房型成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新房型信息');
        }
    }

    /**
     * 删除房型
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            // 检查是否有关联的房间（暂时跳过，因为rooms表未创建）
            // if ($roomType->rooms()->exists()) {
            //     return $this->error('房型存在关联房间，无法删除');
            // }

            // 检查是否有关联的订单
            if ($roomType->bookings()->exists()) {
                return $this->error('房型存在关联订单，无法删除');
            }

            // 检查是否有关联的库存
            if ($roomType->inventory()->exists()) {
                return $this->error('房型存在关联库存，无法删除');
            }

            // 检查是否有关联的价格
            if ($roomType->rates()->exists()) {
                return $this->error('房型存在关联价格，无法删除');
            }

            $roomType->delete();

            return $this->success(null, '删除房型成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '删除房型');
        }
    }

    /**
     * 获取房型库存信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function inventory(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $params = $this->getInput($request);
            
            // 设置默认日期范围（未来30天）
            $startDate = $params['start_date'] ?? date('Y-m-d');
            $endDate = $params['end_date'] ?? date('Y-m-d', strtotime('+30 days'));

            // 验证日期格式
            $dateErrors = $this->validateFormat([
                'start_date' => $startDate,
                'end_date' => $endDate
            ], [
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($dateErrors) {
                return $this->error('日期格式错误', 400, $dateErrors);
            }

            $inventory = $roomType->getInventoryForDateRange($startDate, $endDate);

            return $this->success([
                'room_type' => $roomType,
                'inventory' => $inventory,
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型库存');
        }
    }

    /**
     * 获取房型价格信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function rates(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $params = $this->getInput($request);
            
            // 设置默认日期范围（未来30天）
            $startDate = $params['start_date'] ?? date('Y-m-d');
            $endDate = $params['end_date'] ?? date('Y-m-d', strtotime('+30 days'));
            $ratePlanId = $params['rate_plan_id'] ?? null;

            // 验证日期格式
            $dateErrors = $this->validateFormat([
                'start_date' => $startDate,
                'end_date' => $endDate
            ], [
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($dateErrors) {
                return $this->error('日期格式错误', 400, $dateErrors);
            }

            $rates = $roomType->getRatesForDateRange($startDate, $endDate, $ratePlanId);

            return $this->success([
                'room_type' => $roomType,
                'rates' => $rates,
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'rate_plan_id' => $ratePlanId
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型价格');
        }
    }

    /**
     * 检查房型可用性
     *
     * @param Request $request
     * @return Response
     */
    public function availability(Request $request)
    {
        try {
            $roomTypeId = (int)$request->route('id');
            
            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['check_in_date', 'check_out_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证日期格式
            $dateErrors = $this->validateFormat($params, [
                'check_in_date' => 'date',
                'check_out_date' => 'date'
            ]);
            if ($dateErrors) {
                return $this->error('日期格式错误', 400, $dateErrors);
            }

            // 验证日期范围
            if ($params['check_in_date'] >= $params['check_out_date']) {
                return $this->error('入住日期必须早于离店日期');
            }

            $rooms = (int)($params['rooms'] ?? 1);
            if ($rooms <= 0) {
                return $this->error('房间数量必须大于0');
            }

            $available = $roomType->hasAvailabilityForDateRange(
                $params['check_in_date'],
                date('Y-m-d', strtotime($params['check_out_date'] . ' -1 day')),
                $rooms
            );

            return $this->success([
                'room_type' => $roomType,
                'available' => $available,
                'check_in_date' => $params['check_in_date'],
                'check_out_date' => $params['check_out_date'],
                'rooms' => $rooms
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '检查房型可用性');
        }
    }

    /**
     * 同步房型床型关联
     *
     * @param RoomType $roomType
     * @param array $bedTypesData
     * @return void
     */
    private function syncBedTypes(RoomType $roomType, array $bedTypesData)
    {
        // 删除现有关联
        $roomType->bedTypes()->detach();

        // 添加新的关联
        foreach ($bedTypesData as $index => $bedTypeData) {
            $roomType->bedTypes()->attach($bedTypeData['bed_type_id'], [
                'bed_count' => $bedTypeData['bed_count'] ?? 1,
                'is_primary' => $bedTypeData['is_primary'] ?? false,
                'sort_order' => $bedTypeData['sort_order'] ?? $index,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }
    }

    /**
     * 获取房型设施信息
     *
     * @param Request $request
     * @return Response
     */
    public function amenities(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;

            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $amenities = $roomType->getRoomAmenitiesInfo();

            return $this->success($amenities);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型设施信息');
        }
    }

    /**
     * 更新房型设施信息
     *
     * @param Request $request
     * @return Response
     */
    public function updateAmenities(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;

            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证设施数据格式
            $amenityFields = [
                'bathroom_amenities', 'room_amenities', 'technology_amenities',
                'connectivity', 'entertainment', 'food_beverage',
                'business_services', 'safety_security', 'comfort_features',
                'special_features'
            ];

            foreach ($amenityFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            // 更新设施信息
            $updateData = [];
            foreach ($amenityFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            $roomType->update($updateData);

            return $this->success($roomType, '更新房型设施信息成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新房型设施信息');
        }
    }

    /**
     * 获取房型详细信息
     *
     * @param Request $request
     * @return Response
     */
    public function details(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;

            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::with(['hotel', 'bedTypes'])->find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $details = $roomType->getRoomDetailsInfo();

            return $this->success($details);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房型详细信息');
        }
    }

    /**
     * 更新房型详细信息
     *
     * @param Request $request
     * @return Response
     */
    public function updateDetails(Request $request, $id)
    {
        try {
            $roomTypeId = (int)$id;

            if ($roomTypeId <= 0) {
                return $this->error('房型ID无效');
            }

            $roomType = RoomType::find($roomTypeId);
            if (!$roomType) {
                return $this->error('房型不存在', 404);
            }

            $data = $this->getInput($request);

            // 验证详细信息数据格式
            $detailFields = [
                'room_layout', 'room_dimensions', 'room_location_info',
                'view_details', 'bed_configurations', 'guest_capacity_details'
            ];

            foreach ($detailFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            // 验证数值字段
            $numericFields = ['area', 'ceiling_height'];
            foreach ($numericFields as $field) {
                if (!empty($data[$field]) && !is_numeric($data[$field])) {
                    return $this->error("字段 {$field} 必须是数值");
                }
            }

            // 更新详细信息
            $updateData = [];
            $allowedFields = [
                'room_category', 'room_grade', 'area', 'room_layout',
                'room_dimensions', 'ceiling_height', 'room_shape',
                'room_orientation', 'window_type', 'balcony_type',
                'view_type', 'view_details', 'noise_level', 'privacy_level',
                'lighting_type', 'temperature_control', 'air_quality',
                'decoration_style', 'room_location_info', 'bed_configurations',
                'guest_capacity_details'
            ];

            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            $roomType->update($updateData);

            return $this->success($roomType, '更新房型详细信息成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新房型详细信息');
        }
    }
}
