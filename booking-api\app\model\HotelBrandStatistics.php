<?php

namespace app\model;

use support\Model;

/**
 * 酒店品牌统计模型
 */
class HotelBrandStatistics extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'hotel_brand_statistics';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_brand_id',
        'hotel_count',
        'room_count',
        'active_hotel_count',
        'total_bookings',
        'total_revenue',
        'average_rating',
        'review_count',
        'occupancy_rate',
        'adr',
        'revpar',
        'customer_loyalty_rate',
        'repeat_booking_rate',
        'last_calculated_at',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_brand_id' => 'integer',
        'hotel_count' => 'integer',
        'room_count' => 'integer',
        'active_hotel_count' => 'integer',
        'total_bookings' => 'integer',
        'total_revenue' => 'decimal:2',
        'average_rating' => 'decimal:2',
        'review_count' => 'integer',
        'occupancy_rate' => 'decimal:2',
        'adr' => 'decimal:2',
        'revpar' => 'decimal:2',
        'customer_loyalty_rate' => 'decimal:2',
        'repeat_booking_rate' => 'decimal:2',
        'last_calculated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取关联的酒店品牌
     */
    public function hotelBrand()
    {
        return $this->belongsTo(HotelBrand::class);
    }
}
