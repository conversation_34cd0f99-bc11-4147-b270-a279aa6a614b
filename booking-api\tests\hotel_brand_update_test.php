<?php

/**
 * 酒店品牌更新功能测试脚本
 * 测试品牌编辑功能的API调用和数据处理
 */

require_once __DIR__ . '/../vendor/autoload.php';

class HotelBrandUpdateTest
{
    private $apiUrl = 'http://localhost:9000'; // 使用用户修改后的端口
    private $testBrandId = 1; // 测试品牌ID
    
    public function __construct()
    {
        echo "=== 酒店品牌更新功能测试 ===\n\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        try {
            $this->testGetBrandDetail();
            $this->testUpdateBrand();
            $this->testFieldMapping();
            $this->testValidation();
            
            echo "\n✅ 所有测试通过！\n";
        } catch (Exception $e) {
            echo "\n❌ 测试失败: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * 测试获取品牌详情
     */
    private function testGetBrandDetail()
    {
        echo "1. 测试获取品牌详情...\n";
        
        $response = $this->makeRequest('GET', "/api/v1/hotel-brands/{$this->testBrandId}");
        
        if ($response['code'] === 200) {
            echo "   ✅ 获取品牌详情成功\n";
            echo "   品牌信息: " . json_encode($response['data'], JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "   ⚠️  获取品牌详情失败: " . ($response['message'] ?? 'Unknown error') . "\n";
        }
    }
    
    /**
     * 测试更新品牌
     */
    private function testUpdateBrand()
    {
        echo "\n2. 测试更新品牌...\n";
        
        $updateData = [
            'name' => '测试品牌更新',
            'code' => 'test_brand_update',
            'name_en' => 'Test Brand Update',
            'description' => '这是一个测试更新的品牌描述',
            'brand_type' => 'midscale',
            'target_market' => '商务旅客',
            'positioning' => '中高端商务酒店品牌',
            'status' => 'active'
        ];
        
        $response = $this->makeRequest('PUT', "/api/v1/hotel-brands/{$this->testBrandId}", $updateData);
        
        if ($response['code'] === 200) {
            echo "   ✅ 更新品牌成功\n";
            echo "   更新结果: " . json_encode($response['data'], JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "   ❌ 更新品牌失败: " . ($response['message'] ?? 'Unknown error') . "\n";
            if (isset($response['errors'])) {
                echo "   错误详情: " . json_encode($response['errors'], JSON_UNESCAPED_UNICODE) . "\n";
            }
        }
    }
    
    /**
     * 测试字段映射
     */
    private function testFieldMapping()
    {
        echo "\n3. 测试字段映射...\n";
        
        $testData = [
            'name' => '字段映射测试',
            'name_en' => 'Field Mapping Test',
            'positioning' => '测试品牌定位字段映射',
            'target_market' => '测试目标市场字段',
            'brand_type' => 'premium'
        ];
        
        $response = $this->makeRequest('PUT', "/api/v1/hotel-brands/{$this->testBrandId}", $testData);
        
        if ($response['code'] === 200) {
            echo "   ✅ 字段映射测试成功\n";
            
            // 验证返回的数据是否包含正确的字段
            $data = $response['data'];
            $expectedFields = ['name_en', 'positioning', 'target_market', 'brand_type'];
            
            foreach ($expectedFields as $field) {
                if (isset($data[$field])) {
                    echo "   ✓ 字段 {$field} 映射正确\n";
                } else {
                    echo "   ✗ 字段 {$field} 映射失败\n";
                }
            }
        } else {
            echo "   ❌ 字段映射测试失败\n";
        }
    }
    
    /**
     * 测试数据验证
     */
    private function testValidation()
    {
        echo "\n4. 测试数据验证...\n";
        
        // 测试无效邮箱
        $invalidData = [
            'name' => '验证测试',
            'contact_email' => 'invalid-email'
        ];
        
        $response = $this->makeRequest('PUT', "/api/v1/hotel-brands/{$this->testBrandId}", $invalidData);
        
        if ($response['code'] === 400) {
            echo "   ✅ 邮箱格式验证正常\n";
        } else {
            echo "   ⚠️  邮箱格式验证可能有问题\n";
        }
        
        // 测试重复名称（如果有其他品牌的话）
        $duplicateData = [
            'name' => '锦江', // 假设这是已存在的品牌名称
        ];
        
        $response = $this->makeRequest('PUT', "/api/v1/hotel-brands/999", $duplicateData);
        
        if ($response['code'] === 404 || $response['code'] === 400) {
            echo "   ✅ 重复名称验证正常\n";
        } else {
            echo "   ⚠️  重复名称验证结果: " . $response['code'] . "\n";
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeRequest($method, $path, $data = [])
    {
        $url = $this->apiUrl . $path;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        
        if ($method === 'PUT') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        } elseif ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            return ['code' => 0, 'message' => 'Connection failed'];
        }
        
        $decoded = json_decode($response, true);
        return $decoded ?: ['code' => $httpCode, 'raw' => $response];
    }
}

// 运行测试
$test = new HotelBrandUpdateTest();
$test->runAllTests();
