<?php

namespace base\AuditLog\traits;

trait Module
{

    /**
     * 业务模块
     *
     * @var string
     */
    protected string $module;

    /**
     * 业务跟踪标识
     *
     * @var string
     */
    protected string $traceId = '';

    /**
     * 设置模块标识
     *
     * @access protected
     *
     * @param string $module
     *
     * @return $this
     */
    public function module(string $module): self
    {
        $this->module = $module;

        return $this;
    }

    public function getTraceId(): string
    {
        return $this->parse($this->traceId);
    }

    /**
     * 设置业务跟踪标识
     *
     * @access protected
     *
     * @param string $traceId
     *
     * @return $this
     */
    public function setTraceId(string $traceId): self
    {
        $this->traceId = $traceId;

        return $this;
    }


    /**
     * 获取模块标识
     *
     * @access protected
     *
     */
    private function getModule(): string
    {
        if (empty($this->module)) {
            // 当前模型名
            $name         = str_replace('\\', '/', static::class);
            $this->module = basename($name);
        }

        return $this->module;
    }

}
