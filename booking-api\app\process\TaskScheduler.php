<?php

namespace app\process;

use Workerman\Timer;
use Workerman\Worker;
use support\Log;

/**
 * 定时任务调度器
 */
class TaskScheduler
{
    /**
     * 进程名称
     * @var string
     */
    protected $name = 'TaskScheduler';

    /**
     * 进程数量
     * @var int
     */
    protected $count = 1;

    /**
     * 定时任务配置
     * @var array
     */
    protected $tasks;

    /**
     * 正在运行的任务
     * @var array
     */
    protected $runningTasks = [];

    /**
     * 构造函数
     */
    public function __construct()
    {
        // $this->onWorkerStart = [$this, 'onWorkerStart'];
    }

    /**
     * 进程启动时调用
     * @param Worker $worker
     * @return void
     */
    public function onWorkerStart(Worker $worker)
    {
        $this->tasks = [
            // 每天凌晨2点同步Expedia酒店数据
            [
                'name' => 'Expedia酒店数据同步',
                'command' => 'php webman expedia:sync --type=hotels --limit=1000',
                'time' => '0 2 * * *', // 每天凌晨2点执行
                'callback' => 'syncExpediaHotels',
                'singleton' => true, // 是否单例运行，防止任务重复执行
                'enable' => true, // 是否启用
            ],
            // 每天凌晨3点同步Expedia可用性和价格数据
            [
                'name' => 'Expedia可用性和价格数据同步',
                'command' => 'php webman expedia:sync --type=availability --limit=1000 --start-date=' . date('Y-m-d') . ' --end-date=' . date('Y-m-d', strtotime('+30 days')),
                'time' => '0 3 * * *', // 每天凌晨3点执行
                'callback' => 'syncExpediaAvailability',
                'singleton' => true, // 是否单例运行，防止任务重复执行
                'enable' => true, // 是否启用
            ],
        ];

        // 每分钟检查一次是否有定时任务需要执行
        Timer::add(60, function () {
            $this->checkTasks();
        });

        // 立即执行一次检查
        $this->checkTasks();

        Log::info("定时任务调度器已启动");
    }

    /**
     * 检查定时任务
     * @return void
     */
    protected function checkTasks()
    {
        $now = time();

        foreach ($this->tasks as $task) {
            // 检查任务是否启用
            if (!isset($task['enable']) || $task['enable'] !== true) {
                continue;
            }

            // 检查任务是否应该执行
            if ($this->shouldRunTask($task, $now)) {
                $this->runTask($task);
            }
        }
    }

    /**
     * 判断任务是否应该执行
     * @param array $task
     * @param int $now
     * @return bool
     */
    protected function shouldRunTask(array $task, int $now): bool
    {
        // 检查任务是否正在运行
        if (isset($task['singleton']) && $task['singleton'] && isset($this->runningTasks[$task['name']])) {
            return false;
        }

        // 解析cron表达式
        $cronParts = explode(' ', $task['time']);
        if (count($cronParts) !== 5) {
            Log::error("任务 {$task['name']} 的cron表达式格式错误: {$task['time']}");
            return false;
        }

        $minute = $cronParts[0];
        $hour = $cronParts[1];
        $dayOfMonth = $cronParts[2];
        $month = $cronParts[3];
        $dayOfWeek = $cronParts[4];

        $date = getdate($now);

        // 检查分钟
        if ($minute !== '*' && $minute != $date['minutes']) {
            return false;
        }

        // 检查小时
        if ($hour !== '*' && $hour != $date['hours']) {
            return false;
        }

        // 检查日期
        if ($dayOfMonth !== '*' && $dayOfMonth != $date['mday']) {
            return false;
        }

        // 检查月份
        if ($month !== '*' && $month != $date['mon']) {
            return false;
        }

        // 检查星期
        if ($dayOfWeek !== '*' && $dayOfWeek != $date['wday']) {
            return false;
        }

        return true;
    }

    /**
     * 执行任务
     * @param array $task
     * @return void
     */
    protected function runTask(array $task)
    {
        // 标记任务为运行中
        if (isset($task['singleton']) && $task['singleton']) {
            $this->runningTasks[$task['name']] = true;
        }

        Log::info("开始执行定时任务: {$task['name']}");

        // 执行命令
        if (isset($task['command'])) {
            $this->executeCommand($task);
        }

        // 执行回调函数
        if (isset($task['callback']) && method_exists($this, $task['callback'])) {
            try {
                $this->{$task['callback']}();
            } catch (\Exception $e) {
                Log::error("执行任务 {$task['name']} 的回调函数失败: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            }
        }

        // 标记任务为已完成
        if (isset($task['singleton']) && $task['singleton']) {
            unset($this->runningTasks[$task['name']]);
        }
    }

    /**
     * 执行命令
     * @param array $task
     * @return void
     */
    protected function executeCommand(array $task)
    {
        // 使用popen执行命令，并获取输出
        $descriptorspec = [
            0 => ["pipe", "r"],  // 标准输入
            1 => ["pipe", "w"],  // 标准输出
            2 => ["pipe", "w"]   // 标准错误
        ];

        $process = proc_open($task['command'], $descriptorspec, $pipes);

        if (is_resource($process)) {
            // 关闭标准输入
            fclose($pipes[0]);

            // 读取标准输出
            $output = stream_get_contents($pipes[1]);
            fclose($pipes[1]);

            // 读取标准错误
            $error = stream_get_contents($pipes[2]);
            fclose($pipes[2]);

            // 关闭进程
            $return_value = proc_close($process);

            if ($return_value === 0) {
                Log::info("任务 {$task['name']} 执行成功", ['output' => $output]);
            } else {
                Log::error("任务 {$task['name']} 执行失败", ['error' => $error, 'return_value' => $return_value]);
            }
        } else {
            Log::error("无法启动任务 {$task['name']} 的进程");
        }
    }

    /**
     * 同步Expedia酒店数据
     * @return void
     */
    protected function syncExpediaHotels()
    {
        try {
            // 这里可以直接调用命令类的方法，而不是通过命令行执行
            $command = new \app\command\ExpediaSync();
            $input = new \Symfony\Component\Console\Input\ArrayInput([
                '--type' => 'hotels',
                '--limit' => 1000
            ]);
            $output = new \Symfony\Component\Console\Output\BufferedOutput();

            $result = $command->run($input, $output);

            if ($result === 0) {
                Log::info("Expedia酒店数据同步成功", ['output' => $output->fetch()]);
            } else {
                Log::error("Expedia酒店数据同步失败", ['output' => $output->fetch()]);
            }
        } catch (\Exception $e) {
            Log::error("Expedia酒店数据同步异常: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
        }
    }

    /**
     * 同步Expedia可用性和价格数据
     * @return void
     */
    protected function syncExpediaAvailability()
    {
        try {
            // 这里可以直接调用命令类的方法，而不是通过命令行执行
            $command = new \app\command\ExpediaSync();
            $input = new \Symfony\Component\Console\Input\ArrayInput([
                '--type' => 'availability',
                '--limit' => 1000,
                '--start-date' => date('Y-m-d'),
                '--end-date' => date('Y-m-d', strtotime('+30 days'))
            ]);
            $output = new \Symfony\Component\Console\Output\BufferedOutput();

            $result = $command->run($input, $output);

            if ($result === 0) {
                Log::info("Expedia可用性和价格数据同步成功", ['output' => $output->fetch()]);
            } else {
                Log::error("Expedia可用性和价格数据同步失败", ['output' => $output->fetch()]);
            }
        } catch (\Exception $e) {
            Log::error("Expedia可用性和价格数据同步异常: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
        }
    }
}

