<?php

namespace app\model;

use support\Model;

/**
 * 酒店儿童政策模型
 */
class HotelPoliciesChildrenPolicy extends Model
{
    protected $table = 'hotel_policies_children_policies';
    protected $primaryKey = 'id';

    protected $fillable = [
        'hotel_id', 'children_allowed', 'free_child_age_limit', 'max_children_per_room',
        'child_bed_policy', 'extra_bed_available', 'extra_bed_fee', 'crib_available', 'crib_fee',
        'child_meal_policy', 'babysitting_service', 'children_activities', 'children_facilities',
        'supervision_required', 'children_notes', 'status',
    ];

    protected $casts = [
        'id' => 'integer', 'hotel_id' => 'integer', 'children_allowed' => 'boolean',
        'free_child_age_limit' => 'integer', 'max_children_per_room' => 'integer',
        'extra_bed_available' => 'boolean', 'extra_bed_fee' => 'decimal:2', 'crib_available' => 'boolean',
        'crib_fee' => 'decimal:2', 'babysitting_service' => 'boolean', 'children_activities' => 'array',
        'children_facilities' => 'array', 'supervision_required' => 'boolean',
        'created_at' => 'datetime', 'updated_at' => 'datetime',
    ];

    public function hotel() { return $this->belongsTo(Hotel::class, 'hotel_id', 'id'); }
    public function scopeActive($query) { return $query->where('status', 'active'); }
    public function scopeByHotel($query, $hotelId) { return $query->where('hotel_id', $hotelId); }
    public function getPolicyType(): string { return 'children'; }
    public function getPolicyDisplayName(): string { return '儿童政策'; }

    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId, 'children_allowed' => true, 'free_child_age_limit' => 12,
            'supervision_required' => true, 'status' => 'active'
        ]);
    }
}
