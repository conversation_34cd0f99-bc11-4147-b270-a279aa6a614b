<?php


namespace base\helper;


class Password
{
    /**
     *<div id="function.password-hash" class="refentry"> <div class="refnamediv">  <h1 class="refname">password_hash</h1>  <p class="verinfo">(PHP 5 &gt;= 5.5.0, PHP 7)</p><p class="refpurpose"><span class="refname">password_hash</span> &mdash; <span class="dc-title">创建密码的散列（hash）</span></p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 description" id="refsect1-function.password-hash-description">  <h3 class="title">说明</h3>  <div class="methodsynopsis dc-description">   <span class="methodname" style="color:#CC7832"><strong>password_hash</strong></span>    ( <span class="methodparam"><span class="type" style="color:#EAB766">string</span> <span class="parameter" style="color:#3A95FF">$password</span></span>   , <span class="methodparam"><span class="type" style="color:#EAB766"><a href="http://php.net/manual/zh/language.pseudo-types.php#language.types.mixed" class="type mixed" style="color:#EAB766">mixed</a></span> <span class="parameter" style="color:#3A95FF">$algo</span></span>   [, <span class="methodparam"><span class="type" style="color:#EAB766">array</span> <span class="parameter" style="color:#3A95FF">$options</span></span>  ] ) : <span class="type" style="color:#EAB766"><span class="type" style="color:#EAB766">string</span>|<span class="type" style="color:#EAB766"><span class="type false" style="color:#EAB766">false</span></span></span></div>  <p class="para rdfs-comment">   <span class="function"><strong style="color:#CC7832">password_hash()</strong></span> 使用足够强度的单向散列算法创建密码的散列（hash）。      <span class="function"><strong style="color:#CC7832">password_hash()</strong></span> 兼容 <span class="function">{@link crypt()}</span>。   所以， <span class="function">{@link crypt()}</span> 创建的密码散列也可用于   <span class="function"><strong style="color:#CC7832">password_hash()</strong></span>。  </p>  <p class="simpara">   当前支持的算法：  </p>  <p class="para">   <ul class="itemizedlist">    <li class="listitem">     <span class="simpara">      <strong><span>PASSWORD_DEFAULT</span></strong> - 使用 bcrypt 算法 (PHP 5.5.0 默认)。      注意，该常量会随着 PHP 加入更新更高强度的算法而改变。      所以，使用此常量生成结果的长度将在未来有变化。      因此，数据库里储存结果的列可超过60个字符（最好是255个字符）。     </span>    </li>    <li class="listitem">     <span class="simpara">      <strong><span>PASSWORD_BCRYPT</span></strong> - 使用 <strong><span>CRYPT_BLOWFISH</span></strong> 算法创建散列。      这会产生兼容使用 &quot;$2y$&quot; 的 <span class="function">{@link crypt()}</span>。      结果将会是 60 个字符的字符串，  或者在失败时返回 <strong><span>FALSE</span></strong>。     </span>    </li>    <li class="listitem">     <span class="simpara">      <strong><span>PASSWORD_ARGON2I</span></strong> - 使用 Argon2i 散列算法创建散列。      只有在 PHP 编译时加入 Argon2 支持时才能使用该算法。     </span>    </li>    <li class="listitem">     <span class="simpara">      <strong><span>PASSWORD_ARGON2ID</span></strong> - 使用 Argon2id 散列算法创建散列。      只有在 PHP 编译时加入 Argon2 支持时才能使用该算法。     </span>    </li>   </ul>  </p>  <p class="simpara">      <strong><span>PASSWORD_BCRYPT</span></strong> 支持的选项：  </p>  <p class="para">   <ul class="itemizedlist">      <li class="listitem">       <p class="para">        <span>salt</span>(<span class="type" style="color:#EAB766">string</span>) - 手动提供散列密码的盐值（salt）。这将避免自动生成盐值（salt）。       </p>       <p class="para">        省略此值后，<span class="function"><strong style="color:#CC7832">password_hash()</strong></span> 会为每个密码散列自动生成随机的盐值。这种操作是有意的模式。       </p>       <div class="warning"><strong class="warning">Warning</strong>        <p class="para">         盐值（salt）选项从 PHP 7.0.0 开始被废弃（deprecated）了。         现在最好选择简单的使用默认产生的盐值。        </p>       </div>      </li>      <li class="listitem">       <p class="para">        <span>cost</span> (<span class="type" style="color:#EAB766">int</span>) - 代表算法使用的 cost。<span class="function">{@link crypt()}</span> 页面上有 cost 值的例子。       </p>       <p class="para">        省略时，默认值是 <span>10</span>。        这个 cost 是个不错的底线，但也许可以根据自己硬件的情况，加大这个值。       </p>      </li>     </ul>  </p>  <p class="simpara">    <strong><span>PASSWORD_ARGON2I</span></strong> 和 <strong><span>PASSWORD_ARGON2ID</span></strong> 支持的选项：  </p>  <p class="para">   <ul class="itemizedlist">    <li class="listitem">     <p class="para">      <span>memory_cost</span> (<span class="type" style="color:#EAB766">int</span>) - 计算 Argon2 散列时的最大内存（单位：KB）。默认值： <strong><span>PASSWORD_ARGON2_DEFAULT_MEMORY_COST</span></strong>。     </p>    </li>    <li class="listitem">     <p class="para">      <span>time_cost</span> (<span class="type" style="color:#EAB766">int</span>) - 计算 Argon2 散列时最多的时间。默认值： <strong><span>PASSWORD_ARGON2_DEFAULT_TIME_COST</span></strong>。     </p>    </li>    <li class="listitem">     <p class="para">      <span>threads</span> (<span class="type" style="color:#EAB766">int</span>) - 计算 Argon2 散列时最多的线程数。默认值： <strong><span>PASSWORD_ARGON2_DEFAULT_THREADS</span></strong>。     </p>    </li>   </ul>  </p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 parameters" id="refsect1-function.password-hash-parameters">  <h3 class="title">参数</h3>  <dl>       <dt><span class="parameter" style="color:#3A95FF">password</span></dt>    <dd>     <span>      用户的密码。     </span>      <div class="caution"><strong class="caution">Caution</strong>       <p class="para">        使用<strong><span>PASSWORD_BCRYPT</span></strong> 做算法，将使 <span class="parameter" style="color:#3A95FF">password</span> 参数最长为72个字符，超过会被截断。       </p>      </div>    </dd>          <dt><span class="parameter" style="color:#3A95FF">algo</span></dt>    <dd>     <p class="para">      一个用来在散列密码时指示算法的<a href="http://php.net/manual/zh/password.constants.php" class="link">密码算法常量</a>。     </p>    </dd>          <dt><span class="parameter" style="color:#3A95FF">options</span></dt>    <dd>     <p class="para">      一个包含有选项的关联数组。详细的参数说明，请参考文档 <a href="http://php.net/manual/zh/password.constants.php" class="link">密码算法常数</a>。     </p>     <p class="para">      省略后，将使用随机盐值与默认 cost。     </p>    </dd>     </dl> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 returnvalues" id="refsect1-function.password-hash-returnvalues">  <h3 class="title">返回值</h3>  <p class="para">   返回散列后的密码， 或者在失败时返回 <strong><span>FALSE</span></strong>。  </p>  <p class="para">   使用的算法、cost 和盐值作为散列的一部分返回。所以验证散列值的所有信息都已经包含在内。   这使 <span class="function">{@link password_verify()}</span> 函数验证的时候，不需要额外储存盐值或者算法的信息。  </p> </div>   <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 changelog" id="refsect1-function.password-hash-changelog">  <h3 class="title">更新日志</h3>  <span>   <table class="doctable informaltable">         <thead>      <tr>       <th>版本</th>       <th>说明</th>      </tr>     </thead>     <tbody class="tbody">      <tr>       <td>7.4.0</td>       <td>        现在 <span class="parameter" style="color:#3A95FF">algo</span> 参数可支持 <span class="type" style="color:#EAB766">string</span> 类型，但为了向后兼容也支持        <span class="type" style="color:#EAB766">int</span> 类型。       </td>      </tr>      <tr>       <td>7.3.0</td>       <td>        增加 <strong><span>PASSWORD_ARGON2ID</span></strong>，支持 Argon2id 密码算法。       </td>      </tr>      <tr>       <td>7.2.0</td>       <td>        增加 <strong><span>PASSWORD_ARGON2I</span></strong>，支持 Argon2i 密码算法。       </td>      </tr>     </tbody>       </table>  </span> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 examples" id="refsect1-function.password-hash-examples">  <h3 class="title">范例</h3>  <span>   <div class="example" id="example-890">    <p><strong>Example #1 <span class="function"><strong style="color:#CC7832">password_hash()</strong></span> 例子</strong></p>    <div class="example-contents"><div class="phpcode" style="border-color:gray;background:#232525"><span><span style="color: #000000"><span style="color: #9876AA">&lt;?php<br /></span><span style="color: #FF8000">//*<br />&nbsp;*&nbsp;我们想要使用默认算法散列密码<br />&nbsp;*&nbsp;当前是&nbsp;BCRYPT，并会产生&nbsp;60&nbsp;个字符的结果。<br />&nbsp;*<br />&nbsp;*&nbsp;请注意，随时间推移，默认算法可能会有变化，<br />&nbsp;*&nbsp;所以需要储存的空间能够超过&nbsp;60&nbsp;字（255字不错）<br />&nbsp;<br /></span><span style="color: #007700">echo&nbsp;</span><span style="color: #9876AA">password_hash</span><span style="color: #007700">(</span><span style="color: #DD0000">"rasmuslerdorf"</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">PASSWORD_DEFAULT</span><span style="color: #007700">);<br /></span><span style="color: #9876AA">?&gt;</span></span></span></div>    </div>    <div class="example-contents"><p>以上例程的输出类似于：</p></div>    <div class="example-contents screen" style="color:AFB1B3;background:black;padding-left:5px;"><div class="cdata"><span>$2y$10$.vGA1O9wmRjrwAVXD98HNOgsNpDczlqm3Jq7KnEd1rVAGv3Fykk1a<br></span></div>    </div>   </div>  </span>   <p class="para">   <div class="example" id="example-891">    <p><strong>Example #2 <span class="function"><strong style="color:#CC7832">password_hash()</strong></span> 手动设置 cost 的例子</strong></p>    <div class="example-contents"><div class="phpcode" style="border-color:gray;background:#232525"><span><span style="color: #000000"><span style="color: #9876AA">&lt;?php<br /></span><span style="color: #FF8000">//*<br />&nbsp;*&nbsp;在这个案例里，我们为&nbsp;BCRYPT&nbsp;增加&nbsp;cost&nbsp;到&nbsp;12。<br />&nbsp;*&nbsp;注意，我们已经切换到了，将始终产生&nbsp;60&nbsp;个字符。<br />&nbsp;<br /></span><span style="color: #9876AA">$options&nbsp;</span><span style="color: #007700">=&nbsp;[<br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #DD0000">'cost'&nbsp;</span><span style="color: #007700">=&gt;&nbsp;</span><span style="color: #9876AA">12</span><span style="color: #007700">,<br />];<br />echo&nbsp;</span><span style="color: #9876AA">password_hash</span><span style="color: #007700">(</span><span style="color: #DD0000">"rasmuslerdorf"</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">PASSWORD_BCRYPT</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">$options</span><span style="color: #007700">);<br /></span><span style="color: #9876AA">?&gt;</span></span></span></div>    </div>    <div class="example-contents"><p>以上例程的输出类似于：</p></div>    <div class="example-contents screen" style="color:AFB1B3;background:black;padding-left:5px;"><div class="cdata"><span>$2y$12$QjSH496pcT5CEbzjD/vtVeH03tfHKFy36d4J0Ltp3lRtee9HDxY3K<br></span></div>    </div>   </div>  </p>    <p class="para">   <div class="example" id="example-892">    <p><strong>Example #3 寻找最佳 cost 的 <span class="function"><strong style="color:#CC7832">password_hash()</strong></span> 例子</strong></p>    <div class="example-contents"><div class="phpcode" style="border-color:gray;background:#232525"><span><span style="color: #000000"><span style="color: #9876AA">&lt;?php<br /></span><span style="color: #FF8000">//*<br />&nbsp;*&nbsp;这个例子对服务器做了基准测试（benchmark），检测服务器能承受多高的&nbsp;cost<br />&nbsp;*&nbsp;在不明显拖慢服务器的情况下可以设置最高的值<br />&nbsp;*&nbsp;8-10&nbsp;是个不错的底线，在服务器够快的情况下，越高越好。<br />&nbsp;*&nbsp;以下代码目标为&nbsp;&nbsp;≤&nbsp;50&nbsp;毫秒（milliseconds），<br />&nbsp;*&nbsp;适合系统处理交互登录。<br />&nbsp;<br /></span><span style="color: #9876AA">$timeTarget&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #9876AA">0.05</span><span style="color: #007700">;&nbsp;</span><span style="color: #FF8000">//&nbsp;50&nbsp;毫秒（milliseconds）&nbsp;<br /><br /></span><span style="color: #9876AA">$cost&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #9876AA">8</span><span style="color: #007700">;<br />do&nbsp;{<br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #9876AA">$cost</span><span style="color: #007700">++;<br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #9876AA">$start&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #9876AA">microtime</span><span style="color: #007700">(</span><span style="color: #9876AA">true</span><span style="color: #007700">);<br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #9876AA">password_hash</span><span style="color: #007700">(</span><span style="color: #DD0000">"test"</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">PASSWORD_BCRYPT</span><span style="color: #007700">,&nbsp;[</span><span style="color: #DD0000">"cost"&nbsp;</span><span style="color: #007700">=&gt;&nbsp;</span><span style="color: #9876AA">$cost</span><span style="color: #007700">]);<br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #9876AA">$end&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #9876AA">microtime</span><span style="color: #007700">(</span><span style="color: #9876AA">true</span><span style="color: #007700">);<br />}&nbsp;while&nbsp;((</span><span style="color: #9876AA">$end&nbsp;</span><span style="color: #007700">-&nbsp;</span><span style="color: #9876AA">$start</span><span style="color: #007700">)&nbsp;&lt;&nbsp;</span><span style="color: #9876AA">$timeTarget</span><span style="color: #007700">);<br /><br />echo&nbsp;</span><span style="color: #DD0000">"Appropriate&nbsp;Cost&nbsp;Found:&nbsp;"&nbsp;</span><span style="color: #007700">.&nbsp;</span><span style="color: #9876AA">$cost</span><span style="color: #007700">;<br /></span><span style="color: #9876AA">?&gt;</span></span></span></div>    </div>    <div class="example-contents"><p>以上例程的输出类似于：</p></div>    <div class="example-contents screen" style="color:AFB1B3;background:black;padding-left:5px;"><div class="cdata"><span>Appropriate Cost Found: 10<br></span></div>    </div>   </div>  </p>  <p class="para">   <div class="example" id="example-893">    <p><strong>Example #4 使用 Argon2i 的<span class="function"><strong style="color:#CC7832">password_hash()</strong></span>例子</strong></p>    <div class="example-contents"><div class="phpcode" style="border-color:gray;background:#232525"><span><span style="color: #000000"><span style="color: #9876AA">&lt;?php<br /></span><span style="color: #007700">echo&nbsp;</span><span style="color: #DD0000">'Argon2i&nbsp;hash:&nbsp;'&nbsp;</span><span style="color: #007700">.&nbsp;</span><span style="color: #9876AA">password_hash</span><span style="color: #007700">(</span><span style="color: #DD0000">'rasmuslerdorf'</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">PASSWORD_ARGON2I</span><span style="color: #007700">);<br /></span><span style="color: #9876AA">?&gt;</span></span></span></div>    </div>    <div class="example-contents"><p>以上例程的输出类似于：</p></div>    <div class="example-contents screen" style="color:AFB1B3;background:black;padding-left:5px;"><div class="cdata"><span>Argon2i hash: $argon2i$v=19$m=1024,t=2,p=2$YzJBSzV4TUhkMzc3d3laeg$zqU/1IN0/AogfP4cmSJI1vc8lpXRW9/S0sYY2i2jHT0<br></span></div>    </div>   </div>  </p> </div>  <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 notes" id="refsect1-function.password-hash-notes">  <h3 class="title">注释</h3>  <div class="caution"><strong class="caution">Caution</strong>   <p class="para">    强烈建议不要自己为这个函数生成盐值（salt）。只要不设置，它会自动创建安全的盐值。   </p>   <p class="para">    就像以上提及的，在 PHP 7.0 提供 <span>salt</span>选项会导致废弃（deprecation）警告。    未来的 PHP 发行版里，手动提供盐值的功能可能会被删掉。   </p>  </div>  <blockquote class="note" style="border:1px gray solid"><p><strong class="note" style="border:1px gray solid">Note</strong>:    <p class="para">    在交互的系统上，推荐在自己的服务器上测试此函数，调整 cost 参数直至函数时间开销小于 100 毫秒（milliseconds）。    上面脚本的例子会帮助选择合适硬件的最佳 cost。   </p>  </p></blockquote>  <blockquote class="note" style="border:1px gray solid"><p><strong class="note" style="border:1px gray solid">Note</strong>:    <span class="simpara">    这个函数更新支持的算法时（或修改默认算法），必定会遵守以下规则：   </span>   <p class="para">    <ul class="itemizedlist">     <li class="listitem">      <span class="simpara">       任何内核中的新算法必须在经历一次 PHP 完整发行才能成为默认算法。       比如，在 PHP 7.5.5 中添加的新算法，在 PHP 7.7 之前不能成为默认算法       （由于 7.6 是第一个完整发行版）。       但如果是在 7.6.0 里添加的不同算法，在 7.7.0 里也可以成为默认算法。      </span>     </li>     <li class="listitem">      <span class="simpara">       仅仅允许在完整发行版中修改默认算法（比如 7.3.0, 8.0.0，等等），不能是在修订版。       唯一的例外是：在当前默认算法里发现了紧急的安全威胁。      </span>     </li>    </ul>   </p>  </p></blockquote> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 seealso" id="refsect1-function.password-hash-seealso">  <h3 class="title">参见</h3>  <span>   <ul class="simplelist">    <li class="member"><span class="function">{@link password_verify()} - 验证密码是否和散列值匹配</span></li>    <li class="member"><span class="function">{@link crypt()} - 单向字符串散列</span></li>    <li class="member"><a href="http://php.net/manual/zh/https://github.com/ircmaxell/password_compat" class="link external">&raquo;&nbsp;用户的使用</a></li>    <li class="member"><span class="function">{@link sodium_crypto_pwhash_str()} - Get an ASCII-encoded hash</span></li>   </ul>  </span> </div></div>
     * @param $password
     * @param $algo
     * @param array $options
     * @return string|false|null Returns the hashed password, or FALSE on failure, or null if the algorithm is invalid
     */
    public static function hash($password, $algo = PASSWORD_DEFAULT, $options = ['cost' => 11]): string
    {
        return password_hash($password, $algo, $options);
    }

    /**
     *<div id="function.password-verify" class="refentry"> <div class="refnamediv">  <h1 class="refname">password_verify</h1>  <p class="verinfo">(PHP 5 &gt;= 5.5.0, PHP 7)</p><p class="refpurpose"><span class="refname">password_verify</span> &mdash; <span class="dc-title">验证密码是否和散列值匹配</span></p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 description" id="refsect1-function.password-verify-description">  <h3 class="title">说明</h3>  <div class="methodsynopsis dc-description">   <span class="methodname" style="color:#CC7832"><strong>password_verify</strong></span>    ( <span class="methodparam"><span class="type" style="color:#EAB766">string</span> <span class="parameter" style="color:#3A95FF">$password</span></span>   , <span class="methodparam"><span class="type" style="color:#EAB766">string</span> <span class="parameter" style="color:#3A95FF">$hash</span></span>   ) : <span class="type" style="color:#EAB766">bool</span></div>  <p class="para rdfs-comment">   验证密码是否和指定的散列值匹配。  </p>  <p class="para">   注意 <span class="function">{@link password_hash()}</span> 返回的散列包含了算法、 cost 和盐值。   因此，所有需要的信息都包含内。使得验证函数不需要储存额外盐值等信息即可验证哈希。  </p>  <p class="para">   时序攻击（timing attacks）对此函数不起作用。  </p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 parameters" id="refsect1-function.password-verify-parameters">  <h3 class="title">参数</h3>  <dl>       <dt><span class="parameter" style="color:#3A95FF">password</span></dt>    <dd>     <span>      用户的密码。     </span>    </dd>          <dt><span class="parameter" style="color:#3A95FF">hash</span></dt>    <dd>     <p class="para">      一个由 <span class="function">{@link password_hash()}</span> 创建的散列值。     </p>    </dd>     </dl> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 returnvalues" id="refsect1-function.password-verify-returnvalues">  <h3 class="title">返回值</h3>  <p class="para">   如果密码和散列值匹配则返回 <strong><span>TRUE</span></strong>，否则返回 <strong><span>FALSE</span></strong> 。  </p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 examples" id="refsect1-function.password-verify-examples">  <h3 class="title">范例</h3>  <span>   <div class="example" id="openssl_spki_export_challenge.example.keygen">    <p><strong>Example #1 <span class="function"><strong style="color:#CC7832">password_verify()</strong></span> 例子</strong></p>    <div class="example-contents"><div class="phpcode" style="border-color:gray;background:#232525"><span><span style="color: #000000"><span style="color: #9876AA">&lt;?php<br /></span><span style="color: #FF8000">//&nbsp;想知道以下字符从哪里来，可参见&nbsp;password_hash()&nbsp;的例子<br /></span><span style="color: #9876AA">$hash&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #DD0000">'$2y$07$BCryptRequires22Chrcte/VlQH0piJtjXl.0t1XkA8pw9dMXTpOq'</span><span style="color: #007700">;<br /><br />if&nbsp;(</span><span style="color: #9876AA">password_verify</span><span style="color: #007700">(</span><span style="color: #DD0000">'rasmuslerdorf'</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">$hash</span><span style="color: #007700">))&nbsp;{<br />&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;</span><span style="color: #DD0000">'Password&nbsp;is&nbsp;valid!'</span><span style="color: #007700">;<br />}&nbsp;else&nbsp;{<br />&nbsp;&nbsp;&nbsp;&nbsp;echo&nbsp;</span><span style="color: #DD0000">'Invalid&nbsp;password.'</span><span style="color: #007700">;<br />}<br /></span><span style="color: #9876AA">?&gt;</span></span></span></div>    </div>    <div class="example-contents"><p>以上例程会输出：</p></div>    <div class="example-contents screen" style="color:AFB1B3;background:black;padding-left:5px;"><div class="cdata"><span>Password is valid!<br></span></div>    </div>   </div>  </span> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 seealso" id="refsect1-function.password-verify-seealso">  <h3 class="title">参见</h3>  <span>   <ul class="simplelist">    <li class="member"><span class="function">{@link password_hash()} - 创建密码的散列（hash）</span></li>    <li class="member"><a href="http://php.net/manual/zh/https://github.com/ircmaxell/password_compat" class="link external">&raquo;&nbsp;用户使用</a></li>    <li class="member"><span class="function">{@link sodium_crypto_pwhash_str_verify()} - Verifies that a password matches a hash</span></li>   </ul>  </span> </div></div>
     * @param $password
     * @param $hash
     * @return bool Returns TRUE if the password and hash match, or FALSE otherwise.
     */
    public static function verify($password, $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     *<div id="function.password-get-info" class="refentry"> <div class="refnamediv">  <h1 class="refname">password_get_info</h1>  <p class="verinfo">(PHP 5 &gt;= 5.5.0, PHP 7)</p><p class="refpurpose"><span class="refname">password_get_info</span> &mdash; <span class="dc-title">返回指定散列（hash）的相关信息</span></p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 description" id="refsect1-function.password-get-info-description">  <h3 class="title">说明</h3>  <div class="methodsynopsis dc-description">   <span class="methodname" style="color:#CC7832"><strong>password_get_info</strong></span>    ( <span class="methodparam"><span class="type" style="color:#EAB766">string</span> <span class="parameter" style="color:#3A95FF">$hash</span></span>   ) : <span class="type" style="color:#EAB766">array</span></div>  <p class="para rdfs-comment">   如果传入的散列值（hash）是由 <span class="function">{@link password_hash()}</span> 支持的算法生成的，   这个函数就会返回关于此散列的信息数组。  </p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 parameters" id="refsect1-function.password-get-info-parameters">  <h3 class="title">参数</h3>  <dl>       <dt><span class="parameter" style="color:#3A95FF">hash</span></dt>    <dd>     <span>      一个由 <span class="function">{@link password_hash()}</span> 创建的散列值。     </span>    </dd>     </dl> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 returnvalues" id="refsect1-function.password-get-info-returnvalues">  <h3 class="title">返回值</h3>  <p class="para">   返回三个元素的关联数组：   <ul class="itemizedlist">    <li class="listitem">     <span class="simpara">       <span>algo</span>， 匹配       <a href="http://php.net/manual/zh/password.constants.php" class="link">密码算法的常量</a>     </span>    </li>    <li class="listitem">     <span class="simpara">      <span>algoName</span>，人类可读的算法名称     </span>    </li>    <li class="listitem">     <span class="simpara">      <span>options</span>，调用 <span class="function">{@link password_hash()}</span> 时提供的选项。     </span>    </li>   </ul>  </p> </div></div>
     * @param $hash
     * @return array Returns an associative array with three elements:
     */
    public static function getInfo($hash)
    {
        return password_get_info($hash);
    }

    /**
     *<div id="function.password-needs-rehash" class="refentry">
     * <div class="refnamediv">
     * <h1 class="refname">password_needs_rehash</h1>
     * <p class="verinfo">(PHP 5 &gt;= 5.5.0, PHP 7)</p>
     * <p class="refpurpose"><span class="refname">password_needs_rehash</span> &mdash; <span class="dc-title">检测散列值是否匹配指定的选项</span></p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 description" id="refsect1-function.password-needs-rehash-description">  <h3 class="title">说明</h3>  <div class="methodsynopsis dc-description">   <span class="methodname" style="color:#CC7832"><strong>password_needs_rehash</strong></span>    ( <span class="methodparam"><span class="type" style="color:#EAB766">string</span> <span class="parameter" style="color:#3A95FF">$hash</span></span>   , <span class="methodparam"><span class="type" style="color:#EAB766"><a href="http://php.net/manual/zh/language.pseudo-types.php#language.types.mixed" class="type mixed" style="color:#EAB766">mixed</a></span> <span class="parameter" style="color:#3A95FF">$algo</span></span>   [, <span class="methodparam"><span class="type" style="color:#EAB766">array</span> <span class="parameter" style="color:#3A95FF">$options</span></span>  ] ) : <span class="type" style="color:#EAB766">bool</span></div>  <p class="para rdfs-comment">   此函数检测指定的散列值是否实现了提供的算法和选项。   如果没有，需要重新生成散列值。  </p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 parameters" id="refsect1-function.password-needs-rehash-parameters">  <h3 class="title">参数</h3>  <dl>       <dt><span class="parameter" style="color:#3A95FF">hash</span></dt>    <dd>     <span>      一个由 <span class="function">{@link password_hash()}</span> 创建的散列值。     </span>    </dd>          <dt><span class="parameter" style="color:#3A95FF">algo</span></dt>    <dd>     <p class="para">      一个用来在散列密码时指示算法的<a href="http://php.net/manual/zh/password.constants.php" class="link">密码算法常量</a>。     </p>    </dd>          <dt><span class="parameter" style="color:#3A95FF">options</span></dt>    <dd>     <p class="para">      一个包含有选项的关联数组。详细的参数说明，请参考文档 <a href="http://php.net/manual/zh/password.constants.php" class="link">密码算法常数</a>。     </p>    </dd>     </dl> </div>  <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 returnvalues" id="refsect1-function.password-needs-rehash-returnvalues">  <h3 class="title">返回值</h3>  <p class="para">   如果散列需要重新生成才能匹配指定的 <span class="parameter" style="color:#3A95FF">algo</span> 和 <span class="parameter" style="color:#3A95FF">options</span>，   则返回 <strong><span>TRUE</span></strong>，否则返回 <strong><span>FALSE</span></strong>。  </p> </div> <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 changelog" id="refsect1-function.password-needs-rehash-changelog">  <h3 class="title">更新日志</h3>  <span>   <table class="doctable informaltable">         <thead>      <tr>       <th>版本</th>       <th>说明</th>      </tr>     </thead>     <tbody class="tbody">      <tr>       <td>7.4.0</td>       <td>        现在 <span class="parameter" style="color:#3A95FF">algo</span> 参数可以支持 <span class="type" style="color:#EAB766">string</span> 类型，但为了向后兼容性，同时支持        <span class="type" style="color:#EAB766">int</span> 类型。       </td>      </tr>     </tbody>       </table>  </span> </div>  <br></br><div style="BORDER-TOP: gray 1px dashed; OVERFLOW: hidden; HEIGHT: 1px"></div><div class="refsect1 examples" id="refsect1-function.password-needs-rehash-examples">  <h3 class="title">范例</h3>  <span>   <div class="example" id="openssl_spki_export_challenge.example.basic">    <p><strong>Example #1 <span class="function"><strong style="color:#CC7832">password_needs_rehash()</strong></span>用法</strong></p>    <div class="example-contents"><div class="phpcode" style="border-color:gray;background:#232525"><span><span style="color: #000000"><span style="color: #9876AA">&lt;?php<br /><br />$password&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #DD0000">'rasmuslerdorf'</span><span style="color: #007700">;<br /></span><span style="color: #9876AA">$hash&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #DD0000">'$2y$10$YCFsG6elYca568hBi2pZ0.3LDL5wjgxct1N8w/oLR/jfHsiQwCqTS'</span><span style="color: #007700">;<br /><br /></span><span style="color: #FF8000">//&nbsp;当硬件性能得到改善时，cost&nbsp;参数可以再修改<br /></span><span style="color: #9876AA">$options&nbsp;</span><span style="color: #007700">=&nbsp;array(</span><span style="color: #DD0000">'cost'&nbsp;</span><span style="color: #007700">=&gt;&nbsp;</span><span style="color: #9876AA">11</span><span style="color: #007700">);<br /><br /></span><span style="color: #FF8000">//&nbsp;根据明文密码验证储存的散列<br /></span><span style="color: #007700">if&nbsp;(</span><span style="color: #9876AA">password_verify</span><span style="color: #007700">(</span><span style="color: #9876AA">$password</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">$hash</span><span style="color: #007700">))&nbsp;{<br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #FF8000">//&nbsp;检测是否有更新的可用散列算法<br />&nbsp;&nbsp;&nbsp;&nbsp;//&nbsp;或者&nbsp;cost&nbsp;发生变化<br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #007700">if&nbsp;(</span><span style="color: #9876AA">password_needs_rehash</span><span style="color: #007700">(</span><span style="color: #9876AA">$hash</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">PASSWORD_DEFAULT</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">$options</span><span style="color: #007700">))&nbsp;{<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #FF8000">//&nbsp;如果是这样，则创建新散列，替换旧散列<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #9876AA">$newHash&nbsp;</span><span style="color: #007700">=&nbsp;</span><span style="color: #9876AA">password_hash</span><span style="color: #007700">(</span><span style="color: #9876AA">$password</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">PASSWORD_DEFAULT</span><span style="color: #007700">,&nbsp;</span><span style="color: #9876AA">$options</span><span style="color: #007700">);<br />&nbsp;&nbsp;&nbsp;&nbsp;}<br /><br />&nbsp;&nbsp;&nbsp;&nbsp;</span><span style="color: #FF8000">//&nbsp;使用户登录<br /></span><span style="color: #007700">}<br /></span><span style="color: #9876AA">?&gt;</span></span></span></div>    </div>   </div>  </span>  </div></div>
     * @param $hash
     * @param $algo
     * @param array $options
     * @return bool Returns TRUE if the hash should be rehashed to match the given algo and options, or FALSE otherwise.
     */
    public static function needsRehash($hash, $algo = PASSWORD_DEFAULT, $options = ['cost' => 11]): bool
    {
        return password_needs_rehash($hash, $algo, $options);
    }
}