<?php

namespace app\controller;

use support\Request;
use support\Response;
use app\model\HotelPaymentMethod;
use app\model\PaymentMethod;
use app\model\Hotel;

/**
 * 酒店支付方式控制器
 */
class HotelPaymentController
{
    /**
     * 获取酒店支付方式列表
     */
    public function index(Request $request, $id): Response
    {
        try {
            // 验证酒店是否存在
            $hotel = Hotel::find($id);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            // 获取支付方式列表
            $paymentMethods = HotelPaymentMethod::where('hotel_id', $id)
                ->ordered()
                ->get();

            $data = $paymentMethods->map(function ($method) {
                return $method->toApiArray();
            });

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取支付方式列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取单个支付方式详情
     */
    public function show(Request $request, $id, $payment_id): Response
    {
        try {
            $paymentMethod = HotelPaymentMethod::where('hotel_id', $id)
                ->where('id', $payment_id)
                ->first();

            if (!$paymentMethod) {
                return $this->error('支付方式不存在', 404);
            }

            return $this->success($paymentMethod->toApiArray());
        } catch (\Exception $e) {
            return $this->error('获取支付方式详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建支付方式
     */
    public function store(Request $request, $id): Response
    {
        try {
            // 验证酒店是否存在
            $hotel = Hotel::find($id);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            $data = $request->all();
            $data['hotel_id'] = $id;

            // 验证数据
            $validator = validator($data, HotelPaymentMethod::rules());
            if ($validator->fails()) {
                return $this->error('数据验证失败', 400, $validator->errors());
            }

            // 如果没有设置排序，自动设置为最大值+1
            if (!isset($data['sort_order'])) {
                $maxOrder = HotelPaymentMethod::where('hotel_id', $id)->max('sort_order') ?? 0;
                $data['sort_order'] = $maxOrder + 1;
            }

            // 创建支付方式
            $paymentMethod = HotelPaymentMethod::create($data);

            // 如果设置为默认，更新其他支付方式
            if (!empty($data['is_default'])) {
                $paymentMethod->setAsDefault();
            }

            return $this->success($paymentMethod->toApiArray(), '支付方式创建成功');
        } catch (\Exception $e) {
            return $this->error('创建支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新支付方式
     */
    public function update(Request $request, $id, $payment_id): Response
    {
        try {
            $paymentMethod = HotelPaymentMethod::where('hotel_id', $id)
                ->where('id', $payment_id)
                ->first();

            if (!$paymentMethod) {
                return $this->error('支付方式不存在', 404);
            }

            $data = $request->all();

            // 验证数据
            $rules = HotelPaymentMethod::rules();
            // 更新时不需要验证hotel_id
            unset($rules['hotel_id']);
            
            $validator = validator($data, $rules);
            if ($validator->fails()) {
                return $this->error('数据验证失败', 400, $validator->errors());
            }

            // 更新支付方式
            $paymentMethod->update($data);

            // 如果设置为默认，更新其他支付方式
            if (!empty($data['is_default'])) {
                $paymentMethod->setAsDefault();
            }

            return $this->success($paymentMethod->fresh()->toApiArray(), '支付方式更新成功');
        } catch (\Exception $e) {
            return $this->error('更新支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除支付方式
     */
    public function destroy(Request $request, $id, $payment_id): Response
    {
        try {
            $paymentMethod = HotelPaymentMethod::where('hotel_id', $id)
                ->where('id', $payment_id)
                ->first();

            if (!$paymentMethod) {
                return $this->error('支付方式不存在', 404);
            }

            // 如果是默认支付方式，需要先设置其他支付方式为默认
            if ($paymentMethod->is_default) {
                $otherMethod = HotelPaymentMethod::where('hotel_id', $id)
                    ->where('id', '!=', $payment_id)
                    ->where('status', 'active')
                    ->ordered()
                    ->first();
                
                if ($otherMethod) {
                    $otherMethod->setAsDefault();
                }
            }

            $paymentMethod->delete();

            return $this->success(null, '支付方式删除成功');
        } catch (\Exception $e) {
            return $this->error('删除支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新支付方式
     */
    public function batchUpdate(Request $request, $id): Response
    {
        try {
            $data = $request->all();
            $paymentMethods = $data['payment_methods'] ?? [];

            if (empty($paymentMethods)) {
                return $this->error('支付方式数据不能为空', 400);
            }

            $updated = [];
            foreach ($paymentMethods as $methodData) {
                if (isset($methodData['id'])) {
                    $paymentMethod = HotelPaymentMethod::where('hotel_id', $id)
                        ->where('id', $methodData['id'])
                        ->first();
                    
                    if ($paymentMethod) {
                        $paymentMethod->update($methodData);
                        $updated[] = $paymentMethod->fresh()->toApiArray();
                    }
                }
            }

            return $this->success($updated, '批量更新成功');
        } catch (\Exception $e) {
            return $this->error('批量更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新支付方式排序
     */
    public function updateOrder(Request $request, $id): Response
    {
        try {
            $data = $request->all();
            $orders = $data['orders'] ?? [];

            if (empty($orders)) {
                return $this->error('排序数据不能为空', 400);
            }

            // 验证所有ID都属于该酒店
            $ids = array_column($orders, 'id');
            $count = HotelPaymentMethod::where('hotel_id', $id)
                ->whereIn('id', $ids)
                ->count();
            
            if ($count !== count($ids)) {
                return $this->error('包含无效的支付方式ID', 400);
            }

            // 更新排序
            $success = HotelPaymentMethod::updateSortOrders($orders);
            
            if ($success) {
                return $this->success(null, '排序更新成功');
            } else {
                return $this->error('排序更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新排序失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置默认支付方式
     */
    public function setDefault(Request $request, $id, $payment_id): Response
    {
        try {
            $paymentMethod = HotelPaymentMethod::where('hotel_id', $id)
                ->where('id', $payment_id)
                ->first();

            if (!$paymentMethod) {
                return $this->error('支付方式不存在', 404);
            }

            $paymentMethod->setAsDefault();

            return $this->success($paymentMethod->fresh()->toApiArray(), '默认支付方式设置成功');
        } catch (\Exception $e) {
            return $this->error('设置默认支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取常用支付方式列表
     */
    public function commonMethods(Request $request): Response
    {
        try {
            $paymentMethods = PaymentMethod::getCommonMethods();
            
            $data = $paymentMethods->map(function ($method) {
                return $method->toApiArray();
            });

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取常用支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量添加常用支付方式到酒店
     */
    public function addCommonMethods(Request $request, $id): Response
    {
        try {
            $data = $request->all();
            $paymentIds = $data['payment_ids'] ?? [];

            if (empty($paymentIds)) {
                return $this->error('支付方式ID不能为空', 400);
            }

            // 验证酒店是否存在
            $hotel = Hotel::find($id);
            if (!$hotel) {
                return $this->error('酒店不存在', 404);
            }

            // 获取常用支付方式
            $commonMethods = PaymentMethod::whereIn('id', $paymentIds)->get();
            
            if ($commonMethods->isEmpty()) {
                return $this->error('未找到有效的支付方式', 404);
            }

            $created = [];
            $maxOrder = HotelPaymentMethod::where('hotel_id', $id)->max('sort_order') ?? 0;

            foreach ($commonMethods as $index => $method) {
                // 检查是否已存在
                $existing = HotelPaymentMethod::where('hotel_id', $id)
                    ->where('payment_code', $method->payment_code)
                    ->first();
                
                if (!$existing) {
                    $hotelPayment = HotelPaymentMethod::create([
                        'hotel_id' => $id,
                        'payment_id' => $method->id,
                        'payment_name' => $method->payment_name,
                        'payment_code' => $method->payment_code,
                        'payment_type' => $method->payment_type,
                        'bookable' => true,
                        'cvc_required' => $method->cvc_required,
                        'payable' => true,
                        'sort_order' => $maxOrder + $index + 1,
                        'status' => 'active',
                        'description' => $method->description,
                        'is_default' => false,
                    ]);
                    
                    $created[] = $hotelPayment->toApiArray();
                }
            }

            return $this->success($created, "成功添加 " . count($created) . " 个支付方式");
        } catch (\Exception $e) {
            return $this->error('添加常用支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 成功响应
     */
    private function success($data = null, string $message = '操作成功'): Response
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'request_id' => uniqid(),
        ]);
    }

    /**
     * 错误响应
     */
    private function error(string $message, int $code = 500, $errors = null): Response
    {
        $response = [
            'code' => $code,
            'message' => $message,
            'timestamp' => time(),
            'request_id' => uniqid(),
        ];

        if ($errors) {
            $response['errors'] = $errors;
        }

        return json($response, $code >= 400 && $code < 500 ? $code : 500);
    }
}
