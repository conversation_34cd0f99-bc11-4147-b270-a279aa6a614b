<?php

namespace app\controller;

use app\service\RateService;
use support\Request;
use support\Response;

/**
 * 价格管理控制器
 */
class RateController extends BaseController
{
    /**
     * 价格服务
     *
     * @var RateService
     */
    private $rateService;

    public function __construct()
    {
        $this->rateService = new RateService();
    }

    /**
     * 查询价格
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'rate_plan_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($params['start_date'] > $params['end_date']) {
                return $this->error('开始日期不能晚于结束日期');
            }

            $result = $this->rateService->queryRates($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '查询价格');
        }
    }

    /**
     * 更新价格
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'room_type_id', 'rate_plan_id', 'date', 'selling_price']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'rate_plan_id' => 'integer',
                'date' => 'date',
                'selling_price' => 'positive',
                'base_price' => 'positive',
                'room_fee' => 'positive',
                'service_fee' => 'positive',
                'tax_fee' => 'positive',
                'city_tax' => 'positive',
                'discount_value' => 'positive'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证价格
            if ($data['selling_price'] <= 0) {
                return $this->error('销售价格必须大于0');
            }

            $result = $this->rateService->updateRate($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新价格');
        }
    }

    /**
     * 批量更新价格
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdate(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['updates']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['updates'])) {
                return $this->error('updates必须是数组');
            }

            if (empty($data['updates'])) {
                return $this->error('updates不能为空');
            }

            if (count($data['updates']) > 100) {
                return $this->error('单次最多更新100条记录');
            }

            // 验证每个更新项
            foreach ($data['updates'] as $index => $update) {
                $updateErrors = $this->validateRequired($update, ['hotel_id', 'room_type_id', 'rate_plan_id', 'date', 'selling_price']);
                if ($updateErrors) {
                    return $this->error("第" . ($index + 1) . "条记录参数验证失败", 400, $updateErrors);
                }

                $formatErrors = $this->validateFormat($update, [
                    'hotel_id' => 'integer',
                    'room_type_id' => 'integer',
                    'rate_plan_id' => 'integer',
                    'date' => 'date',
                    'selling_price' => 'positive'
                ]);
                if ($formatErrors) {
                    return $this->error("第" . ($index + 1) . "条记录数据格式错误", 400, $formatErrors);
                }

                if ($update['selling_price'] <= 0) {
                    return $this->error("第" . ($index + 1) . "条记录销售价格必须大于0");
                }
            }

            $result = $this->rateService->batchUpdateRates($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新价格');
        }
    }

    /**
     * 获取价格统计
     *
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'rate_plan_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 查询价格数据
            $ratesResult = $this->rateService->queryRates($params);
            if ($ratesResult['code'] !== 0) {
                return $this->handleServiceResult($ratesResult);
            }

            $rates = $ratesResult['data'];

            // 计算统计数据
            $statistics = [
                'total_records' => count($rates),
                'avg_price' => 0,
                'min_price' => 0,
                'max_price' => 0,
                'total_revenue' => 0,
                'currency_breakdown' => []
            ];

            if (!empty($rates)) {
                $prices = array_column($rates, 'selling_price');
                $statistics['avg_price'] = round(array_sum($prices) / count($prices), 2);
                $statistics['min_price'] = min($prices);
                $statistics['max_price'] = max($prices);
                $statistics['total_revenue'] = array_sum($prices);

                // 按货币分组统计
                $currencyGroups = [];
                foreach ($rates as $rate) {
                    $currency = $rate['currency'] ?? 'CNY';
                    if (!isset($currencyGroups[$currency])) {
                        $currencyGroups[$currency] = [
                            'count' => 0,
                            'total_amount' => 0,
                            'avg_price' => 0
                        ];
                    }
                    $currencyGroups[$currency]['count']++;
                    $currencyGroups[$currency]['total_amount'] += $rate['selling_price'];
                }

                foreach ($currencyGroups as $currency => $data) {
                    $currencyGroups[$currency]['avg_price'] = round($data['total_amount'] / $data['count'], 2);
                }

                $statistics['currency_breakdown'] = $currencyGroups;
            }

            return $this->success([
                'statistics' => $statistics,
                'date_range' => [
                    'start_date' => $params['start_date'],
                    'end_date' => $params['end_date']
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取价格统计');
        }
    }

    /**
     * 价格比较
     *
     * @param Request $request
     * @return Response
     */
    public function compare(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'room_type_id', 'date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 查询该房型在指定日期的所有价格计划
            $queryParams = [
                'hotel_id' => $params['hotel_id'],
                'room_type_id' => $params['room_type_id'],
                'start_date' => $params['date'],
                'end_date' => $params['date']
            ];

            $ratesResult = $this->rateService->queryRates($queryParams);
            if ($ratesResult['code'] !== 0) {
                return $this->handleServiceResult($ratesResult);
            }

            $rates = $ratesResult['data'];

            // 按价格计划分组
            $comparison = [];
            foreach ($rates as $rate) {
                $comparison[] = [
                    'rate_plan_id' => $rate['rate_plan_id'],
                    'rate_plan' => $rate['rate_plan'] ?? null,
                    'selling_price' => $rate['selling_price'],
                    'base_price' => $rate['base_price'],
                    'total_price' => $rate['total_price'],
                    'discount_amount' => $rate['discount_amount'] ?? 0,
                    'currency' => $rate['currency']
                ];
            }

            // 按价格排序
            usort($comparison, function($a, $b) {
                return $a['selling_price'] <=> $b['selling_price'];
            });

            return $this->success([
                'date' => $params['date'],
                'hotel_id' => $params['hotel_id'],
                'room_type_id' => $params['room_type_id'],
                'rate_plans' => $comparison,
                'lowest_price' => !empty($comparison) ? $comparison[0]['selling_price'] : 0,
                'highest_price' => !empty($comparison) ? end($comparison)['selling_price'] : 0
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '价格比较');
        }
    }
}
