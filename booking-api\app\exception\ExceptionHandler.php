<?php

namespace app\exception;

use Throwable;
use Webman\Http\Request;
use Webman\Http\Response;
use support\Log;
use app\service\LogService;

/**
 * 全局异常处理器
 */
class ExceptionHandler
{
    /**
     * 处理异常
     *
     * @param Throwable $exception
     * @param Request $request
     * @return Response
     */
    public static function handle(Throwable $exception, Request $request): Response
    {
        // 记录异常日志
        self::logException($exception, $request);

        // 根据异常类型返回不同的响应
        if ($exception instanceof BusinessException) {
            return self::handleBusinessException($exception);
        }

        if ($exception instanceof ValidationException) {
            return self::handleValidationException($exception);
        }

        if ($exception instanceof \PDOException) {
            return self::handleDatabaseException($exception);
        }

        if ($exception instanceof \Redis\Exception) {
            return self::handleRedisException($exception);
        }

        // 处理其他异常
        return self::handleGenericException($exception);
    }

    /**
     * 处理业务异常
     *
     * @param BusinessException $exception
     * @return Response
     */
    private static function handleBusinessException(BusinessException $exception): Response
    {
        return json([
            'code' => $exception->getHttpStatusCode(),
            'error_code' => $exception->getErrorCode(),
            'message' => $exception->getMessage(),
            'data' => $exception->getErrorData(),
            'timestamp' => time(),
            'request_id' => self::getRequestId()
        ], $exception->getHttpStatusCode());
    }

    /**
     * 处理验证异常
     *
     * @param ValidationException $exception
     * @return Response
     */
    private static function handleValidationException(ValidationException $exception): Response
    {
        return json([
            'code' => 422,
            'error_code' => 'VALIDATION_ERROR',
            'message' => '数据验证失败',
            'data' => [
                'errors' => $exception->getErrors()
            ],
            'timestamp' => time(),
            'request_id' => self::getRequestId()
        ], 422);
    }

    /**
     * 处理数据库异常
     *
     * @param \PDOException $exception
     * @return Response
     */
    private static function handleDatabaseException(\PDOException $exception): Response
    {
        $message = '数据库操作失败';
        $errorCode = 'DATABASE_ERROR';
        $statusCode = 500;

        // 根据错误代码提供更具体的错误信息
        switch ($exception->getCode()) {
            case '23000': // 完整性约束违反
                $message = '数据完整性约束违反';
                $errorCode = 'INTEGRITY_CONSTRAINT_VIOLATION';
                $statusCode = 400;
                break;
            case '42S02': // 表不存在
                $message = '数据表不存在';
                $errorCode = 'TABLE_NOT_EXISTS';
                break;
            case '42S22': // 列不存在
                $message = '数据列不存在';
                $errorCode = 'COLUMN_NOT_EXISTS';
                break;
            case '08004': // 连接被拒绝
                $message = '数据库连接失败';
                $errorCode = 'DATABASE_CONNECTION_FAILED';
                break;
        }

        return json([
            'code' => $statusCode,
            'error_code' => $errorCode,
            'message' => $message,
            'timestamp' => time(),
            'request_id' => self::getRequestId(),
            'debug' => config('app.debug', false) ? [
                'sql_state' => $exception->getCode(),
                'sql_message' => $exception->getMessage()
            ] : null
        ], $statusCode);
    }

    /**
     * 处理Redis异常
     *
     * @param \Redis\Exception $exception
     * @return Response
     */
    private static function handleRedisException(\Redis\Exception $exception): Response
    {
        return json([
            'code' => 500,
            'error_code' => 'CACHE_ERROR',
            'message' => '缓存服务异常',
            'timestamp' => time(),
            'request_id' => self::getRequestId(),
            'debug' => config('app.debug', false) ? [
                'redis_message' => $exception->getMessage()
            ] : null
        ], 500);
    }

    /**
     * 处理通用异常
     *
     * @param Throwable $exception
     * @return Response
     */
    private static function handleGenericException(Throwable $exception): Response
    {
        $statusCode = 500;
        $errorCode = 'INTERNAL_SERVER_ERROR';
        $message = '服务器内部错误';

        // 根据异常类型调整响应
        if ($exception instanceof \InvalidArgumentException) {
            $statusCode = 400;
            $errorCode = 'INVALID_ARGUMENT';
            $message = '参数错误';
        } elseif ($exception instanceof \UnauthorizedHttpException) {
            $statusCode = 401;
            $errorCode = 'UNAUTHORIZED';
            $message = '未授权访问';
        } elseif ($exception instanceof \ForbiddenHttpException) {
            $statusCode = 403;
            $errorCode = 'FORBIDDEN';
            $message = '禁止访问';
        } elseif ($exception instanceof \NotFoundHttpException) {
            $statusCode = 404;
            $errorCode = 'NOT_FOUND';
            $message = '资源不存在';
        }

        return json([
            'code' => $statusCode,
            'error_code' => $errorCode,
            'message' => $message,
            'timestamp' => time(),
            'request_id' => self::getRequestId(),
            'debug' => config('app.debug', false) ? [
                'exception' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'message' => $exception->getMessage(),
                'trace' => array_slice($exception->getTrace(), 0, 10) // 只显示前10层调用栈
            ] : null
        ], $statusCode);
    }

    /**
     * 记录异常日志
     *
     * @param Throwable $exception
     * @param Request $request
     */
    private static function logException(Throwable $exception, Request $request): void
    {
        $context = [
            'exception' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'url' => $request->url(),
            'method' => $request->method(),
            'ip' => $request->getRealIp(),
            'user_agent' => $request->header('User-Agent'),
            'user_id' => self::getCurrentUserId($request),
            'request_id' => self::getRequestId(),
            'request_data' => self::getRequestData($request)
        ];

        // 根据异常类型选择日志级别
        if ($exception instanceof BusinessException) {
            $level = $exception->getHttpStatusCode() >= 500 ? 'error' : 'warning';
        } elseif ($exception instanceof ValidationException) {
            $level = 'info';
        } else {
            $level = 'error';
        }

        LogService::log($level, '异常处理: ' . $exception->getMessage(), $context, LogService::TYPE_ERROR);
    }

    /**
     * 获取请求数据
     *
     * @param Request $request
     * @return array
     */
    private static function getRequestData(Request $request): array
    {
        $data = [];

        // GET参数
        if ($request->get()) {
            $data['get'] = $request->get();
        }

        // POST参数
        if ($request->post()) {
            $data['post'] = self::filterSensitiveData($request->post());
        }

        // JSON数据
        $contentType = $request->header('content-type', '');
        if (strpos($contentType, 'application/json') !== false) {
            $rawBody = $request->rawBody();
            $jsonData = json_decode($rawBody, true);
            if (is_array($jsonData)) {
                $data['json'] = self::filterSensitiveData($jsonData);
            }
        }

        return $data;
    }

    /**
     * 过滤敏感数据
     *
     * @param array $data
     * @return array
     */
    private static function filterSensitiveData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_hash',
            'password_confirmation',
            'token',
            'api_key',
            'api_secret',
            'access_token',
            'refresh_token',
            'credit_card',
            'card_number',
            'cvv',
            'ssn',
            'id_card'
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***FILTERED***';
            }
        }

        return $data;
    }

    /**
     * 获取当前用户ID
     *
     * @param Request $request
     * @return int|null
     */
    private static function getCurrentUserId(Request $request): ?int
    {
        $user = $request->user ?? null;
        return $user ? ($user->id ?? null) : null;
    }

    /**
     * 获取请求ID
     *
     * @return string
     */
    private static function getRequestId(): string
    {
        return $_SERVER['HTTP_X_REQUEST_ID'] ?? 'req_' . uniqid() . '_' . mt_rand(1000, 9999);
    }

    /**
     * 检查是否应该报告异常
     *
     * @param Throwable $exception
     * @return bool
     */
    public static function shouldReport(Throwable $exception): bool
    {
        // 不报告的异常类型
        $dontReport = [
            ValidationException::class,
            // 可以添加其他不需要报告的异常类型
        ];

        foreach ($dontReport as $type) {
            if ($exception instanceof $type) {
                return false;
            }
        }

        // 业务异常根据HTTP状态码决定是否报告
        if ($exception instanceof BusinessException) {
            return $exception->getHttpStatusCode() >= 500;
        }

        return true;
    }

    /**
     * 发送异常通知
     *
     * @param Throwable $exception
     * @param Request $request
     */
    public static function notify(Throwable $exception, Request $request): void
    {
        // 只通知严重异常
        if (!self::shouldReport($exception)) {
            return;
        }

        // 这里可以实现邮件、短信、钉钉等通知方式
        // 例如：发送邮件给开发团队
        try {
            $subject = '系统异常通知: ' . get_class($exception);
            $content = sprintf(
                "异常信息: %s\n文件: %s:%d\nURL: %s\n用户: %s\n时间: %s",
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine(),
                $request->url(),
                self::getCurrentUserId($request) ?? 'Guest',
                date('Y-m-d H:i:s')
            );

            // 发送通知的具体实现
            // MailService::send('<EMAIL>', $subject, $content);
            
        } catch (\Exception $e) {
            // 通知发送失败，记录日志但不抛出异常
            Log::error('异常通知发送失败', [
                'original_exception' => $exception->getMessage(),
                'notification_error' => $e->getMessage()
            ]);
        }
    }
}
