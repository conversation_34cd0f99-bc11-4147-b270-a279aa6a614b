<?php

namespace app\model;

use support\Model;

/**
 * 设施设备模板模型
 */
class FacilityTemplate extends Model
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'facility_templates';

    /**
     * 主键
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     * @var array
     */
    protected $fillable = [
        'name_zh',
        'name_en',
        'category',
        'description',
        'icon',
        'is_active',
        'sort_order',
    ];

    /**
     * 属性类型转换
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 设施分类定义
     */
    const CATEGORIES = [
        'basic' => '基础设施',
        'dining' => '餐饮设施',
        'entertainment' => '娱乐设施',
        'business' => '商务设施',
        'sports' => '运动设施',
        'health' => '健康美容',
        'fitness' => '健身设施',
        'recreation' => '休闲设施',
        'children' => '儿童设施',
        'transport' => '交通服务',
        'service' => '服务设施',
        'safety' => '安全设施',
        'accessibility' => '无障碍设施',
        'shopping' => '购物设施',
        'policy' => '政策规定',
        'other' => '其他设施',
    ];

    /**
     * 获取分类名称
     */
    public function getCategoryNameAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? $this->category;
    }

    /**
     * 获取显示名称（优先中文）
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name_zh ?: $this->name_en;
    }

    /**
     * 作用域：启用的设施
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        if ($category) {
            return $query->where('category', $category);
        }
        return $query;
    }

    /**
     * 作用域：按关键词搜索
     */
    public function scopeSearch($query, $keyword)
    {
        if ($keyword) {
            return $query->where(function ($q) use ($keyword) {
                $q->where('name_zh', 'like', "%{$keyword}%")
                  ->orWhere('name_en', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }
        return $query;
    }

    /**
     * 作用域：排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取所有分类及统计
     */
    public static function getCategoriesWithCount(): array
    {
        $categories = [];
        $counts = self::selectRaw('category, COUNT(*) as count')
            ->where('is_active', true)
            ->groupBy('category')
            ->pluck('count', 'category')
            ->toArray();

        foreach (self::CATEGORIES as $key => $name) {
            $categories[] = [
                'key' => $key,
                'name' => $name,
                'count' => $counts[$key] ?? 0,
            ];
        }

        return $categories;
    }

    /**
     * 批量导入设施模板
     */
    public static function batchImport(array $data): array
    {
        $imported = 0;
        $skipped = 0;
        $errors = [];

        foreach ($data as $item) {
            try {
                // 检查是否已存在
                $existing = self::where('id', $item['id'])->first();
                if ($existing) {
                    $skipped++;
                    continue;
                }

                // 创建新记录
                self::create($item);
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "导入失败: {$item['name_zh']} - {$e->getMessage()}";
            }
        }

        return [
            'imported' => $imported,
            'skipped' => $skipped,
            'errors' => $errors,
        ];
    }

    /**
     * 获取推荐设施（按使用频率）
     */
    public static function getRecommended(int $limit = 20): array
    {
        // 这里可以根据实际使用情况来推荐
        // 暂时返回排序靠前的设施
        return self::active()
            ->ordered()
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * 同步到酒店设施
     */
    public function syncToHotelFacility(int $hotelId, array $options = []): bool
    {
        try {
            $facilityData = [
                'hotel_id' => $hotelId,
                'facility_name' => $this->name_zh,
                'facility_name_en' => $this->name_en,
                'category' => $this->category,
                'description' => $this->description,
                'icon' => $this->icon,
                'available' => $options['available'] ?? true,
                'has_fee' => $options['has_fee'] ?? false,
                'fee_amount' => $options['fee_amount'] ?? 0,
                'fee_type' => $options['fee_type'] ?? 'one_time',
                'template_id' => $this->id,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // 检查是否已存在
            $existing = \app\model\HotelFacility::where('hotel_id', $hotelId)
                ->where('template_id', $this->id)
                ->first();

            if ($existing) {
                return false; // 已存在，不重复添加
            }

            \app\model\HotelFacility::create($facilityData);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
