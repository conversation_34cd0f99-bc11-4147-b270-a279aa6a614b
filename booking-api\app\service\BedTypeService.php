<?php

namespace app\service;

use app\model\BedType;
use app\model\BedTypeTranslation;
use app\model\BedTypeStatistics;

/**
 * 床型类型服务类
 */
class BedTypeService extends BaseService
{
    /**
     * 获取床型类型列表
     *
     * @param array $params
     * @return array
     */
    public function getBedTypeList(array $params = [])
    {
        try {
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;
            $locale = $params['locale'] ?? 'zh-CN';

            $query = BedType::query();

            // 按状态筛选
            if (isset($params['is_active'])) {
                $query->where('is_active', $params['is_active']);
            }

            // 按分类筛选
            if (!empty($params['category'])) {
                $query->where('category', $params['category']);
            }

            // 按尺寸筛选
            if (!empty($params['size'])) {
                $query->where('size', $params['size']);
            }

            // 按是否标准床型筛选
            if (isset($params['is_standard'])) {
                $query->where('is_standard', $params['is_standard']);
            }

            // 搜索
            if (!empty($params['search'])) {
                $search = $params['search'];
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // 关联统计信息
            $query->with(['statistics']);

            // 排序
            $sortBy = $params['sort_by'] ?? 'sort_order';
            $sortDirection = $params['sort_direction'] ?? 'asc';

            if ($sortBy === 'usage_count') {
                $query->leftJoin('bed_type_statistics', 'bed_types.id', '=', 'bed_type_statistics.bed_type_id')
                      ->orderBy('bed_type_statistics.usage_count', $sortDirection)
                      ->select('bed_types.*');
            } else {
                $query->orderBy($sortBy, $sortDirection);
            }

            // 分页
            $result = $query->paginate($perPage, ['*'], 'page', $page);

            // 格式化数据
            $items = $result->items();
            foreach ($items as $item) {
                $item->localized_name = $item->getLocalizedName($locale);
                $item->localized_description = $item->getLocalizedDescription($locale);
                $item->category_name = $item->category_name;
                $item->size_name = $item->size_name;
                $item->size_description = $item->size_description;
            }

            return $this->success([
                'items' => $items,
                'pagination' => [
                    'current' => $result->currentPage(),
                    'per_page' => $result->perPage(),
                    'total' => $result->total(),
                    'last_page' => $result->lastPage(),
                ]
            ]);

        } catch (\Exception $e) {
            $this->logError('获取床型类型列表失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取床型类型列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取床型类型详情
     *
     * @param int $id
     * @param string $locale
     * @return array
     */
    public function getBedTypeDetail(int $id, string $locale = 'zh-CN')
    {
        try {
            $bedType = BedType::with(['translations', 'statistics'])->find($id);

            if (!$bedType) {
                return $this->error('床型类型不存在', 404);
            }

            // 添加本地化信息
            $bedType->localized_name = $bedType->getLocalizedName($locale);
            $bedType->localized_description = $bedType->getLocalizedDescription($locale);
            $bedType->category_name = $bedType->category_name;
            $bedType->size_name = $bedType->size_name;
            $bedType->size_description = $bedType->size_description;

            return $this->success($bedType);

        } catch (\Exception $e) {
            $this->logError('获取床型类型详情失败', [
                'id' => $id,
                'locale' => $locale,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取床型类型详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建床型类型
     *
     * @param array $data
     * @return array
     */
    public function createBedType(array $data)
    {
        try {
            // 验证代码唯一性
            if (BedType::where('code', $data['code'])->exists()) {
                return $this->error('床型代码已存在');
            }

            return $this->transaction(function () use ($data) {
                // 创建床型类型
                $bedType = BedType::create($data);

                // 创建多语言翻译
                if (!empty($data['translations'])) {
                    foreach ($data['translations'] as $translation) {
                        $translation['bed_type_id'] = $bedType->id;
                        BedTypeTranslation::create($translation);
                    }
                }

                // 初始化统计信息
                BedTypeStatistics::create([
                    'bed_type_id' => $bedType->id,
                    'usage_count' => 0,
                    'room_type_count' => 0,
                    'hotel_count' => 0,
                ]);

                $this->logInfo('创建床型类型成功', [
                    'bed_type_id' => $bedType->id,
                    'code' => $bedType->code,
                    'name' => $bedType->name
                ]);

                return $this->success($bedType, '创建床型类型成功');
            });

        } catch (\Exception $e) {
            $this->logError('创建床型类型失败', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            return $this->error('创建床型类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新床型类型
     *
     * @param int $id
     * @param array $data
     * @return array
     */
    public function updateBedType(int $id, array $data)
    {
        try {
            $bedType = BedType::find($id);

            if (!$bedType) {
                return $this->error('床型类型不存在', 404);
            }

            // 验证代码唯一性（排除当前记录）
            if (isset($data['code']) && 
                BedType::where('code', $data['code'])->where('id', '!=', $id)->exists()) {
                return $this->error('床型代码已存在');
            }

            return $this->transaction(function () use ($bedType, $data) {
                // 更新床型类型
                $bedType->update($data);

                // 更新多语言翻译
                if (!empty($data['translations'])) {
                    // 删除现有翻译
                    BedTypeTranslation::where('bed_type_id', $bedType->id)->delete();
                    
                    // 创建新翻译
                    foreach ($data['translations'] as $translation) {
                        $translation['bed_type_id'] = $bedType->id;
                        BedTypeTranslation::create($translation);
                    }
                }

                $this->logInfo('更新床型类型成功', [
                    'bed_type_id' => $bedType->id,
                    'code' => $bedType->code,
                    'name' => $bedType->name
                ]);

                return $this->success($bedType, '更新床型类型成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新床型类型失败', [
                'id' => $id,
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            return $this->error('更新床型类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除床型类型
     *
     * @param int $id
     * @return array
     */
    public function deleteBedType(int $id)
    {
        try {
            $bedType = BedType::find($id);

            if (!$bedType) {
                return $this->error('床型类型不存在', 404);
            }

            // 检查是否有房型在使用
            $roomTypeCount = $bedType->roomTypes()->count();
            if ($roomTypeCount > 0) {
                return $this->error("该床型类型正在被 {$roomTypeCount} 个房型使用，无法删除");
            }

            return $this->transaction(function () use ($bedType) {
                // 删除相关数据
                BedTypeTranslation::where('bed_type_id', $bedType->id)->delete();
                BedTypeStatistics::where('bed_type_id', $bedType->id)->delete();
                
                // 删除床型类型
                $bedType->delete();

                $this->logInfo('删除床型类型成功', [
                    'bed_type_id' => $bedType->id,
                    'code' => $bedType->code,
                    'name' => $bedType->name
                ]);

                return $this->success(null, '删除床型类型成功');
            });

        } catch (\Exception $e) {
            $this->logError('删除床型类型失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            return $this->error('删除床型类型失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取床型类型选项（用于下拉选择）
     *
     * @param array $params
     * @return array
     */
    public function getBedTypeOptions(array $params = [])
    {
        try {
            $locale = $params['locale'] ?? 'zh-CN';
            $grouped = $params['grouped'] ?? false;

            if ($grouped) {
                $options = BedType::getGroupedOptions($locale);
            } else {
                $options = BedType::getActiveOptions($locale);
            }

            return $this->success($options);

        } catch (\Exception $e) {
            $this->logError('获取床型类型选项失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取床型类型选项失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新床型类型状态
     *
     * @param array $ids
     * @param bool $isActive
     * @return array
     */
    public function batchUpdateStatus(array $ids, bool $isActive)
    {
        try {
            $count = BedType::whereIn('id', $ids)->update(['is_active' => $isActive]);

            $this->logInfo('批量更新床型类型状态', [
                'ids' => $ids,
                'is_active' => $isActive,
                'affected_count' => $count
            ]);

            return $this->success([
                'affected_count' => $count
            ], '批量更新状态成功');

        } catch (\Exception $e) {
            $this->logError('批量更新床型类型状态失败', [
                'ids' => $ids,
                'is_active' => $isActive,
                'error' => $e->getMessage()
            ]);
            return $this->error('批量更新状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取床型类型统计信息
     *
     * @return array
     */
    public function getBedTypeStatistics()
    {
        try {
            $statistics = [
                'total_count' => BedType::count(),
                'active_count' => BedType::where('is_active', true)->count(),
                'standard_count' => BedType::where('is_standard', true)->count(),
                'category_stats' => [],
                'size_stats' => [],
                'popular_bed_types' => [],
            ];

            // 按分类统计
            $categories = BedType::getCategories();
            foreach ($categories as $code => $name) {
                $statistics['category_stats'][] = [
                    'category' => $code,
                    'category_name' => $name,
                    'count' => BedType::where('category', $code)->count(),
                    'active_count' => BedType::where('category', $code)->where('is_active', true)->count(),
                ];
            }

            // 按尺寸统计
            $sizes = BedType::getSizes();
            foreach ($sizes as $code => $name) {
                $statistics['size_stats'][] = [
                    'size' => $code,
                    'size_name' => $name,
                    'count' => BedType::where('size', $code)->count(),
                    'active_count' => BedType::where('size', $code)->where('is_active', true)->count(),
                ];
            }

            // 热门床型（按使用次数排序）
            $statistics['popular_bed_types'] = BedType::with('statistics')
                ->whereHas('statistics')
                ->get()
                ->sortByDesc('statistics.usage_count')
                ->take(10)
                ->map(function ($bedType) {
                    return [
                        'id' => $bedType->id,
                        'name' => $bedType->name,
                        'code' => $bedType->code,
                        'usage_count' => $bedType->statistics->usage_count ?? 0,
                        'room_type_count' => $bedType->statistics->room_type_count ?? 0,
                        'hotel_count' => $bedType->statistics->hotel_count ?? 0,
                    ];
                })
                ->values()
                ->toArray();

            return $this->success($statistics);

        } catch (\Exception $e) {
            $this->logError('获取床型类型统计信息失败', [
                'error' => $e->getMessage()
            ]);
            return $this->error('获取统计信息失败: ' . $e->getMessage());
        }
    }
}
