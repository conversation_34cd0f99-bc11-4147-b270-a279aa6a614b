<?php

namespace app\service;

use app\model\RoomInventory;
use app\model\RoomInventoryLog;
use app\model\RoomInventoryTemplate;
use app\model\RoomInventoryAlert;
use app\model\RoomInventoryAlertRecord;
use app\model\Hotel;
use app\model\RoomType;
use support\Db;

/**
 * 房态房量服务类
 * 基于携程房态房量标准的完整业务逻辑
 */
class RoomInventoryService extends BaseService
{
    /**
     * 获取房态房量列表
     *
     * @param array $params
     * @return array
     */
    public function getRoomInventoryList(array $params)
    {
        try {
            $hotelId = $params['hotel_id'];
            $startDate = $params['start_date'];
            $endDate = $params['end_date'];
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;

            $query = RoomInventory::where('hotel_id', $hotelId)
                ->whereBetween('date', [$startDate, $endDate]);

            // 按房型筛选
            if (!empty($params['room_type_id'])) {
                $query->where('room_type_id', $params['room_type_id']);
            }

            // 按状态筛选
            if (isset($params['is_closed'])) {
                $query->where('is_closed', $params['is_closed']);
            }

            if (isset($params['is_stop_sale'])) {
                $query->where('is_stop_sale', $params['is_stop_sale']);
            }

            // 按可售房间数筛选
            if (!empty($params['min_available_rooms'])) {
                $query->where('available_rooms', '>=', $params['min_available_rooms']);
            }

            if (!empty($params['max_available_rooms'])) {
                $query->where('available_rooms', '<=', $params['max_available_rooms']);
            }

            // 关联房型信息
            $query->with(['roomType:id,name,description', 'hotel:id,name']);

            // 排序
            $query->orderBy('date')->orderBy('room_type_id');

            // 分页
            $result = $query->paginate($perPage, ['*'], 'page', $page);

            // 格式化数据
            $items = $result->items();
            foreach ($items as $item) {
                $item->occupancy_rate = $this->calculateOccupancyRate($item);
                $item->availability_status = $this->getAvailabilityStatus($item);
            }

            return $this->success([
                'items' => $items,
                'pagination' => [
                    'current' => $result->currentPage(),
                    'per_page' => $result->perPage(),
                    'total' => $result->total(),
                    'last_page' => $result->lastPage(),
                ]
            ]);

        } catch (\Exception $e) {
            $this->logError('获取房态房量列表失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取房态房量列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取房态房量详情
     *
     * @param array $params
     * @return array
     */
    public function getRoomInventoryDetail(array $params)
    {
        try {
            $hotelId = $params['hotel_id'];
            $date = $params['date'];
            $roomTypeId = $params['room_type_id'] ?? null;

            $query = RoomInventory::where('hotel_id', $hotelId)
                ->where('date', $date);

            if ($roomTypeId) {
                $query->where('room_type_id', $roomTypeId);
            }

            $query->with(['roomType', 'hotel']);

            $inventories = $query->get();

            if ($inventories->isEmpty()) {
                // 如果没有数据，创建默认数据
                $inventories = $this->createDefaultInventoryData($hotelId, $date, $roomTypeId);
            }

            // 计算统计数据
            $statistics = $this->calculateInventoryStatistics($inventories);

            return $this->success([
                'inventories' => $inventories,
                'statistics' => $statistics,
                'date' => $date,
                'hotel_id' => $hotelId
            ]);

        } catch (\Exception $e) {
            $this->logError('获取房态房量详情失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            return $this->error('获取房态房量详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新房态房量
     *
     * @param array $data
     * @return array
     */
    public function batchUpdateRoomInventory(array $data)
    {
        try {
            $hotelId = $data['hotel_id'];
            $startDate = $data['start_date'];
            $endDate = $data['end_date'];
            $updates = $data['updates'];
            $roomTypeIds = $data['room_type_ids'] ?? [];
            $reason = $data['reason'] ?? '批量更新';

            return $this->transaction(function () use ($hotelId, $startDate, $endDate, $updates, $roomTypeIds, $reason) {
                $affectedCount = 0;
                $affectedDates = [];
                $errors = [];

                // 生成日期范围
                $dates = $this->generateDateRange($startDate, $endDate);

                foreach ($dates as $date) {
                    foreach ($updates as $update) {
                        $field = $update['field'];
                        $value = $update['value'];
                        $operation = $update['operation'] ?? 'set'; // set, add, subtract

                        $query = RoomInventory::where('hotel_id', $hotelId)
                            ->where('date', $date);

                        if (!empty($roomTypeIds)) {
                            $query->whereIn('room_type_id', $roomTypeIds);
                        }

                        $inventories = $query->get();

                        foreach ($inventories as $inventory) {
                            try {
                                $oldValue = $inventory->$field;
                                $newValue = $this->calculateNewValue($oldValue, $value, $operation);

                                $inventory->update([
                                    $field => $newValue,
                                    'last_updated_by' => $this->getCurrentUserId(),
                                    'last_updated_reason' => $reason
                                ]);

                                $affectedCount++;
                                $affectedDates[] = $date;

                                // 记录日志
                                RoomInventoryLog::create([
                                    'hotel_id' => $hotelId,
                                    'room_type_id' => $inventory->room_type_id,
                                    'start_date' => $date,
                                    'end_date' => $date,
                                    'action' => 'update',
                                    'field' => $field,
                                    'old_value' => $oldValue,
                                    'new_value' => $newValue,
                                    'affected_dates' => [$date],
                                    'affected_count' => 1,
                                    'reason' => $reason,
                                    'user_id' => $this->getCurrentUserId(),
                                    'user_name' => $this->getCurrentUserName(),
                                    'ip_address' => $this->getClientIp()
                                ]);

                            } catch (\Exception $e) {
                                $errors[] = [
                                    'date' => $date,
                                    'room_type_id' => $inventory->room_type_id,
                                    'field' => $field,
                                    'error' => $e->getMessage()
                                ];
                            }
                        }
                    }
                }

                $this->logInfo('批量更新房态房量完成', [
                    'hotel_id' => $hotelId,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'affected_count' => $affectedCount,
                    'error_count' => count($errors)
                ]);

                return $this->success([
                    'affected_count' => $affectedCount,
                    'affected_dates' => array_unique($affectedDates),
                    'errors' => $errors,
                    'success_count' => $affectedCount,
                    'error_count' => count($errors)
                ], '批量更新完成');
            });

        } catch (\Exception $e) {
            $this->logError('批量更新房态房量失败', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            return $this->error('批量更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量关闭销售
     *
     * @param array $data
     * @return array
     */
    public function batchCloseRoomInventory(array $data)
    {
        $data['updates'] = [
            [
                'field' => 'is_closed',
                'value' => true,
                'operation' => 'set'
            ]
        ];
        $data['reason'] = $data['reason'] ?? '批量关闭销售';

        return $this->batchUpdateRoomInventory($data);
    }

    /**
     * 批量开放销售
     *
     * @param array $data
     * @return array
     */
    public function batchOpenRoomInventory(array $data)
    {
        $data['updates'] = [
            [
                'field' => 'is_closed',
                'value' => false,
                'operation' => 'set'
            ]
        ];
        $data['reason'] = $data['reason'] ?? '批量开放销售';

        return $this->batchUpdateRoomInventory($data);
    }

    /**
     * 计算入住率
     *
     * @param RoomInventory $inventory
     * @return float
     */
    private function calculateOccupancyRate($inventory)
    {
        if ($inventory->total_rooms <= 0) {
            return 0;
        }

        return round(($inventory->sold_rooms / $inventory->total_rooms) * 100, 2);
    }

    /**
     * 获取可售状态
     *
     * @param RoomInventory $inventory
     * @return string
     */
    private function getAvailabilityStatus($inventory)
    {
        if ($inventory->is_closed) {
            return 'closed';
        }

        if ($inventory->is_stop_sale) {
            return 'stop_sale';
        }

        if ($inventory->available_rooms <= 0) {
            return 'sold_out';
        }

        if ($inventory->available_rooms <= 5) {
            return 'low_inventory';
        }

        return 'available';
    }

    /**
     * 创建默认库存数据
     *
     * @param int $hotelId
     * @param string $date
     * @param int|null $roomTypeId
     * @return \Illuminate\Support\Collection
     */
    private function createDefaultInventoryData($hotelId, $date, $roomTypeId = null)
    {
        $query = RoomType::where('hotel_id', $hotelId);

        if ($roomTypeId) {
            $query->where('id', $roomTypeId);
        }

        $roomTypes = $query->get();
        $inventories = collect();

        foreach ($roomTypes as $roomType) {
            $inventory = RoomInventory::create([
                'hotel_id' => $hotelId,
                'room_type_id' => $roomType->id,
                'date' => $date,
                'total_rooms' => $roomType->total_rooms ?? 10,
                'available_rooms' => $roomType->total_rooms ?? 10,
                'sold_rooms' => 0,
                'blocked_rooms' => 0,
                'maintenance_rooms' => 0,
                'overbooking_rooms' => 0,
                'is_closed' => false,
                'is_stop_sale' => false,
                'currency' => 'CNY',
                'booking_count' => 0,
                'checkin_count' => 0,
                'checkout_count' => 0,
                'cancellation_count' => 0,
            ]);

            $inventory->load('roomType', 'hotel');
            $inventories->push($inventory);
        }

        return $inventories;
    }

    /**
     * 计算库存统计数据
     *
     * @param \Illuminate\Support\Collection $inventories
     * @return array
     */
    private function calculateInventoryStatistics($inventories)
    {
        return [
            'total_rooms' => $inventories->sum('total_rooms'),
            'available_rooms' => $inventories->sum('available_rooms'),
            'sold_rooms' => $inventories->sum('sold_rooms'),
            'blocked_rooms' => $inventories->sum('blocked_rooms'),
            'maintenance_rooms' => $inventories->sum('maintenance_rooms'),
            'overbooking_rooms' => $inventories->sum('overbooking_rooms'),
            'occupancy_rate' => $inventories->avg(function ($item) {
                return $this->calculateOccupancyRate($item);
            }),
            'closed_count' => $inventories->where('is_closed', true)->count(),
            'stop_sale_count' => $inventories->where('is_stop_sale', true)->count(),
            'room_type_count' => $inventories->count(),
        ];
    }

    /**
     * 生成日期范围
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    private function generateDateRange($startDate, $endDate)
    {
        $dates = [];
        $current = new \DateTime($startDate);
        $end = new \DateTime($endDate);

        while ($current <= $end) {
            $dates[] = $current->format('Y-m-d');
            $current->add(new \DateInterval('P1D'));
        }

        return $dates;
    }

    /**
     * 计算新值
     *
     * @param mixed $oldValue
     * @param mixed $value
     * @param string $operation
     * @return mixed
     */
    private function calculateNewValue($oldValue, $value, $operation)
    {
        switch ($operation) {
            case 'add':
                return $oldValue + $value;
            case 'subtract':
                return max(0, $oldValue - $value);
            case 'set':
            default:
                return $value;
        }
    }

    /**
     * 获取当前用户ID
     *
     * @return int|null
     */
    private function getCurrentUserId()
    {
        // 这里应该从认证系统获取当前用户ID
        return 1; // 临时返回固定值
    }

    /**
     * 获取当前用户名
     *
     * @return string
     */
    private function getCurrentUserName()
    {
        // 这里应该从认证系统获取当前用户名
        return '管理员'; // 临时返回固定值
    }

    /**
     * 获取客户端IP
     *
     * @return string
     */
    private function getClientIp()
    {
        return request()->getRealIp() ?? '127.0.0.1';
    }
}
