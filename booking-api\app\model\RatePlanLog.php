<?php

namespace app\model;

use support\Model;

/**
 * 房价计划操作日志模型
 */
class RatePlanLog extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'rate_plan_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'rate_plan_id',
        'hotel_id',
        'action',
        'action_text',
        'user_id',
        'user_name',
        'old_data',
        'new_data',
        'details',
        'ip_address',
        'user_agent',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'rate_plan_id' => 'integer',
        'hotel_id' => 'integer',
        'user_id' => 'integer',
        'old_data' => 'array',
        'new_data' => 'array',
        'created_at' => 'datetime',
    ];

    /**
     * 获取关联的房价计划
     */
    public function ratePlan()
    {
        return $this->belongsTo(RatePlan::class);
    }

    /**
     * 获取关联的酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 作用域：按房价计划筛选
     */
    public function scopeByRatePlan($query, $ratePlanId)
    {
        return $query->where('rate_plan_id', $ratePlanId);
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：按操作类型筛选
     */
    public function scopeByAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 创建操作日志
     *
     * @param array $data
     * @return static
     */
    public static function createLog(array $data)
    {
        // 自动添加IP地址和用户代理
        if (!isset($data['ip_address'])) {
            $data['ip_address'] = request()->getRealIp();
        }
        
        if (!isset($data['user_agent'])) {
            $data['user_agent'] = request()->header('User-Agent');
        }

        return static::create($data);
    }

    /**
     * 记录房价计划创建日志
     *
     * @param RatePlan $ratePlan
     * @param array $userData
     * @return static
     */
    public static function logCreate(RatePlan $ratePlan, array $userData = [])
    {
        return static::createLog([
            'rate_plan_id' => $ratePlan->id,
            'hotel_id' => $ratePlan->hotel_id,
            'action' => 'create',
            'action_text' => '创建房价计划',
            'user_id' => $userData['user_id'] ?? null,
            'user_name' => $userData['user_name'] ?? '系统',
            'new_data' => $ratePlan->toArray(),
            'details' => "创建了房价计划: {$ratePlan->name}",
        ]);
    }

    /**
     * 记录房价计划更新日志
     *
     * @param RatePlan $ratePlan
     * @param array $oldData
     * @param array $userData
     * @return static
     */
    public static function logUpdate(RatePlan $ratePlan, array $oldData, array $userData = [])
    {
        return static::createLog([
            'rate_plan_id' => $ratePlan->id,
            'hotel_id' => $ratePlan->hotel_id,
            'action' => 'update',
            'action_text' => '更新房价计划',
            'user_id' => $userData['user_id'] ?? null,
            'user_name' => $userData['user_name'] ?? '系统',
            'old_data' => $oldData,
            'new_data' => $ratePlan->toArray(),
            'details' => "更新了房价计划: {$ratePlan->name}",
        ]);
    }

    /**
     * 记录房价计划发布日志
     *
     * @param RatePlan $ratePlan
     * @param array $userData
     * @return static
     */
    public static function logPublish(RatePlan $ratePlan, array $userData = [])
    {
        return static::createLog([
            'rate_plan_id' => $ratePlan->id,
            'hotel_id' => $ratePlan->hotel_id,
            'action' => 'publish',
            'action_text' => '发布房价计划',
            'user_id' => $userData['user_id'] ?? null,
            'user_name' => $userData['user_name'] ?? '系统',
            'new_data' => ['is_published' => true, 'published_at' => $ratePlan->published_at],
            'details' => "发布了房价计划: {$ratePlan->name}",
        ]);
    }

    /**
     * 记录房价计划取消发布日志
     *
     * @param RatePlan $ratePlan
     * @param array $userData
     * @return static
     */
    public static function logUnpublish(RatePlan $ratePlan, array $userData = [])
    {
        return static::createLog([
            'rate_plan_id' => $ratePlan->id,
            'hotel_id' => $ratePlan->hotel_id,
            'action' => 'unpublish',
            'action_text' => '取消发布房价计划',
            'user_id' => $userData['user_id'] ?? null,
            'user_name' => $userData['user_name'] ?? '系统',
            'new_data' => ['is_published' => false, 'unpublished_at' => $ratePlan->unpublished_at],
            'details' => "取消发布了房价计划: {$ratePlan->name}",
        ]);
    }

    /**
     * 记录房价计划激活日志
     *
     * @param RatePlan $ratePlan
     * @param array $userData
     * @return static
     */
    public static function logActivate(RatePlan $ratePlan, array $userData = [])
    {
        return static::createLog([
            'rate_plan_id' => $ratePlan->id,
            'hotel_id' => $ratePlan->hotel_id,
            'action' => 'activate',
            'action_text' => '激活房价计划',
            'user_id' => $userData['user_id'] ?? null,
            'user_name' => $userData['user_name'] ?? '系统',
            'new_data' => ['is_active' => true, 'activated_at' => $ratePlan->activated_at],
            'details' => "激活了房价计划: {$ratePlan->name}",
        ]);
    }

    /**
     * 记录房价计划停用日志
     *
     * @param RatePlan $ratePlan
     * @param array $userData
     * @return static
     */
    public static function logDeactivate(RatePlan $ratePlan, array $userData = [])
    {
        return static::createLog([
            'rate_plan_id' => $ratePlan->id,
            'hotel_id' => $ratePlan->hotel_id,
            'action' => 'deactivate',
            'action_text' => '停用房价计划',
            'user_id' => $userData['user_id'] ?? null,
            'user_name' => $userData['user_name'] ?? '系统',
            'new_data' => ['is_active' => false, 'deactivated_at' => $ratePlan->deactivated_at],
            'details' => "停用了房价计划: {$ratePlan->name}",
        ]);
    }

    /**
     * 记录房价计划删除日志
     *
     * @param RatePlan $ratePlan
     * @param array $userData
     * @return static
     */
    public static function logDelete(RatePlan $ratePlan, array $userData = [])
    {
        return static::createLog([
            'rate_plan_id' => $ratePlan->id,
            'hotel_id' => $ratePlan->hotel_id,
            'action' => 'delete',
            'action_text' => '删除房价计划',
            'user_id' => $userData['user_id'] ?? null,
            'user_name' => $userData['user_name'] ?? '系统',
            'old_data' => $ratePlan->toArray(),
            'details' => "删除了房价计划: {$ratePlan->name}",
        ]);
    }

    /**
     * 获取操作类型文本
     *
     * @return string
     */
    public function getActionTextAttribute()
    {
        $actionTexts = [
            'create' => '创建',
            'update' => '更新',
            'publish' => '发布',
            'unpublish' => '取消发布',
            'activate' => '激活',
            'deactivate' => '停用',
            'delete' => '删除',
        ];

        return $actionTexts[$this->action] ?? $this->action;
    }

    /**
     * 获取操作颜色
     *
     * @return string
     */
    public function getActionColor()
    {
        $actionColors = [
            'create' => 'green',
            'update' => 'blue',
            'publish' => 'orange',
            'unpublish' => 'gray',
            'activate' => 'green',
            'deactivate' => 'red',
            'delete' => 'red',
        ];

        return $actionColors[$this->action] ?? 'default';
    }
}
