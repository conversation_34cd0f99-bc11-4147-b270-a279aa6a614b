<?php

namespace app\controller;

use app\model\Region;
use support\Request;
use support\Response;

/**
 * 地区管理控制器
 */
class RegionController extends BaseController
{
    /**
     * 获取地区列表
     */
    public function index(Request $request): Response
    {
        try {
            $page = $request->get('page', 1);
            $pageSize = $request->get('page_size', 20);
            $level = $request->get('level');
            $parentId = $request->get('parent_id');
            $status = $request->get('status');
            $keyword = $request->get('keyword');

            $query = Region::query();

            // 按层级筛选
            if ($level !== null) {
                $query->where('level', $level);
            }

            // 按父级ID筛选
            if ($parentId !== null) {
                $query->where('parent_id', $parentId);
            }

            // 按状态筛选
            if ($status) {
                $query->where('status', $status);
            }

            // 关键词搜索
            if ($keyword) {
                $query->where(function($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%")
                      ->orWhere('code', 'like', "%{$keyword}%");
                });
            }

            $query->orderBy('sort_order')->orderBy('id');

            $total = $query->count();
            $regions = $query->offset(($page - 1) * $pageSize)
                           ->limit($pageSize)
                           ->get()
                           ->toArray();

            return $this->success([
                'data' => $regions,
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize,
            ]);

        } catch (\Exception $e) {
            return $this->error('获取地区列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取地区树形结构
     */
    public function tree(Request $request): Response
    {
        try {
            $parentId = $request->get('parent_id', null);
            $maxLevel = $request->get('max_level', 3);
            $status = $request->get('status', 'active');

            $query = Region::query();

            if ($status) {
                $query->where('status', $status);
            }

            if ($maxLevel) {
                $query->where('level', '<=', $maxLevel);
            }

            $regions = $query->orderBy('sort_order')->orderBy('id')->get();

            $tree = $this->buildTree($regions->toArray(), $parentId);

            return $this->success($tree);

        } catch (\Exception $e) {
            return $this->error('获取地区树形结构失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取地区详情
     */
    public function show(Request $request, $id): Response
    {
        try {
            $region = Region::find($id);

            if (!$region) {
                return $this->error('地区不存在', 404);
            }

            return $this->success($region->toArray());

        } catch (\Exception $e) {
            return $this->error('获取地区详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建地区
     */
    public function store(Request $request): Response
    {
        try {
            $data = $request->post();

            // 验证必填字段
            if (empty($data['code']) || empty($data['name'])) {
                return $this->error('地区代码和名称不能为空', 400);
            }

            // 检查代码是否已存在
            if (Region::where('code', $data['code'])->exists()) {
                return $this->error('地区代码已存在', 400);
            }

            // 设置默认值
            $data['parent_id'] = $data['parent_id'] ?? null;
            $data['level'] = $data['level'] ?? 1;
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['status'] = $data['status'] ?? 'active';

            $region = Region::create($data);

            return $this->success($region->toArray(), '地区创建成功');

        } catch (\Exception $e) {
            return $this->error('创建地区失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新地区
     */
    public function update(Request $request, $id): Response
    {
        try {
            $region = Region::find($id);

            if (!$region) {
                return $this->error('地区不存在', 404);
            }

            $data = $request->post();

            // 检查代码是否已存在
            if (!empty($data['code']) && Region::where('code', $data['code'])->where('id', '!=', $id)->exists()) {
                return $this->error('地区代码已存在', 400);
            }

            $region->update($data);

            return $this->success($region->toArray(), '地区更新成功');

        } catch (\Exception $e) {
            return $this->error('更新地区失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除地区
     */
    public function destroy(Request $request, $id): Response
    {
        try {
            $region = Region::find($id);

            if (!$region) {
                return $this->error('地区不存在', 404);
            }

            // 检查是否有子地区
            if (Region::where('parent_id', $id)->exists()) {
                return $this->error('该地区下还有子地区，无法删除', 400);
            }

            // 检查是否有关联的酒店
            if ($region->hotels()->exists()) {
                return $this->error('该地区下还有酒店，无法删除', 400);
            }

            $region->delete();

            return $this->success(null, '地区删除成功');

        } catch (\Exception $e) {
            return $this->error('删除地区失败: ' . $e->getMessage());
        }
    }

    /**
     * 构建树形结构
     */
    private function buildTree($regions, $parentId = null)
    {
        $tree = [];

        foreach ($regions as $region) {
            if ($region['parent_id'] == $parentId) {
                $children = $this->buildTree($regions, $region['id']);
                if (!empty($children)) {
                    $region['children'] = $children;
                }
                $tree[] = $region;
            }
        }

        return $tree;
    }

    /**
     * 获取省份列表
     */
    public function provinces(Request $request): Response
    {
        try {
            $provinces = Region::where('level', Region::LEVEL_PROVINCE)
                             ->where('status', 'active')
                             ->orderBy('sort_order')
                             ->orderBy('id')
                             ->get()
                             ->toArray();

            return $this->success($provinces);

        } catch (\Exception $e) {
            return $this->error('获取省份列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取城市列表
     */
    public function cities(Request $request): Response
    {
        try {
            $provinceId = $request->get('province_id');

            $query = Region::where('level', Region::LEVEL_CITY)
                          ->where('status', 'active');

            if ($provinceId) {
                $query->where('parent_id', $provinceId);
            }

            $cities = $query->orderBy('sort_order')
                           ->orderBy('id')
                           ->get()
                           ->toArray();

            return $this->success($cities);

        } catch (\Exception $e) {
            return $this->error('获取城市列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取区县列表
     */
    public function districts(Request $request): Response
    {
        try {
            $cityId = $request->get('city_id');

            $query = Region::where('level', Region::LEVEL_DISTRICT)
                          ->where('status', 'active');

            if ($cityId) {
                $query->where('parent_id', $cityId);
            }

            $districts = $query->orderBy('sort_order')
                              ->orderBy('id')
                              ->get()
                              ->toArray();

            return $this->success($districts);

        } catch (\Exception $e) {
            return $this->error('获取区县列表失败: ' . $e->getMessage());
        }
    }
}
