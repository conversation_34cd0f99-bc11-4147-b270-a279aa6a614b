<?php

namespace app\service;

use support\Db;

/**
 * 酒店房东信息服务类
 */
class HotelHostService extends BaseService
{
    public function getHotelHostInfo(int $hotelId): ?array
    {
        try {
            $hostInfo = Db::table('hotel_host_info')
                ->where('hotel_id', $hotelId)
                ->where('status', 'active')
                ->first();

            return $hostInfo ? (array)$hostInfo : null;
        } catch (\Exception $e) {
            $this->logError('获取房东信息失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    public function updateHotelHostInfo(int $hotelId, array $hostData): bool
    {
        try {
            Db::beginTransaction();

            $data = [
                'info_id' => $hostData['info_id'] ?? null,
                'host_name' => $hostData['host_name'] ?? null,
                'host_introduction' => $hostData['host_introduction'] ?? null,
                'host_photo' => $hostData['host_photo'] ?? null,
                'host_gender' => $hostData['host_gender'] ?? null,
                'birthday' => $hostData['birthday'] ?? null,
                'province_id' => $hostData['province_id'] ?? null,
                'city_id' => $hostData['city_id'] ?? null,
                'auth_status' => $hostData['auth_status'] ?? false,
                'is_del' => $hostData['is_del'] ?? false,
                'is_accept_dishonesty' => $hostData['is_accept_dishonesty'] ?? true,
                'career' => $hostData['career'] ?? null,
                'hobby' => $hostData['hobby'] ?? null,
                'background_pic' => $hostData['background_pic'] ?? null,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $existingHost = Db::table('hotel_host_info')
                ->where('hotel_id', $hotelId)
                ->first();

            if ($existingHost) {
                $result = Db::table('hotel_host_info')
                    ->where('hotel_id', $hotelId)
                    ->update($data);
            } else {
                $data['hotel_id'] = $hotelId;
                $data['status'] = 'active';
                $data['created_at'] = date('Y-m-d H:i:s');
                
                $result = Db::table('hotel_host_info')->insert($data);
            }

            Db::commit();
            return $result > 0;
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('更新房东信息失败', [
                'hotel_id' => $hotelId,
                'host_data' => $hostData,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
