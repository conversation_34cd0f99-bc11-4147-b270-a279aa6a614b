<?php
declare (strict_types=1);

namespace base\base;

use ArrayAccess;
use ArrayIterator;
use Countable;
use IteratorAggregate;
use JsonSerializable;
use Serializable;
use think\helper\Arr;

/**
 * 数据集管理类
 * Most of the methods in this file come from topthink/think-helper,
 * thanks provide such a useful class.
 */
class DS implements ArrayAccess, Countable, IteratorAggregate, JsonSerializable, Serializable
{
    /**
     * The collection data.
     *
     * @var array
     */
    protected array $items = [];

    /**
     * set data.
     *
     * @param mixed $items
     */
    public function __construct(mixed $items = [])
    {
        $this->items = $this->convertToArray($items);
    }

    /**
     * To Json.
     */
    public function __toString(): string
    {
        return $this->toJson();
    }

    /**
     * Get a data by key.
     *
     * @param $name
     * @return mixed
     */
    public function __get($name): mixed
    {
        return $this->get($name);
    }

    /**
     * Assigns a value to the specified data.
     *
     * @param $name
     * @param mixed $value
     */
    public function __set($name, mixed $value)
    {
        $this->set($name, $value);
    }

    /**
     * Whether or not a data exists by key.
     * @param $name
     * @return bool
     */
    public function __isset($name): bool
    {
        return $this->has($name);
    }

    /**
     * Unsets a data by key.
     * @param $name
     */
    public function __unset($name)
    {
        $this->forget($name);
    }

    /**
     * 是否为空
     * @access public
     * @return bool
     */
    public function isEmpty(): bool
    {
        return empty($this->items);
    }

    /**
     * Return all items.
     */
    public function all(): array
    {
        return $this->items;
    }

    /**
     * Return specific items.
     * @param array $keys
     * @return array
     */
    public function only(array $keys): array
    {
        $return = [];

        foreach ($keys as $key) {
            $value = $this->get($key);

            if (!is_null($value)) {
                $return[$key] = $value;
            }
        }

        return $return;
    }

    /**
     * Get all items except for those with the specified keys.
     *
     * @param mixed $keys
     *
     * @return static
     */
    public function except(mixed $keys): DS
    {
        $keys = is_array($keys) ? $keys : func_get_args();

        return new static(Arr::except($this->items, $keys));
    }

    /**
     * Merge data.
     *
     * @param array|self $items
     *
     * @return DS
     */
    public function merge(array|DS $items): DS
    {
        foreach ($items as $key => $value) {
            $this->set($key, $value);
        }

        return $this;
    }

    /**
     * 确定指定的元素是否存在
     * @param string $key
     * @return bool
     */
    public function has(string $key): bool
    {
        return !is_null(Arr::get($this->items, $key));
    }

    /**
     * 将数组的内部指针设置为数组的第一个元素
     *
     * @return mixed
     */
    public function first(): mixed
    {
        return reset($this->items);
    }

    /**
     *  将数组的内部指针设置为数组的最后一个元素
     *
     * @return mixed
     */
    public function last(): mixed
    {
        $end = end($this->items);

        reset($this->items);

        return $end;
    }

    /**
     * 如果数组中不存在元素，则使用“点”表示法添加该元素。.
     *
     * @param string $key
     * @param mixed $value
     */
    public function add(string $key, mixed $value): void
    {
        Arr::add($this->items, $key, $value);
    }

    /**
     * 设置值。
     *
     * @param string $key
     * @param mixed $value
     *
     * @return void
     */
    public function set(string $key, mixed $value): void
    {
        Arr::set($this->items, $key, $value);
    }

    /**
     * Retrieve item from Collection.
     *
     * @param string|null $key
     * @param mixed|null $default
     *
     * @return mixed
     */
    public function get(?string $key = null, mixed $default = null): mixed
    {
        return Arr::get($this->items, $key, $default);
    }

    /**
     * 从集合中移除键
     *
     * @param array|string $keys
     *
     * @return void
     */
    public function forget(array|string $keys): void
    {
        Arr::forget($this->items, $keys);
    }


    /**
     * 获取数组
     * @return array
     */
    public function toArray(): array
    {
        return $this->all();
    }

    /**
     * 获取JSON
     *
     * @param int $option
     *
     * @return string
     * @throws \JsonException
     */
    public function toJson(int $option = JSON_UNESCAPED_UNICODE): string
    {
        return json_encode($this->all(), JSON_THROW_ON_ERROR | $option);
    }

    public function jsonSerialize(): array
    {
        return $this->items;
    }

    /**
     * (PHP 5 &gt;= 5.1.0)<br/>
     * String representation of object.
     *
     * @see http://php.net/manual/en/serializable.serialize.php
     *
     * @return string the string representation of the object or null
     */
    public function serialize(): string
    {
        return serialize($this->items);
    }

    public function getIterator(): ArrayIterator
    {
        return new ArrayIterator($this->items);
    }

    public function count(): int
    {
        return count($this->items);
    }

    public function unserialize($serialized): array
    {
        return $this->items = (array)unserialize($serialized,['allowed_classes' => true]);
    }

    public function offsetExists($offset): bool
    {
        return $this->has($offset);
    }

    public function offsetUnset($offset): void
    {
        if ($this->offsetExists($offset)) {
            $this->forget($offset);
        }
    }

    public function offsetGet(mixed $offset): mixed
    {
        return $this->offsetExists($offset) ? $this->get($offset) : null;
    }

    public function offsetSet($offset, $value): void
    {
        $this->set($offset, $value);
    }


    /**
     * 转换成数组
     *
     * @access public
     * @param mixed $items 数据
     * @return array
     */
    protected function convertToArray(mixed $items): array
    {
        if ($items instanceof self) {
            return $items->all();
        }

        return (array)$items;
    }

    public function __serialize(): array
    {
        return $this->items;
    }

    public function __unserialize(array $data): void
    {
        $this->items = $data;
    }
}
