<?php

namespace app\controller;

use app\service\OtaChannelService;
use support\Request;
use support\Response;

/**
 * OTA渠道控制器
 */
class OtaChannelController extends BaseController
{
    /**
     * OTA渠道服务
     *
     * @var OtaChannelService
     */
    protected $otaChannelService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->otaChannelService = new OtaChannelService();
    }

    /**
     * 获取OTA渠道列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $params = $request->all();
            $result = $this->otaChannelService->getOtaChannels($params);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error('获取OTA渠道列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取OTA渠道详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, int $id): Response
    {
        try {
            $result = $this->otaChannelService->getOtaChannelDetail($id);

            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }

            return $this->success($result['data']);
        } catch (\Exception $e) {
            return $this->error('获取OTA渠道详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建OTA渠道
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request): Response
    {
        try {
            $data = $request->all();
            $result = $this->otaChannelService->createOtaChannel($data);

            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }

            return $this->success($result['data'], '创建OTA渠道成功');
        } catch (\Exception $e) {
            return $this->error('创建OTA渠道失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新OTA渠道
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, int $id): Response
    {
        try {
            $data = $request->all();
            $result = $this->otaChannelService->updateOtaChannel($id, $data);

            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }

            return $this->success($result['data'], '更新OTA渠道成功');
        } catch (\Exception $e) {
            return $this->error('更新OTA渠道失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除OTA渠道
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, int $id): Response
    {
        try {
            $result = $this->otaChannelService->deleteOtaChannel($id);

            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }

            return $this->success(null, '删除OTA渠道成功');
        } catch (\Exception $e) {
            return $this->error('删除OTA渠道失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作OTA渠道
     *
     * @param Request $request
     * @return Response
     */
    public function batchAction(Request $request): Response
    {
        try {
            $data = $request->all();
            $result = $this->otaChannelService->batchOtaChannels($data);

            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }

            return $this->success($result['data'], '批量操作成功');
        } catch (\Exception $e) {
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 启用/禁用OTA渠道
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function toggleStatus(Request $request, int $id): Response
    {
        try {
            $result = $this->otaChannelService->toggleOtaChannelStatus($id);
            
            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }
            
            return $this->success($result['data'], '状态切换成功');
        } catch (\Exception $e) {
            return $this->error('状态切换失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试OTA渠道连接
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function testConnection(Request $request, int $id): Response
    {
        try {
            $result = $this->otaChannelService->testOtaChannelConnection($id);
            
            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }
            
            return $this->success($result['data'], '连接测试完成');
        } catch (\Exception $e) {
            return $this->error('连接测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 同步OTA渠道数据
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function syncData(Request $request, int $id): Response
    {
        try {
            $result = $this->otaChannelService->syncOtaChannelData($id);
            
            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }
            
            return $this->success($result['data'], '数据同步完成');
        } catch (\Exception $e) {
            return $this->error('数据同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取OTA渠道统计信息
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function statistics(Request $request, int $id): Response
    {
        try {
            $params = $request->all();
            $result = $this->otaChannelService->getOtaChannelStatistics($id, $params);
            
            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }
            
            return $this->success($result['data']);
        } catch (\Exception $e) {
            return $this->error('获取统计信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出OTA渠道数据
     *
     * @param Request $request
     * @return Response
     */
    public function export(Request $request): Response
    {
        try {
            $params = $request->all();
            $result = $this->otaChannelService->exportOtaChannels($params);
            
            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }
            
            return $this->success($result['data'], '导出成功');
        } catch (\Exception $e) {
            return $this->error('导出失败: ' . $e->getMessage());
        }
    }

    /**
     * 导入OTA渠道数据
     *
     * @param Request $request
     * @return Response
     */
    public function import(Request $request): Response
    {
        try {
            $file = $request->file('file');
            if (!$file) {
                return $this->error('请选择要导入的文件');
            }
            
            $result = $this->otaChannelService->importOtaChannels($file);
            
            if ($result['code'] !== 0) {
                return $this->error($result['message'], $result['code']);
            }
            
            return $this->success($result['data'], '导入成功');
        } catch (\Exception $e) {
            return $this->error('导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取OTA渠道选项（用于下拉选择）
     *
     * @param Request $request
     * @return Response
     */
    public function options(Request $request): Response
    {
        try {
            $result = $this->otaChannelService->getOtaChannelOptions();
            
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error('获取OTA渠道选项失败: ' . $e->getMessage());
        }
    }
}
