# 预订系统后端环境配置

# 应用基本配置
APP_DEBUG=true
APP_URL=http://localhost:8787

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=booking_system
DB_USERNAME=root
DB_PASSWORD=

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# JWT配置 - 与IAM系统保持一致
JWT_SECRET=your-jwt-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE=3600

# SSO配置
SSO_BASE_URL=http://localhost:5667
SSO_CLIENT_ID=booking
SSO_CLIENT_SECRET=

# IAM系统配置
IAM_ADMIN_URL=http://localhost:5173

# 日志配置
LOG_LEVEL=debug
LOG_CHANNEL=daily

# 缓存配置
CACHE_DRIVER=redis
CACHE_PREFIX=booking_

# 会话配置
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# 队列配置
QUEUE_CONNECTION=redis
QUEUE_PREFIX=booking_queue_

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="预订系统"

# 文件存储配置
FILESYSTEM_DISK=local
UPLOAD_MAX_SIZE=10240

# API配置
API_RATE_LIMIT=60
API_RATE_LIMIT_WINDOW=60

# 安全配置
CORS_ALLOWED_ORIGINS=http://localhost:5668,http://localhost:3000
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With

# 功能开关
ENABLE_API_DOCS=true
ENABLE_METRICS=true
ENABLE_HEALTH_CHECK=true
