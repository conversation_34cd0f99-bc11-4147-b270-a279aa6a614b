<?php

namespace app\model;

use support\Model;

/**
 * 酒店餐食政策模型
 */
class HotelPoliciesMealPolicy extends Model
{
    /**
     * 表名
     */
    protected $table = 'hotel_policies_meal_policies';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'hotel_id',
        'breakfast_included',
        'breakfast_type',
        'breakfast_time_from',
        'breakfast_time_to',
        'breakfast_location',
        'lunch_available',
        'lunch_time_from',
        'lunch_time_to',
        'dinner_available',
        'dinner_time_from',
        'dinner_time_to',
        'room_service_available',
        'room_service_hours',
        'dietary_restrictions_supported',
        'special_meal_requests',
        'meal_notes',
        'status',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'hotel_id' => 'integer',
        'breakfast_included' => 'boolean',
        'lunch_available' => 'boolean',
        'dinner_available' => 'boolean',
        'room_service_available' => 'boolean',
        'special_meal_requests' => 'boolean',
        'dietary_restrictions_supported' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(): array
    {
        return [
            'hotel_id' => 'required|integer|min:1',
            'breakfast_included' => 'boolean',
            'breakfast_type' => 'nullable|string|max:50',
            'breakfast_time_from' => 'nullable|date_format:H:i:s',
            'breakfast_time_to' => 'nullable|date_format:H:i:s',
            'breakfast_location' => 'nullable|string|max:200',
            'lunch_available' => 'boolean',
            'lunch_time_from' => 'nullable|date_format:H:i:s',
            'lunch_time_to' => 'nullable|date_format:H:i:s',
            'dinner_available' => 'boolean',
            'dinner_time_from' => 'nullable|date_format:H:i:s',
            'dinner_time_to' => 'nullable|date_format:H:i:s',
            'room_service_available' => 'boolean',
            'room_service_hours' => 'nullable|string|max:100',
            'special_meal_requests' => 'boolean',
            'meal_notes' => 'nullable|string',
            'status' => 'in:active,inactive',
        ];
    }

    /**
     * 关联酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class, 'hotel_id', 'id');
    }

    /**
     * 作用域：启用的政策
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 获取政策类型
     */
    public function getPolicyType(): string
    {
        return 'meal';
    }

    /**
     * 获取政策显示名称
     */
    public function getPolicyDisplayName(): string
    {
        return '餐食政策';
    }

    /**
     * 获取政策英文名称
     */
    public function getPolicyEnglishName(): string
    {
        return 'Meal Policy';
    }

    /**
     * 格式化早餐时间显示
     */
    public function getFormattedBreakfastTime(): string
    {
        if (!$this->breakfast_time_from || !$this->breakfast_time_to) {
            return '-';
        }
        return date('H:i', strtotime($this->breakfast_time_from)) . ' - ' . date('H:i', strtotime($this->breakfast_time_to));
    }

    /**
     * 格式化午餐时间显示
     */
    public function getFormattedLunchTime(): string
    {
        if (!$this->lunch_time_from || !$this->lunch_time_to) {
            return '-';
        }
        return date('H:i', strtotime($this->lunch_time_from)) . ' - ' . date('H:i', strtotime($this->lunch_time_to));
    }

    /**
     * 格式化晚餐时间显示
     */
    public function getFormattedDinnerTime(): string
    {
        if (!$this->dinner_time_from || !$this->dinner_time_to) {
            return '-';
        }
        return date('H:i', strtotime($this->dinner_time_from)) . ' - ' . date('H:i', strtotime($this->dinner_time_to));
    }

    /**
     * 创建默认政策
     */
    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId,
            'breakfast_included' => false,
            'breakfast_type' => 'buffet',
            'breakfast_time_from' => '07:00:00',
            'breakfast_time_to' => '10:00:00',
            'lunch_available' => false,
            'dinner_available' => false,
            'room_service_available' => false,
            'special_meal_requests' => false,
            'status' => 'active',
        ]);
    }
}
