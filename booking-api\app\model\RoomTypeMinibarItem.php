<?php

namespace app\model;

/**
 * 房型迷你吧物品模型
 * 对应数据库表：room_type_minibar_items
 */
class RoomTypeMinibarItem extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_type_minibar_items';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'room_type_id',
        'item_name',
        'item_description',
        'item_category',
        'quantity',
        'price',
        'is_complimentary',
        'is_refillable',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'quantity' => 'integer',
        'price' => 'decimal:2',
        'is_complimentary' => 'boolean',
        'is_refillable' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * 关联房型
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type_id');
    }

    /**
     * 获取饮料
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBeverage($query)
    {
        return $query->where('item_category', 'beverage');
    }

    /**
     * 获取零食
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSnack($query)
    {
        return $query->where('item_category', 'snack');
    }

    /**
     * 获取酒类
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAlcohol($query)
    {
        return $query->where('item_category', 'alcohol');
    }

    /**
     * 获取其他物品
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOther($query)
    {
        return $query->where('item_category', 'other');
    }

    /**
     * 获取免费物品
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeComplimentary($query)
    {
        return $query->where('is_complimentary', true);
    }

    /**
     * 获取可补充物品
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRefillable($query)
    {
        return $query->where('is_refillable', true);
    }

    /**
     * 按排序排序
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取物品分类名称
     *
     * @return string
     */
    public function getItemCategoryNameAttribute()
    {
        $categories = [
            'beverage' => '饮料',
            'snack' => '零食',
            'alcohol' => '酒类',
            'other' => '其他',
        ];

        return $categories[$this->item_category] ?? $this->item_category;
    }

    /**
     * 获取价格信息
     *
     * @return string
     */
    public function getPriceInfoAttribute()
    {
        if ($this->is_complimentary) {
            return '免费';
        }
        
        if ($this->price) {
            return "{$this->price}元";
        }
        
        return '按标准收费';
    }

    /**
     * 获取物品完整信息
     *
     * @return array
     */
    public function getItemInfoAttribute()
    {
        return [
            'id' => $this->id,
            'item_name' => $this->item_name,
            'item_description' => $this->item_description,
            'item_category' => $this->item_category,
            'item_category_name' => $this->item_category_name,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'price_info' => $this->price_info,
            'is_complimentary' => $this->is_complimentary,
            'is_refillable' => $this->is_refillable,
        ];
    }

    /**
     * 验证物品
     *
     * @param array $data
     * @return array
     */
    public static function validateItem($data)
    {
        $rules = [
            'room_type_id' => 'required|integer|exists:room_types,id',
            'item_name' => 'required|string|max:100',
            'item_description' => 'nullable|string',
            'item_category' => 'nullable|in:beverage,snack,alcohol,other',
            'quantity' => 'required|integer|min:1',
            'price' => 'nullable|numeric|min:0',
            'is_complimentary' => 'boolean',
            'is_refillable' => 'boolean',
            'sort_order' => 'integer|min:0',
        ];

        $messages = [
            'room_type_id.required' => '房型ID不能为空',
            'room_type_id.exists' => '房型不存在',
            'item_name.required' => '物品名称不能为空',
            'item_name.max' => '物品名称不能超过100个字符',
            'item_category.in' => '物品分类无效',
            'quantity.required' => '数量不能为空',
            'quantity.min' => '数量必须大于0',
            'price.min' => '价格不能为负数',
            'sort_order.min' => '排序不能为负数',
        ];

        return [
            'rules' => $rules,
            'messages' => $messages,
        ];
    }

    /**
     * 创建物品
     *
     * @param array $data
     * @return static
     */
    public static function createItem($data)
    {
        $validation = self::validateItem($data);
        
        // 这里应该使用验证器，暂时简化处理
        $item = new self($data);
        $item->save();
        
        return $item;
    }

    /**
     * 更新物品
     *
     * @param array $data
     * @return bool
     */
    public function updateItem($data)
    {
        $validation = self::validateItem($data);
        
        // 这里应该使用验证器，暂时简化处理
        return $this->update($data);
    }

    /**
     * 获取房型的所有迷你吧物品
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的指定分类物品
     *
     * @param int $roomTypeId
     * @param string $itemCategory
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomTypeAndCategory($roomTypeId, $itemCategory)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('item_category', $itemCategory)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的饮料
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getBeveragesByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndCategory($roomTypeId, 'beverage');
    }

    /**
     * 获取房型的零食
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSnacksByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndCategory($roomTypeId, 'snack');
    }

    /**
     * 获取房型的酒类
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getAlcoholsByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndCategory($roomTypeId, 'alcohol');
    }

    /**
     * 获取房型的其他物品
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getOthersByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndCategory($roomTypeId, 'other');
    }

    /**
     * 获取房型的免费物品
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getComplimentaryItemsByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('is_complimentary', true)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的可补充物品
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRefillableItemsByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('is_refillable', true)
            ->ordered()
            ->get();
    }
}
