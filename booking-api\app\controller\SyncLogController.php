<?php

namespace app\controller;

use app\service\SyncLogService;
use support\Request;
use support\Response;

/**
 * 同步日志控制器
 */
class SyncLogController extends BaseController
{
    /**
     * 同步日志服务
     *
     * @var SyncLogService
     */
    private $syncLogService;

    public function __construct()
    {
        $this->syncLogService = new SyncLogService();
    }

    /**
     * 获取同步日志列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            $result = $this->syncLogService->getSyncLogList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取同步日志列表');
        }
    }

    /**
     * 获取同步日志详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $logId = (int)$id;
            
            if ($logId <= 0) {
                return $this->error('日志ID无效');
            }

            $result = $this->syncLogService->getSyncLogDetail($logId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取同步日志详情');
        }
    }

    /**
     * 创建同步日志
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'sync_type', 'sync_direction'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'channel_id' => 'integer',
                'supplier_id' => 'integer',
                'hotel_id' => 'integer',
                'retry_count' => 'integer',
                'max_retries' => 'integer',
                'execution_time' => 'numeric',
                'records_processed' => 'integer',
                'records_success' => 'integer',
                'records_failed' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证同步类型
            $validSyncTypes = ['hotel', 'room_type', 'inventory', 'rate', 'booking'];
            if (!in_array($data['sync_type'], $validSyncTypes)) {
                return $this->error('无效的同步类型');
            }

            // 验证同步方向
            $validDirections = ['push', 'pull', 'bidirectional'];
            if (!in_array($data['sync_direction'], $validDirections)) {
                return $this->error('无效的同步方向');
            }

            // 验证同步状态
            if (!empty($data['sync_status'])) {
                $validStatuses = ['pending', 'processing', 'success', 'failed', 'partial'];
                if (!in_array($data['sync_status'], $validStatuses)) {
                    return $this->error('无效的同步状态');
                }
            }

            // 验证JSON字段
            $jsonFields = ['request_data', 'response_data'];
            foreach ($jsonFields as $field) {
                if (!empty($data[$field]) && !is_array($data[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->syncLogService->createSyncLog($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建同步日志');
        }
    }

    /**
     * 更新同步日志
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $logId = (int)$id;
            
            if ($logId <= 0) {
                return $this->error('日志ID无效');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'retry_count' => 'integer',
                'max_retries' => 'integer',
                'execution_time' => 'numeric',
                'records_processed' => 'integer',
                'records_success' => 'integer',
                'records_failed' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->syncLogService->updateSyncLog($logId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新同步日志');
        }
    }

    /**
     * 删除同步日志
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $logId = (int)$id;
            
            if ($logId <= 0) {
                return $this->error('日志ID无效');
            }

            $result = $this->syncLogService->deleteSyncLog($logId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除同步日志');
        }
    }

    /**
     * 获取同步统计信息
     *
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证日期范围
            if (!empty($params['start_date']) && !empty($params['end_date'])) {
                $dateErrors = $this->validateFormat($params, [
                    'start_date' => 'date',
                    'end_date' => 'date'
                ]);
                if ($dateErrors) {
                    return $this->error('日期格式错误', 400, $dateErrors);
                }

                if ($params['start_date'] > $params['end_date']) {
                    return $this->error('开始日期不能晚于结束日期');
                }
            }

            $result = $this->syncLogService->getSyncStatistics($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取同步统计信息');
        }
    }

    /**
     * 重试失败的同步
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function retry(Request $request, $id)
    {
        try {
            $logId = (int)$id;
            
            if ($logId <= 0) {
                return $this->error('日志ID无效');
            }

            $result = $this->syncLogService->retrySyncLog($logId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '重试同步');
        }
    }

    /**
     * 批量重试失败的同步
     *
     * @param Request $request
     * @return Response
     */
    public function batchRetry(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            if (empty($data['log_ids']) || !is_array($data['log_ids'])) {
                return $this->error('日志ID列表不能为空且必须是数组');
            }

            // 验证ID格式
            foreach ($data['log_ids'] as $logId) {
                if (!is_int($logId) || $logId <= 0) {
                    return $this->error('日志ID必须是正整数');
                }
            }

            $result = $this->syncLogService->batchRetrySyncLogs($data['log_ids']);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量重试同步');
        }
    }

    /**
     * 清理过期日志
     *
     * @param Request $request
     * @return Response
     */
    public function cleanup(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证保留天数
            $retentionDays = $params['retention_days'] ?? 30;
            if (!is_int($retentionDays) || $retentionDays < 1) {
                return $this->error('保留天数必须是大于0的整数');
            }

            $result = $this->syncLogService->cleanupExpiredLogs($retentionDays);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '清理过期日志');
        }
    }

    /**
     * 导出同步日志
     *
     * @param Request $request
     * @return Response
     */
    public function export(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证导出格式
            $format = $params['format'] ?? 'csv';
            $validFormats = ['csv', 'excel', 'json'];
            if (!in_array($format, $validFormats)) {
                return $this->error('无效的导出格式');
            }

            $result = $this->syncLogService->exportSyncLogs($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '导出同步日志');
        }
    }

    /**
     * 获取同步趋势分析
     *
     * @param Request $request
     * @return Response
     */
    public function trends(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证时间范围
            $timeRange = $params['time_range'] ?? '7d';
            $validRanges = ['1d', '7d', '30d', '90d'];
            if (!in_array($timeRange, $validRanges)) {
                return $this->error('无效的时间范围');
            }

            $result = $this->syncLogService->getSyncTrends($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取同步趋势分析');
        }
    }
}
