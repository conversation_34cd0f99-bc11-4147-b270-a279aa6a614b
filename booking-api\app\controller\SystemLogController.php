<?php

namespace app\controller;

use app\service\SystemLogService;
use support\Request;
use support\Response;

/**
 * 系统日志控制器
 */
class SystemLogController extends BaseController
{
    /**
     * 系统日志服务
     *
     * @var SystemLogService
     */
    private $systemLogService;

    public function __construct()
    {
        $this->systemLogService = new SystemLogService();
    }

    /**
     * 获取系统日志列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            $result = $this->systemLogService->getSystemLogList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取系统日志列表');
        }
    }

    /**
     * 获取系统日志详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $logId = (int)$id;
            
            if ($logId <= 0) {
                return $this->error('日志ID无效');
            }

            $result = $this->systemLogService->getSystemLogDetail($logId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取系统日志详情');
        }
    }

    /**
     * 创建系统日志
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, [
                'level', 'channel', 'message'
            ]);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'user_id' => 'integer',
                'execution_time' => 'numeric',
                'memory_usage' => 'integer',
                'line' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日志级别
            $validLevels = ['debug', 'info', 'warning', 'error', 'critical'];
            if (!in_array($data['level'], $validLevels)) {
                return $this->error('无效的日志级别');
            }

            // 验证日志通道
            $validChannels = ['application', 'database', 'api', 'sync', 'security', 'performance'];
            if (!in_array($data['channel'], $validChannels)) {
                return $this->error('无效的日志通道');
            }

            // 验证HTTP方法
            if (!empty($data['method'])) {
                $validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
                if (!in_array(strtoupper($data['method']), $validMethods)) {
                    return $this->error('无效的HTTP方法');
                }
            }

            // 验证上下文数据
            if (!empty($data['context']) && !is_array($data['context'])) {
                return $this->error('上下文数据必须是数组格式');
            }

            $result = $this->systemLogService->createSystemLog($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建系统日志');
        }
    }

    /**
     * 删除系统日志
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $logId = (int)$id;
            
            if ($logId <= 0) {
                return $this->error('日志ID无效');
            }

            $result = $this->systemLogService->deleteSystemLog($logId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除系统日志');
        }
    }

    /**
     * 获取系统日志统计信息
     *
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证日期范围
            if (!empty($params['start_date']) && !empty($params['end_date'])) {
                $dateErrors = $this->validateFormat($params, [
                    'start_date' => 'date',
                    'end_date' => 'date'
                ]);
                if ($dateErrors) {
                    return $this->error('日期格式错误', 400, $dateErrors);
                }

                if ($params['start_date'] > $params['end_date']) {
                    return $this->error('开始日期不能晚于结束日期');
                }
            }

            $result = $this->systemLogService->getSystemLogStatistics($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取系统日志统计信息');
        }
    }

    /**
     * 清理过期日志
     *
     * @param Request $request
     * @return Response
     */
    public function cleanup(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证保留天数
            $retentionDays = $params['retention_days'] ?? 30;
            if (!is_int($retentionDays) || $retentionDays < 1) {
                return $this->error('保留天数必须是大于0的整数');
            }

            // 验证日志级别（可选）
            if (!empty($params['levels'])) {
                if (!is_array($params['levels'])) {
                    return $this->error('日志级别必须是数组格式');
                }
                $validLevels = ['debug', 'info', 'warning', 'error', 'critical'];
                foreach ($params['levels'] as $level) {
                    if (!in_array($level, $validLevels)) {
                        return $this->error('包含无效的日志级别');
                    }
                }
            }

            $result = $this->systemLogService->cleanupExpiredLogs($retentionDays, $params['levels'] ?? null);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '清理过期日志');
        }
    }

    /**
     * 导出系统日志
     *
     * @param Request $request
     * @return Response
     */
    public function export(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证导出格式
            $format = $params['format'] ?? 'csv';
            $validFormats = ['csv', 'excel', 'json', 'txt'];
            if (!in_array($format, $validFormats)) {
                return $this->error('无效的导出格式');
            }

            // 验证日期范围
            if (!empty($params['start_date']) && !empty($params['end_date'])) {
                $dateErrors = $this->validateFormat($params, [
                    'start_date' => 'date',
                    'end_date' => 'date'
                ]);
                if ($dateErrors) {
                    return $this->error('日期格式错误', 400, $dateErrors);
                }
            }

            $result = $this->systemLogService->exportSystemLogs($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '导出系统日志');
        }
    }

    /**
     * 获取错误日志分析
     *
     * @param Request $request
     * @return Response
     */
    public function errorAnalysis(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证时间范围
            $timeRange = $params['time_range'] ?? '24h';
            $validRanges = ['1h', '6h', '24h', '7d', '30d'];
            if (!in_array($timeRange, $validRanges)) {
                return $this->error('无效的时间范围');
            }

            $result = $this->systemLogService->getErrorAnalysis($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取错误日志分析');
        }
    }

    /**
     * 获取性能分析
     *
     * @param Request $request
     * @return Response
     */
    public function performanceAnalysis(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证时间范围
            $timeRange = $params['time_range'] ?? '24h';
            $validRanges = ['1h', '6h', '24h', '7d', '30d'];
            if (!in_array($timeRange, $validRanges)) {
                return $this->error('无效的时间范围');
            }

            // 验证阈值
            if (!empty($params['slow_threshold'])) {
                if (!is_numeric($params['slow_threshold']) || $params['slow_threshold'] <= 0) {
                    return $this->error('慢查询阈值必须是大于0的数字');
                }
            }

            if (!empty($params['memory_threshold'])) {
                if (!is_int($params['memory_threshold']) || $params['memory_threshold'] <= 0) {
                    return $this->error('内存阈值必须是大于0的整数');
                }
            }

            $result = $this->systemLogService->getPerformanceAnalysis($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取性能分析');
        }
    }

    /**
     * 获取用户活动分析
     *
     * @param Request $request
     * @return Response
     */
    public function userActivity(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证时间范围
            $timeRange = $params['time_range'] ?? '24h';
            $validRanges = ['1h', '6h', '24h', '7d', '30d'];
            if (!in_array($timeRange, $validRanges)) {
                return $this->error('无效的时间范围');
            }

            $result = $this->systemLogService->getUserActivity($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取用户活动分析');
        }
    }

    /**
     * 获取安全事件分析
     *
     * @param Request $request
     * @return Response
     */
    public function securityEvents(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证时间范围
            $timeRange = $params['time_range'] ?? '24h';
            $validRanges = ['1h', '6h', '24h', '7d', '30d'];
            if (!in_array($timeRange, $validRanges)) {
                return $this->error('无效的时间范围');
            }

            $result = $this->systemLogService->getSecurityEvents($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取安全事件分析');
        }
    }

    /**
     * 实时日志监控
     *
     * @param Request $request
     * @return Response
     */
    public function realtime(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证日志级别过滤
            if (!empty($params['levels'])) {
                if (!is_array($params['levels'])) {
                    return $this->error('日志级别必须是数组格式');
                }
                $validLevels = ['debug', 'info', 'warning', 'error', 'critical'];
                foreach ($params['levels'] as $level) {
                    if (!in_array($level, $validLevels)) {
                        return $this->error('包含无效的日志级别');
                    }
                }
            }

            // 验证通道过滤
            if (!empty($params['channels'])) {
                if (!is_array($params['channels'])) {
                    return $this->error('日志通道必须是数组格式');
                }
                $validChannels = ['application', 'database', 'api', 'sync', 'security', 'performance'];
                foreach ($params['channels'] as $channel) {
                    if (!in_array($channel, $validChannels)) {
                        return $this->error('包含无效的日志通道');
                    }
                }
            }

            $result = $this->systemLogService->getRealtimeLogs($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取实时日志');
        }
    }
}
