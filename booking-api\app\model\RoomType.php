<?php

namespace app\model;

/**
 * 房型模型
 * 对应数据库表：room_types
 */
class RoomType extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_types';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'code',
        'name',
        'name_en',
        'description',
        'area',
        'max_occupancy',
        'max_adults',
        'max_children',
        'bed_type',
        'bed_type_id',
        'bed_count',
        'view_type',
        'floor_range',
        'total_rooms',
        'room_category',
        'room_grade',
        'smoking_policy',
        'bathroom_type',
        'bathroom_amenities',
        'amenities',
        'room_amenities',
        'technology_amenities',
        'accessibility_features',
        'room_features',
        'window_type',
        'balcony_type',
        'room_layout',
        'decoration_style',
        'renovation_date',
        'room_orientation',
        'noise_level',
        'privacy_level',
        'lighting_type',
        'temperature_control',
        'air_quality',
        'cleaning_frequency',
        'maintenance_schedule',
        'room_policies',
        'guest_capacity_details',
        'bed_configurations',
        'room_dimensions',
        'ceiling_height',
        'room_shape',
        'room_location_info',
        'view_details',
        'seasonal_availability',
        'special_occasions',
        'target_guests',
        'booking_restrictions',
        'cancellation_policies',
        'modification_policies',
        'check_in_requirements',
        'additional_fees',
        'included_services',
        'optional_services',
        'room_service_menu',
        'minibar_items',
        'connectivity',
        'entertainment',
        'food_beverage',
        'business_services',
        'safety_security',
        'comfort_features',
        'special_features',
        'welcome_amenities',
        'turndown_service',
        'housekeeping_notes',
        'maintenance_notes',
        'marketing_description',
        'selling_points',
        'competitive_advantages',
        'images',
        'videos',
        'virtual_tour_url',
        '360_view_urls',
        'floor_plan_url',
        'base_price',
        'extra_bed_price',
        'child_price',
        'cancellation_policy',
        'modification_policy',
        'special_notes',
        'status',
        'is_featured',
        'sort_order',
        'view_count',
        'booking_count',
        'review_count',
        'average_rating',
        'created_by',
        'updated_by'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'bed_type_id' => 'integer',
        'bed_count' => 'integer',
        'area' => 'decimal:2',
        'max_occupancy' => 'integer',
        'max_adults' => 'integer',
        'max_children' => 'integer',
        'total_rooms' => 'integer',
        'base_price' => 'decimal:2',
        'extra_bed_price' => 'decimal:2',
        'child_price' => 'decimal:2',
        'sort_order' => 'integer',
        'is_featured' => 'boolean',
        'view_count' => 'integer',
        'booking_count' => 'integer',
        'review_count' => 'integer',
        'average_rating' => 'decimal:2',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'renovation_date' => 'date',
        'ceiling_height' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        // JSON字段转换
        'amenities' => 'array',
        'bathroom_amenities' => 'array',
        'room_amenities' => 'array',
        'technology_amenities' => 'array',
        'accessibility_features' => 'array',
        'room_features' => 'array',
        'connectivity' => 'array',
        'entertainment' => 'array',
        'food_beverage' => 'array',
        'business_services' => 'array',
        'safety_security' => 'array',
        'comfort_features' => 'array',
        'special_features' => 'array',
        'room_layout' => 'array',
        'room_policies' => 'array',
        'guest_capacity_details' => 'array',
        'bed_configurations' => 'array',
        'room_dimensions' => 'array',
        'room_location_info' => 'array',
        'view_details' => 'array',
        'seasonal_availability' => 'array',
        'special_occasions' => 'array',
        'target_guests' => 'array',
        'booking_restrictions' => 'array',
        'cancellation_policies' => 'array',
        'modification_policies' => 'array',
        'check_in_requirements' => 'array',
        'additional_fees' => 'array',
        'included_services' => 'array',
        'optional_services' => 'array',
        'room_service_menu' => 'array',
        'minibar_items' => 'array',
        'welcome_amenities' => 'array',
        'housekeeping_notes' => 'array',
        'maintenance_notes' => 'array',
        'images' => 'array',
        'videos' => 'array',
        '360_view_urls' => 'array',
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_MAINTENANCE = 'maintenance';

    /**
     * 床型常量
     */
    const BED_TYPE_SINGLE = 'single';
    const BED_TYPE_DOUBLE = 'double';
    const BED_TYPE_TWIN = 'twin';
    const BED_TYPE_KING = 'king';
    const BED_TYPE_QUEEN = 'queen';
    const BED_TYPE_SOFA = 'sofa';

    /**
     * 作用域：活跃的房型
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取房间
     * 注意：rooms表暂未创建，此方法已注释
     */
    // public function rooms()
    // {
    //     return $this->hasMany(Room::class);
    // }

    /**
     * 获取库存
     */
    public function inventory()
    {
        return $this->hasMany(RoomInventory::class);
    }

    /**
     * 获取关联的床型类型（多对多关系）
     */
    public function bedTypes()
    {
        return $this->belongsToMany(BedType::class, 'room_type_bed_types')
            ->withPivot(['bed_count', 'is_primary', 'sort_order'])
            ->withTimestamps();
    }

    /**
     * 获取主要床型类型
     */
    public function primaryBedType()
    {
        return $this->belongsToMany(BedType::class, 'room_type_bed_types')
            ->wherePivot('is_primary', true)
            ->withPivot(['bed_count', 'is_primary', 'sort_order'])
            ->withTimestamps();
    }

    /**
     * 获取价格
     */
    public function rates()
    {
        return $this->hasMany(RoomRate::class);
    }

    /**
     * 获取价格计划
     */
    public function ratePlans()
    {
        return $this->hasMany(RatePlan::class);
    }

    /**
     * 获取订单
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * 获取床型配置
     */
    public function bedConfigurations()
    {
        return $this->hasMany(RoomTypeBedConfiguration::class);
    }

    /**
     * 获取客人容量
     */
    public function guestCapacities()
    {
        return $this->hasMany(RoomTypeGuestCapacity::class);
    }

    /**
     * 获取设施
     */
    public function amenities()
    {
        return $this->hasMany(RoomTypeAmenity::class);
    }

    /**
     * 获取浴室设施
     */
    public function bathroomAmenities()
    {
        return $this->hasMany(RoomTypeBathroomAmenity::class);
    }

    /**
     * 获取服务
     */
    public function services()
    {
        return $this->hasMany(RoomTypeService::class);
    }

    /**
     * 获取迷你吧物品
     */
    public function minibarItems()
    {
        return $this->hasMany(RoomTypeMinibarItem::class);
    }

    /**
     * 获取政策
     */
    public function policies()
    {
        return $this->hasMany(RoomTypePolicy::class);
    }

    /**
     * 获取额外费用
     */
    public function additionalFees()
    {
        return $this->hasMany(RoomTypeAdditionalFee::class);
    }

    /**
     * 获取预订限制
     */
    public function bookingRestrictions()
    {
        return $this->hasMany(RoomTypeBookingRestriction::class);
    }

    /**
     * 获取可用房间
     * 注意：rooms表暂未创建，此方法已注释
     */
    // public function availableRooms()
    // {
    //     return $this->rooms()->where('status', 'available');
    // }

    /**
     * 作用域：按床型筛选
     */
    public function scopeByBedType($query, $bedType)
    {
        return $query->where('bed_type', $bedType);
    }

    /**
     * 作用域：按最大入住人数筛选
     */
    public function scopeByMaxOccupancy($query, $occupancy)
    {
        return $query->where('max_occupancy', '>=', $occupancy);
    }

    /**
     * 作用域：按价格范围筛选
     */
    public function scopeByPriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('base_price', [$minPrice, $maxPrice]);
    }

    /**
     * 获取床型名称
     */
    public function getBedTypeNameAttribute()
    {
        $bedTypes = [
            self::BED_TYPE_SINGLE => '单人床',
            self::BED_TYPE_DOUBLE => '双人床',
            self::BED_TYPE_TWIN => '双床',
            self::BED_TYPE_KING => '特大床',
            self::BED_TYPE_QUEEN => '大床',
            self::BED_TYPE_SOFA => '沙发床',
        ];

        return $bedTypes[$this->bed_type] ?? '未知';
    }

    /**
     * 获取主图片
     */
    public function getMainImageAttribute()
    {
        $images = $this->images ?? [];
        return !empty($images) ? $images[0] : null;
    }

    /**
     * 检查是否有指定设施
     */
    public function hasAmenity($amenity)
    {
        return in_array($amenity, $this->amenities ?? []);
    }

    /**
     * 获取指定日期的库存
     */
    public function getInventoryForDate($date)
    {
        return $this->inventory()->where('date', $date)->first();
    }

    /**
     * 获取指定日期范围的库存
     */
    public function getInventoryForDateRange($startDate, $endDate)
    {
        return $this->inventory()
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date')
            ->get();
    }

    /**
     * 获取指定日期的价格
     */
    public function getRateForDate($date, $ratePlanId = null)
    {
        $query = $this->rates()->where('date', $date);
        
        if ($ratePlanId) {
            $query->where('rate_plan_id', $ratePlanId);
        }
        
        return $query->first();
    }

    /**
     * 获取指定日期范围的价格
     */
    public function getRatesForDateRange($startDate, $endDate, $ratePlanId = null)
    {
        $query = $this->rates()->whereBetween('date', [$startDate, $endDate]);
        
        if ($ratePlanId) {
            $query->where('rate_plan_id', $ratePlanId);
        }
        
        return $query->orderBy('date')->get();
    }

    /**
     * 检查指定日期范围是否有库存
     */
    public function hasAvailabilityForDateRange($startDate, $endDate, $rooms = 1)
    {
        $inventory = $this->getInventoryForDateRange($startDate, $endDate);

        foreach ($inventory as $item) {
            if ($item->available_rooms < $rooms) {
                return false;
            }
        }

        return $inventory->count() > 0;
    }

    /**
     * 获取房型分类名称
     */
    public function getRoomCategoryNameAttribute()
    {
        $categories = [
            'standard' => '标准房',
            'superior' => '高级房',
            'deluxe' => '豪华房',
            'executive' => '行政房',
            'suite' => '套房',
            'presidential' => '总统套房',
            'villa' => '别墅',
            'apartment' => '公寓'
        ];

        return $categories[$this->room_category] ?? '未分类';
    }

    /**
     * 获取房型等级名称
     */
    public function getRoomGradeNameAttribute()
    {
        $grades = [
            'economy' => '经济型',
            'standard' => '标准型',
            'comfort' => '舒适型',
            'superior' => '高级型',
            'luxury' => '豪华型',
            'premium' => '尊贵型'
        ];

        return $grades[$this->room_grade] ?? '标准型';
    }

    /**
     * 获取吸烟政策名称
     */
    public function getSmokingPolicyNameAttribute()
    {
        $policies = [
            'non_smoking' => '禁烟房',
            'smoking' => '吸烟房',
            'smoking_allowed' => '允许吸烟',
            'designated_area' => '指定区域吸烟'
        ];

        return $policies[$this->smoking_policy] ?? '禁烟房';
    }

    /**
     * 获取浴室类型名称
     */
    public function getBathroomTypeNameAttribute()
    {
        $types = [
            'private' => '独立浴室',
            'shared' => '共用浴室',
            'ensuite' => '套内浴室',
            'half_bath' => '半浴室',
            'full_bath' => '全浴室',
            'jacuzzi' => '按摩浴缸',
            'steam_shower' => '蒸汽淋浴'
        ];

        return $types[$this->bathroom_type] ?? '独立浴室';
    }

    /**
     * 获取窗户类型名称
     */
    public function getWindowTypeNameAttribute()
    {
        $types = [
            'none' => '无窗',
            'small' => '小窗',
            'standard' => '标准窗',
            'large' => '大窗',
            'floor_to_ceiling' => '落地窗',
            'bay_window' => '飘窗',
            'skylight' => '天窗'
        ];

        return $types[$this->window_type] ?? '标准窗';
    }

    /**
     * 获取阳台类型名称
     */
    public function getBalconyTypeNameAttribute()
    {
        $types = [
            'none' => '无阳台',
            'small' => '小阳台',
            'standard' => '标准阳台',
            'large' => '大阳台',
            'terrace' => '露台',
            'private' => '私人阳台',
            'shared' => '共用阳台'
        ];

        return $types[$this->balcony_type] ?? '无阳台';
    }

    /**
     * 获取房间设施信息
     */
    public function getRoomAmenitiesInfo()
    {
        return [
            'bathroom' => $this->bathroom_amenities ?? [],
            'room' => $this->room_amenities ?? [],
            'technology' => $this->technology_amenities ?? [],
            'connectivity' => $this->connectivity ?? [],
            'entertainment' => $this->entertainment ?? [],
            'food_beverage' => $this->food_beverage ?? [],
            'business' => $this->business_services ?? [],
            'safety' => $this->safety_security ?? [],
            'comfort' => $this->comfort_features ?? [],
            'special' => $this->special_features ?? []
        ];
    }

    /**
     * 获取房间详细信息
     */
    public function getRoomDetailsInfo()
    {
        return [
            'basic' => [
                'category' => $this->room_category,
                'grade' => $this->room_grade,
                'area' => $this->area,
                'max_occupancy' => $this->max_occupancy,
                'max_adults' => $this->max_adults,
                'max_children' => $this->max_children
            ],
            'layout' => [
                'dimensions' => $this->room_dimensions ?? [],
                'ceiling_height' => $this->ceiling_height,
                'shape' => $this->room_shape,
                'layout' => $this->room_layout ?? [],
                'orientation' => $this->room_orientation
            ],
            'features' => [
                'window_type' => $this->window_type,
                'balcony_type' => $this->balcony_type,
                'view_type' => $this->view_type,
                'view_details' => $this->view_details ?? [],
                'smoking_policy' => $this->smoking_policy,
                'bathroom_type' => $this->bathroom_type
            ],
            'environment' => [
                'noise_level' => $this->noise_level,
                'privacy_level' => $this->privacy_level,
                'lighting_type' => $this->lighting_type,
                'temperature_control' => $this->temperature_control,
                'air_quality' => $this->air_quality
            ]
        ];
    }

    /**
     * 获取床型配置信息
     */
    public function getBedConfigurationsInfo()
    {
        return $this->bed_configurations ?? [
            [
                'type' => $this->bed_type,
                'count' => $this->bed_count,
                'size' => 'standard',
                'is_primary' => true
            ]
        ];
    }

    /**
     * 获取服务信息
     */
    public function getServicesInfo()
    {
        return [
            'included' => $this->included_services ?? [],
            'optional' => $this->optional_services ?? [],
            'room_service' => $this->room_service_menu ?? [],
            'minibar' => $this->minibar_items ?? [],
            'welcome' => $this->welcome_amenities ?? [],
            'turndown' => $this->turndown_service ?? false,
            'cleaning_frequency' => $this->cleaning_frequency ?? 'daily'
        ];
    }
}
