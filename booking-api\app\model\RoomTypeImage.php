<?php

namespace app\model;

use support\Model;

/**
 * 房型图片模型
 */
class RoomTypeImage extends Model
{
    protected $table = 'room_type_images';
    
    protected $fillable = [
        'room_type_id',
        'image_url',
        'image_title',
        'image_description',
        'image_type',
        'file_name',
        'file_size',
        'file_type',
        'width',
        'height',
        'display_order',
        'is_primary',
        'status'
    ];

    protected $casts = [
        'room_type_id' => 'integer',
        'file_size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'display_order' => 'integer',
        'is_primary' => 'boolean',
        'status' => 'boolean',
    ];

    // 图片类型常量
    const TYPE_ROOM = 'room';
    const TYPE_BATHROOM = 'bathroom';
    const TYPE_VIEW = 'view';
    const TYPE_AMENITY = 'amenity';

    /**
     * 获取房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type_id', 'id');
    }

    /**
     * 获取房型的所有图片
     */
    public static function getRoomTypeImages($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
                  ->where('status', true)
                  ->orderBy('display_order')
                  ->get()
                  ->groupBy('image_type')
                  ->toArray();
    }

    /**
     * 获取房型的主图
     */
    public static function getPrimaryImage($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
                  ->where('is_primary', true)
                  ->where('status', true)
                  ->first();
    }

    /**
     * 获取指定类型的图片
     */
    public static function getImagesByType($roomTypeId, $imageType)
    {
        return self::where('room_type_id', $roomTypeId)
                  ->where('image_type', $imageType)
                  ->where('status', true)
                  ->orderBy('display_order')
                  ->get()
                  ->toArray();
    }

    /**
     * 设置主图
     */
    public function setPrimary()
    {
        // 取消其他主图
        self::where('room_type_id', $this->room_type_id)
           ->where('id', '!=', $this->id)
           ->update(['is_primary' => false]);
        
        // 设置当前为主图
        $this->is_primary = true;
        return $this->save();
    }

    /**
     * 获取下一个显示顺序
     */
    public static function getNextDisplayOrder($roomTypeId, $imageType = null)
    {
        $query = self::where('room_type_id', $roomTypeId);
        
        if ($imageType) {
            $query->where('image_type', $imageType);
        }
        
        return $query->max('display_order') + 1;
    }

    /**
     * 更新显示顺序
     */
    public static function updateDisplayOrder($roomTypeId, $imageOrders)
    {
        foreach ($imageOrders as $imageId => $displayOrder) {
            self::where('id', $imageId)
               ->where('room_type_id', $roomTypeId)
               ->update(['display_order' => $displayOrder]);
        }
        
        return true;
    }

    /**
     * 批量上传图片
     */
    public static function batchUpload($roomTypeId, $images)
    {
        $data = [];
        $displayOrder = self::getNextDisplayOrder($roomTypeId);
        
        foreach ($images as $image) {
            $data[] = [
                'room_type_id' => $roomTypeId,
                'image_url' => $image['image_url'],
                'image_title' => $image['image_title'] ?? null,
                'image_description' => $image['image_description'] ?? null,
                'image_type' => $image['image_type'] ?? self::TYPE_ROOM,
                'file_name' => $image['file_name'] ?? null,
                'file_size' => $image['file_size'] ?? null,
                'file_type' => $image['file_type'] ?? null,
                'width' => $image['width'] ?? null,
                'height' => $image['height'] ?? null,
                'display_order' => $displayOrder++,
                'is_primary' => $image['is_primary'] ?? false,
                'status' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }
        
        if (!empty($data)) {
            return self::insert($data);
        }
        
        return true;
    }

    /**
     * 删除图片文件
     */
    public function deleteFile(): bool
    {
        try {
            $publicPath = config('app.public_path', base_path() . '/public');
            $filePath = $publicPath . '/' . ltrim($this->image_url, '/');
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // 删除缩略图
            $thumbnailPath = $publicPath . '/' . ltrim(str_replace('.', '_thumb.', $this->image_url), '/');
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 软删除图片
     */
    public function softDelete()
    {
        $this->status = false;
        return $this->save();
    }

    /**
     * 硬删除图片
     */
    public function hardDelete()
    {
        // 删除文件
        $this->deleteFile();
        
        // 删除记录
        return $this->delete();
    }

    /**
     * 批量删除图片
     */
    public static function batchDelete($imageIds, $hard = false)
    {
        $images = self::whereIn('id', $imageIds)->get();
        
        foreach ($images as $image) {
            if ($hard) {
                $image->hardDelete();
            } else {
                $image->softDelete();
            }
        }
        
        return true;
    }

    /**
     * 复制房型图片到另一个房型
     */
    public static function copyImages($fromRoomTypeId, $toRoomTypeId)
    {
        $images = self::where('room_type_id', $fromRoomTypeId)
                     ->where('status', true)
                     ->get();
        
        foreach ($images as $image) {
            self::create([
                'room_type_id' => $toRoomTypeId,
                'image_url' => $image->image_url,
                'image_title' => $image->image_title,
                'image_description' => $image->image_description,
                'image_type' => $image->image_type,
                'file_name' => $image->file_name,
                'file_size' => $image->file_size,
                'file_type' => $image->file_type,
                'width' => $image->width,
                'height' => $image->height,
                'display_order' => $image->display_order,
                'is_primary' => false, // 复制时不设为主图
                'status' => true,
            ]);
        }
        
        return true;
    }

    /**
     * 获取图片类型列表
     */
    public static function getImageTypes()
    {
        return [
            self::TYPE_ROOM => '房间',
            self::TYPE_BATHROOM => '浴室',
            self::TYPE_VIEW => '景观',
            self::TYPE_AMENITY => '设施',
        ];
    }

    /**
     * 获取图片统计信息
     */
    public static function getImageStats($roomTypeId)
    {
        $stats = [];
        $types = self::getImageTypes();
        
        foreach ($types as $type => $name) {
            $count = self::where('room_type_id', $roomTypeId)
                        ->where('image_type', $type)
                        ->where('status', true)
                        ->count();
            
            $stats[] = [
                'type' => $type,
                'name' => $name,
                'count' => $count,
            ];
        }
        
        return $stats;
    }
}
