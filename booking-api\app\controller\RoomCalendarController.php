<?php

namespace app\controller;

use app\model\Hotel;
use app\model\RatePlan;
use app\model\RoomType;
use app\model\RoomInventory;
use app\model\RoomRate;
use support\Request;
use support\Response;
use Carbon\Carbon;

/**
 * 房态房价日历控制器
 */
class RoomCalendarController extends BaseController
{
    /**
     * 获取酒店房态房价日历数据
     *
     * @param Request $request
     * @return Response
     */
    public function getCalendarData(Request $request)
    {
        try {
            $startDate = (string) $request->get('startDate');
            $endDate = (string) $request->get('endDate');
            $hotelId = $request->get('hotelId'); // 可选

            // 参数校验
            if (!$startDate || !$endDate) {
                return json([
                    'code' => 400,
                    'msg' => '参数缺失: startDate 或 endDate',
                    'data' => null,
                ]);
            }

            try {
                $start = Carbon::createFromFormat('Y-m-d', $startDate);
                $end = Carbon::createFromFormat('Y-m-d', $endDate);
            } catch (\Throwable $e) {
                return error(400, '日期格式错误，请使用 YYYY-MM-DD');
            }

            if ($start->gt($end)) {
                return error(400, '开始日期不能大于结束日期');
            }

            // 限制范围
            if ($start->diffInDays($end) > 90) {
                return error(400, '查询范围不能超过90天');
            }

            // 生成日期列表
            $dates = [];
            $cursor = $start->copy();
            while ($cursor->lte($end)) {
                $dates[] = $cursor->format('Y-m-d');
                $cursor->addDay();
            }

            // 房型查询
            $roomTypesQuery = RoomType::query()->where('status', RoomType::STATUS_ACTIVE);
            if ($hotelId) {
                $roomTypesQuery->where('hotel_id', (int) $hotelId);
            }
            $roomTypes = $roomTypesQuery->orderBy('sort_order')->get();

            if ($roomTypes->isEmpty()) {
                return success();
            }

            $roomTypeIds = $roomTypes->pluck('id')->toArray();
            $hotelIds = $roomTypes->pluck('hotel_id')->unique()->values()->toArray();

            // Hotel 映射（storeId 用酒店 code，若无则用ID）
            $hotels = Hotel::whereIn('id', $hotelIds)->get()->keyBy('id');

            // 批量读取库存与价格
            $inventories = RoomInventory::whereIn('room_type_id', $roomTypeIds)
                ->whereBetween('date', [$startDate, $endDate])
                ->get()
                ->groupBy(['room_type_id', 'date']);

            $rates = RoomRate::whereIn('room_type_id', $roomTypeIds)
                ->whereBetween('date', [$startDate, $endDate])
                ->get()
                ->groupBy(['room_type_id', 'rate_plan_id', 'date']);

            // 价格计划（作为 children）
            $ratePlans = RatePlan::with('otaChannels')
                ->whereIn('room_type_id', $roomTypeIds)
                ->active()
                ->get()
                ->groupBy('room_type_id');



            $list = [];

            foreach ($roomTypes as $roomType) {
                $hotel = $hotels->get($roomType->hotel_id);

                // 语言映射
                $languages = [
                    'en' => (string)($roomType->name_en ?? ''),
                    'zh_CN' => (string)($roomType->name ?? ''),
                ];

                // children（使用价格计划）
                $children = [];
                $plans = $ratePlans->get($roomType->id) ?? collect();
                if (!$plans->isEmpty()) {
                    $sortIndex = 0;
                    foreach ($plans as $plan) {
                        $children[] = $this->buildChildEntry($plan, $roomType, $hotels, $dates, $inventories, $rates, $sortIndex);
                        $sortIndex++;
                    }
                }

                // 顶层房型按日字段
                $perDate = [];
                foreach ($dates as $date) {
                    $invByRoom = $inventories->get($roomType->id);
                    $invByDate = $invByRoom ? $invByRoom->get($date) : null;
                    $inventory = $invByDate ? $invByDate->first() : null;
                    $state = 1;
                    $sold = 0;
                    $limit = 0;
                    if ($inventory) {
                        $state = ($inventory->is_stop_sale || $inventory->is_closed || ($inventory->available_rooms ?? 0) <= 0) ? 0 : 1;
                        $sold = (int)($inventory->booked_rooms ?? $inventory->sold_rooms ?? 0);
                        $limit = (int)($inventory->oversell_limit ?? 0);
                    }
                    $perDate[$date] = [
                        'limit' => $limit,
                        'sold' => $sold,
                        'state' => $state,
                    ];
                }

                $list[] = array_merge([
                    'name' => '',
                    'languages' => $languages,
                    'sort' => (int)($roomType->sort_order ?? 0),
                    'room_type_id' => (int)$roomType->id,
                    'hotel_id' => (string)($hotel->code ?? (string)$roomType->hotel_id),
                    'sale_type' => '0',
                    'base_price' => (float)($roomType->base_price ?? 0),
                    'hour_price' => 0,
                    'rent_price' => 0,
                    'create_time' => (string)($roomType->created_at ?? date('Y-m-d H:i:s')),
                    'update_time' => (string)($roomType->updated_at ?? date('Y-m-d H:i:s')),
                    'delete_time' => 0,
                    'sales_method' => 1,
                    'occupancy_max' => (int)($roomType->max_occupancy ?? 0),
                    'occupancy_adults_max' => (int)($roomType->max_adults ?? 0),
                    'occupancy_children_max' => (int)($roomType->max_children ?? 0),
                    'children_age_min' => 0,
                    'room_count' => (int)($roomType->total_rooms ?? 0),
                    'children' => $children,
                ], $perDate);
            }

            return success($list);
        } catch (\Throwable $e) {
            return error(500, '服务器错误: ' . $e->getMessage());
        }
    }

    private function buildChildEntry($plan, $roomType, $hotels, array $dates, $inventories, $rates, int $sortIndex = 0): array
    {
        $hotel = $hotels->get($roomType->hotel_id);
        $planId = $plan ? (int)$plan->id : 0;

        // 渠道集合
        $channels = [];
        if ($plan && $plan->relationLoaded('otaChannels')) {
            foreach ($plan->otaChannels as $ch) {
                $channels[] = (string)$ch->id;
            }
        }

        // 房价名称= `房型名称` + `<价格计划名称>` + `<是否有早>` + `(计划code)` 
        $child = [
            'name' => get_plan_name($roomType, $plan, get_locale()),
            'channels' => $channels,
            'sort' => $sortIndex,
            'plan_id' => $planId,
            'store_id' => (string)($hotel->code ?? (string)$roomType->hotel_id),
            'room_type_id' => (int)$roomType->id,
            'price_type' => 0,
            'sale_type' => 1,
            'meal_type' => 0,
            'meal_count' => (int)($plan->breakfast_count ?? 0),
            'create_time' => (string)($plan->created_at ?? date('Y-m-d H:i:s')),
            'update_time' => (string)($plan->updated_at ?? date('Y-m-d H:i:s')),
            'delete_time' => 0,
            'base_price' => (float)($plan->base_price ?? ($roomType->base_price ?? 0)),
        ];

        foreach ($dates as $date) {
            $invByRoom = $inventories->get($roomType->id);
            $invByDate = $invByRoom ? $invByRoom->get($date) : null;
            $inventory = $invByDate ? $invByDate->first() : null;

            $rate = null;
            $rateByRoom = $rates->get($roomType->id);
            if ($planId && $rateByRoom) {
                $rateByPlan = $rateByRoom->get($planId);
                $rateByDate = $rateByPlan ? $rateByPlan->get($date) : null;
                $rate = $rateByDate ? $rateByDate->first() : null;
            }
            if (!$rate && $rateByRoom) {
                $rateByPlan = $rateByRoom->get(null);
                $rateByDate = $rateByPlan ? $rateByPlan->get($date) : null;
                $rate = $rateByDate ? $rateByDate->first() : null;
            }

            $price = 0.0;
            if ($rate) {
                $price = (float)($rate->selling_price ?? $rate->base_price ?? 0);
            } else {
                $price = (float)($plan->base_price ?? ($roomType->base_price ?? 0));
            }

            $state = 1;
            $sold = 0;
            $limit = 0;
            if ($inventory) {
                $state = ($inventory->is_stop_sale || $inventory->is_closed || ($inventory->available_rooms ?? 0) <= 0) ? 0 : 1;
                $sold = (int)($inventory->booked_rooms ?? $inventory->sold_rooms ?? 0);
                $limit = (int)($inventory->oversell_limit ?? 0);
            }

            $child[$date] = [
                'price' => $price,
                'date' => $date,
                'meal' => [
                    'mealCount' => (int)($plan->breakfast_count ?? 0),
                    'mealType' => 0,
                ],
                'state' => [
                    'date' => $date,
                    'state' => $state,
                    'limit' => $limit,
                    'sold' => $sold,
                ],
            ];
        }

        return $child;
    }
    /**
     * 获取房间状态
     */
    private function getRoomStatus($inventory, $rate)
    {
        if (!$inventory && !$rate) {
            return 'no_data'; // 无数据
        }

        if ($inventory && $inventory->is_stop_sale) {
            return 'stop_sale'; // 停售
        }

        if ($inventory && $inventory->available_rooms <= 0) {
            return 'sold_out'; // 售罄
        }

        if ($inventory && $inventory->available_rooms <= 5) {
            return 'low_inventory'; // 库存紧张
        }

        return 'available'; // 可售
    }

    /**
     * 计算汇总数据
     */
    private function calculateSummary($dailyData)
    {
        $totalDays = count($dailyData);
        $availableDays = 0;
        $stopSaleDays = 0;
        $soldOutDays = 0;
        $totalRevenue = 0;
        $totalBookedRooms = 0;
        $avgPrice = 0;
        $minPrice = null;
        $maxPrice = null;

        foreach ($dailyData as $day) {
            $status = $day['status'];
            $sellPrice = $day['rates']['sell_price'];
            $bookedRooms = $day['inventory']['booked_rooms'];

            switch ($status) {
                case 'available':
                case 'low_inventory':
                    $availableDays++;
                    break;
                case 'stop_sale':
                    $stopSaleDays++;
                    break;
                case 'sold_out':
                    $soldOutDays++;
                    break;
            }

            $totalRevenue += $sellPrice * $bookedRooms;
            $totalBookedRooms += $bookedRooms;

            if ($minPrice === null || $sellPrice < $minPrice) {
                $minPrice = $sellPrice;
            }
            if ($maxPrice === null || $sellPrice > $maxPrice) {
                $maxPrice = $sellPrice;
            }
        }

        if ($totalDays > 0) {
            $avgPrice = array_sum(array_column($dailyData, 'rates.sell_price')) / $totalDays;
        }

        return [
            'total_days' => $totalDays,
            'available_days' => $availableDays,
            'stop_sale_days' => $stopSaleDays,
            'sold_out_days' => $soldOutDays,
            'total_booked_rooms' => $totalBookedRooms,
            'total_revenue' => round($totalRevenue, 2),
            'avg_price' => round($avgPrice, 2),
            'min_price' => $minPrice,
            'max_price' => $maxPrice,
            'occupancy_rate' => $totalDays > 0 ? round(($totalDays - $stopSaleDays) / $totalDays * 100, 2) : 0,
        ];
    }

    /**
     * 批量更新房态数据
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdateInventory(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['updates']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['updates'])) {
                return $this->error('updates必须是数组');
            }

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($data['updates'] as $index => $update) {
                try {
                    // 验证每个更新项的必需字段
                    $updateErrors = $this->validateRequired($update, ['room_type_id', 'date']);
                    if ($updateErrors) {
                        $errors[] = "第{$index}项: " . implode(', ', $updateErrors);
                        $errorCount++;
                        continue;
                    }

                    // 查找或创建库存记录
                    $inventory = RoomInventory::firstOrCreate(
                        [
                            'room_type_id' => $update['room_type_id'],
                            'date' => $update['date'],
                        ],
                        [
                            'total_rooms' => 0,
                            'available_rooms' => 0,
                            'booked_rooms' => 0,
                            'blocked_rooms' => 0,
                            'maintenance_rooms' => 0,
                        ]
                    );

                    // 更新字段
                    $allowedFields = [
                        'total_rooms', 'available_rooms', 'booked_rooms', 'blocked_rooms',
                        'maintenance_rooms', 'is_stop_sale', 'min_stay', 'max_stay',
                        'close_arrival', 'close_departure'
                    ];

                    $updateData = [];
                    foreach ($allowedFields as $field) {
                        if (isset($update[$field])) {
                            $updateData[$field] = $update[$field];
                        }
                    }

                    if ($updateData) {
                        $inventory->update($updateData);
                    }

                    $successCount++;

                } catch (\Exception $e) {
                    $errors[] = "第{$index}项: " . $e->getMessage();
                    $errorCount++;
                }
            }

            return $this->success([
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors,
            ], '批量更新完成');

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新房态数据');
        }
    }

    /**
     * 批量更新房价数据
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdateRates(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['updates']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['updates'])) {
                return $this->error('updates必须是数组');
            }

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($data['updates'] as $index => $update) {
                try {
                    // 验证每个更新项的必需字段
                    $updateErrors = $this->validateRequired($update, ['room_type_id', 'date']);
                    if ($updateErrors) {
                        $errors[] = "第{$index}项: " . implode(', ', $updateErrors);
                        $errorCount++;
                        continue;
                    }

                    // 查找或创建价格记录
                    $rate = RoomRate::firstOrCreate(
                        [
                            'room_type_id' => $update['room_type_id'],
                            'date' => $update['date'],
                        ],
                        [
                            'base_price' => 0,
                            'sell_price' => 0,
                            'currency' => 'CNY',
                        ]
                    );

                    // 更新字段
                    $allowedFields = [
                        'base_price', 'sell_price', 'extra_bed_price', 'child_price',
                        'currency', 'rate_plan_id'
                    ];

                    $updateData = [];
                    foreach ($allowedFields as $field) {
                        if (isset($update[$field])) {
                            $updateData[$field] = $update[$field];
                        }
                    }

                    if ($updateData) {
                        $rate->update($updateData);
                    }

                    $successCount++;

                } catch (\Exception $e) {
                    $errors[] = "第{$index}项: " . $e->getMessage();
                    $errorCount++;
                }
            }

            return $this->success([
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors,
            ], '批量更新完成');

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新房价数据');
        }
    }
}
