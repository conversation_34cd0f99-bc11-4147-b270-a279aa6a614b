<?php

namespace app\controller;

use support\Request;
use support\Response;
use app\model\ImageCategory;

/**
 * 图片分类控制器
 */
class ImageCategoryController   extends BaseController
{
    /**
     * 获取图片分类列表
     */
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->get('page', 1);
            $pageSize = (int)$request->get('page_size', 20);
            $parentId = $request->get('parent_id', '');
            $group = $request->get('group', '');
            $keyword = $request->get('keyword', '');
            $isActive = $request->get('is_active', '');

            $query = ImageCategory::query();

            // 父分类筛选
            if ($parentId !== '') {
                $query->where('parent_id', (int)$parentId);
            }

            // 分组筛选
            if ($group) {
                $query->byGroup($group);
            }

            // 关键词搜索
            if ($keyword) {
                $query->where('category_name', 'like', "%{$keyword}%");
            }

            // 状态筛选
            if ($isActive !== '') {
                $query->where('is_active', (bool)$isActive);
            }

            // 排序
            $query->ordered();

            // 分页
            $total = $query->count();
            $categories = $query->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();

            return $this->success([
                'list' => $categories,
                'pagination' => [
                    'current' => $page,
                    'pageSize' => $pageSize,
                    'total' => $total,
                    'pages' => ceil($total / $pageSize),
                ],
            ]);
        } catch (\Exception $e) {
          return $this->error('获取图片分类列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取图片分类详情
     */
    public function show(Request $request, $id): Response
    {
        try {
            $category = ImageCategory::find($id);

            if (!$category) {
                return $this->error('图片分类不存在', 404);
            }

            // 加载关联数据
            $category->load(['parent', 'children']);

            // 获取使用统计
            $stats = $category->getUsageStats();
            $category->usage_stats = $stats;

            return $this->success($category);
        } catch (\Exception $e) {
            return $this->error('获取图片分类详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建图片分类
     */
    public function store(Request $request): Response
    {
        try {
            $data = $request->post();

            // 验证必填字段
            if (empty($data['category_id']) || empty($data['category_name'])) {
                return $this->error('分类ID和分类名称不能为空', 400);
            }

            // 检查分类ID是否已存在
            $existing = ImageCategory::where('category_id', $data['category_id'])->first();
            if ($existing) {
                return $this->error('分类ID已存在', 400);
            }

            $category = ImageCategory::create($data);

            return $this->success($category);
        } catch (\Exception $e) {
            return $this->error('创建图片分类失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新图片分类
     */
    public function update(Request $request, $id): Response
    {
        try {
            $category = ImageCategory::find($id);

            if (!$category) {
                return $this->error('图片分类不存在', 404);
            }

            $data = $request->post();

            // 如果更新分类ID，检查是否已存在
            if (isset($data['category_id']) && $data['category_id'] != $category->category_id) {
                $existing = ImageCategory::where('category_id', $data['category_id'])->first();
                if ($existing) {
                    return $this->error('分类ID已存在', 400);
                }
            }

            $category->update($data);

            return $this->success($category,
                '图片分类更新成功'
            );
        } catch (\Exception $e) {
            return $this->error('更新图片分类失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除图片分类
     */
    public function destroy(Request $request, $id): Response
    {
        try {
            $category = ImageCategory::find($id);

            if (!$category) {
                return $this->error('图片分类不存在', 404);
            }

            // 检查是否有子分类
            $hasChildren = ImageCategory::where('parent_id', $category->category_id)->exists();
            if ($hasChildren) {
                return $this->error('该分类下还有子分类，无法删除', 400);
            }

            // 检查是否有关联的图片
            $hasImages = \app\model\HotelImage::where('category_id', $category->category_id)->exists();
            if ($hasImages) {
                return $this->error('该分类下还有图片，无法删除', 400);
            }

            $category->delete();

            return $this->success(null, '图片分类删除成功');
        } catch (\Exception $e) {
            return $this->error('删除图片分类失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取树形结构的分类数据
     */
    public function tree(Request $request): Response
    {
        try {
            try {
                $tree = ImageCategory::getTree();
            } catch (\Exception $e) {
                // 返回默认的树形数据
                $tree = $this->getDefaultTree();
            }

            return $this->success($tree);
        } catch (\Exception $e) {
            return $this->error('获取分类树失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取扁平化的分类列表
     */
    public function flat(Request $request): Response
    {
        try {
            // 如果表不存在，返回默认数据
            try {
                $categories = ImageCategory::getFlat();
            } catch (\Exception $e) {
                // 返回默认的分类数据
                $categories = $this->getDefaultCategories();
            }

            return $this->success($categories);
        } catch (\Exception $e) {
            return $this->error('获取分类列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取默认分类数据
     */
    private function getDefaultCategories(): array
    {
        return [
            [
                'id' => 1,
                'category_id' => 1,
                'parent_id' => 0,
                'category_name' => '外观',
                'category_group' => '外观',
                'description' => '酒店外观图片',
                'icon' => 'lucide:building',
                'sort_order' => 1,
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 2,
                'category_id' => 11,
                'parent_id' => 1,
                'category_name' => '院子',
                'category_group' => '外观',
                'description' => '酒店院子图片',
                'icon' => 'lucide:trees',
                'sort_order' => 11,
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 3,
                'category_id' => 17,
                'parent_id' => 0,
                'category_name' => '餐饮',
                'category_group' => '餐饮',
                'description' => '餐饮设施图片',
                'icon' => 'lucide:utensils',
                'sort_order' => 2,
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
            [
                'id' => 4,
                'category_id' => 38,
                'parent_id' => 17,
                'category_name' => '中餐厅',
                'category_group' => '餐饮',
                'description' => '中餐厅图片',
                'icon' => 'lucide:utensils',
                'sort_order' => 38,
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ],
        ];
    }

    /**
     * 获取分组统计
     */
    public function groups(Request $request): Response
    {
        try {
            try {
                $groups = ImageCategory::getGroupStats();
            } catch (\Exception $e) {
                // 返回默认的分组数据
                $groups = [
                    ['group' => '外观', 'name' => '外观', 'count' => 2],
                    ['group' => '餐饮', 'name' => '餐饮', 'count' => 2],
                    ['group' => '休闲', 'name' => '休闲', 'count' => 0],
                    ['group' => '商务', 'name' => '商务', 'count' => 0],
                    ['group' => '家庭亲子', 'name' => '家庭亲子', 'count' => 0],
                    ['group' => '公共区域', 'name' => '公共区域', 'count' => 0],
                    ['group' => '周边', 'name' => '周边', 'count' => 0],
                    ['group' => '其他', 'name' => '其他', 'count' => 0],
                ];
            }

            return $this->success($groups);
        } catch (\Exception $e) {
            return $this->error('获取分组统计失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    public function batchUpdateStatus(Request $request): Response
    {
        try {
            $ids = $request->post('ids', []);
            $isActive = (bool)$request->post('is_active', true);

            if (empty($ids) || !is_array($ids)) {
                return $this->error('请选择要更新的分类', 400);
            }

            $count = ImageCategory::whereIn('id', $ids)
                ->update(['is_active' => $isActive]);

            $status = $isActive ? '启用' : '禁用';
            return $this->success([
                'updated_count' => $count
            ],
                "成功{$status} {$count} 个分类"
            );
        } catch (\Exception $e) {
            return $this->error('批量更新状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量导入分类
     */
    public function batchImport(Request $request): Response
    {
        try {
            $data = $request->post('data', []);

            if (empty($data) || !is_array($data)) {
                return $this->error('请提供要导入的数据', 400);
            }

            $result = ImageCategory::batchImport($data);

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error('批量导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取默认树形数据
     */
    private function getDefaultTree(): array
    {
        return [
            [
                'id' => 1,
                'category_id' => 1,
                'parent_id' => 0,
                'category_name' => '外观',
                'category_group' => '外观',
                'description' => '酒店外观图片',
                'icon' => 'lucide:building',
                'sort_order' => 1,
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'children' => [
                    [
                        'id' => 2,
                        'category_id' => 11,
                        'parent_id' => 1,
                        'category_name' => '院子',
                        'category_group' => '外观',
                        'description' => '酒店院子图片',
                        'icon' => 'lucide:trees',
                        'sort_order' => 11,
                        'is_active' => true,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                        'children' => [],
                    ],
                ],
            ],
            [
                'id' => 3,
                'category_id' => 17,
                'parent_id' => 0,
                'category_name' => '餐饮',
                'category_group' => '餐饮',
                'description' => '餐饮设施图片',
                'icon' => 'lucide:utensils',
                'sort_order' => 2,
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'children' => [
                    [
                        'id' => 4,
                        'category_id' => 38,
                        'parent_id' => 17,
                        'category_name' => '中餐厅',
                        'category_group' => '餐饮',
                        'description' => '中餐厅图片',
                        'icon' => 'lucide:utensils',
                        'sort_order' => 38,
                        'is_active' => true,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                        'children' => [],
                    ],
                ],
            ],
        ];
    }
}
