<?php

namespace app\model;

use support\Model;

/**
 * 标准设施模型
 */
class StandardFacility extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'standard_facilities';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'category_id',
        'code',
        'name',
        'name_en',
        'description',
        'icon',
        'is_chargeable',
        'charge_type',
        'charge_amount',
        'is_bookable',
        'booking_advance_hours',
        'operating_hours',
        'capacity',
        'age_restriction',
        'special_requirements',
        'sort_order',
        'is_active',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'category_id' => 'integer',
        'is_chargeable' => 'boolean',
        'charge_amount' => 'decimal:2',
        'is_bookable' => 'boolean',
        'booking_advance_hours' => 'integer',
        'operating_hours' => 'array',
        'capacity' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取所属分类
     */
    public function category()
    {
        return $this->belongsTo(FacilityCategory::class, 'category_id');
    }

    /**
     * 获取使用此设施的酒店
     */
    public function hotels()
    {
        return $this->belongsToMany(Hotel::class, 'hotel_facilities', 'facility_id', 'hotel_id')
            ->withPivot([
                'is_available',
                'custom_name',
                'custom_description',
                'custom_charge_amount',
                'custom_operating_hours',
                'custom_capacity',
                'location',
                'floor',
                'room_number',
                'contact_phone',
                'booking_required',
                'advance_booking_hours',
                'special_notes',
                'images',
                'sort_order'
            ])
            ->withTimestamps();
    }

    /**
     * 作用域：启用的设施
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：收费设施
     */
    public function scopeChargeable($query)
    {
        return $query->where('is_chargeable', true);
    }

    /**
     * 作用域：免费设施
     */
    public function scopeFree($query)
    {
        return $query->where('is_chargeable', false);
    }

    /**
     * 作用域：需要预订的设施
     */
    public function scopeBookable($query)
    {
        return $query->where('is_bookable', true);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取收费类型名称
     *
     * @return string
     */
    public function getChargeTypeNameAttribute()
    {
        $types = [
            'fixed' => '固定收费',
            'hourly' => '按小时收费',
            'daily' => '按天收费',
        ];

        return $types[$this->charge_type] ?? '';
    }

    /**
     * 获取所有启用的设施（用于下拉选择）
     *
     * @param int|null $categoryId
     * @return array
     */
    public static function getActiveOptions($categoryId = null)
    {
        $query = static::active()->with('category')->ordered();
        
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }
        
        return $query->get()->map(function ($facility) {
            return [
                'value' => $facility->id,
                'label' => $facility->name,
                'code' => $facility->code,
                'category_name' => $facility->category->name,
                'icon' => $facility->icon,
                'is_chargeable' => $facility->is_chargeable,
                'is_bookable' => $facility->is_bookable,
                'charge_amount' => $facility->charge_amount,
                'charge_type' => $facility->charge_type,
                'charge_type_name' => $facility->charge_type_name,
            ];
        })->toArray();
    }

    /**
     * 按分类分组获取设施选项
     *
     * @return array
     */
    public static function getGroupedOptions()
    {
        $facilities = static::active()->with('category')->ordered()->get();
        $grouped = [];

        foreach ($facilities->groupBy('category.name') as $categoryName => $categoryFacilities) {
            $grouped[] = [
                'label' => $categoryName,
                'options' => $categoryFacilities->map(function ($facility) {
                    return [
                        'value' => $facility->id,
                        'label' => $facility->name,
                        'code' => $facility->code,
                        'icon' => $facility->icon,
                        'is_chargeable' => $facility->is_chargeable,
                        'is_bookable' => $facility->is_bookable,
                    ];
                })->values()->toArray()
            ];
        }

        return $grouped;
    }
}
