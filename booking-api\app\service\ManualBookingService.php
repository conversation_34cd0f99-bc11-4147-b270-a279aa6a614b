<?php

namespace app\service;

use app\model\Booking;
use app\model\Hotel;
use app\model\RoomType;
use app\model\RatePlan;
use app\model\RoomInventory;
use app\model\BookingLog;
use support\Db;

/**
 * 手动订单服务类
 * 对标携程EBooking的手动订单管理功能
 */
class ManualBookingService extends BaseService
{
    /**
     * 创建手动订单
     */
    public function createManualBooking($data)
    {
        try {
            Db::beginTransaction();

            // 验证酒店存在
            $hotel = Hotel::find($data['hotel_id']);
            if (!$hotel) {
                return $this->error('酒店不存在');
            }

            // 验证房型存在
            $roomType = RoomType::where('hotel_id', $data['hotel_id'])
                ->where('id', $data['room_type_id'])
                ->first();
            if (!$roomType) {
                return $this->error('房型不存在');
            }

            // 验证价格计划存在
            if (!empty($data['rate_plan_id'])) {
                $ratePlan = RatePlan::where('hotel_id', $data['hotel_id'])
                    ->where('id', $data['rate_plan_id'])
                    ->first();
                if (!$ratePlan) {
                    return $this->error('价格计划不存在');
                }
            }

            // 验证房态和库存
            $inventoryCheck = $this->checkInventoryAvailability($data);
            if (!$inventoryCheck['success']) {
                return $this->error($inventoryCheck['message']);
            }

            // 计算价格
            $priceCalculation = $this->calculateBookingPrice($data);
            if (!$priceCalculation['success']) {
                return $this->error($priceCalculation['message']);
            }

            // 生成订单号
            $bookingNo = $this->generateBookingNumber($data['booking_source'] ?? 'manual');

            // 创建订单数据
            $bookingData = array_merge($data, [
                'booking_no' => $bookingNo,
                'booking_type' => 'individual',
                'booking_source' => $data['booking_source'] ?? 'manual',
                'booking_channel' => $data['booking_channel'] ?? 'direct',
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'room_price' => $priceCalculation['data']['room_price'],
                'service_fee' => $priceCalculation['data']['service_fee'],
                'tax_fee' => $priceCalculation['data']['tax_fee'],
                'total_amount' => $priceCalculation['data']['total_amount'],
                'nights' => $this->calculateNights($data['check_in_date'], $data['check_out_date']),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 创建订单
            $booking = Booking::create($bookingData);

            // 锁定库存
            $this->lockInventory($data, $booking->id);

            // 记录订单日志
            $this->logBookingAction($booking->id, 'created', '手动创建订单', $data['created_by'] ?? null);

            Db::commit();

            return $this->success([
                'booking' => $booking->fresh(),
                'inventory_locked' => true,
                'price_calculation' => $priceCalculation['data']
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('创建订单失败：' . $e->getMessage());
        }
    }

    /**
     * 验证库存可用性
     */
    private function checkInventoryAvailability($data)
    {
        $checkInDate = $data['check_in_date'];
        $checkOutDate = $data['check_out_date'];
        $rooms = $data['rooms'] ?? 1;
        $hotelId = $data['hotel_id'];
        $roomTypeId = $data['room_type_id'];

        // 获取入住期间的所有日期
        $dates = $this->getDateRange($checkInDate, $checkOutDate);

        foreach ($dates as $date) {
            $inventory = RoomInventory::where('hotel_id', $hotelId)
                ->where('room_type_id', $roomTypeId)
                ->where('date', $date)
                ->first();

            if (!$inventory) {
                return [
                    'success' => false,
                    'message' => "日期 {$date} 没有库存信息"
                ];
            }

            // 检查是否关房
            if ($inventory->is_closed) {
                return [
                    'success' => false,
                    'message' => "日期 {$date} 已关房：" . ($inventory->close_reason ?? '未知原因')
                ];
            }

            // 检查是否停售
            if ($inventory->is_stop_sale) {
                return [
                    'success' => false,
                    'message' => "日期 {$date} 已停售：" . ($inventory->stop_sale_reason ?? '未知原因')
                ];
            }

            // 检查库存是否充足
            $availableRooms = $inventory->calculateActualAvailability($date);
            if ($availableRooms < $rooms) {
                return [
                    'success' => false,
                    'message' => "日期 {$date} 库存不足，需要 {$rooms} 间，可用 {$availableRooms} 间"
                ];
            }

            // 检查最少入住天数限制
            if ($inventory->min_stay > 0) {
                $totalNights = $this->calculateNights($checkInDate, $checkOutDate);
                if ($totalNights < $inventory->min_stay) {
                    return [
                        'success' => false,
                        'message' => "不满足最少入住天数要求，最少需要 {$inventory->min_stay} 晚"
                    ];
                }
            }

            // 检查最多入住天数限制
            if ($inventory->max_stay > 0) {
                $totalNights = $this->calculateNights($checkInDate, $checkOutDate);
                if ($totalNights > $inventory->max_stay) {
                    return [
                        'success' => false,
                        'message' => "超过最多入住天数限制，最多允许 {$inventory->max_stay} 晚"
                    ];
                }
            }

            // 检查提前预订天数限制
            if ($inventory->min_advance_booking > 0) {
                $advanceDays = $this->calculateAdvanceDays($date);
                if ($advanceDays < $inventory->min_advance_booking) {
                    return [
                        'success' => false,
                        'message' => "不满足提前预订天数要求，最少需要提前 {$inventory->min_advance_booking} 天"
                    ];
                }
            }

            if ($inventory->max_advance_booking > 0) {
                $advanceDays = $this->calculateAdvanceDays($date);
                if ($advanceDays > $inventory->max_advance_booking) {
                    return [
                        'success' => false,
                        'message' => "超过最大提前预订天数限制，最多允许提前 {$inventory->max_advance_booking} 天"
                    ];
                }
            }
        }

        return ['success' => true];
    }

    /**
     * 计算订单价格
     */
    private function calculateBookingPrice($data)
    {
        try {
            $checkInDate = $data['check_in_date'];
            $checkOutDate = $data['check_out_date'];
            $rooms = $data['rooms'] ?? 1;
            $adults = $data['adults'] ?? 2;
            $children = $data['children'] ?? 0;
            $hotelId = $data['hotel_id'];
            $roomTypeId = $data['room_type_id'];
            $ratePlanId = $data['rate_plan_id'] ?? null;

            $totalRoomPrice = 0;
            $totalServiceFee = 0;
            $totalTaxFee = 0;

            // 获取入住期间的所有日期（不包括退房日期）
            $dates = $this->getDateRange($checkInDate, $checkOutDate, false);

            foreach ($dates as $date) {
                // 获取当日价格
                $rateQuery = Db::table('rates')
                    ->where('hotel_id', $hotelId)
                    ->where('room_type_id', $roomTypeId)
                    ->where('date', $date);

                if ($ratePlanId) {
                    $rateQuery->where('rate_plan_id', $ratePlanId);
                }

                $rate = $rateQuery->first();

                if (!$rate) {
                    return [
                        'success' => false,
                        'message' => "日期 {$date} 没有价格信息"
                    ];
                }

                $dailyRoomPrice = $rate->base_price * $rooms;
                $dailyServiceFee = ($rate->service_fee ?? 0) * $rooms;
                $dailyTaxFee = ($rate->tax_fee ?? 0) * $rooms;

                // 应用折扣
                if (!empty($rate->discount_percentage) && $rate->discount_percentage > 0) {
                    $discountAmount = $dailyRoomPrice * ($rate->discount_percentage / 100);
                    $dailyRoomPrice -= $discountAmount;
                }

                $totalRoomPrice += $dailyRoomPrice;
                $totalServiceFee += $dailyServiceFee;
                $totalTaxFee += $dailyTaxFee;
            }

            $totalAmount = $totalRoomPrice + $totalServiceFee + $totalTaxFee;

            return [
                'success' => true,
                'data' => [
                    'room_price' => $totalRoomPrice,
                    'service_fee' => $totalServiceFee,
                    'tax_fee' => $totalTaxFee,
                    'total_amount' => $totalAmount,
                    'nights' => count($dates),
                    'avg_daily_rate' => count($dates) > 0 ? round($totalRoomPrice / count($dates), 2) : 0
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '价格计算失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 锁定库存
     */
    private function lockInventory($data, $bookingId)
    {
        $checkInDate = $data['check_in_date'];
        $checkOutDate = $data['check_out_date'];
        $rooms = $data['rooms'] ?? 1;
        $hotelId = $data['hotel_id'];
        $roomTypeId = $data['room_type_id'];

        // 获取入住期间的所有日期
        $dates = $this->getDateRange($checkInDate, $checkOutDate);

        foreach ($dates as $date) {
            $inventory = RoomInventory::where('hotel_id', $hotelId)
                ->where('room_type_id', $roomTypeId)
                ->where('date', $date)
                ->first();

            if ($inventory) {
                $inventory->available_rooms -= $rooms;
                $inventory->sold_rooms += $rooms;
                $inventory->booking_count += 1;
                $inventory->last_updated_by = $data['created_by'] ?? null;
                $inventory->last_updated_reason = "手动订单锁定库存 - 订单号: BK{$bookingId}";
                $inventory->save();
            }
        }
    }

    /**
     * 生成订单号
     */
    private function generateBookingNumber($source = 'manual')
    {
        $prefix = 'BK';
        $date = date('Ymd');
        $sourceCode = strtoupper(substr($source, 0, 2));
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        return $prefix . $date . $sourceCode . $random;
    }

    /**
     * 记录订单日志
     */
    private function logBookingAction($bookingId, $action, $description, $userId = null)
    {
        BookingLog::create([
            'booking_id' => $bookingId,
            'action' => $action,
            'description' => $description,
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 计算入住天数
     */
    private function calculateNights($checkInDate, $checkOutDate)
    {
        $checkIn = new \DateTime($checkInDate);
        $checkOut = new \DateTime($checkOutDate);
        return $checkOut->diff($checkIn)->days;
    }

    /**
     * 获取日期范围
     */
    private function getDateRange($startDate, $endDate, $includeEndDate = true)
    {
        $dates = [];
        $current = new \DateTime($startDate);
        $end = new \DateTime($endDate);
        
        if (!$includeEndDate) {
            $end->modify('-1 day');
        }

        while ($current <= $end) {
            $dates[] = $current->format('Y-m-d');
            $current->modify('+1 day');
        }

        return $dates;
    }

    /**
     * 计算提前预订天数
     */
    private function calculateAdvanceDays($date)
    {
        $today = new \DateTime();
        $targetDate = new \DateTime($date);
        return $targetDate->diff($today)->days;
    }

    /**
     * 修改手动订单
     */
    public function modifyManualBooking($bookingId, $data, $userId = null)
    {
        try {
            Db::beginTransaction();

            $booking = Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在');
            }

            // 检查订单状态是否允许修改
            if (!in_array($booking->status, ['pending', 'confirmed'])) {
                return $this->error('订单当前状态不允许修改');
            }

            $originalData = $booking->toArray();
            $changes = [];

            // 如果修改了入住日期、退房日期或房间数，需要重新验证库存和价格
            $needInventoryCheck = false;
            $needPriceRecalculation = false;

            if (isset($data['check_in_date']) && $data['check_in_date'] != $booking->check_in_date) {
                $changes['check_in_date'] = ['from' => $booking->check_in_date, 'to' => $data['check_in_date']];
                $needInventoryCheck = true;
                $needPriceRecalculation = true;
            }

            if (isset($data['check_out_date']) && $data['check_out_date'] != $booking->check_out_date) {
                $changes['check_out_date'] = ['from' => $booking->check_out_date, 'to' => $data['check_out_date']];
                $needInventoryCheck = true;
                $needPriceRecalculation = true;
            }

            if (isset($data['rooms']) && $data['rooms'] != $booking->rooms) {
                $changes['rooms'] = ['from' => $booking->rooms, 'to' => $data['rooms']];
                $needInventoryCheck = true;
                $needPriceRecalculation = true;
            }

            if (isset($data['adults']) && $data['adults'] != $booking->adults) {
                $changes['adults'] = ['from' => $booking->adults, 'to' => $data['adults']];
                $needPriceRecalculation = true;
            }

            if (isset($data['children']) && $data['children'] != $booking->children) {
                $changes['children'] = ['from' => $booking->children, 'to' => $data['children']];
                $needPriceRecalculation = true;
            }

            // 如果需要重新验证库存
            if ($needInventoryCheck) {
                // 先释放原有库存
                $this->releaseInventory($originalData, $bookingId);

                // 验证新的库存可用性
                $modifiedData = array_merge($originalData, $data);
                $inventoryCheck = $this->checkInventoryAvailability($modifiedData);
                if (!$inventoryCheck['success']) {
                    // 恢复原有库存锁定
                    $this->lockInventory($originalData, $bookingId);
                    return $this->error($inventoryCheck['message']);
                }

                // 锁定新的库存
                $this->lockInventory($modifiedData, $bookingId);
            }

            // 如果需要重新计算价格
            if ($needPriceRecalculation) {
                $modifiedData = array_merge($originalData, $data);
                $priceCalculation = $this->calculateBookingPrice($modifiedData);
                if (!$priceCalculation['success']) {
                    return $this->error($priceCalculation['message']);
                }

                $data['room_price'] = $priceCalculation['data']['room_price'];
                $data['service_fee'] = $priceCalculation['data']['service_fee'];
                $data['tax_fee'] = $priceCalculation['data']['tax_fee'];
                $data['total_amount'] = $priceCalculation['data']['total_amount'];
                $data['nights'] = $priceCalculation['data']['nights'];

                $changes['room_price'] = ['from' => $booking->room_price, 'to' => $data['room_price']];
                $changes['total_amount'] = ['from' => $booking->total_amount, 'to' => $data['total_amount']];
            }

            // 更新订单信息
            $data['updated_at'] = date('Y-m-d H:i:s');
            $booking->update($data);

            // 记录修改日志
            $this->logBookingAction(
                $bookingId,
                'modified',
                '手动修改订单：' . json_encode($changes, JSON_UNESCAPED_UNICODE),
                $userId
            );

            Db::commit();

            return $this->success([
                'booking' => $booking->fresh(),
                'changes' => $changes,
                'price_recalculated' => $needPriceRecalculation
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('修改订单失败：' . $e->getMessage());
        }
    }

    /**
     * 取消手动订单
     */
    public function cancelManualBooking($bookingId, $reason, $userId = null)
    {
        try {
            Db::beginTransaction();

            $booking = Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在');
            }

            // 检查订单状态是否允许取消
            if (!in_array($booking->status, ['pending', 'confirmed'])) {
                return $this->error('订单当前状态不允许取消');
            }

            // 释放库存
            $this->releaseInventory($booking->toArray(), $bookingId);

            // 更新订单状态
            $booking->update([
                'status' => 'cancelled',
                'cancelled_at' => date('Y-m-d H:i:s'),
                'cancelled_by' => $userId,
                'cancellation_reason' => $reason,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 记录取消日志
            $this->logBookingAction($bookingId, 'cancelled', "手动取消订单：{$reason}", $userId);

            Db::commit();

            return $this->success([
                'booking' => $booking->fresh(),
                'inventory_released' => true
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('取消订单失败：' . $e->getMessage());
        }
    }

    /**
     * 释放库存
     */
    private function releaseInventory($bookingData, $bookingId)
    {
        $checkInDate = $bookingData['check_in_date'];
        $checkOutDate = $bookingData['check_out_date'];
        $rooms = $bookingData['rooms'] ?? 1;
        $hotelId = $bookingData['hotel_id'];
        $roomTypeId = $bookingData['room_type_id'];

        // 获取入住期间的所有日期
        $dates = $this->getDateRange($checkInDate, $checkOutDate);

        foreach ($dates as $date) {
            $inventory = RoomInventory::where('hotel_id', $hotelId)
                ->where('room_type_id', $roomTypeId)
                ->where('date', $date)
                ->first();

            if ($inventory) {
                $inventory->available_rooms += $rooms;
                $inventory->sold_rooms = max(0, $inventory->sold_rooms - $rooms);
                $inventory->booking_count = max(0, $inventory->booking_count - 1);
                $inventory->last_updated_reason = "释放库存 - 订单号: BK{$bookingId}";
                $inventory->save();
            }
        }
    }

    /**
     * 确认手动订单
     */
    public function confirmManualBooking($bookingId, $confirmationNo = null, $userId = null)
    {
        try {
            $booking = Booking::find($bookingId);
            if (!$booking) {
                return $this->error('订单不存在');
            }

            if ($booking->status !== 'pending') {
                return $this->error('订单状态不允许确认');
            }

            $booking->update([
                'status' => 'confirmed',
                'confirmation_no' => $confirmationNo ?: $this->generateConfirmationNumber($bookingId),
                'confirmed_at' => date('Y-m-d H:i:s'),
                'confirmed_by' => $userId,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 记录确认日志
            $this->logBookingAction($bookingId, 'confirmed', '手动确认订单', $userId);

            return $this->success([
                'booking' => $booking->fresh(),
                'confirmation_no' => $booking->confirmation_no
            ]);

        } catch (\Exception $e) {
            return $this->error('确认订单失败：' . $e->getMessage());
        }
    }

    /**
     * 生成确认号
     */
    private function generateConfirmationNumber($bookingId)
    {
        return 'CONF' . date('Ymd') . str_pad($bookingId, 6, '0', STR_PAD_LEFT);
    }

    /**
     * 获取可用房型和价格
     */
    public function getAvailableRoomTypesAndRates($hotelId, $checkInDate, $checkOutDate, $rooms = 1)
    {
        try {
            // 获取酒店的所有房型
            $roomTypes = RoomType::where('hotel_id', $hotelId)
                ->where('status', 'active')
                ->get();

            $availableRoomTypes = [];

            foreach ($roomTypes as $roomType) {
                // 检查库存可用性
                $inventoryData = [
                    'hotel_id' => $hotelId,
                    'room_type_id' => $roomType->id,
                    'check_in_date' => $checkInDate,
                    'check_out_date' => $checkOutDate,
                    'rooms' => $rooms
                ];

                $inventoryCheck = $this->checkInventoryAvailability($inventoryData);
                if (!$inventoryCheck['success']) {
                    continue;
                }

                // 获取价格信息
                $priceCalculation = $this->calculateBookingPrice($inventoryData);
                if (!$priceCalculation['success']) {
                    continue;
                }

                // 获取可用的价格计划
                $ratePlans = RatePlan::where('hotel_id', $hotelId)
                    ->where('is_active', true)
                    ->where('is_published', true)
                    ->where(function($query) use ($checkInDate, $checkOutDate) {
                        $query->where(function($q) use ($checkInDate, $checkOutDate) {
                            $q->whereNull('valid_from')
                              ->orWhere('valid_from', '<=', $checkInDate);
                        })->where(function($q) use ($checkInDate, $checkOutDate) {
                            $q->whereNull('valid_to')
                              ->orWhere('valid_to', '>=', $checkOutDate);
                        });
                    })
                    ->get();

                $availableRatePlans = [];
                foreach ($ratePlans as $ratePlan) {
                    $ratePlanData = array_merge($inventoryData, ['rate_plan_id' => $ratePlan->id]);
                    $ratePlanPrice = $this->calculateBookingPrice($ratePlanData);

                    if ($ratePlanPrice['success']) {
                        $availableRatePlans[] = [
                            'rate_plan' => $ratePlan,
                            'pricing' => $ratePlanPrice['data']
                        ];
                    }
                }

                $availableRoomTypes[] = [
                    'room_type' => $roomType,
                    'base_pricing' => $priceCalculation['data'],
                    'rate_plans' => $availableRatePlans,
                    'inventory_status' => 'available'
                ];
            }

            return $this->success([
                'hotel_id' => $hotelId,
                'check_in_date' => $checkInDate,
                'check_out_date' => $checkOutDate,
                'rooms' => $rooms,
                'available_room_types' => $availableRoomTypes,
                'total_options' => count($availableRoomTypes)
            ]);

        } catch (\Exception $e) {
            return $this->error('获取可用房型失败：' . $e->getMessage());
        }
    }
}
