<?php

namespace app\controller;

use app\service\RoomInventoryService;
use support\Request;
use support\Response;

/**
 * 房态房量管理控制器
 * 基于携程房态房量标准的完整API接口
 */
class RoomInventoryController extends BaseController
{
    /**
     * 房态房量服务
     *
     * @var RoomInventoryService
     */
    protected $roomInventoryService;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->roomInventoryService = new RoomInventoryService();
    }

    /**
     * 获取房态房量列表
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function index(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);

            // 设置默认日期范围
            $startDate = $params['start_date'] ?? date('Y-m-d');
            $endDate = $params['end_date'] ?? date('Y-m-d', strtotime('+30 days'));

            // 验证日期格式
            $dateErrors = $this->validateFormat([
                'start_date' => $startDate,
                'end_date' => $endDate
            ], [
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($dateErrors) {
                return $this->error('日期格式错误', 400, $dateErrors);
            }

            $queryParams = array_merge($params, [
                'hotel_id' => $hotelId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);

            $result = $this->roomInventoryService->getRoomInventoryList($queryParams);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房态房量列表');
        }
    }

    /**
     * 获取房态房量详情
     *
     * @param Request $request
     * @param int $hotel_id
     * @param string $date
     * @return Response
     */
    public function show(Request $request, $hotel_id, $date)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            // 验证日期格式
            if (!$this->isValidDate($date)) {
                return $this->error('日期格式无效');
            }

            $params = $this->getInput($request);
            $params['hotel_id'] = $hotelId;
            $params['date'] = $date;

            $result = $this->roomInventoryService->getRoomInventoryDetail($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房态房量详情');
        }
    }

    /**
     * 批量更新房态房量
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function batchUpdate(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['start_date', 'end_date', 'updates']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证日期格式
            $dateErrors = $this->validateFormat($data, [
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($dateErrors) {
                return $this->error('日期格式错误', 400, $dateErrors);
            }

            if (!is_array($data['updates']) || empty($data['updates'])) {
                return $this->error('updates必须是非空数组');
            }

            $data['hotel_id'] = $hotelId;
            $result = $this->roomInventoryService->batchUpdateRoomInventory($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新房态房量');
        }
    }

    /**
     * 批量关闭销售
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function batchClose(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $data['hotel_id'] = $hotelId;
            $result = $this->roomInventoryService->batchCloseRoomInventory($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量关闭销售');
        }
    }

    /**
     * 批量开放销售
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function batchOpen(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $data['hotel_id'] = $hotelId;
            $result = $this->roomInventoryService->batchOpenRoomInventory($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量开放销售');
        }
    }

    /**
     * 获取房态房量统计
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function statistics(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);
            $params['hotel_id'] = $hotelId;

            $result = $this->roomInventoryService->getRoomInventoryStatistics($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房态房量统计');
        }
    }

    /**
     * 获取房态房量操作日志
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function logs(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);
            $params['hotel_id'] = $hotelId;

            $result = $this->roomInventoryService->getRoomInventoryLogs($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房态房量操作日志');
        }
    }

    /**
     * 获取房态房量模板
     *
     * @param Request $request
     * @return Response
     */
    public function templates(Request $request)
    {
        try {
            $params = $this->getInput($request);
            $result = $this->roomInventoryService->getRoomInventoryTemplates($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房态房量模板');
        }
    }

    /**
     * 应用模板
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function applyTemplate(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['template_id', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            $data['hotel_id'] = $hotelId;
            $result = $this->roomInventoryService->applyTemplate($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '应用模板');
        }
    }

    /**
     * 导出房态房量数据
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function export(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);
            $params['hotel_id'] = $hotelId;

            $result = $this->roomInventoryService->exportRoomInventory($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '导出房态房量数据');
        }
    }

    /**
     * 导入房态房量数据
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function import(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            
            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $file = $request->file('file');
            if (!$file) {
                return $this->error('请选择要导入的文件');
            }

            $result = $this->roomInventoryService->importRoomInventory($hotelId, $file);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '导入房态房量数据');
        }
    }

    /**
     * 获取房态房量预警
     *
     * @param Request $request
     * @param int $hotel_id
     * @return Response
     */
    public function alerts(Request $request, $hotel_id)
    {
        try {
            $hotelId = (int)$hotel_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            $params = $this->getInput($request);
            $params['hotel_id'] = $hotelId;

            $result = $this->roomInventoryService->getRoomInventoryAlerts($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取房态房量预警');
        }
    }

    /**
     * 处理预警
     *
     * @param Request $request
     * @param int $hotel_id
     * @param int $alert_id
     * @return Response
     */
    public function handleAlert(Request $request, $hotel_id, $alert_id)
    {
        try {
            $hotelId = (int)$hotel_id;
            $alertId = (int)$alert_id;

            if ($hotelId <= 0) {
                return $this->error('酒店ID无效');
            }

            if ($alertId <= 0) {
                return $this->error('预警ID无效');
            }

            $data = $this->getInput($request);
            $data['hotel_id'] = $hotelId;
            $data['alert_id'] = $alertId;

            $result = $this->roomInventoryService->handleAlert($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '处理预警');
        }
    }

    /**
     * 验证日期格式
     *
     * @param string $date
     * @return bool
     */
    private function isValidDate($date)
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
