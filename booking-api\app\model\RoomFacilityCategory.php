<?php

namespace app\model;

use support\Model;

/**
 * 房型设施设备分类模型
 */
class RoomFacilityCategory extends Model
{
    protected $table = 'room_facility_categories';
    
    protected $fillable = [
        'code',
        'name',
        'name_en',
        'parent_id',
        'icon',
        'sort_order',
        'is_active',
        'description'
    ];

    protected $casts = [
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(RoomFacilityCategory::class, 'parent_id', 'id')
                   ->where('is_active', true)
                   ->orderBy('sort_order');
    }

    /**
     * 获取父分类
     */
    public function parent()
    {
        return $this->belongsTo(RoomFacilityCategory::class, 'parent_id', 'id');
    }

    /**
     * 获取分类下的设施
     */
    public function facilities()
    {
        return $this->hasMany(RoomFacility::class, 'category_id', 'id')
                   ->where('is_active', true)
                   ->orderBy('sort_order');
    }

    /**
     * 获取树形结构的分类列表
     */
    public static function getTree()
    {
        $categories = self::where('is_active', true)
                         ->orderBy('sort_order')
                         ->get()
                         ->toArray();

        return self::buildTree($categories);
    }

    /**
     * 构建树形结构
     */
    private static function buildTree($categories, $parentId = 0)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $children = self::buildTree($categories, $category['id']);
                if (!empty($children)) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }
        
        return $tree;
    }

    /**
     * 获取扁平化的分类列表
     */
    public static function getFlat()
    {
        return self::where('is_active', true)
                  ->orderBy('sort_order')
                  ->get()
                  ->toArray();
    }

    /**
     * 获取分类统计信息
     */
    public static function getStats()
    {
        $stats = [];
        
        $categories = self::where('is_active', true)
                         ->whereNull('parent_id')
                         ->orderBy('sort_order')
                         ->get();

        foreach ($categories as $category) {
            $facilityCount = RoomFacility::where('category_id', $category->id)
                                       ->where('is_active', true)
                                       ->count();
            
            $stats[] = [
                'category_id' => $category->id,
                'category_name' => $category->name,
                'category_code' => $category->code,
                'icon' => $category->icon,
                'facility_count' => $facilityCount,
            ];
        }
        
        return $stats;
    }

    /**
     * 检查分类代码是否存在
     */
    public static function codeExists($code, $excludeId = null)
    {
        $query = self::where('code', $code);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 获取下一个排序号
     */
    public static function getNextSortOrder($parentId = 0)
    {
        return self::where('parent_id', $parentId)->max('sort_order') + 1;
    }

    /**
     * 启用/禁用分类
     */
    public function toggleActive()
    {
        $this->is_active = !$this->is_active;
        return $this->save();
    }

    /**
     * 删除分类（软删除，设为禁用）
     */
    public function softDelete()
    {
        // 禁用所有子分类
        self::where('parent_id', $this->id)->update(['is_active' => false]);
        
        // 禁用分类下的所有设施
        RoomFacility::where('category_id', $this->id)->update(['is_active' => false]);
        
        // 禁用当前分类
        $this->is_active = false;
        return $this->save();
    }

    /**
     * 验证是否可以删除
     */
    public function canDelete()
    {
        // 检查是否有子分类
        $hasChildren = self::where('parent_id', $this->id)->where('is_active', true)->exists();
        
        // 检查是否有设施
        $hasFacilities = RoomFacility::where('category_id', $this->id)->where('is_active', true)->exists();
        
        return !$hasChildren && !$hasFacilities;
    }
}
