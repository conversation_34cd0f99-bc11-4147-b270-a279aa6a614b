<?php

namespace app\controller;

use support\Request;
use support\Response;
use app\service\HotelDetailService;
use app\service\HotelFacilityService;
use app\service\HotelImageService;
use app\service\HotelPolicyService;
use app\service\HotelPaymentService;
use app\service\HotelLicenseService;
use app\service\HotelHostService;

/**
 * 酒店详情控制器
 * 处理酒店详细信息的展示和编辑功能
 */
class HotelDetailController extends BaseController
{
    protected HotelDetailService $hotelDetailService;
    protected HotelFacilityService $facilityService;
    protected HotelImageService $imageService;
    protected HotelPolicyService $policyService;
    protected HotelPaymentService $paymentService;
    protected HotelLicenseService $licenseService;
    protected HotelHostService $hostService;

    public function __construct()
    {
        $this->hotelDetailService = new HotelDetailService();
        $this->facilityService = new HotelFacilityService();
        $this->imageService = new HotelImageService();
        $this->policyService = new HotelPolicyService();
        $this->paymentService = new HotelPaymentService();
        $this->licenseService = new HotelLicenseService();
        $this->hostService = new HotelHostService();
    }

    /**
     * 更新酒店基础信息
     */
    public function updateBasicInfo(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $this->getJsonData($request);

            // 验证必填字段
            $requiredFields = ['name', 'code', 'language_code', 'currency'];
            foreach ($requiredFields as $field) {
                if (!isset($data[$field]) || $data[$field] === '') {
                    return $this->error("字段 {$field} 不能为空", 400);
                }
            }

            $result = $this->hotelDetailService->updateBasicInfo($hotelId, $data);

            if ($result) {
                return $this->success(['id' => $hotelId], '基础信息更新成功');
            } else {
                return $this->error('基础信息更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店设施
     */
    public function updateFacilities(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $this->getJsonData($request);

            // 验证数据格式
            if (!isset($data['facilities']) || !is_array($data['facilities'])) {
                return $this->error('设施数据格式错误', 400);
            }

            // 更新设施信息
            $result = $this->facilityService->updateHotelFacilities($hotelId, $data['facilities']);

            if ($result) {
                return $this->success(['hotel_id' => $hotelId], '设施信息更新成功');
            } else {
                return $this->error('设施信息更新失败', 500);
            }
        } catch (\Exception $e) {
            return $this->error('更新设施信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店设施列表
     */
    public function getFacilities(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $facilities = $this->facilityService->getHotelFacilities($hotelId);
            return $this->success($facilities);
        } catch (\Exception $e) {
            return $this->error('获取设施列表失败: ' . $e->getMessage());
        }
    }



    /**
     * 获取酒店图片
     */
    public function getImages(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $images = $this->imageService->getHotelImages($hotelId);
            return $this->success($images);
        } catch (\Exception $e) {
            return $this->error('获取图片列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店图片
     */
    public function updateImages(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $request->post();

            $validator = $this->validate($data, [
                'images' => 'required|array',
                'images.*.image_url' => 'required|string|max:500',
                'images.*.image_category' => 'string|max:100',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $result = $this->imageService->updateHotelImages($hotelId, $data['images']);
            
            if ($result) {
                return $this->success(['hotel_id' => $hotelId], '图片信息更新成功');
            } else {
                return $this->error('图片信息更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店政策
     */
    public function getPolicies(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $policyType = $request->get('type'); // 可选：获取特定类型的政策
            
            if ($policyType) {
                $policy = $this->policyService->getHotelPolicy($hotelId, $policyType);
                return $this->success($policy);
            } else {
                $policies = $this->policyService->getHotelPolicies($hotelId);
                return $this->success($policies);
            }
        } catch (\Exception $e) {
            return $this->error('获取政策信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新特定类型的酒店政策
     */
    public function updatePolicy(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $request->post();

            $validator = $this->validate($data, [
                'policy_type' => 'required|string|in:check,dining,parking,children,pet,checkin,guest,stay,deposit',
                'policy_data' => 'required|array',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $result = $this->policyService->updateHotelPolicy(
                $hotelId, 
                $data['policy_type'], 
                $data['policy_data']
            );
            
            if ($result) {
                return $this->success([
                    'hotel_id' => $hotelId,
                    'policy_type' => $data['policy_type']
                ], '政策信息更新成功');
            } else {
                return $this->error('政策信息更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店支付方式
     */
    public function getPayments(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $payments = $this->paymentService->getHotelPayments($hotelId);
            return $this->success($payments);
        } catch (\Exception $e) {
            return $this->error('获取支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店支付方式
     */
    public function updatePayments(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $request->post();

            $validator = $this->validate($data, [
                'payments' => 'required|array',
                'payments.*.payment_id' => 'required|integer',
                'payments.*.payment_name' => 'required|string|max:100',
                'payments.*.bookable' => 'boolean',
                'payments.*.payable' => 'boolean',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $result = $this->paymentService->updateHotelPayments($hotelId, $data['payments']);
            
            if ($result) {
                return $this->success(['hotel_id' => $hotelId], '支付方式更新成功');
            } else {
                return $this->error('支付方式更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取酒店资质信息
     */
    public function getLicenses(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $licenses = $this->licenseService->getHotelLicenses($hotelId);
            return $this->success($licenses);
        } catch (\Exception $e) {
            return $this->error('获取资质信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新酒店资质信息
     */
    public function updateLicenses(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $request->post();

            $validator = $this->validate($data, [
                'license_type' => 'required|string|in:business,identity_card,bank_card,property',
                'license_data' => 'required|array',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $result = $this->licenseService->updateHotelLicense(
                $hotelId, 
                $data['license_type'], 
                $data['license_data']
            );
            
            if ($result) {
                return $this->success([
                    'hotel_id' => $hotelId,
                    'license_type' => $data['license_type']
                ], '资质信息更新成功');
            } else {
                return $this->error('资质信息更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取房东信息
     */
    public function getHostInfo(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $hostInfo = $this->hostService->getHotelHostInfo($hotelId);
            return $this->success($hostInfo);
        } catch (\Exception $e) {
            return $this->error('获取房东信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新联系信息
     */
    public function updateContact(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $request->post();

            $validator = $this->validate($data, [
                'phone' => 'string|max:50',
                'email' => 'email|max:100',
                'country' => 'string|max:10',
                'province' => 'string|max:50',
                'city' => 'string|max:50',
                'address' => 'string|max:500',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $result = $this->hotelDetailService->updateContactInfo($hotelId, $data);

            if ($result) {
                return $this->success(['hotel_id' => $hotelId], '联系信息更新成功');
            } else {
                return $this->error('联系信息更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新房东信息
     */
    public function updateHostInfo(Request $request, $id): Response
    {
        try {
            $hotelId = (int)$id;
            $data = $request->post();

            $validator = $this->validate($data, [
                'host_name' => 'string|max:100',
                'host_introduction' => 'string',
                'host_gender' => 'string|in:Male,Female',
                'career' => 'string|max:100',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $result = $this->hostService->updateHotelHostInfo($hotelId, $data);

            if ($result) {
                return $this->success(['hotel_id' => $hotelId], '房东信息更新成功');
            } else {
                return $this->error('房东信息更新失败');
            }
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage());
        }
    }
}
