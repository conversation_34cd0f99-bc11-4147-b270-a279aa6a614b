<?php
namespace app\service\expedia;

use GuzzleHttp\Client;
use support\Log;
use support\Redis;
use think\Exception;

class ExpediaClient {
    // expedia API基础URL
    const EX_API_BASE_URL = 'https://test.ean.com';
    const EX_SANDBOX_URL = 'https://test.ean.com';

    const PROPERTIES_CONTENT = '/v3/properties/content';
    // 获取酒店房价和空房信息
    const PROPERTIES_AVAILABILITY = '/v3/properties/availability';
    // 获取酒店详情
    const PROPERTIES_AVAILABILITY_DETAIL = '/v3/properties/{property_id}/availability';
    // 价格检测
    const PROPERTIES_CHECK_RATE = '/v3/properties/{property_id}/rooms/{room_id}/rates/{rate_id}';
    // 获取可接受的付款类型
    const PAYMENT_OPTIONS = '/v3/properties/{property_id}/payment-options';
    // 获取房源的可用日期日历。目前，此功能仅限于Vrbo房源。
    const CALENDARS_AVAILABILITY = '/v3/calendars/availability';

    protected $appId;
    protected $appSecret;
    protected $isDebug = false; // 是否使用沙箱环境

    public function __construct() {
        $expediaConfig = config('expedia');
        $this->appId = $expediaConfig['api_key'];
        $this->appSecret = $expediaConfig['api_secret'];
        $this->isDebug = $expediaConfig['is_debug'];
    }

    /**
     * 获取API基础URL
     * @return string
     */
    private function getApiBaseUrl()
    {
        return $this->isDebug ? self::EX_SANDBOX_URL : self::EX_API_BASE_URL;
    }

    /**
     * 发送HTTP请求到expediaAPI
     * @param string $endpoint API端点
     * @param array $params 请求参数
     * @param string $method 请求方法
     * @return array
     * @throws Exception
     */
    public function sendRequest($endpoint, $params = [], $method = 'POST')
    {
        $url = $this->getApiBaseUrl() . $endpoint;

        // 获取认证头部
        $authHeader = $this->getAuthHeader();

        $client = new Client([
            'verify'  => false,
            'timeout' => 3,
            'http_errors' => false,
        ]);
        try {
            if ($method === 'POST') {
                $response = $client->post($url, [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'charset' => 'UTF-8',
                        'Authorization' => $authHeader
                    ],
                    'json' => $params
                ]);
                Log::info("expedia API响应:" . json_encode([
                    'url' => $url,
                    'params' => json_encode($params),
                    'code' => $response->getStatusCode(),
                    'body' => json_decode($response->getBody(), true, 512, JSON_BIGINT_AS_STRING)
                ], JSON_UNESCAPED_UNICODE));

                $result = $response->getBody();
                return json_decode($result, true);
            } else {
                // GET请求
                $response = $client->get($url . '?' . http_build_query($params), [
                    'headers' => [
                        'Authorization' => $authHeader
                    ]
                ]);
                Log::info("expedia API(GET)响应:" . json_encode([
                    'url' => $url,
                    'params' => json_encode($params),
                    'code' => $response->getStatusCode(),
                    'body' => json_decode($response->getBody(), true, 512, JSON_BIGINT_AS_STRING)
                ], JSON_UNESCAPED_UNICODE));
                $result = json_decode($response->getBody(), true);
                return $result ?: [];
            }
        } catch (Exception $e) {
            Log::error("expedia API请求异常: {$url}" . var_export([
                    'error' => $e->getMessage(),
                    'params' => $params,
                    'trace' => $e->getTraceAsString()
                ]));
            throw $e;
        }
    }

    /**
     * 获取Expedia API签名
     * @param int|null $timestamp 可选时间戳，默认使用当前时间
     * @return string
     * @throws Exception
     */
    public function getSignature($timestamp = null)
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            throw new Exception("请正确配置expedia应用信息");
        }

        $apiKey = $this->appId;
        $secret = $this->appSecret;
        $timestamp = $timestamp ?: time();
        return hash("sha512", $apiKey.$secret.$timestamp);
    }

    /**
     * 获取完整的认证头部
     * @return string
     * @throws Exception
     */
    public function getAuthHeader()
    {
        if (empty($this->appId) || empty($this->appSecret)) {
            throw new Exception("请正确配置expedia应用信息");
        }

        $apiKey = $this->appId;
        $timestamp = time();
        $signature = $this->getSignature($timestamp);

        return 'EAN APIKey=' . $apiKey . ',Signature=' . $signature . ',timestamp=' . $timestamp;
    }
}
