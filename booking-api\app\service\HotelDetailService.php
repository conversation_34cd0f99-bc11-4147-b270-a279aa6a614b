<?php

namespace app\service;

use support\Db;
use app\model\Hotel;

/**
 * 酒店详情服务类
 * 处理酒店基础信息的业务逻辑
 */
class HotelDetailService extends BaseService
{
    /**
     * 获取酒店详细信息
     */
    public function getHotelDetail(int $hotelId): ?array
    {
        try {
            return  Hotel::with(['group','brand'])
                ->where('id', $hotelId)
                ->first()->toArray();

        } catch (\Exception $e) {
            $this->logError('获取酒店详情失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 更新酒店基础信息
     */
    public function updateBasicInfo(int $hotelId, array $data): bool
    {
        try {
            Db::beginTransaction();

            // 准备更新数据
            $updateData = [];
            
            // 基础字段
            $basicFields = [
                'language_code', 'active', 'currency', 'when_built', 
                'last_renovation', 'total_room_quantity', 'star_rating',
                'star_licence', 'hotel_building_area', 'receive_foreign_guest',
                'chain_code', 'chain_name', 'brand_code', 'brand_name'
            ];

            foreach ($basicFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            // JSON字段
            $jsonFields = [
                'hotel_names', 'hotel_briefs', 'hotel_descriptions',
                'important_informations', 'hotel_category', 'positions',
                'addresses', 'phones', 'emails'
            ];

            foreach ($jsonFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = json_encode($data[$field], JSON_UNESCAPED_UNICODE);
                }
            }

            // 更新时间
            $updateData['updated_at'] = date('Y-m-d H:i:s');

            // 执行更新
            $result = Db::table('hotels')
                ->where('id', $hotelId)
                ->update($updateData);

            if ($result) {
                // 记录操作日志
                $this->logHotelOperation($hotelId, 'update', 'basic_info', '更新酒店基础信息', [], $data);
                
                Db::commit();
                return true;
            } else {
                Db::rollback();
                return false;
            }
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('更新酒店基础信息失败', [
                'hotel_id' => $hotelId, 
                'data' => $data, 
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 记录酒店操作日志
     */
    protected function logHotelOperation(
        int $hotelId, 
        string $operationType, 
        string $operationModule, 
        string $description, 
        array $oldData = [], 
        array $newData = []
    ): void {
        try {
            Db::table('hotel_operation_logs')->insert([
                'hotel_id' => $hotelId,
                'operation_type' => $operationType,
                'operation_module' => $operationModule,
                'operation_description' => $description,
                'old_data' => !empty($oldData) ? json_encode($oldData, JSON_UNESCAPED_UNICODE) : null,
                'new_data' => !empty($newData) ? json_encode($newData, JSON_UNESCAPED_UNICODE) : null,
                'operator_id' => $this->getCurrentUserId(),
                'operator_name' => $this->getCurrentUserName(),
                'operator_ip' => $this->getClientIp(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            // 日志记录失败不应该影响主要业务流程
            error_log('记录酒店操作日志失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户ID（示例实现）
     */
    protected function getCurrentUserId(): ?int
    {
        return 1; // 临时返回固定值
    }

    /**
     * 更新联系信息
     */
    public function updateContactInfo(int $hotelId, array $data): bool
    {
        try {
            Db::beginTransaction();

            // 准备更新数据
            $updateData = [];

            // 联系信息字段
            $contactFields = [
                'phone', 'fax', 'email', 'website', 'booking_phone',
                'country', 'province', 'city', 'district', 'postal_code',
                'timezone', 'address', 'longitude', 'latitude',
                'wechat', 'weibo', 'facebook', 'instagram', 'twitter', 'linkedin',
                'reception_hours', 'checkin_time', 'checkout_time', 'restaurant_hours',
                'emergency_phone', 'security_phone', 'medical_phone', 'contact_notes'
            ];

            foreach ($contactFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            // 更新时间
            $updateData['updated_at'] = date('Y-m-d H:i:s');

            // 执行更新
            $result = Db::table('hotels')
                ->where('id', $hotelId)
                ->update($updateData);

            if ($result !== false) {
                Db::commit();
                $this->logInfo('联系信息更新成功', ['hotel_id' => $hotelId, 'data' => $updateData]);
                return true;
            } else {
                Db::rollback();
                return false;
            }
        } catch (\Exception $e) {
            Db::rollback();
            $this->logError('联系信息更新失败', ['hotel_id' => $hotelId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 获取当前用户名（示例实现）
     */
    protected function getCurrentUserName(): ?string
    {
        return 'admin'; // 临时返回固定值
    }

    /**
     * 获取客户端IP
     */
    protected function getClientIp(): string
    {
        $request = request();
        return $request->getRealIp();
    }
}
