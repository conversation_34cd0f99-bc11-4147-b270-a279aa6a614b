<?php

namespace app\model;

use support\Model;

class RoomTypePolicy extends Model
{
    protected $table = 'room_type_policies';

    protected $fillable = [
        'room_type_id',
        'cancellation_type',
        'free_cancellation_hours',
        'cancellation_fee_rate',
        'cancellation_policy',
        'min_check_in_age',
        'id_required',
        'credit_card_required',
        'deposit_amount',
        'deposit_type',
        'max_guests',
        'visitors_allowed',
        'visitor_hours',
        'parties_allowed',
        'smoking_allowed',
        'quiet_hours',
        'children_free_age',
        'pets_allowed',
        'pet_fee_per_night',
        'pet_restrictions',
        'children_facilities',
        'early_check_in_allowed',
        'late_check_out_allowed',
        'luggage_storage',
        'special_policies',
        'status',
    ];

    protected $casts = [
        'room_type_id' => 'integer',
        'free_cancellation_hours' => 'integer',
        'cancellation_fee_rate' => 'decimal:2',
        'min_check_in_age' => 'integer',
        'id_required' => 'boolean',
        'credit_card_required' => 'boolean',
        'deposit_amount' => 'decimal:2',
        'max_guests' => 'integer',
        'visitors_allowed' => 'boolean',
        'parties_allowed' => 'boolean',
        'smoking_allowed' => 'boolean',
        'children_free_age' => 'integer',
        'pets_allowed' => 'boolean',
        'pet_fee_per_night' => 'decimal:2',
        'early_check_in_allowed' => 'boolean',
        'late_check_out_allowed' => 'boolean',
        'luggage_storage' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}

