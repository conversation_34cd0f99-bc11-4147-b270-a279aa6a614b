<?php

namespace base\AuditLog\traits;

use base\AuditLog\AirField;

trait Struct
{
    /**
     * Returns the difference between two structures
     *
     * @param $struct1
     * @param $struct2
     *
     * @return array[]
     */
    private function diff($struct1, $struct2): array
    {
        $structPath1Array = [];
        $structPath2Array = [];
        $query            = $this->getListParse('');
        $this->structPathArray($struct1, $structPath1Array, "", $query, $query);
        $this->structPathArray($struct2, $structPath2Array, "", $query, $query);

        return $this->structPathArrayDiff($structPath1Array, $structPath2Array);
    }

    private function structPathArray($assocArray, &$array, $currentPath, AirField $query, AirField $queryOlf): void
    {
        if (is_array($assocArray)) {
            foreach ($assocArray as $key => $value) {
                if (isset($value)) {
                    $index_key = is_object($value) ? ($value->{$query->getIndex()} ?? $key) : ($value[$query->getIndex()] ?? $key);
                    $path      = $currentPath !== '' ? $currentPath . "." . $index_key : (string)$index_key;
                    if (is_array($value)) {
                        $this->structPathArray($value, $array, $path, $this->getListParse($key), $query);
                    } elseif (is_object($value)) {
                        $this->structPathArray((array)$value, $array, $path, $this->getListParse($key), $query);
                    } elseif ($path !== "") {
                        $array[$path] = $value;
                        if ($queryOlf->getIndex() && $alias = $queryOlf->getAlias($key)) {
                            $this->alias($path, $alias);
                            $this->title($alias, $queryOlf->getTitle($alias));
                            $arr = explode('.', $path);
                            if (count($arr) >= 2) {
                                $this->indexList[$path]  = $arr[count($arr) - 2];
                                $this->indexList2[$path] = $queryOlf->getAlias($queryOlf->getIndex());
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     *
     *
     * @param array $structPath1Array
     * @param array $structPath2Array
     *
     * @return array[]
     *
     * @copyright  2022/11/15 11:03
     * <AUTHOR> lixuecong <<EMAIL>>
     */
    private function structPathArrayDiff(array $structPath1Array, array $structPath2Array): array
    {
        $diffGatherArray = [
            "new"     => [],
            "removed" => [],
            "edited"  => [],
        ];

        foreach ($structPath1Array as $key1 => $value1) {
            if (array_key_exists($key1, $structPath2Array)) {
                if ($value1 !== $structPath2Array[$key1]) {
                    $edited                           = [
                        "oldValue" => $structPath2Array[$key1],
                        "newValue" => $value1,
                    ];
                    $diffGatherArray["edited"][$key1] = $edited;
                }
            } else {
                $diffGatherArray["new"][$key1] = $value1;
            }
        }

        $removedDiff = array_diff_key($structPath2Array, $structPath1Array);

        if ( ! empty($removedDiff)) {
            foreach ($removedDiff as $key => $value) {
                $diffGatherArray["removed"][$key] = $value;
            }
        }

        return $diffGatherArray;
    }
}
