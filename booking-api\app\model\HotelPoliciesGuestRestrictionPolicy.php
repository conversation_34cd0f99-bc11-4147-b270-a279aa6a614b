<?php

namespace app\model;

use support\Model;

/**
 * 酒店住客限制政策模型
 */
class HotelPoliciesGuestRestrictionPolicy extends Model
{
    protected $table = 'hotel_policies_guest_restriction_policies';
    protected $primaryKey = 'id';

    protected $fillable = [
        'hotel_id', 'max_guests_per_room', 'additional_guest_fee', 'visitor_policy', 'visitor_hours',
        'overnight_visitors', 'party_events_allowed', 'noise_restrictions', 'quiet_hours',
        'smoking_policy', 'alcohol_policy', 'age_restrictions', 'group_booking_restrictions',
        'restriction_notes', 'status',
    ];

    protected $casts = [
        'id' => 'integer', 'hotel_id' => 'integer', 'max_guests_per_room' => 'integer',
        'additional_guest_fee' => 'decimal:2', 'overnight_visitors' => 'boolean',
        'party_events_allowed' => 'boolean', 'created_at' => 'datetime', 'updated_at' => 'datetime',
    ];

    public function hotel() { return $this->belongsTo(Hotel::class, 'hotel_id', 'id'); }
    public function scopeActive($query) { return $query->where('status', 'active'); }
    public function scopeByHotel($query, $hotelId) { return $query->where('hotel_id', $hotelId); }
    public function getPolicyType(): string { return 'guest_restriction'; }
    public function getPolicyDisplayName(): string { return '住客限制'; }

    public static function createDefault(int $hotelId): self
    {
        return self::create([
            'hotel_id' => $hotelId, 'overnight_visitors' => false, 'party_events_allowed' => false,
            'smoking_policy' => 'non_smoking', 'status' => 'active'
        ]);
    }
}
