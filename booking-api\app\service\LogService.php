<?php

namespace app\service;

use support\Log;

/**
 * 日志服务类
 * 提供统一的日志记录功能
 */
class LogService
{
    /**
     * 日志类型
     */
    const TYPE_SYSTEM = 'system';
    const TYPE_BUSINESS = 'business';
    const TYPE_SECURITY = 'security';
    const TYPE_PERFORMANCE = 'performance';
    const TYPE_ERROR = 'error';
    const TYPE_API = 'api';
    const TYPE_DATABASE = 'database';
    const TYPE_CACHE = 'cache';

    /**
     * 日志级别
     */
    const LEVEL_DEBUG = 'debug';
    const LEVEL_INFO = 'info';
    const LEVEL_WARNING = 'warning';
    const LEVEL_ERROR = 'error';
    const LEVEL_CRITICAL = 'critical';

    /**
     * 记录系统日志
     *
     * @param string $level
     * @param string $message
     * @param array $context
     * @param string $type
     */
    public static function log(string $level, string $message, array $context = [], string $type = self::TYPE_SYSTEM)
    {
        $logData = [
            'type' => $type,
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'request_id' => self::getRequestId(),
            'user_id' => self::getCurrentUserId(),
            'ip' => self::getClientIp(),
            'user_agent' => self::getUserAgent()
        ];

        Log::$level($message, $logData);
    }

    /**
     * 记录业务日志
     *
     * @param string $action
     * @param string $message
     * @param array $data
     * @param string $level
     */
    public static function business(string $action, string $message, array $data = [], string $level = self::LEVEL_INFO)
    {
        self::log($level, $message, [
            'action' => $action,
            'data' => $data
        ], self::TYPE_BUSINESS);
    }

    /**
     * 记录安全日志
     *
     * @param string $event
     * @param string $message
     * @param array $data
     * @param string $level
     */
    public static function security(string $event, string $message, array $data = [], string $level = self::LEVEL_WARNING)
    {
        self::log($level, $message, [
            'event' => $event,
            'data' => $data
        ], self::TYPE_SECURITY);
    }

    /**
     * 记录性能日志
     *
     * @param string $operation
     * @param float $duration
     * @param array $data
     */
    public static function performance(string $operation, float $duration, array $data = [])
    {
        $level = $duration > 5.0 ? self::LEVEL_WARNING : self::LEVEL_INFO;
        
        self::log($level, "性能监控: {$operation}", [
            'operation' => $operation,
            'duration' => $duration,
            'data' => $data
        ], self::TYPE_PERFORMANCE);
    }

    /**
     * 记录错误日志
     *
     * @param \Exception $exception
     * @param string $operation
     * @param array $context
     */
    public static function error(\Exception $exception, string $operation = '', array $context = [])
    {
        self::log(self::LEVEL_ERROR, $exception->getMessage(), [
            'operation' => $operation,
            'exception' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'context' => $context
        ], self::TYPE_ERROR);
    }

    /**
     * 记录API日志
     *
     * @param string $method
     * @param string $url
     * @param array $request
     * @param array $response
     * @param float $duration
     * @param int $statusCode
     */
    public static function api(string $method, string $url, array $request = [], array $response = [], float $duration = 0, int $statusCode = 200)
    {
        $level = $statusCode >= 400 ? self::LEVEL_WARNING : self::LEVEL_INFO;
        
        self::log($level, "API请求: {$method} {$url}", [
            'method' => $method,
            'url' => $url,
            'request' => self::filterSensitiveData($request),
            'response' => self::filterSensitiveData($response),
            'duration' => $duration,
            'status_code' => $statusCode
        ], self::TYPE_API);
    }

    /**
     * 记录数据库日志
     *
     * @param string $query
     * @param array $bindings
     * @param float $duration
     */
    public static function database(string $query, array $bindings = [], float $duration = 0)
    {
        $level = $duration > 2.0 ? self::LEVEL_WARNING : self::LEVEL_DEBUG;
        
        self::log($level, "数据库查询", [
            'query' => $query,
            'bindings' => $bindings,
            'duration' => $duration
        ], self::TYPE_DATABASE);
    }

    /**
     * 记录缓存日志
     *
     * @param string $operation
     * @param string $key
     * @param bool $hit
     * @param float $duration
     */
    public static function cache(string $operation, string $key, bool $hit = true, float $duration = 0)
    {
        self::log(self::LEVEL_DEBUG, "缓存操作: {$operation}", [
            'operation' => $operation,
            'key' => $key,
            'hit' => $hit,
            'duration' => $duration
        ], self::TYPE_CACHE);
    }

    /**
     * 记录用户操作日志
     *
     * @param string $action
     * @param string $resource
     * @param array $data
     * @param int $userId
     */
    public static function userAction(string $action, string $resource, array $data = [], int $userId = null)
    {
        self::business('user_action', "用户操作: {$action} {$resource}", [
            'action' => $action,
            'resource' => $resource,
            'user_id' => $userId ?: self::getCurrentUserId(),
            'data' => $data
        ]);
    }

    /**
     * 记录订单操作日志
     *
     * @param string $action
     * @param int $bookingId
     * @param array $data
     * @param int $userId
     */
    public static function bookingAction(string $action, int $bookingId, array $data = [], int $userId = null)
    {
        self::business('booking_action', "订单操作: {$action}", [
            'action' => $action,
            'booking_id' => $bookingId,
            'user_id' => $userId ?: self::getCurrentUserId(),
            'data' => $data
        ]);
    }

    /**
     * 记录支付日志
     *
     * @param string $action
     * @param int $bookingId
     * @param float $amount
     * @param string $paymentMethod
     * @param array $data
     */
    public static function payment(string $action, int $bookingId, float $amount, string $paymentMethod, array $data = [])
    {
        self::business('payment', "支付操作: {$action}", [
            'action' => $action,
            'booking_id' => $bookingId,
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'data' => $data
        ]);
    }

    /**
     * 记录库存操作日志
     *
     * @param string $action
     * @param int $roomTypeId
     * @param string $date
     * @param int $quantity
     * @param array $data
     */
    public static function inventory(string $action, int $roomTypeId, string $date, int $quantity, array $data = [])
    {
        self::business('inventory', "库存操作: {$action}", [
            'action' => $action,
            'room_type_id' => $roomTypeId,
            'date' => $date,
            'quantity' => $quantity,
            'data' => $data
        ]);
    }

    /**
     * 记录同步日志
     *
     * @param string $channel
     * @param string $action
     * @param bool $success
     * @param array $data
     */
    public static function sync(string $channel, string $action, bool $success, array $data = [])
    {
        $level = $success ? self::LEVEL_INFO : self::LEVEL_ERROR;
        
        self::business('sync', "数据同步: {$channel} {$action}", [
            'channel' => $channel,
            'action' => $action,
            'success' => $success,
            'data' => $data
        ], $level);
    }

    /**
     * 过滤敏感数据
     *
     * @param array $data
     * @return array
     */
    private static function filterSensitiveData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_hash',
            'password_confirmation',
            'token',
            'api_key',
            'api_secret',
            'access_token',
            'refresh_token',
            'credit_card',
            'card_number',
            'cvv',
            'ssn',
            'id_card'
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***FILTERED***';
            }
        }

        return $data;
    }

    /**
     * 获取请求ID
     *
     * @return string
     */
    private static function getRequestId(): string
    {
        return $_SERVER['HTTP_X_REQUEST_ID'] ?? 'req_' . uniqid();
    }

    /**
     * 获取当前用户ID
     *
     * @return int|null
     */
    private static function getCurrentUserId(): ?int
    {
        // 这里需要根据实际的认证系统获取用户ID
        return $_SERVER['HTTP_X_USER_ID'] ?? null;
    }

    /**
     * 获取客户端IP
     *
     * @return string
     */
    private static function getClientIp(): string
    {
        return $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
               $_SERVER['HTTP_X_REAL_IP'] ?? 
               $_SERVER['REMOTE_ADDR'] ?? 
               'unknown';
    }

    /**
     * 获取用户代理
     *
     * @return string
     */
    private static function getUserAgent(): string
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }

    /**
     * 批量记录日志
     *
     * @param array $logs
     */
    public static function batch(array $logs)
    {
        foreach ($logs as $logEntry) {
            if (isset($logEntry['level'], $logEntry['message'])) {
                self::log(
                    $logEntry['level'],
                    $logEntry['message'],
                    $logEntry['context'] ?? [],
                    $logEntry['type'] ?? self::TYPE_SYSTEM
                );
            }
        }
    }

    /**
     * 清理过期日志
     *
     * @param int $days
     * @return bool
     */
    public static function cleanup(int $days = 30): bool
    {
        try {
            // 这里需要根据实际的日志存储实现清理逻辑
            self::log(self::LEVEL_INFO, "日志清理完成", [
                'days' => $days,
                'cleanup_time' => date('Y-m-d H:i:s')
            ]);
            
            return true;
        } catch (\Exception $e) {
            self::error($e, 'log_cleanup');
            return false;
        }
    }
}
