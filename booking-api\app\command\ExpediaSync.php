<?php

namespace app\command;

use app\model\Hotel;
use app\model\RoomType;
use app\service\expedia\ExpediaClient;
use support\Log;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

#[AsCommand('expedia:sync', 'Expedia数据同步脚本')]
class ExpediaSync extends Command
{
    /**
     * 配置命令
     * @return void
     */
    protected function configure()
    {
        $this
            ->addOption('type', 't', InputOption::VALUE_REQUIRED, '同步类型: hotels, availability, all', 'all')
            ->addOption('limit', 'l', InputOption::VALUE_REQUIRED, '限制数量', 100)
            ->addOption('property-id', 'p', InputOption::VALUE_REQUIRED, '指定酒店ID')
            ->addOption('start-date', 's', InputOption::VALUE_REQUIRED, '开始日期 (YYYY-MM-DD)')
            ->addOption('end-date', 'e', InputOption::VALUE_REQUIRED, '结束日期 (YYYY-MM-DD)');
    }

    /**
     * 执行命令
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $type = $input->getOption('type');
        $limit = (int)$input->getOption('limit');
        $propertyId = $input->getOption('property-id');
        $startDate = $input->getOption('start-date') ?: date('Y-m-d');
        $endDate = $input->getOption('end-date') ?: date('Y-m-d', strtotime('+30 days'));

        $output->writeln("<info>开始同步Expedia数据...</info>");
        $output->writeln("同步类型: $type");
        $output->writeln("限制数量: $limit");
        
        if ($propertyId) {
            $output->writeln("指定酒店ID: $propertyId");
        }
        
        $output->writeln("日期范围: $startDate 至 $endDate");

        try {
            $client = new ExpediaClient();
            
            // 根据同步类型执行不同的操作
            switch ($type) {
                case 'hotels':
                    $this->syncHotels($client, $output, $limit, $propertyId);
                    break;
                case 'availability':
                    $this->syncAvailability($client, $output, $limit, $propertyId, $startDate, $endDate);
                    break;
                case 'all':
                default:
                    $this->syncHotels($client, $output, $limit, $propertyId);
                    $this->syncAvailability($client, $output, $limit, $propertyId, $startDate, $endDate);
                    break;
            }
            
            $output->writeln("<info>Expedia数据同步完成!</info>");
            return self::SUCCESS;
        } catch (\Exception $e) {
            $output->writeln("<error>同步失败: " . $e->getMessage() . "</error>");
            Log::error("Expedia同步失败: " . $e->getMessage(), ['trace' => $e->getTraceAsString()]);
            return self::FAILURE;
        }
    }

    /**
     * 同步酒店数据
     * @param ExpediaClient $client
     * @param OutputInterface $output
     * @param int $limit
     * @param string|null $propertyId
     * @return void
     */
    protected function syncHotels(ExpediaClient $client, OutputInterface $output, int $limit, ?string $propertyId = null): void
    {
        $output->writeln("<info>开始同步酒店数据...</info>");
        
        // 构建请求参数
        $params = [
            'limit' => $limit,
            'language' => 'zh-CN',
            'include' => ['property_ids', 'property_details', 'room_types', 'images', 'facilities', 'policies']
        ];
        
        if ($propertyId) {
            $params['property_id'] = $propertyId;
        }
        
        // 发送请求获取酒店数据
        $result = $client->sendRequest(ExpediaClient::PROPERTIES_CONTENT, $params, 'GET');
        
        if (empty($result) || !isset($result['data']) || empty($result['data'])) {
            $output->writeln("<comment>未获取到酒店数据</comment>");
            return;
        }
        
        $hotels = $result['data'];
        $output->writeln("获取到 " . count($hotels) . " 个酒店数据");
        
        // 开始处理酒店数据
        $successCount = 0;
        $failCount = 0;
        
        foreach ($hotels as $hotelData) {
            try {
                $this->processHotelData($hotelData);
                $successCount++;
                $output->writeln("处理酒店: {$hotelData['property_id']} - {$hotelData['name']} 成功");
            } catch (\Exception $e) {
                $failCount++;
                $output->writeln("<error>处理酒店 {$hotelData['property_id']} 失败: {$e->getMessage()}</error>");
                Log::error("处理Expedia酒店数据失败", [
                    'property_id' => $hotelData['property_id'] ?? 'unknown',
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        $output->writeln("<info>酒店数据同步完成! 成功: $successCount, 失败: $failCount</info>");
    }

    /**
     * 处理酒店数据
     * @param array $hotelData
     * @return void
     */
    protected function processHotelData(array $hotelData): void
    {
        // 开始数据库事务
        Db::startTrans();
        
        try {
            // 检查酒店是否已存在
            $hotel = Hotel::where('hotel_code', $hotelData['property_id'])->first();
            
            if (!$hotel) {
                // 创建新酒店记录
                $hotel = new Hotel();
                $hotel->hotel_code = $hotelData['property_id'];
                $hotel->code = 'EX' . $hotelData['property_id'];
            }
            
            // 更新酒店基本信息
            $hotel->name = $hotelData['name'] ?? '';
            $hotel->name_en = $hotelData['name_en'] ?? '';
            $hotel->description = $hotelData['description'] ?? '';
            
            // 地址信息
            if (isset($hotelData['address'])) {
                $hotel->country = $hotelData['address']['country_code'] ?? '';
                $hotel->province = $hotelData['address']['state_province_name'] ?? '';
                $hotel->city = $hotelData['address']['city'] ?? '';
                $hotel->district = $hotelData['address']['district'] ?? '';
                $hotel->address = $hotelData['address']['line1'] ?? '';
                $hotel->postal_code = $hotelData['address']['postal_code'] ?? '';
            }
            
            // 坐标信息
            if (isset($hotelData['coordinates'])) {
                $hotel->latitude = $hotelData['coordinates']['latitude'] ?? 0;
                $hotel->longitude = $hotelData['coordinates']['longitude'] ?? 0;
            }
            
            // 星级
            $hotel->star_rating = $hotelData['star_rating'] ?? 0;
            
            // 房间总数
            $hotel->total_rooms = $hotelData['room_count'] ?? 0;
            
            // 联系信息
            $hotel->phone = $hotelData['phone'] ?? '';
            $hotel->fax = $hotelData['fax'] ?? '';
            $hotel->email = $hotelData['email'] ?? '';
            $hotel->website = $hotelData['website'] ?? '';
            
            // 入住和退房时间
            if (isset($hotelData['check_in_time'])) {
                $hotel->check_in_time = $hotelData['check_in_time'];
            }
            if (isset($hotelData['check_out_time'])) {
                $hotel->check_out_time = $hotelData['check_out_time'];
            }
            
            // 政策信息
            $hotel->cancellation_policy = $hotelData['cancellation_policy'] ?? '';
            $hotel->child_policy = $hotelData['child_policy'] ?? '';
            $hotel->pet_policy = $hotelData['pet_policy'] ?? '';
            
            // 设施和服务
            if (isset($hotelData['facilities']) && is_array($hotelData['facilities'])) {
                $hotel->facilities = $hotelData['facilities'];
            }
            
            if (isset($hotelData['services']) && is_array($hotelData['services'])) {
                $hotel->services = $hotelData['services'];
            }
            
            // 图片
            if (isset($hotelData['images']) && is_array($hotelData['images'])) {
                $images = [];
                foreach ($hotelData['images'] as $image) {
                    if (isset($image['url'])) {
                        $images[] = $image['url'];
                    }
                }
                $hotel->images = $images;
            }
            
            // 设置状态为活跃
            $hotel->status = Hotel::STATUS_ACTIVE;
            
            // 保存酒店信息
            $hotel->save();
            
            // 处理房型信息
            if (isset($hotelData['room_types']) && is_array($hotelData['room_types'])) {
                $this->processRoomTypes($hotel, $hotelData['room_types']);
            }
            
            // 提交事务
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 处理房型数据
     * @param Hotel $hotel
     * @param array $roomTypesData
     * @return void
     */
    protected function processRoomTypes(Hotel $hotel, array $roomTypesData): void
    {
        foreach ($roomTypesData as $roomTypeData) {
            try {
                // 检查房型是否已存在
                $roomType = RoomType::where('hotel_id', $hotel->id)
                    ->where('code', $roomTypeData['room_type_id'])
                    ->first();
                
                if (!$roomType) {
                    // 创建新房型记录
                    $roomType = new RoomType();
                    $roomType->hotel_id = $hotel->id;
                    $roomType->code = $roomTypeData['room_type_id'];
                }
                
                // 更新房型基本信息
                $roomType->name = $roomTypeData['name'] ?? '';
                $roomType->name_en = $roomTypeData['name_en'] ?? '';
                $roomType->description = $roomTypeData['description'] ?? '';
                
                // 房间面积
                $roomType->area = $roomTypeData['area'] ?? 0;
                
                // 入住人数
                $roomType->max_occupancy = $roomTypeData['max_occupancy'] ?? 2;
                $roomType->max_adults = $roomTypeData['max_adults'] ?? 2;
                $roomType->max_children = $roomTypeData['max_children'] ?? 0;
                
                // 床型信息
                $roomType->bed_type = $roomTypeData['bed_type'] ?? RoomType::BED_TYPE_DOUBLE;
                $roomType->bed_count = $roomTypeData['bed_count'] ?? 1;
                
                // 视图类型
                $roomType->view_type = $roomTypeData['view_type'] ?? '';
                
                // 楼层信息
                $roomType->floor_range = $roomTypeData['floor_range'] ?? '';
                
                // 房间总数
                $roomType->total_rooms = $roomTypeData['total_rooms'] ?? 0;
                
                // 房间类别和等级
                $roomType->room_category = $roomTypeData['room_category'] ?? 'standard';
                $roomType->room_grade = $roomTypeData['room_grade'] ?? 'standard';
                
                // 吸烟政策
                $roomType->smoking_policy = $roomTypeData['smoking_policy'] ?? 'non_smoking';
                
                // 浴室类型
                $roomType->bathroom_type = $roomTypeData['bathroom_type'] ?? 'private';
                
                // 设施信息
                if (isset($roomTypeData['amenities']) && is_array($roomTypeData['amenities'])) {
                    $roomType->amenities = $roomTypeData['amenities'];
                }
                
                if (isset($roomTypeData['bathroom_amenities']) && is_array($roomTypeData['bathroom_amenities'])) {
                    $roomType->bathroom_amenities = $roomTypeData['bathroom_amenities'];
                }
                
                // 图片
                if (isset($roomTypeData['images']) && is_array($roomTypeData['images'])) {
                    $images = [];
                    foreach ($roomTypeData['images'] as $image) {
                        if (isset($image['url'])) {
                            $images[] = $image['url'];
                        }
                    }
                    $roomType->images = $images;
                }
                
                // 价格信息
                $roomType->base_price = $roomTypeData['base_price'] ?? 0;
                $roomType->extra_bed_price = $roomTypeData['extra_bed_price'] ?? 0;
                $roomType->child_price = $roomTypeData['child_price'] ?? 0;
                
                // 取消政策
                $roomType->cancellation_policy = $roomTypeData['cancellation_policy'] ?? '';
                
                // 设置状态为活跃
                $roomType->status = RoomType::STATUS_ACTIVE;
                
                // 保存房型信息
                $roomType->save();
            } catch (\Exception $e) {
                Log::error("处理房型数据失败", [
                    'hotel_id' => $hotel->id,
                    'room_type_id' => $roomTypeData['room_type_id'] ?? 'unknown',
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // 继续处理下一个房型，不影响整体流程
            }
        }
    }

    /**
     * 同步可用性和价格数据
     * @param ExpediaClient $client
     * @param OutputInterface $output
     * @param int $limit
     * @param string|null $propertyId
     * @param string $startDate
     * @param string $endDate
     * @return void
     */
    protected function syncAvailability(ExpediaClient $client, OutputInterface $output, int $limit, ?string $propertyId = null, string $startDate = null, string $endDate = null): void
    {
        $output->writeln("<info>开始同步可用性和价格数据...</info>");
        
        // 如果没有指定酒店ID，则获取系统中的酒店列表
        if (!$propertyId) {
            $hotels = Hotel::where('status', Hotel::STATUS_ACTIVE)
                ->limit($limit)
                ->select(['id', 'hotel_code'])
                ->get();
                
            if ($hotels->isEmpty()) {
                $output->writeln("<comment>系统中没有可同步的酒店</comment>");
                return;
            }
            
            $output->writeln("获取到 " . count($hotels) . " 个需要同步的酒店");
            
            // 遍历酒店列表，同步每个酒店的可用性和价格数据
            $successCount = 0;
            $failCount = 0;
            
            foreach ($hotels as $hotel) {
                try {
                    $this->syncHotelAvailability($client, $output, $hotel->hotel_code, $startDate, $endDate);
                    $successCount++;
                } catch (\Exception $e) {
                    $failCount++;
                    $output->writeln("<error>同步酒店 {$hotel->hotel_code} 可用性数据失败: {$e->getMessage()}</error>");
                    Log::error("同步Expedia酒店可用性数据失败", [
                        'hotel_code' => $hotel->hotel_code,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }
            
            $output->writeln("<info>可用性和价格数据同步完成! 成功: $successCount, 失败: $failCount</info>");
        } else {
            // 同步指定酒店的可用性和价格数据
            $this->syncHotelAvailability($client, $output, $propertyId, $startDate, $endDate);
        }
    }

    /**
     * 同步单个酒店的可用性和价格数据
     * @param ExpediaClient $client
     * @param OutputInterface $output
     * @param string $propertyId
     * @param string $startDate
     * @param string $endDate
     * @return void
     */
    protected function syncHotelAvailability(ExpediaClient $client, OutputInterface $output, string $propertyId, string $startDate, string $endDate): void
    {
        $output->writeln("同步酒店 $propertyId 的可用性和价格数据...");
        
        // 构建请求参数
        $params = [
            'property_id' => $propertyId,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'language' => 'zh-CN',
            'currency' => 'CNY',
            'occupancy' => [
                'adults' => 2,
                'children' => []
            ]
        ];
        
        // 发送请求获取可用性和价格数据
        $endpoint = str_replace('{property_id}', $propertyId, ExpediaClient::PROPERTIES_AVAILABILITY_DETAIL);
        $result = $client->sendRequest($endpoint, $params, 'GET');
        
        if (empty($result) || !isset($result['data']) || empty($result['data'])) {
            $output->writeln("<comment>未获取到酒店 $propertyId 的可用性和价格数据</comment>");
            return;
        }
        
        // 获取酒店记录
        $hotel = Hotel::where('hotel_code', $propertyId)->first();
        
        if (!$hotel) {
            $output->writeln("<comment>系统中不存在酒店 $propertyId 的记录</comment>");
            return;
        }
        
        // 处理可用性和价格数据
        $availabilityData = $result['data'];
        
        // 开始数据库事务
        Db::startTrans();
        
        try {
            // 处理房型可用性和价格数据
            if (isset($availabilityData['room_types']) && is_array($availabilityData['room_types'])) {
                foreach ($availabilityData['room_types'] as $roomTypeData) {
                    $this->processRoomAvailability($hotel, $roomTypeData, $startDate, $endDate);
                }
            }
            
            // 提交事务
            Db::commit();
            $output->writeln("酒店 $propertyId 的可用性和价格数据同步成功");
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 处理房型可用性和价格数据
     * @param Hotel $hotel
     * @param array $roomTypeData
     * @param string $startDate
     * @param string $endDate
     * @return void
     */
    protected function processRoomAvailability(Hotel $hotel, array $roomTypeData, string $startDate, string $endDate): void
    {
        // 获取房型记录
        $roomType = RoomType::where('hotel_id', $hotel->id)
            ->where('code', $roomTypeData['room_type_id'])
            ->first();
        
        if (!$roomType) {
            Log::warning("系统中不存在房型记录", [
                'hotel_id' => $hotel->id,
                'room_type_id' => $roomTypeData['room_type_id']
            ]);
            return;
        }
        
        // 处理日期范围内的可用性和价格数据
        $currentDate = new \DateTime($startDate);
        $endDateTime = new \DateTime($endDate);
        
        while ($currentDate <= $endDateTime) {
            $dateString = $currentDate->format('Y-m-d');
            
            // 获取当前日期的可用性数据
            $dailyData = $this->getDailyAvailability($roomTypeData, $dateString);
            
            if ($dailyData) {
                // 更新库存
                $this->updateRoomInventory($roomType, $dateString, $dailyData);
                
                // 更新价格
                $this->updateRoomRate($roomType, $dateString, $dailyData);
            }
            
            // 增加一天
            $currentDate->modify('+1 day');
        }
    }

    /**
     * 获取指定日期的可用性数据
     * @param array $roomTypeData
     * @param string $date
     * @return array|null
     */
    protected function getDailyAvailability(array $roomTypeData, string $date): ?array
    {
        if (!isset($roomTypeData['availability']) || !is_array($roomTypeData['availability'])) {
            return null;
        }
        
        foreach ($roomTypeData['availability'] as $dailyData) {
            if (isset($dailyData['date']) && $dailyData['date'] === $date) {
                return $dailyData;
            }
        }
        
        return null;
    }

    /**
     * 更新房间库存
     * @param RoomType $roomType
     * @param string $date
     * @param array $dailyData
     * @return void
     */
    protected function updateRoomInventory(RoomType $roomType, string $date, array $dailyData): void
    {
        // 获取当前日期的库存记录
        $inventory = $roomType->getInventoryForDate($date);
        
        if (!$inventory) {
            // 创建新的库存记录
            $inventory = new \app\model\RoomInventory();
            $inventory->hotel_id = $roomType->hotel_id;
            $inventory->room_type_id = $roomType->id;
            $inventory->date = $date;
        }
        
        // 更新库存数据
        $inventory->total_rooms = $roomType->total_rooms;
        $inventory->available_rooms = $dailyData['available_rooms'] ?? 0;
        $inventory->booked_rooms = $inventory->total_rooms - $inventory->available_rooms;
        $inventory->status = ($inventory->available_rooms > 0) ? 'available' : 'sold_out';
        
        // 保存库存记录
        $inventory->save();
    }

    /**
     * 更新房间价格
     * @param RoomType $roomType
     * @param string $date
     * @param array $dailyData
     * @return void
     */
    protected function updateRoomRate(RoomType $roomType, string $date, array $dailyData): void
    {
        // 获取当前日期的价格记录
        $rate = $roomType->getRateForDate($date);
        
        if (!$rate) {
            // 创建新的价格记录
            $rate = new \app\model\RoomRate();
            $rate->hotel_id = $roomType->hotel_id;
            $rate->room_type_id = $roomType->id;
            $rate->date = $date;
        }
        
        // 更新价格数据
        if (isset($dailyData['rates']) && is_array($dailyData['rates']) && !empty($dailyData['rates'])) {
            $rateData = $dailyData['rates'][0]; // 取第一个价格方案
            
            $rate->base_price = $rateData['base_rate'] ?? 0;
            $rate->sell_price = $rateData['total_rate'] ?? 0;
            $rate->discount_price = $rateData['discount_rate'] ?? 0;
            $rate->tax_price = $rateData['tax_rate'] ?? 0;
            $rate->currency = $rateData['currency'] ?? 'CNY';
        } else {
            // 如果没有价格数据，使用房型的基础价格
            $rate->base_price = $roomType->base_price;
            $rate->sell_price = $roomType->base_price;
            $rate->discount_price = 0;
            $rate->tax_price = 0;
            $rate->currency = 'CNY';
        }
        
        // 保存价格记录
        $rate->save();
    }
}

