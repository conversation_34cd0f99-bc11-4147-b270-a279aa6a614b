<?php

namespace app\service;

use app\exception\ValidationException;

/**
 * 数据验证服务类
 * 提供统一的数据验证功能
 */
class ValidationService
{
    /**
     * 验证规则
     */
    private static $rules = [
        'required' => '必填字段',
        'email' => '邮箱格式',
        'phone' => '手机号格式',
        'date' => '日期格式',
        'datetime' => '日期时间格式',
        'integer' => '整数',
        'positive' => '正数',
        'decimal' => '小数',
        'boolean' => '布尔值',
        'url' => 'URL格式',
        'json' => 'JSON格式',
        'array' => '数组格式',
        'string' => '字符串',
        'min' => '最小值',
        'max' => '最大值',
        'length' => '长度',
        'in' => '枚举值',
        'regex' => '正则表达式'
    ];

    /**
     * 验证数据
     *
     * @param array $data 要验证的数据
     * @param array $rules 验证规则
     * @return array 验证结果
     */
    public static function validate(array $data, array $rules): array
    {
        $errors = [];

        foreach ($rules as $field => $rule) {
            $fieldErrors = self::validateField($data, $field, $rule);
            if (!empty($fieldErrors)) {
                $errors[$field] = $fieldErrors;
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证单个字段
     *
     * @param array $data
     * @param string $field
     * @param string|array $rules
     * @return array
     */
    private static function validateField(array $data, string $field, $rules): array
    {
        $errors = [];
        $value = $data[$field] ?? null;

        // 将规则字符串转换为数组
        if (is_string($rules)) {
            $rules = explode('|', $rules);
        }

        foreach ($rules as $rule) {
            $error = self::applyRule($field, $value, $rule);
            if ($error) {
                $errors[] = $error;
            }
        }

        return $errors;
    }

    /**
     * 应用验证规则
     *
     * @param string $field
     * @param mixed $value
     * @param string $rule
     * @return string|null
     */
    private static function applyRule(string $field, $value, string $rule): ?string
    {
        // 解析规则参数
        $ruleParts = explode(':', $rule, 2);
        $ruleName = $ruleParts[0];
        $ruleParam = $ruleParts[1] ?? null;

        switch ($ruleName) {
            case 'required':
                if ($value === null || $value === '' || $value === []) {
                    return "字段 {$field} 是必需的";
                }
                break;

            case 'email':
                if ($value !== null && $value !== '' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "字段 {$field} 必须是有效的邮箱地址";
                }
                break;

            case 'phone':
                if ($value !== null && $value !== '' && !preg_match('/^1[3-9]\d{9}$/', $value)) {
                    return "字段 {$field} 必须是有效的手机号码";
                }
                break;

            case 'date':
                if ($value !== null && $value !== '' && !self::validateDate($value)) {
                    return "字段 {$field} 必须是有效的日期格式(Y-m-d)";
                }
                break;

            case 'datetime':
                if ($value !== null && $value !== '' && !self::validateDateTime($value)) {
                    return "字段 {$field} 必须是有效的日期时间格式(Y-m-d H:i:s)";
                }
                break;

            case 'integer':
                if ($value !== null && $value !== '' && (!is_numeric($value) || (int)$value != $value)) {
                    return "字段 {$field} 必须是整数";
                }
                break;

            case 'positive':
                if ($value !== null && $value !== '' && (!is_numeric($value) || $value <= 0)) {
                    return "字段 {$field} 必须是正数";
                }
                break;

            case 'decimal':
                if ($value !== null && $value !== '' && !is_numeric($value)) {
                    return "字段 {$field} 必须是数字";
                }
                break;

            case 'boolean':
                if ($value !== null && !is_bool($value) && !in_array($value, [0, 1, '0', '1', 'true', 'false'], true)) {
                    return "字段 {$field} 必须是布尔值";
                }
                break;

            case 'url':
                if ($value !== null && $value !== '' && !filter_var($value, FILTER_VALIDATE_URL)) {
                    return "字段 {$field} 必须是有效的URL";
                }
                break;

            case 'json':
                if ($value !== null && $value !== '') {
                    json_decode($value);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return "字段 {$field} 必须是有效的JSON格式";
                    }
                }
                break;

            case 'array':
                if ($value !== null && !is_array($value)) {
                    return "字段 {$field} 必须是数组";
                }
                break;

            case 'string':
                if ($value !== null && !is_string($value)) {
                    return "字段 {$field} 必须是字符串";
                }
                break;

            case 'min':
                if ($value !== null && $ruleParam !== null) {
                    if (is_numeric($value) && $value < $ruleParam) {
                        return "字段 {$field} 不能小于 {$ruleParam}";
                    }
                    if (is_string($value) && strlen($value) < $ruleParam) {
                        return "字段 {$field} 长度不能小于 {$ruleParam}";
                    }
                    if (is_array($value) && count($value) < $ruleParam) {
                        return "字段 {$field} 元素个数不能小于 {$ruleParam}";
                    }
                }
                break;

            case 'max':
                if ($value !== null && $ruleParam !== null) {
                    if (is_numeric($value) && $value > $ruleParam) {
                        return "字段 {$field} 不能大于 {$ruleParam}";
                    }
                    if (is_string($value) && strlen($value) > $ruleParam) {
                        return "字段 {$field} 长度不能大于 {$ruleParam}";
                    }
                    if (is_array($value) && count($value) > $ruleParam) {
                        return "字段 {$field} 元素个数不能大于 {$ruleParam}";
                    }
                }
                break;

            case 'length':
                if ($value !== null && $ruleParam !== null) {
                    $length = is_string($value) ? strlen($value) : (is_array($value) ? count($value) : 0);
                    if ($length != $ruleParam) {
                        return "字段 {$field} 长度必须等于 {$ruleParam}";
                    }
                }
                break;

            case 'in':
                if ($value !== null && $ruleParam !== null) {
                    $allowedValues = explode(',', $ruleParam);
                    if (!in_array($value, $allowedValues, true)) {
                        return "字段 {$field} 必须是以下值之一: " . implode(', ', $allowedValues);
                    }
                }
                break;

            case 'regex':
                if ($value !== null && $value !== '' && $ruleParam !== null) {
                    if (!preg_match($ruleParam, $value)) {
                        return "字段 {$field} 格式不正确";
                    }
                }
                break;
        }

        return null;
    }

    /**
     * 验证日期格式
     *
     * @param string $date
     * @param string $format
     * @return bool
     */
    private static function validateDate(string $date, string $format = 'Y-m-d'): bool
    {
        $d = \DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    /**
     * 验证日期时间格式
     *
     * @param string $datetime
     * @param string $format
     * @return bool
     */
    private static function validateDateTime(string $datetime, string $format = 'Y-m-d H:i:s'): bool
    {
        $d = \DateTime::createFromFormat($format, $datetime);
        return $d && $d->format($format) === $datetime;
    }

    /**
     * 验证日期范围
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function validateDateRange(string $startDate, string $endDate): array
    {
        $errors = [];

        if (!self::validateDate($startDate)) {
            $errors[] = '开始日期格式错误';
        }

        if (!self::validateDate($endDate)) {
            $errors[] = '结束日期格式错误';
        }

        if (empty($errors) && $startDate > $endDate) {
            $errors[] = '开始日期不能晚于结束日期';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证价格数据
     *
     * @param mixed $price
     * @return array
     */
    public static function validatePrice($price): array
    {
        $errors = [];

        if (!is_numeric($price)) {
            $errors[] = '价格必须是数字';
        } elseif ($price < 0) {
            $errors[] = '价格不能为负数';
        } elseif ($price > 999999.99) {
            $errors[] = '价格不能超过999999.99';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 验证库存数量
     *
     * @param mixed $quantity
     * @return array
     */
    public static function validateQuantity($quantity): array
    {
        $errors = [];

        if (!is_numeric($quantity)) {
            $errors[] = '数量必须是数字';
        } elseif ((int)$quantity != $quantity) {
            $errors[] = '数量必须是整数';
        } elseif ($quantity < 0) {
            $errors[] = '数量不能为负数';
        } elseif ($quantity > 9999) {
            $errors[] = '数量不能超过9999';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 获取所有验证规则
     *
     * @return array
     */
    public static function getAllRules(): array
    {
        return self::$rules;
    }

    /**
     * 验证数据并在失败时抛出异常
     *
     * @param array $data 要验证的数据
     * @param array $rules 验证规则
     * @param string $message 自定义错误消息
     * @throws ValidationException
     */
    public static function validateOrFail(array $data, array $rules, string $message = '数据验证失败'): void
    {
        $result = self::validate($data, $rules);

        if (!$result['valid']) {
            throw ValidationException::withErrors($result['errors'], $message);
        }
    }

    /**
     * 验证单个字段并在失败时抛出异常
     *
     * @param string $field
     * @param mixed $value
     * @param string|array $rules
     * @param string $message
     * @throws ValidationException
     */
    public static function validateFieldOrFail(string $field, $value, $rules, string $message = '字段验证失败'): void
    {
        $data = [$field => $value];
        $ruleSet = [$field => $rules];

        self::validateOrFail($data, $ruleSet, $message);
    }

    /**
     * 快速验证必填字段
     *
     * @param array $data
     * @param array $requiredFields
     * @param string $message
     * @throws ValidationException
     */
    public static function validateRequiredOrFail(array $data, array $requiredFields, string $message = '必填字段验证失败'): void
    {
        $rules = [];
        foreach ($requiredFields as $field) {
            $rules[$field] = 'required';
        }

        self::validateOrFail($data, $rules, $message);
    }
}
