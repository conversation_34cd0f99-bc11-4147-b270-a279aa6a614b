<?php

namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

/**
 * CORS 中间件
 * 处理跨域请求
 */
class CorsMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $next): Response
    {
        // 处理预检请求
        if ($request->method() === 'OPTIONS') {
            return $this->handlePreflightRequest();
        }

        // 处理实际请求
        $response = $next($request);
        
        return $this->addCorsHeaders($response);
    }

    /**
     * 处理预检请求
     */
    private function handlePreflightRequest(): Response
    {
        return response('', 200, $this->getCorsHeaders());
    }

    /**
     * 添加 CORS 头部
     */
    private function addCorsHeaders(Response $response): Response
    {
        $headers = $this->getCorsHeaders();
        
        foreach ($headers as $key => $value) {
            $response->header($key, $value);
        }
        
        return $response;
    }

    /**
     * 获取 CORS 头部
     */
    private function getCorsHeaders(): array
    {
        return [
            'Access-Control-Allow-Origin' => $this->getAllowedOrigin(),
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With, X-API-Key, Accept',
            'Access-Control-Allow-Credentials' => 'true',
            'Access-Control-Max-Age' => '86400',
            'Access-Control-Expose-Headers' => 'X-Total-Count, X-Page-Count'
        ];
    }

    /**
     * 获取允许的源
     */
    private function getAllowedOrigin(): string
    {
        // 在生产环境中，应该配置具体的域名
        $allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:5173',
            'http://localhost:8080',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:5173',
            'http://127.0.0.1:8080'
        ];

        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        if (in_array($origin, $allowedOrigins) || config('app.env') === 'local') {
            return $origin ?: '*';
        }

        return config('app.cors.allowed_origins', '*');
    }
}
