<?php

namespace app\controller;

use app\service\InventoryService;
use support\Request;
use support\Response;

/**
 * 库存管理控制器
 */
class InventoryController extends BaseController
{
    /**
     * 库存服务
     *
     * @var InventoryService
     */
    private $inventoryService;

    public function __construct()
    {
        $this->inventoryService = new InventoryService();
    }

    /**
     * 查询库存
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($params['start_date'] > $params['end_date']) {
                return $this->error('开始日期不能晚于结束日期');
            }

            $result = $this->inventoryService->queryInventory($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '查询库存');
        }
    }

    /**
     * 批量查询库存
     *
     * @param Request $request
     * @return Response
     */
    public function batchQuery(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'room_type_ids', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证房型ID数组
            if (!is_array($params['room_type_ids'])) {
                return $this->error('room_type_ids必须是数组');
            }

            foreach ($params['room_type_ids'] as $roomTypeId) {
                if (!is_numeric($roomTypeId) || (int)$roomTypeId <= 0) {
                    return $this->error('房型ID必须是正整数');
                }
            }

            // 验证日期范围
            if ($params['start_date'] > $params['end_date']) {
                return $this->error('开始日期不能晚于结束日期');
            }

            $result = $this->inventoryService->batchQueryInventory($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量查询库存');
        }
    }

    /**
     * 更新库存
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'room_type_id', 'date', 'available_rooms']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'date' => 'date',
                'available_rooms' => 'integer',
                'total_rooms' => 'integer',
                'booked_rooms' => 'integer',
                'blocked_rooms' => 'integer',
                'maintenance_rooms' => 'integer',
                'min_stay' => 'integer',
                'max_stay' => 'integer',
                'advance_booking_days' => 'integer',
                'oversell_limit' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证库存数量
            if ($data['available_rooms'] < 0) {
                return $this->error('可售房间数不能为负数');
            }

            $result = $this->inventoryService->updateInventory($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新库存');
        }
    }

    /**
     * 批量更新库存
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdate(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['updates']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            if (!is_array($data['updates'])) {
                return $this->error('updates必须是数组');
            }

            if (empty($data['updates'])) {
                return $this->error('updates不能为空');
            }

            if (count($data['updates']) > 100) {
                return $this->error('单次最多更新100条记录');
            }

            // 验证每个更新项
            foreach ($data['updates'] as $index => $update) {
                $updateErrors = $this->validateRequired($update, ['hotel_id', 'room_type_id', 'date', 'available_rooms']);
                if ($updateErrors) {
                    return $this->error("第" . ($index + 1) . "条记录参数验证失败", 400, $updateErrors);
                }

                $formatErrors = $this->validateFormat($update, [
                    'hotel_id' => 'integer',
                    'room_type_id' => 'integer',
                    'date' => 'date',
                    'available_rooms' => 'integer'
                ]);
                if ($formatErrors) {
                    return $this->error("第" . ($index + 1) . "条记录数据格式错误", 400, $formatErrors);
                }

                if ($update['available_rooms'] < 0) {
                    return $this->error("第" . ($index + 1) . "条记录可售房间数不能为负数");
                }
            }

            $result = $this->inventoryService->batchUpdateInventory($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新库存');
        }
    }

    /**
     * 锁定库存
     *
     * @param Request $request
     * @return Response
     */
    public function lock(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'room_type_id', 'check_in_date', 'check_out_date', 'rooms']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'check_in_date' => 'date',
                'check_out_date' => 'date',
                'rooms' => 'positive'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['check_in_date'] >= $data['check_out_date']) {
                return $this->error('入住日期必须早于离店日期');
            }

            // 验证房间数量
            if ($data['rooms'] <= 0) {
                return $this->error('房间数量必须大于0');
            }

            $result = $this->inventoryService->lockInventory($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '锁定库存');
        }
    }

    /**
     * 释放库存
     *
     * @param Request $request
     * @return Response
     */
    public function release(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'room_type_id', 'check_in_date', 'check_out_date', 'rooms']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'check_in_date' => 'date',
                'check_out_date' => 'date',
                'rooms' => 'positive'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证日期范围
            if ($data['check_in_date'] >= $data['check_out_date']) {
                return $this->error('入住日期必须早于离店日期');
            }

            // 验证房间数量
            if ($data['rooms'] <= 0) {
                return $this->error('房间数量必须大于0');
            }

            $result = $this->inventoryService->releaseInventory($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '释放库存');
        }
    }

    /**
     * 获取库存统计
     *
     * @param Request $request
     * @return Response
     */
    public function statistics(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 查询库存数据
            $inventoryResult = $this->inventoryService->queryInventory($params);
            if ($inventoryResult['code'] !== 0) {
                return $this->handleServiceResult($inventoryResult);
            }

            $inventory = $inventoryResult['data'];

            // 计算统计数据
            $statistics = [
                'total_days' => count($inventory),
                'total_rooms' => array_sum(array_column($inventory, 'total_rooms')),
                'total_available' => array_sum(array_column($inventory, 'available_rooms')),
                'total_booked' => array_sum(array_column($inventory, 'booked_rooms')),
                'total_blocked' => array_sum(array_column($inventory, 'blocked_rooms')),
                'total_maintenance' => array_sum(array_column($inventory, 'maintenance_rooms')),
                'avg_occupancy_rate' => 0,
                'closed_days' => 0
            ];

            if ($statistics['total_days'] > 0) {
                $occupancyRates = [];
                $closedDays = 0;

                foreach ($inventory as $item) {
                    if ($item['is_closed']) {
                        $closedDays++;
                    }
                    
                    if ($item['total_rooms'] > 0) {
                        $occupancyRates[] = ($item['booked_rooms'] / $item['total_rooms']) * 100;
                    }
                }

                $statistics['avg_occupancy_rate'] = count($occupancyRates) > 0 
                    ? round(array_sum($occupancyRates) / count($occupancyRates), 2) 
                    : 0;
                $statistics['closed_days'] = $closedDays;
            }

            return $this->success([
                'statistics' => $statistics,
                'date_range' => [
                    'start_date' => $params['start_date'],
                    'end_date' => $params['end_date']
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取库存统计');
        }
    }

    /**
     * 获取库存分配信息
     *
     * @param Request $request
     * @return Response
     */
    public function allocation(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'room_type_id', 'date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->inventoryService->getInventoryAllocation($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取库存分配信息');
        }
    }

    /**
     * 更新库存分配信息
     *
     * @param Request $request
     * @return Response
     */
    public function updateAllocation(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'room_type_id', 'date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'date' => 'date',
                'contracted_rooms' => 'integer',
                'released_rooms' => 'integer',
                'pickup_rooms' => 'integer',
                'no_show_rooms' => 'integer',
                'walk_in_rooms' => 'integer',
                'upgrade_rooms' => 'integer',
                'downgrade_rooms' => 'integer',
                'comp_rooms' => 'integer',
                'house_use_rooms' => 'integer',
                'out_of_order_rooms' => 'integer',
                'repair_rooms' => 'integer',
                'deep_clean_rooms' => 'integer',
                'renovation_rooms' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->inventoryService->updateInventoryAllocation($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新库存分配信息');
        }
    }

    /**
     * 获取收益管理信息
     *
     * @param Request $request
     * @return Response
     */
    public function revenueManagement(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->inventoryService->getRevenueManagementInfo($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取收益管理信息');
        }
    }

    /**
     * 更新收益管理信息
     *
     * @param Request $request
     * @return Response
     */
    public function updateRevenueManagement(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'room_type_id', 'date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'date' => 'date',
                'adr_actual' => 'numeric',
                'revpar_actual' => 'numeric',
                'adr_budget' => 'numeric',
                'revpar_budget' => 'numeric',
                'adr_forecast' => 'numeric',
                'revpar_forecast' => 'numeric',
                'pickup_percentage' => 'numeric',
                'wash_factor' => 'numeric',
                'displacement_factor' => 'numeric',
                'booking_pace' => 'numeric',
                'cancellation_rate' => 'numeric',
                'no_show_rate' => 'numeric',
                'seasonal_adjustment' => 'numeric',
                'demand_adjustment' => 'numeric',
                'competitive_adjustment' => 'numeric',
                'holiday_premium' => 'numeric',
                'weekend_premium' => 'numeric',
                'event_premium' => 'numeric'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->inventoryService->updateRevenueManagementInfo($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新收益管理信息');
        }
    }

    /**
     * 获取库存限制条件
     *
     * @param Request $request
     * @return Response
     */
    public function restrictions(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'room_type_id', 'date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->inventoryService->getInventoryRestrictions($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取库存限制条件');
        }
    }

    /**
     * 更新库存限制条件
     *
     * @param Request $request
     * @return Response
     */
    public function updateRestrictions(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'room_type_id', 'date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证JSON字段格式
            $jsonFields = [
                'distribution_channels', 'channel_restrictions', 'booking_restrictions',
                'guest_restrictions', 'length_of_stay_restrictions', 'advance_booking_restrictions'
            ];

            foreach ($jsonFields as $field) {
                if (!empty($params[$field]) && !is_array($params[$field])) {
                    return $this->error("字段 {$field} 必须是数组格式");
                }
            }

            $result = $this->inventoryService->updateInventoryRestrictions($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新库存限制条件');
        }
    }

    /**
     * 库存优化建议
     *
     * @param Request $request
     * @return Response
     */
    public function optimization(Request $request)
    {
        try {
            $params = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($params, [
                'hotel_id' => 'integer',
                'room_type_id' => 'integer',
                'start_date' => 'date',
                'end_date' => 'date'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            $result = $this->inventoryService->getInventoryOptimization($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取库存优化建议');
        }
    }
}
