<?php

namespace app\controller;

use support\Request;
use support\Response;
use app\model\FacilityTemplate;

/**
 * 设施设备模板控制器
 */
class FacilityTemplateController  extends BaseController
{

    /**
     * 验证数据
     */
    protected function validate(array $data, array $rules): mixed
    {
        // 简单验证，实际项目中应该使用专门的验证器
        foreach ($rules as $field => $rule) {
            $ruleArray = explode('|', $rule);
            foreach ($ruleArray as $r) {
                if ($r === 'required' && empty($data[$field])) {
                    return [$field => "{$field} is required"];
                }
            }
        }
        return true;
    }
    /**
     * 获取设施模板列表
     */
    public function index(Request $request): Response
    {
        try {
            $page = (int)$request->get('page', 1);
            $pageSize = (int)$request->get('page_size', 20);
            $category = $request->get('category', '');
            $keyword = $request->get('keyword', '');
            $isActive = $request->get('is_active', '');

            $query = FacilityTemplate::query();

            // 分类筛选
            if ($category) {
                $query->byCategory($category);
            }

            // 关键词搜索
            if ($keyword) {
                $query->search($keyword);
            }

            // 状态筛选
            if ($isActive !== '') {
                $query->where('is_active', (bool)$isActive);
            }

            // 排序
            $query->ordered();

            // 分页
            $total = $query->count();
            $facilities = $query->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();

            return $this->success([
                'list' => $facilities,
                'pagination' => [
                    'current' => $page,
                    'pageSize' => $pageSize,
                    'total' => $total,
                    'pages' => ceil($total / $pageSize),
                ],
            ]);
        } catch (\Exception $e) {
            return $this->error('获取设施模板列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取设施模板详情
     */
    public function show(Request $request, $id): Response
    {
        try {
            $facility = FacilityTemplate::find($id);

            if (!$facility) {
                return $this->error('设施模板不存在', 404);
            }

            return $this->success($facility);
        } catch (\Exception $e) {
            return $this->error('获取设施模板详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建设施模板
     */
    public function store(Request $request): Response
    {
        try {
            $data = $request->post();

            $validator = $this->validate($data, [
                'name_zh' => 'required|string|max:100',
                'name_en' => 'string|max:100',
                'category' => 'required|string|max:50',
                'description' => 'string',
                'icon' => 'string|max:100',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $facility = FacilityTemplate::create($data);

            return $this->success($facility, '设施模板创建成功');
        } catch (\Exception $e) {
            return $this->error('创建设施模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新设施模板
     */
    public function update(Request $request, $id): Response
    {
        try {
            $facility = FacilityTemplate::find($id);

            if (!$facility) {
                return $this->error('设施模板不存在', 404);
            }

            $data = $request->post();

            $validator = $this->validate($data, [
                'name_zh' => 'string|max:100',
                'name_en' => 'string|max:100',
                'category' => 'string|max:50',
                'description' => 'string',
                'icon' => 'string|max:100',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0',
            ]);

            if ($validator !== true) {
                return $this->error('参数验证失败', 400, $validator);
            }

            $facility->update($data);

            return $this->success($facility, '设施模板更新成功');
        } catch (\Exception $e) {
            return $this->error('更新设施模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除设施模板
     */
    public function destroy(Request $request, $id): Response
    {
        try {
            $facility = FacilityTemplate::find($id);

            if (!$facility) {
                return $this->error('设施模板不存在', 404);
            }

            $facility->delete();

            return $this->success(null, '设施模板删除成功');
        } catch (\Exception $e) {
            return $this->error('删除设施模板失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量删除设施模板
     */
    public function batchDestroy(Request $request): Response
    {
        try {
            $ids = $request->post('ids', []);

            if (empty($ids) || !is_array($ids)) {
                return $this->error('请选择要删除的设施模板');
            }

            $count = FacilityTemplate::whereIn('id', $ids)->delete();

            return $this->success(['deleted_count' => $count], "成功删除 {$count} 个设施模板");
        } catch (\Exception $e) {
            return $this->error('批量删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新状态
     */
    public function batchUpdateStatus(Request $request): Response
    {
        try {
            $ids = $request->post('ids', []);
            $isActive = (bool)$request->post('is_active', true);

            if (empty($ids) || !is_array($ids)) {
                return $this->error('请选择要更新的设施模板');
            }

            $count = FacilityTemplate::whereIn('id', $ids)
                ->update(['is_active' => $isActive]);

            $status = $isActive ? '启用' : '禁用';
            return $this->success(['updated_count' => $count], "成功{$status} {$count} 个设施模板");
        } catch (\Exception $e) {
            return $this->error('批量更新状态失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取分类列表
     */
    public function categories(Request $request): Response
    {
        try {
            $withCount = (bool)$request->get('with_count', false);

            if ($withCount) {
                $categories = FacilityTemplate::getCategoriesWithCount();
            } else {
                $categories = [];
                foreach (FacilityTemplate::CATEGORIES as $key => $name) {
                    $categories[] = [
                        'key' => $key,
                        'name' => $name,
                    ];
                }
            }

            return $this->success($categories);
        } catch (\Exception $e) {
            return $this->error('获取分类列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取推荐设施
     */
    public function recommended(Request $request): Response
    {
        try {
            $limit = (int)$request->get('limit', 20);
            $category = $request->get('category', '');

            $query = FacilityTemplate::active()->ordered();

            if ($category) {
                $query->byCategory($category);
            }

            $facilities = $query->limit($limit)->get();

            return $this->success($facilities);
        } catch (\Exception $e) {
            return $this->error('获取推荐设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量导入设施模板
     */
    public function batchImport(Request $request): Response
    {
        try {
            $data = $request->post('data', []);

            if (empty($data) || !is_array($data)) {
                return $this->error('请提供要导入的数据');
            }

            $result = FacilityTemplate::batchImport($data);

            return $this->success($result, '批量导入完成');
        } catch (\Exception $e) {
            return $this->error('批量导入失败: ' . $e->getMessage());
        }
    }

    /**
     * 同步到酒店设施
     */
    public function syncToHotel(Request $request): Response
    {
        try {
            $hotelId = (int)$request->post('hotel_id');
            $templateIds = $request->post('template_ids', []);
            $options = $request->post('options', []);

            if (!$hotelId) {
                return $this->error('请指定酒店ID');
            }

            if (empty($templateIds) || !is_array($templateIds)) {
                return $this->error('请选择要同步的设施模板');
            }

            $synced = 0;
            $skipped = 0;

            foreach ($templateIds as $templateId) {
                $template = FacilityTemplate::find($templateId);
                if ($template) {
                    if ($template->syncToHotelFacility($hotelId, $options)) {
                        $synced++;
                    } else {
                        $skipped++;
                    }
                }
            }

            return $this->success([
                'synced' => $synced,
                'skipped' => $skipped,
            ], "成功同步 {$synced} 个设施，跳过 {$skipped} 个");
        } catch (\Exception $e) {
            return $this->error('同步到酒店设施失败: ' . $e->getMessage());
        }
    }
}
