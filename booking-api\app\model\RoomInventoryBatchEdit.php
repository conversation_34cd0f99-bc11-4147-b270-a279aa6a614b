<?php

namespace app\model;

/**
 * 库存批量编辑模型
 * 对应数据库表：room_inventory_batch_edits
 */
class RoomInventoryBatchEdit extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_inventory_batch_edits';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'room_type_id',
        'start_date',
        'end_date',
        'operation_type',
        'inventory_adjustment',
        'close_sales',
        'stop_sales',
        'min_stay_adjustment',
        'max_stay_adjustment',
        'arrival_restriction',
        'departure_restriction',
        'days_of_week',
        'exclude_dates',
        'include_dates',
        'reason',
        'status',
        'processed_count',
        'total_count',
        'error_message',
        'created_by',
        'processed_at',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'room_type_id' => 'integer',
        'start_date' => 'date',
        'end_date' => 'date',
        'inventory_adjustment' => 'integer',
        'close_sales' => 'boolean',
        'stop_sales' => 'boolean',
        'min_stay_adjustment' => 'integer',
        'max_stay_adjustment' => 'integer',
        'arrival_restriction' => 'boolean',
        'departure_restriction' => 'boolean',
        'days_of_week' => 'array',
        'exclude_dates' => 'array',
        'include_dates' => 'array',
        'processed_count' => 'integer',
        'total_count' => 'integer',
        'created_by' => 'integer',
        'processed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * 操作类型常量
     */
    const OPERATION_SET = 'set';
    const OPERATION_INCREASE = 'increase';
    const OPERATION_DECREASE = 'decrease';
    const OPERATION_CLOSE = 'close';
    const OPERATION_OPEN = 'open';

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取所属房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * 获取创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待处理的
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：处理中的
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', self::STATUS_PROCESSING);
    }

    /**
     * 作用域：已完成的
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：失败的
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：按酒店筛选
     */
    public function scopeByHotel($query, $hotelId)
    {
        return $query->where('hotel_id', $hotelId);
    }

    /**
     * 作用域：按房型筛选
     */
    public function scopeByRoomType($query, $roomTypeId)
    {
        return $query->where('room_type_id', $roomTypeId);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->where('start_date', '>=', $startDate)
                    ->where('end_date', '<=', $endDate);
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute()
    {
        $statuses = [
            self::STATUS_PENDING => '待处理',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_FAILED => '失败',
        ];

        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取操作类型名称
     */
    public function getOperationTypeNameAttribute()
    {
        $types = [
            self::OPERATION_SET => '设置库存',
            self::OPERATION_INCREASE => '增加库存',
            self::OPERATION_DECREASE => '减少库存',
            self::OPERATION_CLOSE => '关房',
            self::OPERATION_OPEN => '开房',
        ];

        return $types[$this->operation_type] ?? '未知';
    }

    /**
     * 获取进度百分比
     */
    public function getProgressPercentageAttribute()
    {
        if ($this->total_count == 0) {
            return 0;
        }

        return round(($this->processed_count / $this->total_count) * 100, 2);
    }

    /**
     * 检查是否已完成
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 检查是否失败
     */
    public function isFailed()
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 检查是否正在处理
     */
    public function isProcessing()
    {
        return $this->status === self::STATUS_PROCESSING;
    }

    /**
     * 检查是否待处理
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 标记为处理中
     */
    public function markAsProcessing()
    {
        $this->update(['status' => self::STATUS_PROCESSING]);
    }

    /**
     * 标记为已完成
     */
    public function markAsCompleted()
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'processed_at' => now(),
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
            'processed_at' => now(),
        ]);
    }

    /**
     * 更新处理进度
     */
    public function updateProgress($processedCount, $totalCount = null)
    {
        $data = ['processed_count' => $processedCount];
        
        if ($totalCount !== null) {
            $data['total_count'] = $totalCount;
        }

        $this->update($data);
    }

    /**
     * 获取影响的日期列表
     */
    public function getAffectedDates()
    {
        $dates = [];
        $current = $this->start_date;
        $end = $this->end_date;

        while ($current <= $end) {
            // 检查是否在包含日期中（如果设置了）
            if (!empty($this->include_dates) && !in_array($current->toDateString(), $this->include_dates)) {
                $current = $current->addDay();
                continue;
            }

            // 检查是否在排除日期中
            if (!empty($this->exclude_dates) && in_array($current->toDateString(), $this->exclude_dates)) {
                $current = $current->addDay();
                continue;
            }

            // 检查星期限制
            if (!empty($this->days_of_week) && !in_array($current->dayOfWeek, $this->days_of_week)) {
                $current = $current->addDay();
                continue;
            }

            $dates[] = $current->toDateString();
            $current = $current->addDay();
        }

        return $dates;
    }

    /**
     * 计算调整后的库存
     */
    public function calculateAdjustedInventory($originalInventory)
    {
        switch ($this->operation_type) {
            case self::OPERATION_SET:
                return $this->inventory_adjustment;
            case self::OPERATION_INCREASE:
                return $originalInventory + $this->inventory_adjustment;
            case self::OPERATION_DECREASE:
                return max(0, $originalInventory - $this->inventory_adjustment);
            case self::OPERATION_CLOSE:
                return 0;
            case self::OPERATION_OPEN:
                return $this->inventory_adjustment ?: $originalInventory;
            default:
                return $originalInventory;
        }
    }

    /**
     * 获取库存调整信息
     */
    public function getInventoryAdjustmentInfo()
    {
        return [
            'operation_type' => $this->operation_type,
            'inventory_adjustment' => $this->inventory_adjustment,
            'close_sales' => $this->close_sales,
            'stop_sales' => $this->stop_sales,
            'min_stay_adjustment' => $this->min_stay_adjustment,
            'max_stay_adjustment' => $this->max_stay_adjustment,
            'arrival_restriction' => $this->arrival_restriction,
            'departure_restriction' => $this->departure_restriction,
            'reason' => $this->reason,
        ];
    }
}
