<?php

namespace app\controller;

use app\model\HotelImage;
use support\Request;
use support\Response;

/**
 * 酒店图片管理控制器
 */
class HotelImageController
{
    /**
     * 获取酒店图片列表
     */
    public function index(Request $request, int $id): Response
    {
        try {
            $images = HotelImage::where('hotel_id', $id)
                ->where('status', 'active')
                ->ordered()
                ->get()->map(function ($item) {
                    $item->image_url = 'https://127.0.0.1:9999/' . $item->image_url;
                    return $item;
                });

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $images->toArray(),
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取图片列表失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 获取单个图片详情
     */
    public function show(Request $request, int $id): Response
    {
        try {
            $image = HotelImage::find($id);

            if (!$image) {
                return json([
                    'code' => 404,
                    'message' => '图片不存在',
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 404);
            }

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $image->toArray(),
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取图片详情失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 更新图片信息
     */
    public function update(Request $request, int $id): Response
    {
        try {
            $image = HotelImage::find($id);

            if (!$image) {
                return json([
                    'code' => 404,
                    'message' => '图片不存在',
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 404);
            }

            // 验证请求数据
            $data = $request->all();
            $allowedFields = ['image_title', 'image_description', 'image_category_id', 'image_category', 'sort_order', 'status'];
            
            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if (empty($updateData)) {
                return json([
                    'code' => 400,
                    'message' => '没有有效的更新数据',
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 400);
            }

            // 更新图片信息
            $image->update($updateData);

            return json([
                'code' => 200,
                'message' => '图片信息更新成功',
                'data' => $image->fresh()->toArray(),
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新图片信息失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 删除图片
     */
    public function destroy(Request $request, int $id): Response
    {
        try {
            $image = HotelImage::find($id);

            if (!$image) {
                return json([
                    'code' => 404,
                    'message' => '图片不存在',
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 404);
            }

            // 软删除：将状态设置为 inactive
            $image->update(['status' => 'inactive']);

            return json([
                'code' => 200,
                'message' => '图片删除成功',
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除图片失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }

    /**
     * 设置主图
     */
    public function setPrimary(Request $request, int $id, int $image_id): Response
    {
        return json([
            'code' => 400,
            'message' => '当前系统未支持主图功能',
            'data' => null,
            'timestamp' => time(),
            'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
        ], 400);
    }

    /**
     * 更新图片排序
     */
    public function updateOrder(Request $request, int $id): Response
    {
        try {
            $orders = $request->post('orders', []);

            if (empty($orders) || !is_array($orders)) {
                return json([
                    'code' => 400,
                    'message' => '排序数据不能为空',
                    'data' => null,
                    'timestamp' => time(),
                    'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
                ], 400);
            }

            $updatedCount = 0;
            foreach ($orders as $orderData) {
                if (!isset($orderData['id']) || !isset($orderData['order'])) {
                    continue;
                }

                $updated = HotelImage::where('id', $orderData['id'])
                    ->where('hotel_id', $id)
                    ->update(['sort_order' => $orderData['order']]);

                if ($updated) {
                    $updatedCount++;
                }
            }

            return json([
                'code' => 200,
                'message' => '排序更新成功',
                'data' => [
                    'updated_count' => $updatedCount,
                    'total_count' => count($orders)
                ],
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新排序失败: ' . $e->getMessage(),
                'data' => null,
                'timestamp' => time(),
                'request_id' => $request->header('X-Request-ID', 'req_' . uniqid())
            ], 500);
        }
    }
}
