<?php

namespace app\model;

/**
 * 房型服务模型
 * 对应数据库表：room_type_services
 */
class RoomTypeService extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_type_services';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'room_type_id',
        'service_type',
        'service_name',
        'service_description',
        'is_chargeable',
        'charge_amount',
        'charge_type',
        'availability_hours',
        'icon',
        'sort_order',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'is_chargeable' => 'boolean',
        'charge_amount' => 'decimal:2',
        'sort_order' => 'integer',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    /**
     * 关联房型
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type_id');
    }

    /**
     * 获取包含服务
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIncluded($query)
    {
        return $query->where('service_type', 'included');
    }

    /**
     * 获取可选服务
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOptional($query)
    {
        return $query->where('service_type', 'optional');
    }

    /**
     * 获取客房服务
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRoomService($query)
    {
        return $query->where('service_type', 'room_service');
    }

    /**
     * 获取商务服务
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBusiness($query)
    {
        return $query->where('service_type', 'business');
    }

    /**
     * 获取收费服务
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeChargeable($query)
    {
        return $query->where('is_chargeable', true);
    }

    /**
     * 按排序排序
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取服务类型名称
     *
     * @return string
     */
    public function getServiceTypeNameAttribute()
    {
        $types = [
            'included' => '包含服务',
            'optional' => '可选服务',
            'room_service' => '客房服务',
            'business' => '商务服务',
        ];

        return $types[$this->service_type] ?? $this->service_type;
    }

    /**
     * 获取收费类型名称
     *
     * @return string
     */
    public function getChargeTypeNameAttribute()
    {
        $types = [
            'per_night' => '每晚',
            'per_stay' => '每次',
            'per_use' => '每次使用',
        ];

        return $types[$this->charge_type] ?? $this->charge_type;
    }

    /**
     * 获取价格信息
     *
     * @return string
     */
    public function getPriceInfoAttribute()
    {
        if (!$this->is_chargeable) {
            return '免费';
        }
        
        if ($this->charge_amount) {
            return "{$this->charge_amount}元";
        }
        
        return '收费';
    }

    /**
     * 获取服务完整信息
     *
     * @return array
     */
    public function getServiceInfoAttribute()
    {
        return [
            'id' => $this->id,
            'service_type' => $this->service_type,
            'service_type_name' => $this->service_type_name,
            'service_name' => $this->service_name,
            'service_description' => $this->service_description,
            'is_chargeable' => $this->is_chargeable,
            'charge_amount' => $this->charge_amount,
            'charge_type' => $this->charge_type,
            'charge_type_name' => $this->charge_type_name,
            'price_info' => $this->price_info,
            'availability_hours' => $this->availability_hours,
            'icon' => $this->icon,
        ];
    }

    /**
     * 验证服务
     *
     * @param array $data
     * @return array
     */
    public static function validateService($data)
    {
        $rules = [
            'room_type_id' => 'required|integer|exists:room_types,id',
            'service_type' => 'required|in:included,optional,room_service,business',
            'service_name' => 'required|string|max:100',
            'service_description' => 'nullable|string',
            'is_chargeable' => 'boolean',
            'charge_amount' => 'nullable|numeric|min:0',
            'charge_type' => 'nullable|in:per_night,per_stay,per_use',
            'availability_hours' => 'nullable|string|max:100',
            'icon' => 'nullable|string|max:100',
            'sort_order' => 'integer|min:0',
        ];

        $messages = [
            'room_type_id.required' => '房型ID不能为空',
            'room_type_id.exists' => '房型不存在',
            'service_type.required' => '服务类型不能为空',
            'service_type.in' => '服务类型无效',
            'service_name.required' => '服务名称不能为空',
            'service_name.max' => '服务名称不能超过100个字符',
            'charge_amount.min' => '收费金额不能为负数',
            'charge_type.in' => '收费类型无效',
            'availability_hours.max' => '可用时间不能超过100个字符',
            'icon.max' => '图标不能超过100个字符',
            'sort_order.min' => '排序不能为负数',
        ];

        return [
            'rules' => $rules,
            'messages' => $messages,
        ];
    }

    /**
     * 创建服务
     *
     * @param array $data
     * @return static
     */
    public static function createService($data)
    {
        $validation = self::validateService($data);
        
        // 这里应该使用验证器，暂时简化处理
        $service = new self($data);
        $service->save();
        
        return $service;
    }

    /**
     * 更新服务
     *
     * @param array $data
     * @return bool
     */
    public function updateService($data)
    {
        $validation = self::validateService($data);
        
        // 这里应该使用验证器，暂时简化处理
        return $this->update($data);
    }

    /**
     * 获取房型的所有服务
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的指定类型服务
     *
     * @param int $roomTypeId
     * @param string $serviceType
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByRoomTypeAndType($roomTypeId, $serviceType)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('service_type', $serviceType)
            ->ordered()
            ->get();
    }

    /**
     * 获取房型的包含服务
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getIncludedServicesByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'included');
    }

    /**
     * 获取房型的可选服务
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getOptionalServicesByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'optional');
    }

    /**
     * 获取房型的客房服务
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getRoomServicesByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'room_service');
    }

    /**
     * 获取房型的商务服务
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getBusinessServicesByRoomType($roomTypeId)
    {
        return self::getByRoomTypeAndType($roomTypeId, 'business');
    }

    /**
     * 获取房型的收费服务
     *
     * @param int $roomTypeId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getChargeableServicesByRoomType($roomTypeId)
    {
        return self::where('room_type_id', $roomTypeId)
            ->where('is_chargeable', true)
            ->ordered()
            ->get();
    }
}
