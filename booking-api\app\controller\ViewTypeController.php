<?php

namespace app\controller;

use app\model\ViewType;
use support\Request;
use support\Response;

/**
 * 景观类型管理控制器
 */
class ViewTypeController extends BaseController
{
    /**
     * 获取景观类型列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = $this->getInput($request);
            
            $query = ViewType::query();
            
            // 关键词搜索
            if (!empty($params['keyword'])) {
                $query->byKeyword($params['keyword']);
            }
            
            // 状态筛选
            if (!empty($params['status'])) {
                $query->byStatus($params['status']);
            }
            
            // 排序
            $sortBy = $params['sort_by'] ?? 'sort_order';
            $sortOrder = $params['sort_order'] ?? 'asc';
            $query->orderBy($sortBy, $sortOrder);
            
            // 分页
            $page = $params['page'] ?? 1;
            $perPage = $params['per_page'] ?? 20;

            $result = $query->paginate($perPage, ['*'], 'page', $page);

            // 添加使用统计
            $items = $result->items();
            foreach ($items as $item) {
                $item['usage_count'] = $item->getUsageCount();
                $item['status_text'] = ViewType::getStatusText($item['status']);
            }

            return $this->success([
                'items' => $items,
                'pagination' => [
                    'current_page' => $result->currentPage(),
                    'per_page' => $result->perPage(),
                    'total' => $result->total(),
                    'last_page' => $result->lastPage(),
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->handleException($e, '获取景观类型列表');
        }
    }

    /**
     * 获取景观类型详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request)
    {
        try {
            $id = $request->route('id');
            
            $viewType = ViewType::find($id);
            if (!$viewType) {
                return $this->error('景观类型不存在', 404);
            }
            
            $viewType['usage_count'] = $viewType->getUsageCount();
            $viewType['status_text'] = ViewType::getStatusText($viewType['status']);
            
            return $this->success($viewType);
            
        } catch (\Exception $e) {
            return $this->handleException($e, '获取景观类型详情');
        }
    }

    /**
     * 创建景观类型
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $params = $this->getInput($request);
            
            // 验证必填字段
            $this->validateRequired($params, ['code', 'name_cn', 'name_en']);
            
            // 检查代码是否已存在
            if (ViewType::where('code', $params['code'])->exists()) {
                return $this->error('景观类型代码已存在');
            }
            
            // 设置默认排序
            if (empty($params['sort_order'])) {
                $params['sort_order'] = ViewType::getNextSortOrder();
            }
            
            // 设置默认状态
            if (empty($params['status'])) {
                $params['status'] = ViewType::STATUS_ACTIVE;
            }
            
            $viewType = ViewType::create($params);
            
            return $this->success($viewType, '创建成功');
            
        } catch (\Exception $e) {
            return $this->handleException($e, '创建景观类型');
        }
    }

    /**
     * 更新景观类型
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        try {
            $id = $request->route('id');
            $params = $this->getInput($request);
            
            $viewType = ViewType::find($id);
            if (!$viewType) {
                return $this->error('景观类型不存在', 404);
            }
            
            // 验证必填字段
            $this->validateRequired($params, ['code', 'name_cn', 'name_en']);
            
            // 检查代码是否已存在（排除当前记录）
            if (ViewType::where('code', $params['code'])->where('id', '!=', $id)->exists()) {
                return $this->error('景观类型代码已存在');
            }
            
            $viewType->update($params);
            
            return $this->success($viewType, '更新成功');
            
        } catch (\Exception $e) {
            return $this->handleException($e, '更新景观类型');
        }
    }

    /**
     * 删除景观类型
     *
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        try {
            $id = $request->route('id');
            
            $viewType = ViewType::find($id);
            if (!$viewType) {
                return $this->error('景观类型不存在', 404);
            }
            
            // 检查是否被房型使用
            if ($viewType->isUsedByRoomTypes()) {
                $usageCount = $viewType->getUsageCount();
                return $this->error("该景观类型正在被 {$usageCount} 个房型使用，无法删除");
            }
            
            $viewType->delete();
            
            return $this->success(null, '删除成功');
            
        } catch (\Exception $e) {
            return $this->handleException($e, '删除景观类型');
        }
    }

    /**
     * 获取景观类型选项（用于下拉选择）
     *
     * @param Request $request
     * @return Response
     */
    public function options(Request $request)
    {
        try {
            $options = ViewType::getOptions();
            return $this->success($options);
            
        } catch (\Exception $e) {
            return $this->handleException($e, '获取景观类型选项');
        }
    }

    /**
     * 批量更新状态
     *
     * @param Request $request
     * @return Response
     */
    public function batchUpdateStatus(Request $request)
    {
        try {
            $params = $this->getInput($request);
            
            $this->validateRequired($params, ['ids', 'status']);
            
            if (!in_array($params['status'], [ViewType::STATUS_ACTIVE, ViewType::STATUS_INACTIVE])) {
                return $this->error('无效的状态值');
            }
            
            $count = ViewType::whereIn('id', $params['ids'])->update(['status' => $params['status']]);
            
            return $this->success(['updated_count' => $count], '批量更新成功');
            
        } catch (\Exception $e) {
            return $this->handleException($e, '批量更新状态');
        }
    }

    /**
     * 更新排序
     *
     * @param Request $request
     * @return Response
     */
    public function updateSort(Request $request)
    {
        try {
            $params = $this->getInput($request);
            
            $this->validateRequired($params, ['items']);
            
            foreach ($params['items'] as $item) {
                if (isset($item['id']) && isset($item['sort_order'])) {
                    ViewType::where('id', $item['id'])->update(['sort_order' => $item['sort_order']]);
                }
            }
            
            return $this->success(null, '排序更新成功');
            
        } catch (\Exception $e) {
            return $this->handleException($e, '更新排序');
        }
    }
}
