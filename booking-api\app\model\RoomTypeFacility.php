<?php

namespace app\model;

use support\Model;

/**
 * 房型设施关联模型
 */
class RoomTypeFacility extends Model
{
    protected $table = 'room_type_facilities';
    
    protected $fillable = [
        'room_type_id',
        'facility_id',
        'quantity',
        'specification',
        'is_highlight',
        'sort_order'
    ];

    protected $casts = [
        'room_type_id' => 'integer',
        'facility_id' => 'integer',
        'quantity' => 'integer',
        'is_highlight' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * 获取房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class, 'room_type_id', 'id');
    }

    /**
     * 获取设施
     */
    public function facility()
    {
        return $this->belongsTo(RoomFacility::class, 'facility_id', 'id');
    }

    /**
     * 获取房型的所有设施
     */
    public static function getRoomTypeFacilities($roomTypeId)
    {
        return self::with(['facility.category'])
                  ->where('room_type_id', $roomTypeId)
                  ->orderBy('sort_order')
                  ->get()
                  ->groupBy('facility.category.name')
                  ->toArray();
    }

    /**
     * 获取房型的重点设施
     */
    public static function getHighlightFacilities($roomTypeId)
    {
        return self::with(['facility.category'])
                  ->where('room_type_id', $roomTypeId)
                  ->where('is_highlight', true)
                  ->orderBy('sort_order')
                  ->get()
                  ->toArray();
    }

    /**
     * 批量设置房型设施
     */
    public static function setRoomTypeFacilities($roomTypeId, $facilities)
    {
        // 删除现有关联
        self::where('room_type_id', $roomTypeId)->delete();
        
        // 添加新关联
        $data = [];
        $sortOrder = 1;
        
        foreach ($facilities as $facility) {
            $data[] = [
                'room_type_id' => $roomTypeId,
                'facility_id' => $facility['facility_id'],
                'quantity' => $facility['quantity'] ?? 1,
                'specification' => $facility['specification'] ?? null,
                'is_highlight' => $facility['is_highlight'] ?? false,
                'sort_order' => $sortOrder++,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }
        
        if (!empty($data)) {
            return self::insert($data);
        }
        
        return true;
    }

    /**
     * 添加房型设施
     */
    public static function addRoomTypeFacility($roomTypeId, $facilityId, $options = [])
    {
        // 检查是否已存在
        $exists = self::where('room_type_id', $roomTypeId)
                     ->where('facility_id', $facilityId)
                     ->exists();
        
        if ($exists) {
            return false;
        }
        
        // 获取下一个排序号
        $sortOrder = self::where('room_type_id', $roomTypeId)->max('sort_order') + 1;
        
        return self::create([
            'room_type_id' => $roomTypeId,
            'facility_id' => $facilityId,
            'quantity' => $options['quantity'] ?? 1,
            'specification' => $options['specification'] ?? null,
            'is_highlight' => $options['is_highlight'] ?? false,
            'sort_order' => $sortOrder,
        ]);
    }

    /**
     * 移除房型设施
     */
    public static function removeRoomTypeFacility($roomTypeId, $facilityId)
    {
        return self::where('room_type_id', $roomTypeId)
                  ->where('facility_id', $facilityId)
                  ->delete();
    }

    /**
     * 更新设施信息
     */
    public function updateFacilityInfo($options)
    {
        $this->quantity = $options['quantity'] ?? $this->quantity;
        $this->specification = $options['specification'] ?? $this->specification;
        $this->is_highlight = $options['is_highlight'] ?? $this->is_highlight;
        
        return $this->save();
    }

    /**
     * 设置为重点设施
     */
    public function setHighlight($highlight = true)
    {
        $this->is_highlight = $highlight;
        return $this->save();
    }

    /**
     * 更新排序
     */
    public static function updateSortOrder($roomTypeId, $facilityOrders)
    {
        foreach ($facilityOrders as $facilityId => $sortOrder) {
            self::where('room_type_id', $roomTypeId)
               ->where('facility_id', $facilityId)
               ->update(['sort_order' => $sortOrder]);
        }
        
        return true;
    }

    /**
     * 复制房型设施到另一个房型
     */
    public static function copyFacilities($fromRoomTypeId, $toRoomTypeId)
    {
        $facilities = self::where('room_type_id', $fromRoomTypeId)->get();
        
        foreach ($facilities as $facility) {
            self::create([
                'room_type_id' => $toRoomTypeId,
                'facility_id' => $facility->facility_id,
                'quantity' => $facility->quantity,
                'specification' => $facility->specification,
                'is_highlight' => $facility->is_highlight,
                'sort_order' => $facility->sort_order,
            ]);
        }
        
        return true;
    }

    /**
     * 获取设施使用统计
     */
    public static function getFacilityStats()
    {
        return self::selectRaw('facility_id, COUNT(*) as usage_count')
                  ->groupBy('facility_id')
                  ->orderBy('usage_count', 'desc')
                  ->with('facility')
                  ->get()
                  ->toArray();
    }
}
