<?php

namespace app\model;

/**
 * 酒店集团模型
 * 对应数据库表：hotel_groups
 */
class HotelGroup extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'hotel_groups';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'parent_id',
        'code',
        'name',
        'name_en',
        'short_name',
        'description',
        'description_en',

        // 联系信息
        'contact_person',
        'contact_phone',
        'contact_email',
        'website',

        // 地址信息
        'country',
        'province',
        'city',
        'address',
        'postal_code',

        // 业务信息
        'established_year',
        'business_type',
        'scale_level',
        'hotel_count',
        'room_count',

        // 品牌信息
        'logo_url',
        'brand_color',
        'brand_slogan',

        // 财务信息
        'annual_revenue',
        'market_value',
        'currency',

        // 认证信息
        'business_license',
        'tax_number',
        'legal_representative',

        // 状态信息
        'status',
        'is_public',
        'is_verified',
        'verified_at',
        'verified_by',

        // 排序和权重
        'sort_order',
        'level',
        'path',

        'created_by',
        'updated_by',

        // 兼容旧字段
        'logo',
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'parent_id' => 'integer',
        'established_year' => 'integer',
        'hotel_count' => 'integer',
        'room_count' => 'integer',
        'annual_revenue' => 'decimal:2',
        'market_value' => 'decimal:2',
        'is_public' => 'boolean',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'verified_by' => 'integer',
        'sort_order' => 'integer',
        'level' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_PENDING = 'pending';
    const STATUS_SUSPENDED = 'suspended';

    /**
     * 获取父集团
     */
    public function parent()
    {
        return $this->belongsTo(HotelGroup::class, 'parent_id');
    }

    /**
     * 获取子集团
     */
    public function children()
    {
        return $this->hasMany(HotelGroup::class, 'parent_id');
    }

    /**
     * 获取集团下的品牌
     */
    public function brands()
    {
        return $this->hasMany(HotelBrand::class, 'group_id');
    }

    /**
     * 获取集团下的酒店
     */
    public function hotels()
    {
        return $this->hasMany(Hotel::class, 'group_id');
    }

    /**
     * 获取多语言翻译
     */
    public function translations()
    {
        return $this->hasMany(HotelGroupTranslation::class);
    }

    /**
     * 获取统计信息
     */
    public function statistics()
    {
        return $this->hasOne(HotelGroupStatistics::class);
    }

    /**
     * 获取活跃的品牌
     */
    public function activeBrands()
    {
        return $this->brands()->active();
    }

    /**
     * 获取活跃的酒店
     */
    public function activeHotels()
    {
        return $this->hotels()->active();
    }

    /**
     * 获取酒店数量
     */
    public function getHotelCountAttribute()
    {
        return $this->hotels()->count();
    }

    /**
     * 获取品牌数量
     */
    public function getBrandCountAttribute()
    {
        return $this->brands()->count();
    }

    /**
     * 作用域：活跃的集团
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 作用域：公开显示的集团
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * 作用域：已认证的集团
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * 作用域：顶级集团
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 获取所有启用的集团（用于下拉选择）
     *
     * @param string $locale
     * @return array
     */
    public static function getActiveOptions($locale = 'zh-CN')
    {
        $groups = static::active()->public()->ordered()->get();

        return $groups->map(function ($group) use ($locale) {
            return [
                'value' => $group->id,
                'label' => $group->getLocalizedName($locale),
                'code' => $group->code,
                'short_name' => $group->short_name,
                'logo_url' => $group->logo_url ?? $group->logo,
                'is_verified' => $group->is_verified,
            ];
        })->toArray();
    }

    /**
     * 获取指定语言的名称
     *
     * @param string $locale
     * @return string
     */
    public function getLocalizedName($locale = 'zh-CN')
    {
        if ($locale === 'zh-CN') {
            return $this->name;
        }

        if ($locale === 'en-US' && $this->name_en) {
            return $this->name_en;
        }

        return $this->name;
    }

    /**
     * 获取所有集团（用于下拉选择）- 兼容旧方法
     */
    public static function getAllForSelect()
    {
        return static::getActiveOptions();
    }
}
