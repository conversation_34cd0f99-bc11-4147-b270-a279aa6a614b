<?php

namespace app\controller;

use app\middleware\AuthMiddleware;
use app\model\User;
use app\model\ApiKey;
use support\Request;
use support\Response;

/**
 * SSO认证控制器
 * 兼容IAM系统的单点登录
 */
class AuthController extends BaseController
{
    /**
     * SSO登录状态检查
     * 替代传统登录，检查SSO token有效性
     *
     * @param Request $request
     * @return Response
     */
    public function checkSsoStatus(Request $request)
    {
        try {
            // 从中间件获取已验证的用户信息
            $user = $request->user;
            $userInfo = $request->userInfo ?? [];

            if (!$user) {
                return $this->error('用户未认证', 401);
            }

            return $this->success([
                'authenticated' => true,
                'user' => [
                    'id' => $user->user_id,
                    'username' => $user->username,
                    'real_name' => $user->real_name,
                    'email' => $user->email,
                    'tenant_id' => $user->tenant_id,
                    'tenant_name' => $user->tenant_name,
                    'roles' => $user->roles,
                    'user_type' => $user->user_type,
                ],
                'token_info' => [
                    'expires_at' => $userInfo['exp'] ?? null,
                    'issuer' => $userInfo['iss'] ?? null,
                ]
            ], 'SSO认证状态正常');

        } catch (\Exception $e) {
            return $this->handleException($e, 'SSO状态检查');
        }
    }

    /**
     * 传统登录接口（已废弃，重定向到SSO）
     *
     * @param Request $request
     * @return Response
     */
    public function login(Request $request)
    {
        return $this->error('请使用SSO登录系统', 410, [
            'message' => '传统登录已废弃，请使用单点登录系统',
            'sso_login_url' => config('app.sso_base_url', 'http://localhost:5667') . '/auth/login',
            'redirect_required' => true
        ]);
    }

    /**
     * 用户注册（已废弃，重定向到SSO）
     *
     * @param Request $request
     * @return Response
     */
    public function register(Request $request)
    {
        return $this->error('请使用SSO注册系统', 410, [
            'message' => '传统注册已废弃，请通过IAM系统管理用户',
            'iam_admin_url' => config('app.iam_admin_url', 'http://localhost:5173'),
            'redirect_required' => true
        ]);
    }

    /**
     * 刷新token（SSO系统中由IAM处理）
     *
     * @param Request $request
     * @return Response
     */
    public function refresh(Request $request)
    {
        return $this->error('Token刷新请通过SSO系统', 410, [
            'message' => 'Token刷新应通过IAM系统的refresh endpoint处理',
            'sso_refresh_url' => config('app.sso_base_url', 'http://localhost:5667') . '/sso/refresh',
            'redirect_required' => true
        ]);
    }

    /**
     * 用户登出（SSO系统处理）
     *
     * @param Request $request
     * @return Response
     */
    public function logout(Request $request)
    {
        try {
            // 在SSO系统中，登出应该通过IAM系统处理
            // 这里只返回成功，实际的token撤销由IAM系统负责
            return $this->success([
                'message' => '登出成功',
                'sso_logout_url' => config('app.sso_base_url', 'http://localhost:5667') . '/sso/logout',
                'redirect_recommended' => true
            ], '本地登出成功，建议访问SSO登出页面完成全局登出');

        } catch (\Exception $e) {
            return $this->handleException($e, '登出');
        }
    }

    /**
     * 获取当前用户信息（兼容SSO）
     *
     * @param Request $request
     * @return Response
     */
    public function me(Request $request)
    {
        try {
            $user = $request->user;
            $userInfo = $request->userInfo ?? [];

            if (!$user) {
                return $this->error('用户未认证', 401);
            }

            return $this->success([
                'id' => $user->user_id,
                'user_id' => $user->user_id,
                'username' => $user->username,
                'real_name' => $user->real_name,
                'email' => $user->email,
                'tenant_id' => $user->tenant_id,
                'tenant_name' => $user->tenant_name,
                'user_type' => $user->user_type,
                'roles' => $user->roles,
                'status' => $user->status,
                'token_expires_at' => $userInfo['exp'] ?? null,
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取用户信息');
        }
    }

    /**
     * 修改密码
     *
     * @param Request $request
     * @return Response
     */
    public function changePassword(Request $request)
    {
        try {
            $user = $request->user;
            
            if (!$user) {
                return $this->error('用户未认证', 401);
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['old_password', 'new_password']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证旧密码
            if (!$user->verifyPassword($data['old_password'])) {
                return $this->error('旧密码错误');
            }

            // 验证新密码长度
            if (strlen($data['new_password']) < 6) {
                return $this->error('新密码长度不能少于6位');
            }

            // 更新密码
            $user->setPassword($data['new_password']);
            $user->save();

            return $this->success(null, '密码修改成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '修改密码');
        }
    }

    /**
     * 创建API密钥
     *
     * @param Request $request
     * @return Response
     */
    public function createApiKey(Request $request)
    {
        try {
            $user = $request->user;
            
            if (!$user) {
                return $this->error('用户未认证', 401);
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['name']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 生成API密钥对
            $keyPair = ApiKey::generateKeyPair();

            // 创建API密钥
            $apiKeyData = [
                'name' => $data['name'],
                'key' => $keyPair['key'],
                'secret' => $keyPair['secret'],
                'user_id' => $user->id,
                'permissions' => $data['permissions'] ?? [],
                'ip_whitelist' => $data['ip_whitelist'] ?? [],
                'rate_limit' => $data['rate_limit'] ?? null,
                'rate_limit_window' => $data['rate_limit_window'] ?? 3600,
                'is_active' => true,
                'expires_at' => !empty($data['expires_at']) ? $data['expires_at'] : null,
                'description' => $data['description'] ?? null,
            ];

            $apiKey = ApiKey::create($apiKeyData);

            return $this->success([
                'id' => $apiKey->id,
                'name' => $apiKey->name,
                'key' => $apiKey->key,
                'secret' => $apiKey->secret, // 只在创建时返回
                'permissions' => $apiKey->permissions,
                'expires_at' => $apiKey->expires_at,
            ], 'API密钥创建成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '创建API密钥');
        }
    }

    /**
     * 获取API密钥列表
     *
     * @param Request $request
     * @return Response
     */
    public function getApiKeys(Request $request)
    {
        try {
            $user = $request->user;
            
            if (!$user) {
                return $this->error('用户未认证', 401);
            }

            $apiKeys = ApiKey::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->get()
                ->map(function ($apiKey) {
                    return [
                        'id' => $apiKey->id,
                        'name' => $apiKey->name,
                        'key' => $apiKey->key,
                        'permissions' => $apiKey->permissions,
                        'is_active' => $apiKey->is_active,
                        'expires_at' => $apiKey->expires_at,
                        'last_used_at' => $apiKey->last_used_at,
                        'usage_count' => $apiKey->usage_count,
                        'created_at' => $apiKey->created_at,
                    ];
                });

            return $this->success($apiKeys);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取API密钥列表');
        }
    }

    /**
     * 删除API密钥
     *
     * @param Request $request
     * @return Response
     */
    public function deleteApiKey(Request $request)
    {
        try {
            $user = $request->user;
            
            if (!$user) {
                return $this->error('用户未认证', 401);
            }

            // 从URL路径中获取API密钥ID
            $path = $request->path();
            $pathParts = explode('/', trim($path, '/'));
            $apiKeyId = (int)end($pathParts);
            
            if ($apiKeyId <= 0) {
                return $this->error('API密钥ID无效');
            }

            $apiKey = ApiKey::where('user_id', $user->id)->find($apiKeyId);
            
            if (!$apiKey) {
                return $this->error('API密钥不存在', 404);
            }

            $apiKey->delete();

            return $this->success(null, 'API密钥删除成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '删除API密钥');
        }
    }
}
