<?php

namespace app\controller;

use app\model\RoomFacility;
use app\model\RoomFacilityCategory;
use support\Request;
use support\Response;

/**
 * 房型设施设备控制器
 */
class RoomFacilityController extends BaseController
{
    /**
     * 获取设施分类列表
     */
    public function categories(Request $request): Response
    {
        try {
            $type = $request->get('type', 'tree'); // tree, flat, stats
            
            switch ($type) {
                case 'flat':
                    $data = RoomFacilityCategory::getFlat();
                    break;
                case 'stats':
                    $data = RoomFacilityCategory::getStats();
                    break;
                default:
                    $data = RoomFacilityCategory::getTree();
                    break;
            }
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取分类列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建设施分类
     */
    public function createCategory(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证必填字段
            if (empty($data['code']) || empty($data['name'])) {
                return $this->error('分类代码和名称不能为空', 400);
            }

            // 检查代码是否已存在
            if (RoomFacilityCategory::codeExists($data['code'])) {
                return $this->error('分类代码已存在', 400);
            }
            
            // 设置默认值
            $data['parent_id'] = $data['parent_id'] ?? 0;
            $data['sort_order'] = $data['sort_order'] ?? RoomFacilityCategory::getNextSortOrder($data['parent_id']);
            $data['is_active'] = $data['is_active'] ?? true;
            
            $category = RoomFacilityCategory::create($data);
            
            return $this->success($category, '分类创建成功');
        } catch (\Exception $e) {
            return $this->error('创建分类失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新设施分类
     */
    public function updateCategory(Request $request): Response
    {
        try {
            $id = (int)$request->post('id');
            $data = $request->post();
            
            $category = RoomFacilityCategory::find($id);
            if (!$category) {
                return $this->error('分类不存在', 404);
            }
            
            // 检查代码是否已存在
            if (!empty($data['code']) && RoomFacilityCategory::codeExists($data['code'], $id)) {
                return $this->error('分类代码已存在', 400);
            }
            
            $category->update($data);
            
            return $this->success($category, '分类更新成功');
        } catch (\Exception $e) {
            return $this->error('更新分类失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除设施分类
     */
    public function deleteCategory(Request $request): Response
    {
        try {
            $id = (int)$request->post('id');
            
            $category = RoomFacilityCategory::find($id);
            if (!$category) {
                return $this->error('分类不存在', 404);
            }
            
            if (!$category->canDelete()) {
                return $this->error('该分类下还有子分类或设施，无法删除', 400);
            }
            
            $category->softDelete();
            
            return $this->success(null, '分类删除成功');
        } catch (\Exception $e) {
            return $this->error('删除分类失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取设施列表
     */
    public function facilities(Request $request): Response
    {
        try {
            $type = $request->get('type', 'category'); // category, flat, tree
            $categoryId = $request->get('category_id');
            $keyword = $request->get('keyword');
            
            if ($keyword) {
                $data = RoomFacility::search($keyword);
            } elseif ($categoryId) {
                $data = RoomFacility::getByCategoryId($categoryId);
            } else {
                switch ($type) {
                    case 'flat':
                        $data = RoomFacility::getFlat();
                        break;
                    case 'tree':
                        $data = RoomFacility::getTree();
                        break;
                    default:
                        $data = RoomFacility::getByCategory();
                        break;
                }
            }
            
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取设施列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建设施
     */
    public function createFacility(Request $request): Response
    {
        try {
            $data = $request->post();
            
            // 验证必填字段
            if (empty($data['code']) || empty($data['name']) || empty($data['category_id'])) {
                return $this->error('设施代码、名称和分类不能为空', 400);
            }

            // 检查代码是否已存在
            if (RoomFacility::codeExists($data['code'])) {
                return $this->error('设施代码已存在', 400);
            }
            
            // 验证分类是否存在
            if (!RoomFacilityCategory::find($data['category_id'])) {
                return $this->error('设施分类不存在', 400);
            }
            
            // 设置默认值
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['is_active'] = $data['is_active'] ?? true;
            $data['is_chargeable'] = $data['is_chargeable'] ?? false;
            
            $facility = RoomFacility::create($data);
            
            return $this->success($facility, '设施创建成功');
        } catch (\Exception $e) {
            return $this->error('创建设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新设施
     */
    public function updateFacility(Request $request): Response
    {
        try {
            $id = (int)$request->post('id');
            $data = $request->post();
            
            $facility = RoomFacility::find($id);
            if (!$facility) {
                return $this->error('设施不存在', 404);
            }
            
            // 检查代码是否已存在
            if (!empty($data['code']) && RoomFacility::codeExists($data['code'], $id)) {
                return $this->error('设施代码已存在', 400);
            }
            
            // 验证分类是否存在
            if (!empty($data['category_id']) && !RoomFacilityCategory::find($data['category_id'])) {
                return $this->error('设施分类不存在', 400);
            }
            
            $facility->update($data);
            
            return $this->success($facility, '设施更新成功');
        } catch (\Exception $e) {
            return $this->error('更新设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除设施
     */
    public function deleteFacility(Request $request): Response
    {
        try {
            $id = (int)$request->post('id');
            
            $facility = RoomFacility::find($id);
            if (!$facility) {
                return $this->error('设施不存在', 404);
            }
            
            if (!$facility->canDelete()) {
                return $this->error('该设施已被房型使用，无法删除', 400);
            }
            
            $facility->softDelete();
            
            return $this->success(null, '设施删除成功');
        } catch (\Exception $e) {
            return $this->error('删除设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量操作设施
     */
    public function batchFacility(Request $request): Response
    {
        try {
            $action = $request->post('action'); // enable, disable, delete
            $ids = $request->post('ids', []);
            
            if (empty($ids)) {
                return $this->error('请选择要操作的设施', 400);
            }
            
            $facilities = RoomFacility::whereIn('id', $ids)->get();
            $successCount = 0;
            
            foreach ($facilities as $facility) {
                switch ($action) {
                    case 'enable':
                        $facility->is_active = true;
                        if ($facility->save()) $successCount++;
                        break;
                    case 'disable':
                        $facility->is_active = false;
                        if ($facility->save()) $successCount++;
                        break;
                    case 'delete':
                        if ($facility->canDelete() && $facility->softDelete()) {
                            $successCount++;
                        }
                        break;
                }
            }
            
            return $this->success([
                'total' => count($facilities),
                'success' => $successCount,
                'failed' => count($facilities) - $successCount,
            ], "批量操作完成，成功 {$successCount} 个");
        } catch (\Exception $e) {
            return $this->error('批量操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取设施使用统计
     */
    public function facilityStats(Request $request): Response
    {
        try {
            $data = \app\model\RoomTypeFacility::getFacilityStats();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取统计数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取房型设施
     */
    public function getRoomTypeFacilities(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->get('room_type_id');

            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }

            $data = \app\model\RoomTypeFacility::getRoomTypeFacilities($roomTypeId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error('获取房型设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 设置房型设施
     */
    public function setRoomTypeFacilities(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->post('room_type_id');
            $facilities = $request->post('facilities', []);

            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }

            // 验证房型是否存在
            if (!\app\model\RoomType::find($roomTypeId)) {
                return $this->error('房型不存在', 404);
            }

            // 验证设施数据
            foreach ($facilities as $facility) {
                if (empty($facility['facility_id'])) {
                    return $this->error('设施ID不能为空', 400);
                }

                if (!RoomFacility::find($facility['facility_id'])) {
                    return $this->error('设施不存在', 400);
                }
            }

            \app\model\RoomTypeFacility::setRoomTypeFacilities($roomTypeId, $facilities);

            return $this->success(null, '房型设施设置成功');
        } catch (\Exception $e) {
            return $this->error('设置房型设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 添加房型设施
     */
    public function addRoomTypeFacility(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->post('room_type_id');
            $facilityId = (int)$request->post('facility_id');
            $options = $request->post('options', []);

            if (!$roomTypeId || !$facilityId) {
                return $this->error('房型ID和设施ID不能为空', 400);
            }

            $result = \app\model\RoomTypeFacility::addRoomTypeFacility($roomTypeId, $facilityId, $options);

            if (!$result) {
                return $this->error('设施已存在', 400);
            }

            return $this->success($result, '设施添加成功');
        } catch (\Exception $e) {
            return $this->error('添加设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 移除房型设施
     */
    public function removeRoomTypeFacility(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->post('room_type_id');
            $facilityId = (int)$request->post('facility_id');

            if (!$roomTypeId || !$facilityId) {
                return $this->error('房型ID和设施ID不能为空', 400);
            }

            \app\model\RoomTypeFacility::removeRoomTypeFacility($roomTypeId, $facilityId);

            return $this->success(null, '设施移除成功');
        } catch (\Exception $e) {
            return $this->error('移除设施失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新房型设施排序
     */
    public function updateFacilityOrder(Request $request): Response
    {
        try {
            $roomTypeId = (int)$request->post('room_type_id');
            $facilityOrders = $request->post('facility_orders', []);

            if (!$roomTypeId) {
                return $this->error('房型ID不能为空', 400);
            }

            \app\model\RoomTypeFacility::updateSortOrder($roomTypeId, $facilityOrders);

            return $this->success(null, '排序更新成功');
        } catch (\Exception $e) {
            return $this->error('更新排序失败: ' . $e->getMessage());
        }
    }
}
