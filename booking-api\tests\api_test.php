<?php

/**
 * API测试脚本
 * 用于测试酒店预订系统API的基本功能
 */

class ApiTester
{
    private $baseUrl;
    private $token;

    public function __construct($baseUrl = 'http://localhost:8787/api/v1')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * 执行HTTP请求
     */
    private function request($method, $endpoint, $data = null, $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        // 设置请求头
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        if ($this->token) {
            $defaultHeaders[] = 'Authorization: Bearer ' . $this->token;
        }
        
        $allHeaders = array_merge($defaultHeaders, $headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
        
        // 设置请求数据
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("CURL Error: $error");
        }
        
        $result = json_decode($response, true);
        
        return [
            'http_code' => $httpCode,
            'response' => $result,
            'raw_response' => $response
        ];
    }

    /**
     * 测试API健康检查
     */
    public function testHealth()
    {
        echo "测试API健康检查...\n";
        
        $result = $this->request('GET', '/health');
        
        if ($result['http_code'] === 200 && $result['response']['code'] === 0) {
            echo "✓ 健康检查通过\n";
            return true;
        } else {
            echo "✗ 健康检查失败\n";
            echo "响应: " . $result['raw_response'] . "\n";
            return false;
        }
    }

    /**
     * 测试用户注册
     */
    public function testRegister()
    {
        echo "测试用户注册...\n";
        
        $userData = [
            'username' => 'test_user_' . time(),
            'email' => 'test_' . time() . '@example.com',
            'password' => 'test123456',
            'real_name' => '测试用户'
        ];
        
        $result = $this->request('POST', '/auth/register', $userData);
        
        if ($result['http_code'] === 200 && $result['response']['code'] === 0) {
            echo "✓ 用户注册成功\n";
            return $userData;
        } else {
            echo "✗ 用户注册失败\n";
            echo "响应: " . $result['raw_response'] . "\n";
            return false;
        }
    }

    /**
     * 测试用户登录
     */
    public function testLogin($userData)
    {
        echo "测试用户登录...\n";
        
        $loginData = [
            'username' => $userData['username'],
            'password' => $userData['password']
        ];
        
        $result = $this->request('POST', '/auth/login', $loginData);
        
        if ($result['http_code'] === 200 && $result['response']['code'] === 0) {
            $this->token = $result['response']['data']['token'];
            echo "✓ 用户登录成功\n";
            return true;
        } else {
            echo "✗ 用户登录失败\n";
            echo "响应: " . $result['raw_response'] . "\n";
            return false;
        }
    }

    /**
     * 测试获取用户信息
     */
    public function testGetUserInfo()
    {
        echo "测试获取用户信息...\n";
        
        $result = $this->request('GET', '/auth/me');
        
        if ($result['http_code'] === 200 && $result['response']['code'] === 0) {
            echo "✓ 获取用户信息成功\n";
            return true;
        } else {
            echo "✗ 获取用户信息失败\n";
            echo "响应: " . $result['raw_response'] . "\n";
            return false;
        }
    }

    /**
     * 测试创建酒店
     */
    public function testCreateHotel()
    {
        echo "测试创建酒店...\n";
        
        $hotelData = [
            'name' => '测试酒店_' . time(),
            'city' => '北京',
            'address' => '测试地址123号',
            'star_rating' => 4,
            'description' => '这是一个测试酒店'
        ];
        
        $result = $this->request('POST', '/hotels', $hotelData);
        
        if ($result['http_code'] === 200 && $result['response']['code'] === 0) {
            echo "✓ 创建酒店成功\n";
            return $result['response']['data'];
        } else {
            echo "✗ 创建酒店失败\n";
            echo "响应: " . $result['raw_response'] . "\n";
            return false;
        }
    }

    /**
     * 测试获取酒店列表
     */
    public function testGetHotels()
    {
        echo "测试获取酒店列表...\n";
        
        $result = $this->request('GET', '/hotels?page=1&limit=10');
        
        if ($result['http_code'] === 200 && $result['response']['code'] === 0) {
            echo "✓ 获取酒店列表成功\n";
            return true;
        } else {
            echo "✗ 获取酒店列表失败\n";
            echo "响应: " . $result['raw_response'] . "\n";
            return false;
        }
    }

    /**
     * 测试创建房型
     */
    public function testCreateRoomType($hotelId)
    {
        echo "测试创建房型...\n";
        
        $roomTypeData = [
            'name' => '标准双人间_' . time(),
            'code' => 'STD_' . time(),
            'max_occupancy' => 2,
            'base_price' => 299.00,
            'description' => '舒适的标准双人间'
        ];
        
        $result = $this->request('POST', "/hotels/{$hotelId}/room-types", $roomTypeData);
        
        if ($result['http_code'] === 200 && $result['response']['code'] === 0) {
            echo "✓ 创建房型成功\n";
            return $result['response']['data'];
        } else {
            echo "✗ 创建房型失败\n";
            echo "响应: " . $result['raw_response'] . "\n";
            return false;
        }
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始运行API测试...\n\n";
        
        $results = [];
        
        // 1. 健康检查
        $results['health'] = $this->testHealth();
        echo "\n";
        
        // 2. 用户注册
        $userData = $this->testRegister();
        $results['register'] = $userData !== false;
        echo "\n";
        
        if ($userData) {
            // 3. 用户登录
            $results['login'] = $this->testLogin($userData);
            echo "\n";
            
            if ($this->token) {
                // 4. 获取用户信息
                $results['user_info'] = $this->testGetUserInfo();
                echo "\n";
                
                // 5. 创建酒店
                $hotel = $this->testCreateHotel();
                $results['create_hotel'] = $hotel !== false;
                echo "\n";
                
                // 6. 获取酒店列表
                $results['get_hotels'] = $this->testGetHotels();
                echo "\n";
                
                if ($hotel) {
                    // 7. 创建房型
                    $roomType = $this->testCreateRoomType($hotel['id']);
                    $results['create_room_type'] = $roomType !== false;
                    echo "\n";
                }
            }
        }
        
        // 输出测试结果
        echo "测试结果汇总:\n";
        echo "================\n";
        
        $passed = 0;
        $total = count($results);
        
        foreach ($results as $test => $result) {
            $status = $result ? '✓ 通过' : '✗ 失败';
            echo sprintf("%-20s: %s\n", $test, $status);
            if ($result) $passed++;
        }
        
        echo "\n总计: {$passed}/{$total} 个测试通过\n";
        
        if ($passed === $total) {
            echo "🎉 所有测试都通过了！\n";
        } else {
            echo "⚠️  有测试失败，请检查API服务\n";
        }
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $tester = new ApiTester();
    $tester->runAllTests();
} else {
    echo "请在命令行中运行此脚本\n";
}
