<?php

namespace app\service;

use app\model\Hotel;
use app\model\RoomType;
use app\model\RoomInventory;
use app\model\InventoryLog;

/**
 * 库存管理服务
 */
class InventoryService extends BaseService
{
    /**
     * 查询库存
     *
     * @param array $params
     * @return array
     */
    public function queryInventory(array $params)
    {
        try {
            $this->validateRequired($params, ['hotel_id', 'start_date', 'end_date']);
            $this->validateDateRange($params['start_date'], $params['end_date']);

            $query = RoomInventory::with(['hotel', 'roomType'])
                ->where('hotel_id', $params['hotel_id'])
                ->whereBetween('date', [$params['start_date'], $params['end_date']]);

            // 房型筛选
            if (!empty($params['room_type_id'])) {
                $query->where('room_type_id', $params['room_type_id']);
            }

            // 只查询有库存的
            if (!empty($params['available_only'])) {
                $query->available();
            }

            $inventory = $query->orderBy('date')->orderBy('room_type_id')->get();

            return $this->success($inventory);

        } catch (\Exception $e) {
            $this->logError('查询库存失败', ['error' => $e->getMessage(), 'params' => $params]);
            return $this->error('查询库存失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量查询库存
     *
     * @param array $params
     * @return array
     */
    public function batchQueryInventory(array $params)
    {
        try {
            $this->validateRequired($params, ['hotel_id', 'room_type_ids', 'start_date', 'end_date']);
            $this->validateDateRange($params['start_date'], $params['end_date']);

            if (!is_array($params['room_type_ids'])) {
                throw new \InvalidArgumentException('room_type_ids必须是数组');
            }

            $inventory = RoomInventory::with(['hotel', 'roomType'])
                ->where('hotel_id', $params['hotel_id'])
                ->whereIn('room_type_id', $params['room_type_ids'])
                ->whereBetween('date', [$params['start_date'], $params['end_date']])
                ->orderBy('room_type_id')
                ->orderBy('date')
                ->get();

            // 按房型分组
            $result = $inventory->groupBy('room_type_id')->map(function ($items) {
                return [
                    'room_type' => $items->first()->roomType,
                    'inventory' => $items->values()
                ];
            });

            return $this->success($result);

        } catch (\Exception $e) {
            $this->logError('批量查询库存失败', ['error' => $e->getMessage(), 'params' => $params]);
            return $this->error('批量查询库存失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新库存
     *
     * @param array $data
     * @return array
     */
    public function updateInventory(array $data)
    {
        try {
            $this->validateRequired($data, ['hotel_id', 'room_type_id', 'date', 'available_rooms']);

            if (!$this->validateDate($data['date'])) {
                throw new \InvalidArgumentException('日期格式错误');
            }

            return $this->transaction(function () use ($data) {
                $inventory = RoomInventory::where('hotel_id', $data['hotel_id'])
                    ->where('room_type_id', $data['room_type_id'])
                    ->where('date', $data['date'])
                    ->first();

                $beforeAvailable = $inventory ? $inventory->available_rooms : 0;

                if ($inventory) {
                    $inventory->update($this->filterEmpty($data));
                } else {
                    $inventory = RoomInventory::create($data);
                }

                // 记录库存变更日志
                InventoryLog::logManualChange(
                    $data['hotel_id'],
                    $data['room_type_id'],
                    $data['date'],
                    $beforeAvailable,
                    $inventory->available_rooms,
                    $data['reason'] ?? '手动更新库存',
                    $this->getCurrentUser()->id ?? null
                );

                $this->logInfo('更新库存成功', [
                    'hotel_id' => $data['hotel_id'],
                    'room_type_id' => $data['room_type_id'],
                    'date' => $data['date'],
                    'before_available' => $beforeAvailable,
                    'after_available' => $inventory->available_rooms
                ]);

                return $this->success($inventory, '更新库存成功');
            });

        } catch (\Exception $e) {
            $this->logError('更新库存失败', ['error' => $e->getMessage(), 'data' => $data]);
            return $this->error('更新库存失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量更新库存
     *
     * @param array $updates
     * @return array
     */
    public function batchUpdateInventory(array $updates)
    {
        try {
            if (empty($updates['updates']) || !is_array($updates['updates'])) {
                throw new \InvalidArgumentException('updates参数必须是数组');
            }

            return $this->transaction(function () use ($updates) {
                $results = [];
                $currentUser = $this->getCurrentUser();

                foreach ($updates['updates'] as $data) {
                    $this->validateRequired($data, ['hotel_id', 'room_type_id', 'date', 'available_rooms']);

                    $inventory = RoomInventory::where('hotel_id', $data['hotel_id'])
                        ->where('room_type_id', $data['room_type_id'])
                        ->where('date', $data['date'])
                        ->first();

                    $beforeAvailable = $inventory ? $inventory->available_rooms : 0;

                    if ($inventory) {
                        $inventory->update($this->filterEmpty($data));
                    } else {
                        $inventory = RoomInventory::create($data);
                    }

                    // 记录库存变更日志
                    InventoryLog::logManualChange(
                        $data['hotel_id'],
                        $data['room_type_id'],
                        $data['date'],
                        $beforeAvailable,
                        $inventory->available_rooms,
                        $data['reason'] ?? '批量更新库存',
                        $currentUser->id ?? null
                    );

                    $results[] = $inventory;
                }

                $this->logInfo('批量更新库存成功', ['count' => count($results)]);

                return $this->success($results, '批量更新库存成功');
            });

        } catch (\Exception $e) {
            $this->logError('批量更新库存失败', ['error' => $e->getMessage(), 'updates' => $updates]);
            return $this->error('批量更新库存失败: ' . $e->getMessage());
        }
    }

    /**
     * 锁定库存
     *
     * @param array $data
     * @return array
     */
    public function lockInventory(array $data)
    {
        try {
            $this->validateRequired($data, ['hotel_id', 'room_type_id', 'check_in_date', 'check_out_date', 'rooms']);
            $this->validateDateRange($data['check_in_date'], $data['check_out_date']);

            $rooms = (int)$data['rooms'];
            if ($rooms <= 0) {
                throw new \InvalidArgumentException('房间数量必须大于0');
            }

            return $this->transaction(function () use ($data, $rooms) {
                $startDate = $data['check_in_date'];
                $endDate = date('Y-m-d', strtotime($data['check_out_date'] . ' -1 day'));

                $lockId = $this->generateUniqueId('LOCK_');
                $lockedDates = [];

                // 检查并锁定每一天的库存
                $currentDate = $startDate;
                while ($currentDate <= $endDate) {
                    $inventory = RoomInventory::where('hotel_id', $data['hotel_id'])
                        ->where('room_type_id', $data['room_type_id'])
                        ->where('date', $currentDate)
                        ->first();

                    if (!$inventory || !$inventory->hasAvailability($rooms)) {
                        // 如果任何一天库存不足，回滚之前的锁定
                        foreach ($lockedDates as $lockedDate) {
                            $lockedInventory = RoomInventory::where('hotel_id', $data['hotel_id'])
                                ->where('room_type_id', $data['room_type_id'])
                                ->where('date', $lockedDate)
                                ->first();
                            if ($lockedInventory) {
                                $lockedInventory->releaseBlocked($rooms);
                            }
                        }
                        throw new \Exception("日期 {$currentDate} 库存不足");
                    }

                    $inventory->blockRooms($rooms, $data['lock_reason'] ?? '预订锁定');
                    $lockedDates[] = $currentDate;

                    $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
                }

                $this->logInfo('锁定库存成功', [
                    'lock_id' => $lockId,
                    'hotel_id' => $data['hotel_id'],
                    'room_type_id' => $data['room_type_id'],
                    'dates' => $lockedDates,
                    'rooms' => $rooms
                ]);

                return $this->success([
                    'lock_id' => $lockId,
                    'locked_dates' => $lockedDates,
                    'rooms' => $rooms
                ], '锁定库存成功');
            });

        } catch (\Exception $e) {
            $this->logError('锁定库存失败', ['error' => $e->getMessage(), 'data' => $data]);
            return $this->error('锁定库存失败: ' . $e->getMessage());
        }
    }

    /**
     * 释放库存
     *
     * @param array $data
     * @return array
     */
    public function releaseInventory(array $data)
    {
        try {
            $this->validateRequired($data, ['hotel_id', 'room_type_id', 'check_in_date', 'check_out_date', 'rooms']);
            $this->validateDateRange($data['check_in_date'], $data['check_out_date']);

            $rooms = (int)$data['rooms'];
            if ($rooms <= 0) {
                throw new \InvalidArgumentException('房间数量必须大于0');
            }

            return $this->transaction(function () use ($data, $rooms) {
                $startDate = $data['check_in_date'];
                $endDate = date('Y-m-d', strtotime($data['check_out_date'] . ' -1 day'));

                $releasedDates = [];

                // 释放每一天的库存
                $currentDate = $startDate;
                while ($currentDate <= $endDate) {
                    $inventory = RoomInventory::where('hotel_id', $data['hotel_id'])
                        ->where('room_type_id', $data['room_type_id'])
                        ->where('date', $currentDate)
                        ->first();

                    if ($inventory) {
                        $inventory->releaseBlocked($rooms);
                        $releasedDates[] = $currentDate;
                    }

                    $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
                }

                $this->logInfo('释放库存成功', [
                    'hotel_id' => $data['hotel_id'],
                    'room_type_id' => $data['room_type_id'],
                    'dates' => $releasedDates,
                    'rooms' => $rooms
                ]);

                return $this->success([
                    'released_dates' => $releasedDates,
                    'rooms' => $rooms
                ], '释放库存成功');
            });

        } catch (\Exception $e) {
            $this->logError('释放库存失败', ['error' => $e->getMessage(), 'data' => $data]);
            return $this->error('释放库存失败: ' . $e->getMessage());
        }
    }
}
