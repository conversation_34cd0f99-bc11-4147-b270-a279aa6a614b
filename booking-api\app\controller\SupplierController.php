<?php

namespace app\controller;

use app\service\SupplierService;
use support\Request;
use support\Response;

/**
 * 供应商管理控制器
 */
class SupplierController extends BaseController
{
    /**
     * 供应商服务
     *
     * @var SupplierService
     */
    private $supplierService;

    public function __construct()
    {
        $this->supplierService = new SupplierService();
    }

    /**
     * 获取供应商列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            $result = $this->supplierService->getSupplierList($params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取供应商列表');
        }
    }

    /**
     * 获取供应商详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');

            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $result = $this->supplierService->getSupplierDetail($supplierId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取供应商详情');
        }
    }

    /**
     * 创建供应商
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['code', 'name', 'type']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'contact_email' => 'email',
                'contact_phone' => 'phone',
                'commission_rate' => 'positive',
                'settlement_cycle' => 'integer',
                'priority' => 'integer',
                'timeout_seconds' => 'integer',
                'retry_times' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证供应商类型
            $validTypes = ['hotel_direct', 'ota', 'wholesaler', 'bedbank'];
            if (!in_array($data['type'], $validTypes)) {
                return $this->error('无效的供应商类型');
            }

            $result = $this->supplierService->createSupplier($data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '创建供应商');
        }
    }

    /**
     * 更新供应商信息
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $data = $this->getInput($request);

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'contact_email' => 'email',
                'contact_phone' => 'phone',
                'commission_rate' => 'positive',
                'settlement_cycle' => 'integer',
                'priority' => 'integer',
                'timeout_seconds' => 'integer',
                'retry_times' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 验证供应商类型
            if (!empty($data['type'])) {
                $validTypes = ['hotel_direct', 'ota', 'wholesaler', 'bedbank'];
                if (!in_array($data['type'], $validTypes)) {
                    return $this->error('无效的供应商类型');
                }
            }

            $result = $this->supplierService->updateSupplier($supplierId, $data);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '更新供应商信息');
        }
    }

    /**
     * 删除供应商
     *
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $result = $this->supplierService->deleteSupplier($supplierId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '删除供应商');
        }
    }

    /**
     * 测试供应商API连接
     *
     * @param Request $request
     * @return Response
     */
    public function testConnection(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $result = $this->supplierService->testSupplierConnection($supplierId);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '测试供应商连接');
        }
    }

    /**
     * 启用供应商
     *
     * @param Request $request
     * @return Response
     */
    public function enable(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $result = $this->supplierService->toggleSupplierStatus($supplierId, true);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '启用供应商');
        }
    }

    /**
     * 禁用供应商
     *
     * @param Request $request
     * @return Response
     */
    public function disable(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $result = $this->supplierService->toggleSupplierStatus($supplierId, false);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '禁用供应商');
        }
    }

    /**
     * 获取供应商酒店映射列表
     *
     * @param Request $request
     * @return Response
     */
    public function hotelMappings(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $params = array_merge(
                $this->getInput($request),
                $this->getPaginationParams($request)
            );

            $result = $this->supplierService->getSupplierHotelMappings($supplierId, $params);
            return $this->handleServiceResult($result);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取供应商酒店映射');
        }
    }

    /**
     * 创建酒店映射
     *
     * @param Request $request
     * @return Response
     */
    public function createHotelMapping(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['hotel_id', 'supplier_hotel_id']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 验证数据格式
            $formatErrors = $this->validateFormat($data, [
                'hotel_id' => 'integer'
            ]);
            if ($formatErrors) {
                return $this->error('数据格式错误', 400, $formatErrors);
            }

            // 检查映射是否已存在
            $exists = \app\model\SupplierHotelMapping::where('supplier_id', $supplierId)
                ->where('hotel_id', $data['hotel_id'])
                ->exists();

            if ($exists) {
                return $this->error('该酒店映射已存在');
            }

            // 创建映射
            $mappingData = array_merge($data, [
                'supplier_id' => $supplierId,
                'is_active' => $data['is_active'] ?? true,
                'sync_status' => 'pending'
            ]);

            $mapping = \app\model\SupplierHotelMapping::create($mappingData);

            return $this->success($mapping, '创建酒店映射成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '创建酒店映射');
        }
    }

    /**
     * 更新酒店映射
     *
     * @param Request $request
     * @return Response
     */
    public function updateHotelMapping(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            $mappingId = (int)$request->route('mapping_id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            if ($mappingId <= 0) {
                return $this->error('映射ID无效');
            }

            $mapping = \app\model\SupplierHotelMapping::where('supplier_id', $supplierId)
                ->find($mappingId);

            if (!$mapping) {
                return $this->error('酒店映射不存在', 404);
            }

            $data = $this->getInput($request);

            // 过滤允许更新的字段
            $allowedFields = [
                'supplier_hotel_id', 'supplier_hotel_code', 'supplier_hotel_name',
                'room_type_mappings', 'rate_plan_mappings', 'is_active'
            ];

            $updateData = array_intersect_key($data, array_flip($allowedFields));
            $updateData = array_filter($updateData, function($value) {
                return $value !== null && $value !== '';
            });

            if (empty($updateData)) {
                return $this->error('没有可更新的数据');
            }

            $mapping->update($updateData);

            return $this->success($mapping, '更新酒店映射成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新酒店映射');
        }
    }

    /**
     * 删除酒店映射
     *
     * @param Request $request
     * @return Response
     */
    public function deleteHotelMapping(Request $request)
    {
        try {
            $supplierId = (int)$request->route('id');
            $mappingId = (int)$request->route('mapping_id');
            
            if ($supplierId <= 0) {
                return $this->error('供应商ID无效');
            }

            if ($mappingId <= 0) {
                return $this->error('映射ID无效');
            }

            $mapping = \app\model\SupplierHotelMapping::where('supplier_id', $supplierId)
                ->find($mappingId);

            if (!$mapping) {
                return $this->error('酒店映射不存在', 404);
            }

            $mapping->delete();

            return $this->success(null, '删除酒店映射成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '删除酒店映射');
        }
    }
}
