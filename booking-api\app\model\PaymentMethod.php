<?php

namespace app\model;

use support\Model;

/**
 * 常用支付方式模型
 */
class PaymentMethod extends Model
{
    /**
     * 表名
     */
    protected $table = 'payment_methods';

    /**
     * 主键
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'payment_name',
        'payment_code',
        'payment_type',
        'description',
        'icon',
        'cvc_required',
        'sort_order',
        'status',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'id' => 'integer',
        'cvc_required' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 验证规则
     */
    public static function rules(): array
    {
        return [
            'payment_name' => 'required|string|max:100',
            'payment_code' => 'required|string|max:50|unique:payment_methods,payment_code',
            'payment_type' => 'required|string|max:50',
            'description' => 'nullable|string|max:500',
            'icon' => 'nullable|string|max:100',
            'cvc_required' => 'boolean',
            'sort_order' => 'integer|min:0|max:9999',
            'status' => 'in:active,inactive',
        ];
    }

    /**
     * 关联酒店支付方式
     */
    public function hotelPaymentMethods()
    {
        return $this->hasMany(HotelPaymentMethod::class, 'payment_id', 'id');
    }

    /**
     * 作用域：启用的支付方式
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * 作用域：按类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('payment_type', $type);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
    }

    /**
     * 获取常用支付方式列表
     */
    public static function getCommonMethods(): \Illuminate\Database\Eloquent\Collection
    {
        return self::active()->ordered()->get();
    }

    /**
     * 获取支付方式图标
     */
    public function getIcon(): string
    {
        if ($this->icon) {
            return $this->icon;
        }

        $iconMap = [
            'credit_card' => 'credit-card',
            'debit_card' => 'credit-card',
            'alipay' => 'smartphone',
            'wechat_pay' => 'smartphone',
            'bank_transfer' => 'building-2',
            'cash' => 'banknote',
            'paypal' => 'credit-card',
            'apple_pay' => 'smartphone',
            'google_pay' => 'smartphone',
        ];
        
        return $iconMap[$this->payment_type] ?? 'credit-card';
    }

    /**
     * 初始化常用支付方式
     */
    public static function initializeCommonMethods(): array
    {
        $commonMethods = [
            [
                'payment_name' => '现金',
                'payment_code' => 'CASH',
                'payment_type' => 'cash',
                'description' => '现金支付，到店付款',
                'cvc_required' => false,
                'sort_order' => 1,
                'status' => 'active',
            ],
            [
                'payment_name' => '信用卡',
                'payment_code' => 'CREDIT_CARD',
                'payment_type' => 'credit_card',
                'description' => '信用卡支付，支持Visa、MasterCard等',
                'cvc_required' => true,
                'sort_order' => 2,
                'status' => 'active',
            ],
            [
                'payment_name' => '借记卡',
                'payment_code' => 'DEBIT_CARD',
                'payment_type' => 'debit_card',
                'description' => '借记卡支付，银联卡等',
                'cvc_required' => true,
                'sort_order' => 3,
                'status' => 'active',
            ],
            [
                'payment_name' => '支付宝',
                'payment_code' => 'ALIPAY',
                'payment_type' => 'alipay',
                'description' => '支付宝在线支付',
                'cvc_required' => false,
                'sort_order' => 4,
                'status' => 'active',
            ],
            [
                'payment_name' => '微信支付',
                'payment_code' => 'WECHAT_PAY',
                'payment_type' => 'wechat_pay',
                'description' => '微信在线支付',
                'cvc_required' => false,
                'sort_order' => 5,
                'status' => 'active',
            ],
            [
                'payment_name' => '银行转账',
                'payment_code' => 'BANK_TRANSFER',
                'payment_type' => 'bank_transfer',
                'description' => '银行转账支付',
                'cvc_required' => false,
                'sort_order' => 6,
                'status' => 'active',
            ],
            [
                'payment_name' => 'PayPal',
                'payment_code' => 'PAYPAL',
                'payment_type' => 'paypal',
                'description' => 'PayPal在线支付',
                'cvc_required' => false,
                'sort_order' => 7,
                'status' => 'active',
            ],
            [
                'payment_name' => 'Apple Pay',
                'payment_code' => 'APPLE_PAY',
                'payment_type' => 'apple_pay',
                'description' => 'Apple Pay移动支付',
                'cvc_required' => false,
                'sort_order' => 8,
                'status' => 'active',
            ],
            [
                'payment_name' => 'Google Pay',
                'payment_code' => 'GOOGLE_PAY',
                'payment_type' => 'google_pay',
                'description' => 'Google Pay移动支付',
                'cvc_required' => false,
                'sort_order' => 9,
                'status' => 'active',
            ],
        ];

        $created = [];
        foreach ($commonMethods as $method) {
            // 检查是否已存在
            $existing = self::where('payment_code', $method['payment_code'])->first();
            if (!$existing) {
                $created[] = self::create($method);
            }
        }

        return $created;
    }

    /**
     * 转换为数组格式
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'payment_name' => $this->payment_name,
            'payment_code' => $this->payment_code,
            'payment_type' => $this->payment_type,
            'description' => $this->description,
            'icon' => $this->getIcon(),
            'cvc_required' => $this->cvc_required,
            'sort_order' => $this->sort_order,
            'status' => $this->status,
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
