<?php

namespace app\model;

/**
 * 房型库存模型
 * 对应数据库表：room_inventory
 */
class RoomInventory extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'room_inventory';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'hotel_id',
        'room_type_id',
        'date',

        // 房量信息
        'total_rooms',
        'available_rooms',
        'sold_rooms',
        'blocked_rooms',
        'maintenance_rooms',
        'overbooking_rooms',

        // 房态信息
        'is_closed',
        'close_reason',
        'is_stop_sale',
        'stop_sale_reason',

        // 销售限制
        'min_stay',
        'max_stay',
        'min_advance_booking',
        'max_advance_booking',
        'arrival_restriction',
        'departure_restriction',

        // 客人限制
        'max_occupancy',
        'max_adults',
        'max_children',

        // 价格信息
        'base_price',
        'min_price',
        'max_price',
        'currency',

        // 预订统计
        'booking_count',
        'checkin_count',
        'checkout_count',
        'cancellation_count',

        // 操作信息
        'last_updated_by',
        'last_updated_reason',

        // 兼容旧字段
        'booked_rooms',
        'advance_booking_days',
        'oversell_limit',

        // 新增字段 - 对标携程EBooking
        'rate_plan_id',
        'inventory_type',
        'inventory_source',
        'allocation_method',
        'distribution_channels',
        'channel_restrictions',
        'booking_restrictions',
        'guest_restrictions',
        'length_of_stay_restrictions',
        'advance_booking_restrictions',
        'cutoff_time',
        'release_time',
        'allotment_id',
        'allotment_type',
        'allotment_status',
        'allotment_release_days',
        'free_sale_limit',
        'on_request_limit',
        'group_allotment',
        'series_allotment',
        'dynamic_allotment',
        'elastic_allotment',
        'guaranteed_allotment',
        'non_guaranteed_allotment',
        'contracted_rooms',
        'released_rooms',
        'pickup_rooms',
        'no_show_rooms',
        'walk_in_rooms',
        'upgrade_rooms',
        'downgrade_rooms',
        'comp_rooms',
        'house_use_rooms',
        'out_of_order_rooms',
        'out_of_inventory_rooms',
        'repair_rooms',
        'deep_clean_rooms',
        'renovation_rooms',
        'seasonal_closure_rooms',
        'inventory_notes',
        'operational_notes',
        'revenue_notes',
        'forecast_notes',
        'competitive_notes',
        'market_notes',
        'event_notes',
        'weather_notes',
        'special_event_impact',
        'competitor_impact',
        'market_demand_level',
        'price_sensitivity',
        'booking_pace',
        'cancellation_rate',
        'no_show_rate',
        'adr_actual',
        'revpar_actual',
        'adr_budget',
        'revpar_budget',
        'adr_forecast',
        'revpar_forecast',
        'adr_last_year',
        'revpar_last_year',
        'pickup_percentage',
        'wash_factor',
        'displacement_factor',
        'compression_nights',
        'shoulder_nights',
        'low_demand_nights',
        'high_demand_nights',
        'peak_demand_nights',
        'holiday_premium',
        'weekend_premium',
        'event_premium',
        'seasonal_adjustment',
        'demand_adjustment',
        'competitive_adjustment',
        'inventory_optimization',
        'revenue_optimization',
        'yield_optimization',
        'overbooking_optimization',
        'upselling_opportunities',
        'cross_selling_opportunities',
        'package_opportunities',
        'loyalty_opportunities',
        'corporate_opportunities',
        'group_opportunities',
        'mice_opportunities',
        'leisure_opportunities',
        'business_opportunities',
        'international_opportunities',
        'domestic_opportunities',
        'online_opportunities',
        'offline_opportunities',
        'direct_opportunities',
        'indirect_opportunities',
        'mobile_opportunities',
        'social_opportunities',
        'email_opportunities',
        'sms_opportunities',
        'push_opportunities',
        'retargeting_opportunities',
        'referral_opportunities',
        'partnership_opportunities',
        'affiliate_opportunities',
        'influencer_opportunities',
        'content_opportunities',
        'seo_opportunities',
        'sem_opportunities',
        'social_media_opportunities',
        'pr_opportunities',
        'event_marketing_opportunities',
        'trade_show_opportunities',
        'fam_trip_opportunities',
        'site_inspection_opportunities',
        'educational_opportunities',
        'training_opportunities',
        'certification_opportunities',
        'award_opportunities',
        'recognition_opportunities',
        'sustainability_opportunities',
        'innovation_opportunities',
        'technology_opportunities',
        'automation_opportunities',
        'ai_opportunities',
        'ml_opportunities',
        'analytics_opportunities',
        'reporting_opportunities',
        'dashboard_opportunities',
        'kpi_opportunities',
        'benchmark_opportunities',
        'competitive_intelligence_opportunities',
        'market_intelligence_opportunities',
        'guest_intelligence_opportunities',
        'operational_intelligence_opportunities',
        'financial_intelligence_opportunities',
        'strategic_intelligence_opportunities'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'hotel_id' => 'integer',
        'room_type_id' => 'integer',
        'date' => 'date',

        // 房量信息
        'total_rooms' => 'integer',
        'available_rooms' => 'integer',
        'sold_rooms' => 'integer',
        'blocked_rooms' => 'integer',
        'maintenance_rooms' => 'integer',
        'overbooking_rooms' => 'integer',

        // 房态信息
        'is_closed' => 'boolean',
        'is_stop_sale' => 'boolean',

        // 销售限制
        'min_stay' => 'integer',
        'max_stay' => 'integer',
        'min_advance_booking' => 'integer',
        'max_advance_booking' => 'integer',

        // 客人限制
        'max_occupancy' => 'integer',
        'max_adults' => 'integer',
        'max_children' => 'integer',

        // 价格信息
        'base_price' => 'decimal:2',
        'min_price' => 'decimal:2',
        'max_price' => 'decimal:2',

        // 预订统计
        'booking_count' => 'integer',
        'checkin_count' => 'integer',
        'checkout_count' => 'integer',
        'cancellation_count' => 'integer',

        // 操作信息
        'last_updated_by' => 'integer',

        // 兼容旧字段
        'booked_rooms' => 'integer',
        'advance_booking_days' => 'integer',
        'oversell_limit' => 'integer',

        'created_at' => 'datetime',
        'updated_at' => 'datetime',

        // 新增字段转换
        'rate_plan_id' => 'integer',
        'distribution_channels' => 'array',
        'channel_restrictions' => 'array',
        'booking_restrictions' => 'array',
        'guest_restrictions' => 'array',
        'length_of_stay_restrictions' => 'array',
        'advance_booking_restrictions' => 'array',
        'cutoff_time' => 'datetime:H:i:s',
        'release_time' => 'datetime:H:i:s',
        'allotment_id' => 'integer',
        'allotment_release_days' => 'integer',
        'free_sale_limit' => 'integer',
        'on_request_limit' => 'integer',
        'group_allotment' => 'integer',
        'series_allotment' => 'integer',
        'dynamic_allotment' => 'boolean',
        'elastic_allotment' => 'boolean',
        'guaranteed_allotment' => 'boolean',
        'non_guaranteed_allotment' => 'boolean',
        'contracted_rooms' => 'integer',
        'released_rooms' => 'integer',
        'pickup_rooms' => 'integer',
        'no_show_rooms' => 'integer',
        'walk_in_rooms' => 'integer',
        'upgrade_rooms' => 'integer',
        'downgrade_rooms' => 'integer',
        'comp_rooms' => 'integer',
        'house_use_rooms' => 'integer',
        'out_of_order_rooms' => 'integer',
        'out_of_inventory_rooms' => 'integer',
        'repair_rooms' => 'integer',
        'deep_clean_rooms' => 'integer',
        'renovation_rooms' => 'integer',
        'seasonal_closure_rooms' => 'integer',
        'special_event_impact' => 'decimal:2',
        'competitor_impact' => 'decimal:2',
        'price_sensitivity' => 'decimal:2',
        'booking_pace' => 'decimal:2',
        'cancellation_rate' => 'decimal:2',
        'no_show_rate' => 'decimal:2',
        'adr_actual' => 'decimal:2',
        'revpar_actual' => 'decimal:2',
        'adr_budget' => 'decimal:2',
        'revpar_budget' => 'decimal:2',
        'adr_forecast' => 'decimal:2',
        'revpar_forecast' => 'decimal:2',
        'adr_last_year' => 'decimal:2',
        'revpar_last_year' => 'decimal:2',
        'pickup_percentage' => 'decimal:2',
        'wash_factor' => 'decimal:2',
        'displacement_factor' => 'decimal:2',
        'compression_nights' => 'integer',
        'shoulder_nights' => 'integer',
        'low_demand_nights' => 'integer',
        'high_demand_nights' => 'integer',
        'peak_demand_nights' => 'integer',
        'holiday_premium' => 'decimal:2',
        'weekend_premium' => 'decimal:2',
        'event_premium' => 'decimal:2',
        'seasonal_adjustment' => 'decimal:2',
        'demand_adjustment' => 'decimal:2',
        'competitive_adjustment' => 'decimal:2',
        'inventory_optimization' => 'boolean',
        'revenue_optimization' => 'boolean',
        'yield_optimization' => 'boolean',
        'overbooking_optimization' => 'boolean'
    ];

    /**
     * 获取所属酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 获取所属房型
     */
    public function roomType()
    {
        return $this->belongsTo(RoomType::class);
    }

    /**
     * 获取库存变更日志
     */
    public function logs()
    {
        return $this->hasMany(InventoryLog::class, 'room_type_id', 'room_type_id')
            ->where('hotel_id', $this->hotel_id)
            ->where('date', $this->date);
    }

    /**
     * 作用域：按日期筛选
     */
    public function scopeByDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * 作用域：有库存的
     */
    public function scopeAvailable($query)
    {
        return $query->where('available_rooms', '>', 0)->where('is_closed', false);
    }

    /**
     * 作用域：已关房的
     */
    public function scopeClosed($query)
    {
        return $query->where('is_closed', true);
    }

    /**
     * 检查是否有足够库存
     */
    public function hasAvailability($rooms = 1)
    {
        return !$this->is_closed && $this->available_rooms >= $rooms;
    }

    /**
     * 检查是否可以超售
     */
    public function canOversell($rooms = 1)
    {
        $totalAvailable = $this->available_rooms + $this->oversell_limit;
        return !$this->is_closed && $totalAvailable >= $rooms;
    }

    /**
     * 预订房间（减少库存）
     */
    public function bookRooms($rooms = 1)
    {
        if (!$this->hasAvailability($rooms)) {
            throw new \Exception('库存不足');
        }

        $this->available_rooms -= $rooms;
        $this->booked_rooms += $rooms;
        
        return $this->save();
    }

    /**
     * 取消预订（增加库存）
     */
    public function cancelBooking($rooms = 1)
    {
        $this->available_rooms += $rooms;
        $this->booked_rooms = max(0, $this->booked_rooms - $rooms);
        
        return $this->save();
    }

    /**
     * 锁定房间
     */
    public function blockRooms($rooms = 1, $reason = null)
    {
        if (!$this->hasAvailability($rooms)) {
            throw new \Exception('库存不足');
        }

        $this->available_rooms -= $rooms;
        $this->blocked_rooms += $rooms;
        
        if ($reason) {
            $this->close_reason = $reason;
        }
        
        return $this->save();
    }

    /**
     * 释放锁定的房间
     */
    public function releaseBlocked($rooms = 1)
    {
        $releaseRooms = min($rooms, $this->blocked_rooms);
        
        $this->available_rooms += $releaseRooms;
        $this->blocked_rooms -= $releaseRooms;
        
        return $this->save();
    }

    /**
     * 设置维修房间
     */
    public function setMaintenance($rooms = 1)
    {
        if ($this->available_rooms >= $rooms) {
            $this->available_rooms -= $rooms;
        }
        
        $this->maintenance_rooms += $rooms;
        
        return $this->save();
    }

    /**
     * 完成维修
     */
    public function completeMaintenance($rooms = 1)
    {
        $completeRooms = min($rooms, $this->maintenance_rooms);
        
        $this->available_rooms += $completeRooms;
        $this->maintenance_rooms -= $completeRooms;
        
        return $this->save();
    }

    /**
     * 关房
     */
    public function closeInventory($reason = null)
    {
        $this->is_closed = true;
        $this->close_reason = $reason;
        
        return $this->save();
    }

    /**
     * 开房
     */
    public function openInventory()
    {
        $this->is_closed = false;
        $this->close_reason = null;
        
        return $this->save();
    }

    /**
     * 获取实际可售房间数（包含超售）
     */
    public function getActualAvailableRoomsAttribute()
    {
        return $this->available_rooms + $this->oversell_limit;
    }

    /**
     * 获取入住率
     */
    public function getOccupancyRateAttribute()
    {
        if ($this->total_rooms == 0) {
            return 0;
        }
        
        return round(($this->booked_rooms / $this->total_rooms) * 100, 2);
    }

    /**
     * 批量更新库存
     */
    public static function batchUpdate($updates)
    {
        foreach ($updates as $update) {
            static::updateOrCreate(
                [
                    'hotel_id' => $update['hotel_id'],
                    'room_type_id' => $update['room_type_id'],
                    'date' => $update['date']
                ],
                $update
            );
        }
    }

    /**
     * 获取库存类型名称
     */
    public function getInventoryTypeNameAttribute()
    {
        $types = [
            'regular' => '常规库存',
            'allotment' => '配额库存',
            'group' => '团队库存',
            'series' => '系列库存',
            'dynamic' => '动态库存',
            'elastic' => '弹性库存',
            'guaranteed' => '保证库存',
            'non_guaranteed' => '非保证库存',
            'free_sale' => '自由销售',
            'on_request' => '申请确认',
            'stop_sale' => '停止销售',
            'closed' => '关房'
        ];

        return $types[$this->inventory_type] ?? '常规库存';
    }

    /**
     * 获取库存来源名称
     */
    public function getInventorySourceNameAttribute()
    {
        $sources = [
            'manual' => '手动设置',
            'system' => '系统自动',
            'import' => '批量导入',
            'api' => 'API接口',
            'channel' => '渠道同步',
            'pms' => 'PMS系统',
            'rms' => 'RMS系统',
            'crs' => 'CRS系统'
        ];

        return $sources[$this->inventory_source] ?? '手动设置';
    }

    /**
     * 获取分配方法名称
     */
    public function getAllocationMethodNameAttribute()
    {
        $methods = [
            'equal' => '平均分配',
            'priority' => '优先级分配',
            'percentage' => '百分比分配',
            'fixed' => '固定分配',
            'dynamic' => '动态分配',
            'demand_based' => '需求导向',
            'revenue_based' => '收益导向',
            'competitive' => '竞争导向'
        ];

        return $methods[$this->allocation_method] ?? '平均分配';
    }

    /**
     * 获取配额状态名称
     */
    public function getAllotmentStatusNameAttribute()
    {
        $statuses = [
            'active' => '激活',
            'inactive' => '未激活',
            'pending' => '待确认',
            'confirmed' => '已确认',
            'released' => '已释放',
            'expired' => '已过期',
            'cancelled' => '已取消',
            'suspended' => '已暂停'
        ];

        return $statuses[$this->allotment_status] ?? '激活';
    }

    /**
     * 获取需求等级名称
     */
    public function getMarketDemandLevelNameAttribute()
    {
        $levels = [
            'very_low' => '极低需求',
            'low' => '低需求',
            'moderate' => '中等需求',
            'high' => '高需求',
            'very_high' => '极高需求',
            'peak' => '峰值需求',
            'exceptional' => '异常需求'
        ];

        return $levels[$this->market_demand_level] ?? '中等需求';
    }

    /**
     * 获取房间分配信息
     */
    public function getRoomAllocationInfo()
    {
        return [
            'total' => $this->total_rooms,
            'available' => $this->available_rooms,
            'sold' => $this->sold_rooms,
            'blocked' => $this->blocked_rooms,
            'maintenance' => $this->maintenance_rooms,
            'overbooking' => $this->overbooking_rooms,
            'contracted' => $this->contracted_rooms ?? 0,
            'released' => $this->released_rooms ?? 0,
            'pickup' => $this->pickup_rooms ?? 0,
            'no_show' => $this->no_show_rooms ?? 0,
            'walk_in' => $this->walk_in_rooms ?? 0,
            'upgrade' => $this->upgrade_rooms ?? 0,
            'downgrade' => $this->downgrade_rooms ?? 0,
            'comp' => $this->comp_rooms ?? 0,
            'house_use' => $this->house_use_rooms ?? 0,
            'out_of_order' => $this->out_of_order_rooms ?? 0,
            'out_of_inventory' => $this->out_of_inventory_rooms ?? 0,
            'repair' => $this->repair_rooms ?? 0,
            'deep_clean' => $this->deep_clean_rooms ?? 0,
            'renovation' => $this->renovation_rooms ?? 0,
            'seasonal_closure' => $this->seasonal_closure_rooms ?? 0
        ];
    }

    /**
     * 获取收益管理信息
     */
    public function getRevenueManagementInfo()
    {
        return [
            'adr' => [
                'actual' => $this->adr_actual,
                'budget' => $this->adr_budget,
                'forecast' => $this->adr_forecast,
                'last_year' => $this->adr_last_year
            ],
            'revpar' => [
                'actual' => $this->revpar_actual,
                'budget' => $this->revpar_budget,
                'forecast' => $this->revpar_forecast,
                'last_year' => $this->revpar_last_year
            ],
            'metrics' => [
                'pickup_percentage' => $this->pickup_percentage,
                'wash_factor' => $this->wash_factor,
                'displacement_factor' => $this->displacement_factor,
                'booking_pace' => $this->booking_pace,
                'cancellation_rate' => $this->cancellation_rate,
                'no_show_rate' => $this->no_show_rate
            ],
            'adjustments' => [
                'seasonal' => $this->seasonal_adjustment,
                'demand' => $this->demand_adjustment,
                'competitive' => $this->competitive_adjustment,
                'holiday_premium' => $this->holiday_premium,
                'weekend_premium' => $this->weekend_premium,
                'event_premium' => $this->event_premium
            ]
        ];
    }

    /**
     * 获取限制条件信息
     */
    public function getRestrictionsInfo()
    {
        return [
            'distribution_channels' => $this->distribution_channels ?? [],
            'channel_restrictions' => $this->channel_restrictions ?? [],
            'booking_restrictions' => $this->booking_restrictions ?? [],
            'guest_restrictions' => $this->guest_restrictions ?? [],
            'length_of_stay_restrictions' => $this->length_of_stay_restrictions ?? [],
            'advance_booking_restrictions' => $this->advance_booking_restrictions ?? [],
            'cutoff_time' => $this->cutoff_time,
            'release_time' => $this->release_time
        ];
    }

    /**
     * 获取优化设置信息
     */
    public function getOptimizationInfo()
    {
        return [
            'inventory_optimization' => $this->inventory_optimization ?? false,
            'revenue_optimization' => $this->revenue_optimization ?? false,
            'yield_optimization' => $this->yield_optimization ?? false,
            'overbooking_optimization' => $this->overbooking_optimization ?? false
        ];
    }

    /**
     * 计算实际可售房量（考虑各种限制）
     */
    public function calculateActualAvailability($checkDate = null, $channelCode = null)
    {
        $checkDate = $checkDate ?: $this->date;
        $baseAvailable = $this->available_rooms;

        // 检查关房状态
        if ($this->is_closed || $this->is_stop_sale) {
            return 0;
        }

        // 检查渠道限制
        if ($channelCode && !empty($this->channel_restrictions)) {
            $restrictions = $this->channel_restrictions;
            if (isset($restrictions[$channelCode]) && !$restrictions[$channelCode]['available']) {
                return 0;
            }
        }

        // 检查配额限制
        if ($this->allotment_type && $this->free_sale_limit > 0) {
            $baseAvailable = min($baseAvailable, $this->free_sale_limit);
        }

        // 考虑超售限制
        if ($this->oversell_limit > 0) {
            $baseAvailable += $this->oversell_limit;
        }

        return max(0, $baseAvailable);
    }
}
