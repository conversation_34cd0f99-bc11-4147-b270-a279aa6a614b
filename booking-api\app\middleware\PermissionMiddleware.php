<?php

namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

/**
 * 权限控制中间件
 */
class PermissionMiddleware implements MiddlewareInterface
{
    /**
     * 需要的权限
     *
     * @var string
     */
    private $permission;

    /**
     * 构造函数
     *
     * @param string $permission
     */
    public function __construct(string $permission)
    {
        $this->permission = $permission;
    }

    /**
     * 处理请求
     *
     * @param Request $request
     * @param callable $handler
     * @return Response
     */
    public function process(Request $request, callable $handler): Response
    {
        // 检查用户是否已认证
        $user = $request->user ?? null;
        if (!$user) {
            return $this->forbiddenResponse('用户未认证');
        }

        // 检查用户权限
        if (!$this->checkPermission($user, $this->permission)) {
            return $this->forbiddenResponse('权限不足');
        }

        return $handler($request);
    }

    /**
     * 检查用户权限
     *
     * @param mixed $user
     * @param string $permission
     * @return bool
     */
    private function checkPermission($user, string $permission): bool
    {
        // 超级管理员拥有所有权限
        if ($user->user_type === 'admin') {
            return true;
        }

        // 检查用户角色权限
        if (method_exists($user, 'hasPermission')) {
            return $user->hasPermission($permission);
        }

        // 基于用户类型的简单权限检查
        return $this->checkUserTypePermission($user->user_type, $permission);
    }

    /**
     * 基于用户类型的权限检查
     *
     * @param string $userType
     * @param string $permission
     * @return bool
     */
    private function checkUserTypePermission(string $userType, string $permission): bool
    {
        $permissions = [
            'admin' => [
                'hotel.view', 'hotel.create', 'hotel.update', 'hotel.delete',
                'room_type.view', 'room_type.create', 'room_type.update', 'room_type.delete',
                'inventory.view', 'inventory.update',
                'rate.view', 'rate.update',
                'booking.view', 'booking.create', 'booking.update', 'booking.cancel',
                'supplier.view', 'supplier.create', 'supplier.update', 'supplier.delete',
                'user.view', 'user.create', 'user.update', 'user.delete',
            ],
            'hotel_manager' => [
                'hotel.view', 'hotel.update',
                'room_type.view', 'room_type.create', 'room_type.update',
                'inventory.view', 'inventory.update',
                'rate.view', 'rate.update',
                'booking.view', 'booking.create', 'booking.update', 'booking.cancel',
            ],
            'staff' => [
                'hotel.view',
                'room_type.view',
                'inventory.view',
                'rate.view',
                'booking.view', 'booking.create', 'booking.update',
            ],
            'customer' => [
                'hotel.view',
                'room_type.view',
                'booking.view', 'booking.create',
            ],
        ];

        $userPermissions = $permissions[$userType] ?? [];
        return in_array($permission, $userPermissions);
    }

    /**
     * 返回禁止访问响应
     *
     * @param string $message
     * @return Response
     */
    private function forbiddenResponse(string $message): Response
    {
        return json([
            'code' => 403,
            'message' => $message,
            'timestamp' => time()
        ], 403);
    }

    /**
     * 创建权限中间件实例
     *
     * @param string $permission
     * @return PermissionMiddleware
     */
    public static function create(string $permission): self
    {
        return new self($permission);
    }

    /**
     * 权限常量定义
     */
    const HOTEL_VIEW = 'hotel.view';
    const HOTEL_CREATE = 'hotel.create';
    const HOTEL_UPDATE = 'hotel.update';
    const HOTEL_DELETE = 'hotel.delete';

    const ROOM_TYPE_VIEW = 'room_type.view';
    const ROOM_TYPE_CREATE = 'room_type.create';
    const ROOM_TYPE_UPDATE = 'room_type.update';
    const ROOM_TYPE_DELETE = 'room_type.delete';

    const INVENTORY_VIEW = 'inventory.view';
    const INVENTORY_UPDATE = 'inventory.update';

    const RATE_VIEW = 'rate.view';
    const RATE_UPDATE = 'rate.update';

    const BOOKING_VIEW = 'booking.view';
    const BOOKING_CREATE = 'booking.create';
    const BOOKING_UPDATE = 'booking.update';
    const BOOKING_CANCEL = 'booking.cancel';

    const SUPPLIER_VIEW = 'supplier.view';
    const SUPPLIER_CREATE = 'supplier.create';
    const SUPPLIER_UPDATE = 'supplier.update';
    const SUPPLIER_DELETE = 'supplier.delete';

    const USER_VIEW = 'user.view';
    const USER_CREATE = 'user.create';
    const USER_UPDATE = 'user.update';
    const USER_DELETE = 'user.delete';
}
