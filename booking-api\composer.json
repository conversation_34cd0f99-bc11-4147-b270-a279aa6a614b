{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=8.2", "workerman/webman-framework": "~2.1", "monolog/monolog": "^2.0", "webman/redis": "^2.1", "webman/cache": "^2.1", "webman/redis-queue": "^1.3", "vlucas/phpdotenv": "^5.6", "workerman/crontab": "^1.0", "webman/console": "^2.1", "firebase/php-jwt": "^6.11", "tinywan/jwt": "^1.11", "php-di/php-di": "^7.0", "webman-tech/laravel-validation": "^11.0", "ramsey/uuid": "^4.9", "ext-pdo": "*", "webman/event": "^1.0", "webman/database": "^2.1", "illuminate/pagination": "^11.45", "illuminate/events": "^11.45", "symfony/var-dumper": "^7.3", "workerman/workerman": "~5.1", "ext-openssl": "*"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components"}}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}, "minimum-stability": "dev", "prefer-stable": true}