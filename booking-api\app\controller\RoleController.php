<?php

namespace app\controller;

use app\model\Role;
use app\model\UserRole;
use support\Request;
use support\Response;
use support\Db;

/**
 * 角色管理控制器
 */
class RoleController extends BaseController
{
    /**
     * 获取角色列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        try {
            $page = (int)$request->get('page', 1);
            $perPage = (int)$request->get('per_page', 10);
            $name = $request->get('name');
            $code = $request->get('code');
            $status = $request->get('status');

            $query = Role::query();

            // 搜索条件
            if ($name) {
                $query->where('name', 'like', "%{$name}%");
            }
            if ($code) {
                $query->where('code', 'like', "%{$code}%");
            }
            if ($status) {
                $query->where('status', $status);
            }

            // 分页查询
            $total = $query->count();
            $roles = $query->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $perPage)
                ->limit($perPage)
                ->get();

            $items = $roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'code' => $role->code,
                    'description' => $role->description,
                    'permissions' => $role->permissions,
                    'status' => $role->status,
                    'created_at' => $role->created_at,
                    'updated_at' => $role->updated_at,
                    'user_count' => UserRole::where('role_id', $role->id)->count(),
                ];
            });

            return $this->success([
                'items' => $items,
                'pagination' => [
                    'total' => $total,
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'last_page' => ceil($total / $perPage),
                ]
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取角色列表');
        }
    }

    /**
     * 获取角色详情
     *
     * @param Request $request
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $roleId = (int)$id;
            
            if ($roleId <= 0) {
                return $this->error('角色ID无效');
            }

            $role = Role::find($roleId);
            
            if (!$role) {
                return $this->error('角色不存在', 404);
            }

            return $this->success([
                'id' => $role->id,
                'name' => $role->name,
                'code' => $role->code,
                'description' => $role->description,
                'permissions' => $role->permissions,
                'status' => $role->status,
                'created_at' => $role->created_at,
                'updated_at' => $role->updated_at,
                'user_count' => UserRole::where('role_id', $role->id)->count(),
            ]);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取角色详情');
        }
    }

    /**
     * 创建角色
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        try {
            $data = $this->getInput($request);

            // 验证必需参数
            $errors = $this->validateRequired($data, ['name', 'code']);
            if ($errors) {
                return $this->error('参数验证失败', 400, $errors);
            }

            // 检查角色名称是否已存在
            if (Role::where('name', $data['name'])->exists()) {
                return $this->error('角色名称已存在');
            }

            // 检查角色代码是否已存在
            if (Role::where('code', $data['code'])->exists()) {
                return $this->error('角色代码已存在');
            }

            // 创建角色
            $roleData = [
                'name' => $data['name'],
                'code' => $data['code'],
                'description' => $data['description'] ?? null,
                'permissions' => $data['permissions'] ?? [],
                'status' => $data['status'] ?? Role::STATUS_ACTIVE,
            ];

            $role = Role::create($roleData);

            return $this->success([
                'id' => $role->id,
                'name' => $role->name,
                'code' => $role->code,
                'description' => $role->description,
                'permissions' => $role->permissions,
                'status' => $role->status,
            ], '角色创建成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '创建角色');
        }
    }

    /**
     * 更新角色
     *
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id)
    {
        try {
            $roleId = (int)$id;
            
            if ($roleId <= 0) {
                return $this->error('角色ID无效');
            }

            $role = Role::find($roleId);
            
            if (!$role) {
                return $this->error('角色不存在', 404);
            }

            $data = $this->getInput($request);

            // 检查角色名称是否已存在（排除当前角色）
            if (!empty($data['name']) && 
                Role::where('name', $data['name'])->where('id', '!=', $roleId)->exists()) {
                return $this->error('角色名称已存在');
            }

            // 检查角色代码是否已存在（排除当前角色）
            if (!empty($data['code']) && 
                Role::where('code', $data['code'])->where('id', '!=', $roleId)->exists()) {
                return $this->error('角色代码已存在');
            }

            // 更新角色信息
            $updateData = [];
            $allowedFields = ['name', 'code', 'description', 'permissions', 'status'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            if ($updateData) {
                $role->update($updateData);
            }

            return $this->success([
                'id' => $role->id,
                'name' => $role->name,
                'code' => $role->code,
                'description' => $role->description,
                'permissions' => $role->permissions,
                'status' => $role->status,
            ], '角色更新成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '更新角色');
        }
    }

    /**
     * 删除角色
     *
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $roleId = (int)$id;
            
            if ($roleId <= 0) {
                return $this->error('角色ID无效');
            }

            $role = Role::find($roleId);
            
            if (!$role) {
                return $this->error('角色不存在', 404);
            }

            // 检查是否有用户使用此角色
            $userCount = UserRole::where('role_id', $roleId)->count();
            if ($userCount > 0) {
                return $this->error('该角色下还有用户，无法删除');
            }

            $role->delete();

            return $this->success(null, '角色删除成功');

        } catch (\Exception $e) {
            return $this->handleException($e, '删除角色');
        }
    }

    /**
     * 获取所有角色（用于下拉选择）
     *
     * @param Request $request
     * @return Response
     */
    public function all(Request $request)
    {
        try {
            $roles = Role::where('status', Role::STATUS_ACTIVE)
                ->orderBy('name')
                ->get()
                ->map(function ($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'code' => $role->code,
                        'description' => $role->description,
                    ];
                });

            return $this->success($roles);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取所有角色');
        }
    }

    /**
     * 获取权限列表
     *
     * @param Request $request
     * @return Response
     */
    public function getPermissions(Request $request)
    {
        try {
            // 定义系统权限
            $permissions = [
                // 用户管理
                'user.view' => '查看用户',
                'user.create' => '创建用户',
                'user.update' => '更新用户',
                'user.delete' => '删除用户',
                'user.assign_role' => '分配角色',
                
                // 角色管理
                'role.view' => '查看角色',
                'role.create' => '创建角色',
                'role.update' => '更新角色',
                'role.delete' => '删除角色',
                
                // 酒店管理
                'hotel.view' => '查看酒店',
                'hotel.create' => '创建酒店',
                'hotel.update' => '更新酒店',
                'hotel.delete' => '删除酒店',
                
                // 房型管理
                'room_type.view' => '查看房型',
                'room_type.create' => '创建房型',
                'room_type.update' => '更新房型',
                'room_type.delete' => '删除房型',
                
                // 库存管理
                'inventory.view' => '查看库存',
                'inventory.update' => '更新库存',
                
                // 价格管理
                'rate.view' => '查看价格',
                'rate.update' => '更新价格',
                
                // 订单管理
                'booking.view' => '查看订单',
                'booking.create' => '创建订单',
                'booking.update' => '更新订单',
                'booking.cancel' => '取消订单',
                
                // 供应商管理
                'supplier.view' => '查看供应商',
                'supplier.create' => '创建供应商',
                'supplier.update' => '更新供应商',
                'supplier.delete' => '删除供应商',
                
                // 系统管理
                'system.view' => '查看系统信息',
                'system.config' => '系统配置',
            ];

            // 按模块分组
            $groupedPermissions = [];
            foreach ($permissions as $code => $name) {
                $module = explode('.', $code)[0];
                if (!isset($groupedPermissions[$module])) {
                    $groupedPermissions[$module] = [
                        'name' => $this->getModuleName($module),
                        'permissions' => []
                    ];
                }
                $groupedPermissions[$module]['permissions'][] = [
                    'code' => $code,
                    'name' => $name,
                ];
            }

            return $this->success($groupedPermissions);

        } catch (\Exception $e) {
            return $this->handleException($e, '获取权限列表');
        }
    }

    /**
     * 获取模块名称
     */
    private function getModuleName($module)
    {
        $moduleNames = [
            'user' => '用户管理',
            'role' => '角色管理',
            'hotel' => '酒店管理',
            'room_type' => '房型管理',
            'inventory' => '库存管理',
            'rate' => '价格管理',
            'booking' => '订单管理',
            'supplier' => '供应商管理',
            'system' => '系统管理',
        ];

        return $moduleNames[$module] ?? $module;
    }
}
