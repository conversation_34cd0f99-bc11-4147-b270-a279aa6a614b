<?php

namespace app\model;

/**
 * 供应商酒店映射模型
 * 对应数据库表：supplier_hotel_mappings
 */
class SupplierHotelMapping extends BaseModel
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'supplier_hotel_mappings';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'supplier_id',
        'hotel_id',
        'supplier_hotel_id',
        'supplier_hotel_code',
        'supplier_hotel_name',
        'room_type_mappings',
        'rate_plan_mappings',
        'is_active',
        'last_sync_at',
        'sync_status',
        'sync_error'
    ];

    /**
     * 属性转换
     *
     * @var array
     */
    protected $casts = [
        'supplier_id' => 'integer',
        'hotel_id' => 'integer',
        'room_type_mappings' => 'array',
        'rate_plan_mappings' => 'array',
        'is_active' => 'boolean',
        'last_sync_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 同步状态常量
     */
    const SYNC_STATUS_PENDING = 'pending';
    const SYNC_STATUS_SUCCESS = 'success';
    const SYNC_STATUS_FAILED = 'failed';
    const SYNC_STATUS_SYNCING = 'syncing';

    /**
     * 获取供应商
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * 获取酒店
     */
    public function hotel()
    {
        return $this->belongsTo(Hotel::class);
    }

    /**
     * 作用域：活跃的映射
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按同步状态筛选
     */
    public function scopeBySyncStatus($query, $status)
    {
        return $query->where('sync_status', $status);
    }

    /**
     * 作用域：需要同步的
     */
    public function scopeNeedsSync($query, $intervalMinutes = 60)
    {
        return $query->where(function ($q) use ($intervalMinutes) {
            $q->whereNull('last_sync_at')
              ->orWhere('last_sync_at', '<', now()->subMinutes($intervalMinutes));
        })->where('is_active', true);
    }

    /**
     * 获取房型映射
     */
    public function getRoomTypeMapping($localRoomTypeId)
    {
        $mappings = $this->room_type_mappings ?? [];
        return $mappings[$localRoomTypeId] ?? null;
    }

    /**
     * 设置房型映射
     */
    public function setRoomTypeMapping($localRoomTypeId, $supplierRoomTypeId, $supplierRoomTypeName = null)
    {
        $mappings = $this->room_type_mappings ?? [];
        $mappings[$localRoomTypeId] = [
            'supplier_room_type_id' => $supplierRoomTypeId,
            'supplier_room_type_name' => $supplierRoomTypeName,
            'updated_at' => now()->toISOString()
        ];
        $this->room_type_mappings = $mappings;
        return $this;
    }

    /**
     * 移除房型映射
     */
    public function removeRoomTypeMapping($localRoomTypeId)
    {
        $mappings = $this->room_type_mappings ?? [];
        unset($mappings[$localRoomTypeId]);
        $this->room_type_mappings = $mappings;
        return $this;
    }

    /**
     * 获取价格计划映射
     */
    public function getRatePlanMapping($localRatePlanId)
    {
        $mappings = $this->rate_plan_mappings ?? [];
        return $mappings[$localRatePlanId] ?? null;
    }

    /**
     * 设置价格计划映射
     */
    public function setRatePlanMapping($localRatePlanId, $supplierRatePlanId, $supplierRatePlanName = null)
    {
        $mappings = $this->rate_plan_mappings ?? [];
        $mappings[$localRatePlanId] = [
            'supplier_rate_plan_id' => $supplierRatePlanId,
            'supplier_rate_plan_name' => $supplierRatePlanName,
            'updated_at' => now()->toISOString()
        ];
        $this->rate_plan_mappings = $mappings;
        return $this;
    }

    /**
     * 移除价格计划映射
     */
    public function removeRatePlanMapping($localRatePlanId)
    {
        $mappings = $this->rate_plan_mappings ?? [];
        unset($mappings[$localRatePlanId]);
        $this->rate_plan_mappings = $mappings;
        return $this;
    }

    /**
     * 更新同步状态
     */
    public function updateSyncStatus($status, $error = null)
    {
        $this->sync_status = $status;
        $this->sync_error = $error;
        
        if ($status === self::SYNC_STATUS_SUCCESS) {
            $this->last_sync_at = now();
            $this->sync_error = null;
        }
        
        return $this->save();
    }

    /**
     * 标记同步开始
     */
    public function markSyncStarted()
    {
        return $this->updateSyncStatus(self::SYNC_STATUS_SYNCING);
    }

    /**
     * 标记同步成功
     */
    public function markSyncSuccess()
    {
        return $this->updateSyncStatus(self::SYNC_STATUS_SUCCESS);
    }

    /**
     * 标记同步失败
     */
    public function markSyncFailed($error)
    {
        return $this->updateSyncStatus(self::SYNC_STATUS_FAILED, $error);
    }

    /**
     * 获取同步状态名称
     */
    public function getSyncStatusNameAttribute()
    {
        $statuses = [
            self::SYNC_STATUS_PENDING => '待同步',
            self::SYNC_STATUS_SUCCESS => '同步成功',
            self::SYNC_STATUS_FAILED => '同步失败',
            self::SYNC_STATUS_SYNCING => '同步中',
        ];

        return $statuses[$this->sync_status] ?? '未知';
    }

    /**
     * 检查是否需要同步
     */
    public function needsSync($intervalMinutes = 60)
    {
        if (!$this->is_active) {
            return false;
        }

        if (!$this->last_sync_at) {
            return true;
        }

        return $this->last_sync_at->diffInMinutes(now()) >= $intervalMinutes;
    }

    /**
     * 启用映射
     */
    public function enable()
    {
        $this->is_active = true;
        return $this->save();
    }

    /**
     * 禁用映射
     */
    public function disable()
    {
        $this->is_active = false;
        return $this->save();
    }

    /**
     * 获取所有房型映射
     */
    public function getAllRoomTypeMappings()
    {
        return $this->room_type_mappings ?? [];
    }

    /**
     * 获取所有价格计划映射
     */
    public function getAllRatePlanMappings()
    {
        return $this->rate_plan_mappings ?? [];
    }

    /**
     * 批量设置房型映射
     */
    public function batchSetRoomTypeMappings(array $mappings)
    {
        $currentMappings = $this->room_type_mappings ?? [];
        
        foreach ($mappings as $localId => $supplierData) {
            $currentMappings[$localId] = [
                'supplier_room_type_id' => $supplierData['supplier_room_type_id'],
                'supplier_room_type_name' => $supplierData['supplier_room_type_name'] ?? null,
                'updated_at' => now()->toISOString()
            ];
        }
        
        $this->room_type_mappings = $currentMappings;
        return $this;
    }

    /**
     * 批量设置价格计划映射
     */
    public function batchSetRatePlanMappings(array $mappings)
    {
        $currentMappings = $this->rate_plan_mappings ?? [];
        
        foreach ($mappings as $localId => $supplierData) {
            $currentMappings[$localId] = [
                'supplier_rate_plan_id' => $supplierData['supplier_rate_plan_id'],
                'supplier_rate_plan_name' => $supplierData['supplier_rate_plan_name'] ?? null,
                'updated_at' => now()->toISOString()
            ];
        }
        
        $this->rate_plan_mappings = $currentMappings;
        return $this;
    }
}
